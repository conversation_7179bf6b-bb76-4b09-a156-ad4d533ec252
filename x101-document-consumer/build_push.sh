#Go to the parent folder i.e generative, then run this file
mvn clean

mvn clean install -DskipTests -Dproguard.skip=true -Ddependency-check.skip=true

cd target/docker/lib/
xattr -c *.jar

cd ../
tar -cvf x101-document-service.tar config.properties melodyposthook.sh x101-document-service.jar application.properties lib run.sh

docker build --platform=linux/amd64  . -t  registry.visionwaves.com/x101-document-service:$1 --no-cache

# Step 6: Push Docker Image
docker push  registry.visionwaves.com/x101-document-service:$1
