/*
 * 
 */
package com.enttribe.x101;

import java.util.concurrent.Executor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * The Class AsyncConfig.
 */
@EnableAsync
@Configuration
public class AsyncConfig {
	
	/**
	 * Gets the thread pool executor.
	 *
	 * @return the thread pool executor
	 */
    @Primary
	@Bean(name="multithreadingbean")
    public Executor getThreadPoolExecutor() {

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(1);
        executor.setMaxPoolSize(10);
        executor.setThreadNamePrefix("DbThread-");
        executor.initialize();
        
        return executor;
        
    }
}
