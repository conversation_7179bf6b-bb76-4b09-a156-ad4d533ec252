package com.enttribe.x101.controller.impl;

import com.enttribe.x101.controller.UserMailAttachmentController;
import com.enttribe.x101.model.UserMailAttachment;
import com.enttribe.x101.service.UserMailAttachmentService;
import com.enttribe.x101.service.impl.DataLoadingServiceImpl;
import com.enttribe.x101.service.impl.VectorService;
import com.enttribe.x101.utils.DocumentConverter;
import com.enttribe.x101.utils.Utils;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.json.JSONObject;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@RequestMapping("/UserMailAttachment")
@RestController
public class UserMailAttachmentControllerImpl implements UserMailAttachmentController {

    @Autowired
    UserMailAttachmentService userMailAttachmentService;

    @Autowired
    DataLoadingServiceImpl dataLoadingService;

    @Autowired
    VectorService vectorService;

    public static final String PDF_EXTENSION = "pdf";
    public static final String DOC_EXTENSION = "doc";
    public static final String DOCS_EXTENSION = "docs";
    public static final String DOCX_EXTENSION = "docx";

    @Override
    public UserMailAttachment create(UserMailAttachment entityTemp) {
        return userMailAttachmentService.create(entityTemp);
    }


    @Override
    public UserMailAttachment update(UserMailAttachment entityTemp) {
        return userMailAttachmentService.update(entityTemp);
    }

    @Override
    public List<UserMailAttachment> search(
            String filter,
            @NotNull Integer offset,
            @NotNull Integer size,
            String orderBy,
            String orderType
    ) {
        return userMailAttachmentService.search(
                filter,
                offset,
                size,
                orderBy,
                orderType
        );
    }

    @Override
    public Long count(String filter) {
        return userMailAttachmentService.count(filter);
    }


    @Override
    public ResponseEntity<Object> processDocument(String batchId){
        log.info("processing document for batch id {}", batchId);
        dataLoadingService.processDocument(batchId);
        return ResponseEntity.ok("{\"success\":\"Document processing started\"}");
    }

    @Override
    public ResponseEntity<Object> processLargeDocument(){
        dataLoadingService.processLargeDocument();
        return ResponseEntity.ok("{\"success\":\"Document processing started\"}");
    }


    @Override
    public ResponseEntity<Object> getAnswerOnDocument(Map<String, String> body){

        String fileId = body.get("fileId");
        String question = body.get("question");
        String answerOnDocument = dataLoadingService.getAnswerOnDocument(fileId, question);
        System.out.print("answerOnDocument==>"+ answerOnDocument);
        return ResponseEntity.ok(answerOnDocument);
    }

    @Override
    public ResponseEntity<Object> splitText() throws IOException {


        String[] primarySeparators = {"\n"};
        String[] secondarySeparators = {"\\. "};


//        File pdfFilePath =  new File("/Users/<USER>/Downloads/10840-001.pdf");

        String docPath = "/Users/<USER>/Downloads/20240821192323_ADC_EA_High-Level+Design_NourOS_v1.6.docx";

        int lastSlashIndex = docPath.lastIndexOf('/');
        long timestampInSeconds = System.currentTimeMillis();

        String fileName = docPath.substring(lastSlashIndex + 1);
        String filePath = docPath.substring(0, lastSlashIndex);
        String newFileName = fileName + "_" + timestampInSeconds;

        System.out.println("filePath==>"+filePath);
        System.out.println("fileName==>"+fileName);

        int dotIndex = fileName.lastIndexOf('.');

        String extension = dotIndex != -1 ? fileName.substring(dotIndex + 1) : "";
        String fileNameWithoutExtension = fileName.substring(0, dotIndex);

        System.out.println("File extension: " + extension);
        System.out.println("new file name: " + fileNameWithoutExtension + "_" + System.currentTimeMillis()+ "." + extension);

        switch (extension) {
            case PDF_EXTENSION -> {
                Map<String, String> stringStringMap = dataLoadingService.readPdfFile(docPath);
                // return ResponseEntity.ok(dataLoadingService.recursiveChunking(fullText, primarySeparators, secondarySeparators));
                return ResponseEntity.ok(dataLoadingService.normalChunking(stringStringMap.get("content")));
            }
            case DOC_EXTENSION -> {
                Map<String, String> stringStringMap =  dataLoadingService.readWordDocFile(docPath);
                // return ResponseEntity.ok(dataLoadingService.recursiveChunking(fullText, primarySeparators, secondarySeparators));
                return ResponseEntity.ok(dataLoadingService.normalChunking(stringStringMap.get("content")));
            }
            // return ResponseEntity.ok(dataLoadingService.recursiveChunking(fullText, primarySeparators, secondarySeparators));
            case DOCS_EXTENSION,  DOCX_EXTENSION -> {
                Map<String, String> stringStringMap = dataLoadingService.readWordFile(docPath);
                // return ResponseEntity.ok(dataLoadingService.recursiveChunking(fullText, primarySeparators, secondarySeparators));
                return ResponseEntity.ok(dataLoadingService.normalChunking(stringStringMap.get("content")));
                // return ResponseEntity.ok(dataLoadingService.recursiveChunking(fullText, primarySeparators, secondarySeparators));
            }default -> {
                return null;
            }

        }
    }

    @Override
    public ResponseEntity<Object> testSplitText() throws Exception {

        String docPath = "/Users/<USER>/Desktop/TOC.pdf";

        Map<String, String> stringStringMap = dataLoadingService.readPdfFile(docPath);
        // System.out.println("full text = >" + s);
       dataLoadingService.testSplitPDFText(stringStringMap.get("content"));
       // dataLoadingService.testSplitPDFText(s);
        return ResponseEntity.ok("{\"success\":\"done\"}");

    }

    @Override
    public ResponseEntity<Object> checkEnv() throws Exception {
        dataLoadingService.deleteFileFromObjectStorage(null, "/Users/<USER>/Downloads/20240820143226_High+Level+Design_NourOS(HLD)+(1).pdf");
        return ResponseEntity.ok("{\"success\":\"done\"}");
    }

    @Override
    public ResponseEntity<Object> loadMailText(Map<String, String> body) throws Exception {
        dataLoadingService.loadMailText(body);
        return ResponseEntity.ok("{\"success\":\"Document loaded successfully\"}");

    }
    public ResponseEntity<Object> storePluginMessageSummary(String bodyString) {
        try {
            log.debug("Going to save mail summary in vectpr {}",bodyString.toString());
            JSONObject body=new JSONObject(bodyString);
            // Extract keys from the map
            String mailReceivedDate= body.getString("mailReceivedDate");
            String email = body.getString("email");
            String summaryText = body.getString("summaryText");
            log.debug("summary Text is {}",summaryText);

            // Call the service method
            vectorService.storePluginMessageSummary(mailReceivedDate, email, summaryText);

            return ResponseEntity.ok().build();
        } catch (Exception e) {
            // Handle exceptions and return a meaningful response
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Failed to store plugin message summary: " + e.getMessage());
        }
    }

    public ResponseEntity<Object> storeVoiceConversationSummary(Map<String, String> body) {
        try {
            // Extract keys from the map
            String conversationId = body.get("conversationId");
            String date = body.get("date");
            String email = body.get("email");
            String summaryText = body.get("summaryText");

            // Call the service method
            vectorService.storeVoiceConversationSummary(conversationId, date, email, summaryText);

            return ResponseEntity.ok().build();
        } catch (Exception e) {
            // Handle exceptions and return a meaningful response
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Failed to store voice conversation summary: " + e.getMessage());
        }
    }

        public ResponseEntity<Object> queryPluginMessageSummary(Map<String, String> body) {
            try {
                // Extract keys from the map
                String email = body.get("email");
                String queryText = body.get("queryText");
                log.info("user id is {} query is {}",email,queryText);
                // Call the service method
                String result = vectorService.queryPluginMessageSummary(email, queryText);

                return ResponseEntity.ok(result);
            } catch (Exception e) {
                // Handle exceptions and return a meaningful response
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Failed to query plugin message summary: " + e.getMessage());
            }
        }

    }


  
