package com.enttribe.x101.controller.impl;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * The Class ReadinessProbe.
 */
@RestController
@RequestMapping("/readinessProbe")
public class ReadinessProbe {

    /**
     * Ping.
     *
     * @return the response entity
     */
    @GetMapping("/ping")
    public ResponseEntity<Void> ping() {
        return ResponseEntity.ok().build();
    }
}