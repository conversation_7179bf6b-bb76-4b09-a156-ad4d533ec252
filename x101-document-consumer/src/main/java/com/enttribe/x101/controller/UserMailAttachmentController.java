package com.enttribe.x101.controller;

import com.enttribe.x101.model.UserMailAttachment;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@ResponseBody
@FeignClient(name = "UserMailAttachmentController")
public interface UserMailAttachmentController {

    @PostMapping("/create")
    @Operation(summary = "Create UserMailAttachment", tags = "UserMailAttachment", description = "Create a new UserMailAttachment.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    UserMailAttachment create(@RequestBody UserMailAttachment entityTemp);

    @PostMapping("/update")
    @Operation(summary = "Update UserMailAttachment", tags = "UserMailAttachment", description = "Update an existing UserMailAttachment.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    UserMailAttachment update(@RequestBody UserMailAttachment entityTemp);

    /**
     * Search.
     *
     * @param filter    the filter
     * @param offset    the offset
     * @param size      the size
     * @param orderBy   the order by
     * @param orderType the order type
     * @return the list
     */
    @GetMapping("/search")
    @Operation(summary = "Search UserMailAttachment", tags = "UserMailAttachment", description = "Search for UserMailAttachments based on provided criteria.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    public List<UserMailAttachment> search(
            @RequestParam("filter") String filter,
            @NotNull @RequestParam("offset") Integer offset,
            @NotNull @RequestParam("size") Integer size,
            @RequestParam("orderBy") String orderBy,
            @RequestParam("orderType") String orderType
    );

    /**
     * Count.
     *
     * @param filter the filter
     * @return the long
     */
    @GetMapping("/count")
    @Operation(summary = "Count UserMailAttachment", tags = "UserMailAttachment", description = "Count the number of UserMailAttachment matching the provided filter.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    Long count(@RequestParam("filter") String filter);


    @GetMapping("/processDocument")
    @Operation(summary = "Process new documents", tags = "UserMailAttachment", description = "Process new documents for chunking.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    ResponseEntity<Object> processDocument(@RequestParam("batchId") String batchId);

    @GetMapping("/processLargeDocument")
    @Operation(summary = "Process new documents", tags = "UserMailAttachment", description = "Process new documents for chunking.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    ResponseEntity<Object> processLargeDocument();

    @PostMapping("/getAnswer")
    @Operation(summary = "Process new documents", tags = "UserMailAttachment", description = "Process new documents for chunking.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    ResponseEntity<Object> getAnswerOnDocument(@RequestBody Map<String, String> body);

    @GetMapping("/splitText")
    @Operation(summary = "Process new documents", tags = "UserMailAttachment", description = "Process new documents for chunking.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    ResponseEntity<Object> splitText() throws IOException;


    @GetMapping("/testSplitText")
    @Operation(summary = "Process new documents", tags = "UserMailAttachment", description = "Process new documents for chunking.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    ResponseEntity<Object> testSplitText() throws Exception;

    @GetMapping("/checkEnv")
    @Operation(summary = "Process new documents", tags = "UserMailAttachment", description = "Process new documents for chunking.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    ResponseEntity<Object> checkEnv() throws Exception;

    @PostMapping("/loadMailText")
    @Operation(summary = "Process new documents", tags = "UserMailAttachment", description = "Process new documents for chunking.")
    @ApiResponse(responseCode = "400", description = "Application was unable to complete the request because it was invalid. The request should not be retried without modification.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "The specified resource could not be found.")
    @ApiResponse(responseCode = "409", description = "The request was valid but was not in the appropriate state to process it. Retrying the same request later may be successful.")
    ResponseEntity<Object> loadMailText(@RequestBody Map<String, String> body) throws Exception;

    @PostMapping("/storePluginMessageSummary")
    @Operation(summary = "Store plugin message summary", tags = "Plugin", description = "Store plugin message summary in the vector service.")
    @ApiResponse(responseCode = "400", description = "Invalid request.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "Resource not found.")
    @ApiResponse(responseCode = "409", description = "Request conflict, retrying may help.")
    public ResponseEntity<Object> storePluginMessageSummary(@RequestBody String body);

    @PostMapping("/storeVoiceConversationSummary")
    @Operation(summary = "Store voice conversation summary", tags = "Plugin", description = "Store voice conversation summary in the vector service.")
    @ApiResponse(responseCode = "400", description = "Invalid request.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "Resource not found.")
    @ApiResponse(responseCode = "409", description = "Request conflict, retrying may help.")
    public ResponseEntity<Object> storeVoiceConversationSummary(@RequestBody Map<String, String> body);


    @PostMapping("/queryPluginMessageSummary")
    @Operation(summary = "Query plugin message summary", tags = "Plugin", description = "Query plugin message summary from the vector service.")
    @ApiResponse(responseCode = "400", description = "Invalid request.")
    @ApiResponse(responseCode = "401", description = "Client could not be authenticated.")
    @ApiResponse(responseCode = "403", description = "Client is not authorized to make this request.")
    @ApiResponse(responseCode = "404", description = "Resource not found.")
    @ApiResponse(responseCode = "409", description = "Request conflict, retrying may help.")
    public ResponseEntity<Object> queryPluginMessageSummary(@RequestBody Map<String, String> body);



    }
