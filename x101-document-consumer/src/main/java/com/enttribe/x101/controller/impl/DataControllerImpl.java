package com.enttribe.x101.controller.impl;

import com.enttribe.x101.service.impl.DataLoadingServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/data")
public class DataControllerImpl {

    private final DataLoadingServiceImpl dataLoadingService;

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public DataControllerImpl(DataLoadingServiceImpl dataLoadingService, JdbcTemplate jdbcTemplate) {
        this.dataLoadingService = dataLoadingService;
        this.jdbcTemplate = jdbcTemplate;
    }

    @PostMapping("/load")
    public ResponseEntity<String> load() {
        try {
            this.dataLoadingService.load();
            return ResponseEntity.ok("Data loaded successfully!");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An error occurred while loading data: " + e.getMessage());
        }
    }

    @GetMapping("/count")
    public int count() {
        String sql = "SELECT COUNT(*) FROM vector_store";
        return jdbcTemplate.queryForObject(sql, Integer.class);
    }

    @PostMapping("/delete")
    public void delete() {
        String sql = "DELETE FROM vector_store";
        jdbcTemplate.update(sql);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("An error occurred in the controller: " + e.getMessage());
    }

    // Endpoint to handle file upload and return text chunks
    @PostMapping("/split")
    public ResponseEntity<?> splitPDF() {
        try {

            // Convert the file path to a File object
            File pdfFile = new File("/Users/<USER>/Downloads/20240821202507_Selenium+Full+Material+Updated+Greens.pdf");
            String path = "/Users/<USER>/Downloads/20240821202507_Selenium+Full+Material+Updated+Greens.pdf";
            Map<String, String> stringStringMap = dataLoadingService.readFileData(path);
            // Split the PDF text
            String textChunks = dataLoadingService.splitPDFText(stringStringMap.get("content"), null, 1000,null);
            System.out.println("textChunks:"+textChunks);
            // Return the list of text chunks
            return ResponseEntity.ok(textChunks);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(null);
        }
    }

    // Helper method to convert MultipartFile to File
    private File convertMultiPartToFile(MultipartFile file) throws IOException {
        File convFile = new File(Objects.requireNonNull(file.getOriginalFilename()));
        file.transferTo(convFile);
        return convFile;
    }

    @GetMapping("/testInvoke")
    public ResponseEntity<?> testInvoke() {
        try {

           dataLoadingService.testGroqAi();

            // Return the list of text chunks
            return ResponseEntity.ok("done");

        } catch (Exception e) {
            return ResponseEntity.badRequest().body(null);
        }
    }

}
