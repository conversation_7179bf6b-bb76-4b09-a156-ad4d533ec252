package com.enttribe.x101.integration.utils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

@Slf4j
public class ExternalHttpUtil {

	private ExternalHttpUtil() {

	}
	private static final String GET = "GET";
	private static final String POST = "POST";
	private static final String DELETE = "DELETE";
	private static final String PUT = "PUT";
	
	public static ResponseEntity<String> callHTTPMethods(String completeUrl, Object requestBodyJson, String methodType,
			Map<String, String> pathParams, Map<String, String> queryParams, Map<String, String> headers,InputStream inputStream)
			throws IOException {
		log.debug(" Inside Class CallHttpPrortocols @Method callHTTPMethods : methodType {} ", methodType);
		switch (methodType.toUpperCase()) {
		case GET:
			return sendGetRequest(completeUrl, headers, pathParams, queryParams);
		case POST:
			return sendPostRequest(completeUrl, requestBodyJson, headers, pathParams, queryParams,inputStream);
		case DELETE:
			return sendDeleteRequest(completeUrl, headers, pathParams, queryParams);
		case PUT:
			return sendPutRequest(completeUrl, requestBodyJson, headers, pathParams, queryParams);
		default:
			throw new IllegalArgumentException("Unsupported HTTP method: " + methodType);
		}
	}

	public static ResponseEntity<String> sendGetRequest(String baseUrl, Map<String, String> headers,
			Map<String, String> pathParams, Map<String, String> queryParams) throws IOException {
		log.info("Inside @Class ExternalHttpUtil @Method sendGetRequest");
		OkHttpClient httpClient = new OkHttpClient.Builder().build();
		HttpUrl.Builder urlBuilder = HttpUrl.parse(baseUrl).newBuilder();
		IntegrationUtils.getPathParamsValues(pathParams, urlBuilder);
		IntegrationUtils.getQueryParams(queryParams, urlBuilder);
		Request.Builder requestBuilder = new Request.Builder().url(urlBuilder.build());
		Headers.Builder headerBuilder = new Headers.Builder();
		IntegrationUtils.getHeadersValues(headers, headerBuilder);
		//requestBuilder.setHeaders$okhttp(headerBuilder);
		
		log.debug("Inside @Class ExternalHttpUtil @Method sendGetRequest requestBuilder {}",requestBuilder);
		log.debug("Inside @Class ExternalHttpUtil @Method sendGetRequest headerBuilder {}",headerBuilder);
		return execute(httpClient, requestBuilder); 
	}

	
	
	private static ResponseEntity<String> execute(OkHttpClient httpClient, Request.Builder requestBuilder) throws IOException {
		try (Response response = httpClient.newCall(requestBuilder.build()).execute()) {
			String responseBody=  response.body().string();
			HttpStatus httpStatus = HttpStatus.valueOf(response.code());
			 return ResponseEntity.status(httpStatus).body(responseBody);
		}
	}

	//@Retry(name = "apiCallRetry")
	public static ResponseEntity<String> sendPostRequest(String url, Object requestBodyJson, Map<String, String> headers,
			Map<String, String> pathParams, Map<String, String> queryParams,InputStream inputStream) throws IOException {
		HttpUrl.Builder urlBuilder = IntegrationUtils.buildUrlWithParams(url, pathParams, queryParams);
		Request.Builder requestBuilder = new Request.Builder().url(urlBuilder.build());
		Headers.Builder headerBuilder = new Headers.Builder();
		IntegrationUtils.getHeadersValues(headers, headerBuilder);
		//requestBuilder.setHeaders$okhttp(headerBuilder);
		OkHttpClient httpClient = new OkHttpClient();
		
		if (headers.containsKey("content-type")
				&& headers.get("content-type").toLowerCase().startsWith("multipart/form-data;")) {
			log.debug("Inside @Class ExternalHttpUtil @Method sendGetRequest sendPostRequest multipart form data");
			// Multipart request with file upload
			
/*
			byte[] bytes = convertInputStreamToByteArray(inputStream);
			RequestBody requestBody = new MultipartBody.Builder().setType(MultipartBody.FORM).addFormDataPart("file",
					"filename", RequestBody.create(bytes, MediaType.parse("application/octet-stream"))).build();
*/

			requestBuilder.post(null);
		} else {
			// Default case: JSON request
			String unescapedJson = StringEscapeUtils.unescapeJson(requestBodyJson.toString());
			RequestBody requestBody = RequestBody.create(unescapedJson, MediaType.parse("application/json"));
			requestBuilder.post(requestBody);
		}

        Request request = requestBuilder.build();
		
		try (Response response = httpClient.newCall(request).execute();) {
				String responseBody=  response.body().string();
				HttpStatus httpStatus = HttpStatus.valueOf(response.code());
			 return ResponseEntity.status(httpStatus).body(responseBody);
		} 
	}
	
	
	
	
	
	public static ResponseEntity<String> sendDeleteRequest(String url, Map<String, String> headers,
			Map<String, String> pathParams, Map<String, String> queryParams) throws IOException {
		OkHttpClient httpClient = new OkHttpClient.Builder().build();
		Request.Builder requestBuilder = prepareRequestBuilder(url, headers, pathParams, queryParams);
		requestBuilder.delete();
		return execute(httpClient, requestBuilder); 
	}

	private static Request.Builder prepareRequestBuilder(String url, Map<String, String> headers,
			Map<String, String> pathParams, Map<String, String> queryParams) {
		HttpUrl.Builder urlBuilder = IntegrationUtils.buildUrlWithParams(url, pathParams, queryParams);
		Request.Builder requestBuilder = new Request.Builder().url(urlBuilder.build());
		Headers.Builder headerBuilder = new Headers.Builder();
		IntegrationUtils.getHeadersValues(headers, headerBuilder);
		//requestBuilder.setHeaders$okhttp(headerBuilder);
		return requestBuilder;
	}
	
	public static ResponseEntity<String> sendPutRequest(String url, Object json, Map<String, String> headers,
			Map<String, String> pathParams, Map<String, String> queryParams) throws IOException {
		OkHttpClient httpClient = new OkHttpClient.Builder().build();
		Request.Builder requestBuilder = prepareRequestBuilder(url, headers, pathParams, queryParams);
		@SuppressWarnings("deprecation")
		RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), json.toString());
		requestBuilder.put(requestBody);
		return execute(httpClient, requestBuilder);
	}
	
	public static byte[] convertInputStreamToByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024]; 
        int bytesRead;

        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }

        return byteArrayOutputStream.toByteArray();
    }

}
