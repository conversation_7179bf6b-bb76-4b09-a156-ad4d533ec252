package com.enttribe.x101.integration.utils;

import com.enttribe.utils.Utils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Request;

import java.util.Map;
@Slf4j
public class IntegrationUtils {
	
	private IntegrationUtils() {
		
	}
	
	private static final String INSIDE_CLASS_INTEGRATION_UTILS = "Inside @Class IntegrationUtils @Method ";
	
	public static <T> T convertJsonToObject(String jsonString, Class<T> clazz) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
        	objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        	objectMapper.disable(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES);
            return objectMapper.readValue(jsonString, clazz);
        } catch (Exception e) {
        	log.error("Error @Class IntegrationUtils @Method convertJsonToObject error msg {}", Utils.getStackTrace(e));
            return null;
        }
    }
	
	

	public static void getHeadersValues(Map<String, String> headers, Headers.Builder headerBuilder) {
		log.info(INSIDE_CLASS_INTEGRATION_UTILS + "getHeadersValues");
		if (headers != null && !headers.isEmpty()) {
			Headers header = Headers.of(headers);
			headerBuilder.addAll(header);

		}
	}

	public static void getQueryParams(Map<String, String> queryParams, HttpUrl.Builder urlBuilder) {
		log.info(INSIDE_CLASS_INTEGRATION_UTILS + "getQueryParams");

		if (queryParams != null && !queryParams.isEmpty()) {
			for (Map.Entry<String, String> entry : queryParams.entrySet()) {
				String key = entry.getKey();
				String value = entry.getValue();
				if (key != null && value != null) {
					urlBuilder.addQueryParameter(key, value);
				}
			}
		}
	}

	public static void getPathParamsValues(Map<String, String> pathParams, HttpUrl.Builder urlBuilder) {
		log.info(INSIDE_CLASS_INTEGRATION_UTILS + "getPathParamsValues");

		if (pathParams != null && !pathParams.isEmpty()) {
			for (Map.Entry<String, String> entry : pathParams.entrySet()) {
				String key = entry.getKey();
				String value = entry.getValue();
				if (key != null && value != null) {
					urlBuilder.addPathSegment(value);
				}
			}
		}
	}

	public static HttpUrl.Builder buildUrlWithParams(String baseUrl, Map<String, String> pathParams,
			Map<String, String> queryParams) {
		log.info(INSIDE_CLASS_INTEGRATION_UTILS + "buildUrlWithParams");

		HttpUrl.Builder urlBuilder = HttpUrl.parse(baseUrl).newBuilder();
		if (pathParams != null && !pathParams.isEmpty()) {
			pathParams.values().forEach(value -> {
				if (value != null) {
					urlBuilder.addPathSegment(value);
				}
			});
		}
		if (queryParams != null && !queryParams.isEmpty()) {
			queryParams.forEach((key, value) -> {
				if (value != null) {
					urlBuilder.addQueryParameter(key, value);
				}
			});
		}
		return urlBuilder;
	}

	public static String buildUrl(String baseUrl, Map<String, String> pathParams, Map<String, String> queryParams) {
		log.info(INSIDE_CLASS_INTEGRATION_UTILS + "buildUrl");

		StringBuilder urlBuilder = new StringBuilder(baseUrl);

		if ( null!=pathParams  && !pathParams.isEmpty()) {
			urlBuilder.append("/");
			urlBuilder.append(String.join("/", pathParams.values()));
		}

		if (null != queryParams  && !queryParams.isEmpty()) {
			urlBuilder.append("?");
			queryParams.forEach((key, value) -> {
				if (value != null) {
					urlBuilder.append(key).append("=").append(value).append("&");
				}
			});
			urlBuilder.deleteCharAt(urlBuilder.length() - 1);
		}

		return urlBuilder.toString();
	}

	public static Request.Builder buildRequestWithHeaders(HttpUrl url, Map<String, String> headers) {
		log.info(INSIDE_CLASS_INTEGRATION_UTILS + "buildRequestWithHeaders");

		Request.Builder requestBuilder = new Request.Builder().url(url);
		if (headers != null && !headers.isEmpty()) {
			headers.forEach((key, value) -> {
				if (value != null) {
					requestBuilder.addHeader(key, value);
				}
			});
		}
		return requestBuilder;
	}

}
