package com.enttribe.x101.integration.utils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;

import java.io.IOException;

public class JsonUtils {
    private static final Gson gson = new Gson();
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static JsonNode convertToJsonNode(String jsonString) throws IOException {
        return objectMapper.readTree(jsonString);
    }
    public static <T> T fromJson(String jsonString, Class<T> valueType) {
        return gson.fromJson(jsonString, valueType);
    }
}
