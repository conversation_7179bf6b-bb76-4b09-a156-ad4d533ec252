package com.enttribe.x101.integration.dto;

import java.io.InputStream;
import java.util.Map;



import lombok.Data;

@Data
public class HttpConnector {

	String url;
	String completeUrl;
	String endpoint;
	String methodType;
	Map<String, String> headers;
	Object requestBodyJson;
	Map<String, String> pathParams;
	Map<String, String> queryParams;
	String connectormethodType;
	Boolean isAuthenticationRequired;
	String name;
	AuthenticationType authenticationType;
	String username;
	String password;
	String token;
	String apiKey;
	InputStream inputStream;

	public HttpConnector() {
	}

	public HttpConnector(String url, String completeUrl, String endpoint, String methodType,
			Map<String, String> headers, Object requestBodyJson, Map<String, String> pathParams,
			Map<String, String> queryParams, String connectormethodType, Boolean isAuthenticationRequired, String name,
			AuthenticationType authenticationType, String username, String password, String token, String apiKey) {
		super();
		this.url = url;
		this.completeUrl = completeUrl;
		this.endpoint = endpoint;
		this.methodType = methodType;
		this.headers = headers;
		this.requestBodyJson = requestBodyJson;
		this.pathParams = pathParams;
		this.queryParams = queryParams;
		this.connectormethodType = connectormethodType;
		this.isAuthenticationRequired = isAuthenticationRequired;
		this.name = name;
		this.authenticationType = authenticationType;
		this.username = username;
		this.password = password;
		this.token = token;
		this.apiKey = apiKey;
	}

}
