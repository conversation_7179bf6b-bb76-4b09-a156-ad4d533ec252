package com.enttribe.x101.wrapper;

import lombok.*;

import java.util.Date;


@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EmailAgentPromptAuditWrapper {

    private String engine;
    private String requestText;
    private String responseText;
    private String tags;
    private String requestId;
    private String agentName;
    private String question;
    private String uuid;
    private Long totalToken;
    private Long promptToken;
    private Long completionToken;
    private Double responseTime;
    private String failureCategory;
    private String remark;
    private String toolName;
    private String status;
    private Long requestStartTime;
    private Long requestEndTime;

}
