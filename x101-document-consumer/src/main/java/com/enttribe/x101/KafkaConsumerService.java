package com.enttribe.x101;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Service
public class KafkaConsumerService {

   /* @KafkaListener(
            topics = "email-assistant",
            groupId = "email-assistant-group-id",
            containerFactory = "kafkaListenerContainerFactory" // Use the custom container factory
    )
    public void consume(ConsumerRecord<String, String> record, Acknowledgment acknowledgment) {
        String message = record.value();
        int partition = record.partition();
        long offset = record.offset();

        System.out.println("Received message: " + message);
        System.out.println("Partition: " + partition);
        System.out.println("Offset: " + offset);

        // Process the message

        // Manually acknowledge the message
        acknowledgment.acknowledge();
    }*/
}
