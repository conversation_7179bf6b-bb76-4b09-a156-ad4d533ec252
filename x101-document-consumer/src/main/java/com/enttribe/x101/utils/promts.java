package com.enttribe.x101.utils;

public class promts {
  //  public static  String  EXTRACT_TEXT_FROM_IMAGE="Please extract all the text from the provided image. Ensure that every row of text is included without omitting any words. For text contained within boxes, make sure to connect the words together as they appear. If a box is empty, maintain the space where the box was originally located. Preserve the original formatting and spacing as much as possible, and present the extracted text clearly and accurately. Avoid adding any extra remarks or statements.";
//  public static final String EXTRACT_TEXT_FROM_IMAGE = """
//   Please extract all the text from the provided image. Ensure that every row of text is included without omitting any words. For text contained within boxes, connect the words together as they appear. If a box is empty, maintain the space where the box was originally located. Preserve the original formatting and spacing as much as possible, and present the extracted text clearly and accurately. Avoid adding any extra remarks or statements and also clean the text.
//""";
  public static final String EXTRACT_TEXT_FROM_IMAGE = """
   Please extract all the text from the provided image. Ensure that every row of text is included without omitting any words. For text contained within boxes, connect the words together as they appear. If a box is empty, maintain the space where the box was originally located. Preserve the original formatting and spacing as much as possible, and present the extracted text clearly and accurately. Avoid adding any extra remarks or statements.

   Additionally, clean the text by removing any extra lines or words that do not contribute to the meaning of the text. Ensure that there are no duplicate lines, and that all text is relevant and concise.
""";

  public static final String EXTRACT_AND_ANALYZE_TEXT_FROM_IMAGE = """
Please extract all the text from the provided image, ensuring that every row of text is included without omitting any words. For text contained within boxes, connect the words together as they appear. If a box is empty, maintain the space where the box was originally located. Preserve the original formatting and spacing as much as possible.

After extraction, analyze the text to identify and list the following terms, if present: lease commencement date, lease expiration date, escalation terms, escalation frequency, access terms, sublease terms, and any other relevant lease terms. 

1. Provide a summary or list of all identified terms based on the extracted text.
2. Clearly indicate any terms or information that could not be extracted or where the extraction is uncertain. If a term is not present or if there is uncertainty, do not provide a speculative or guessed answer.

Ensure that the information is accurate and complete, and avoid adding any extra remarks or statements.
""";

    public static final String EXTRACT_TYPE_KEY_VALUE_FROM_IMAGE = """
    Please extract all the text from the provided image. For each piece of extracted text, match it to the names and descriptions provided in the JSON list below. 
    - Ensure to connect words as they appear in the image.
    - Preserve the layout and spacing as much as possible, even if some boxes are empty.
    - Match the extracted text to the corresponding name and description from the JSON list.
    - Handle variations in wording while keeping the meaning consistent.
    - Provide a summary or list of all identified terms based on the extracted text.
    - Clearly indicate any terms or information that could not be extracted or where the extraction is uncertain. If a term is not present or if there is uncertainty, do not provide a speculative or guessed answer.

Ensure that the information is accurate and complete, and avoid adding any extra remarks or statements.
    JSON list for reference:
            {INPUT_LIST}
    """;

    public static final String EXTRACT_TEXT_AND_MATCH_DESCRIPTION = """
    Please extract all the text from the provided image. The image may contain text in various formats such as forms or paragraphs. For each description provided in the JSON list below json format like:{'name':'','description':''} ,extracted the text that match the description, description is defination of key,so according to that get there value. Make sure that every vlaue that matches the description also is valid context wise. Alot of values are hidden fields within a paragraph. IF YOU ARE UNABLE TO EXTRACT ANY JSON KEY DONT ADD IT. it is preferred to not provide an answer rather than a wrong / "guessed" answer 
    
    - Extract all text that match the description from the image, maintaining the original formatting and spacing.
    - For each description in the JSON list,  extracted text corresponds to it. Values can be hidden within the paragraph without any key use context for it.
    - Include in the final JSON output only those names where the extracted text matches the description.
    - IF YOU ARE UNABLE TO EXTRACT ANY JSON KEY DONT ADD IT. it is preferred to not provide an answer rather than a wrong / "guessed" answer
    - Provide a summary or list of all identified terms based on the extracted text.
    - Clearly indicate any terms or information that could not be extracted or where the extraction is uncertain. If a term is not present or if there is uncertainty, do not provide a speculative or guessed answer.

Ensure that the information is accurate and complete, and avoid adding any extra remarks or statements.
    JSON list for reference:
            {INPUT_LIST}
    """;







}
