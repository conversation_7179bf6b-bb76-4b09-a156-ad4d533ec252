package com.enttribe.x101.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.knuddels.jtokkit.Encodings;
import com.knuddels.jtokkit.api.Encoding;
import com.knuddels.jtokkit.api.EncodingRegistry;
import com.knuddels.jtokkit.api.EncodingType;
import groovy.util.logging.Slf4j;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class Utils {
    public static String extractJsonString(String inputString) {
        int firstObjectIndex = inputString.indexOf('{');
        int firstArrayIndex = inputString.indexOf('[');

        int firstIndex;
        if (firstObjectIndex == -1 && firstArrayIndex == -1) {
            firstIndex = -1;
        } else if (firstObjectIndex == -1) {
            firstIndex = firstArrayIndex;
        } else if (firstArrayIndex == -1) {
            firstIndex = firstObjectIndex;
        } else {
            firstIndex = Math.min(firstObjectIndex, firstArrayIndex);
        }

        int lastObjectIndex = inputString.lastIndexOf('}');
        int lastArrayIndex = inputString.lastIndexOf(']');

        int lastIndex = Math.max(lastObjectIndex, lastArrayIndex);

        if (firstIndex != -1 && lastIndex != -1 && firstIndex < lastIndex) {
            inputString = inputString.substring(firstIndex, lastIndex + 1);
        }

        return inputString;
    }

    public static String extractFirstJson(String data) {
        Stack<Character> stack = new Stack<>();
        Integer startIndex = null;

        for (int i = 0; i < data.length(); i++) {
            char c = data.charAt(i);

            if (c == '{' || c == '[') {
                if (startIndex == null) {
                    startIndex = i;
                }
                stack.push(c);
            } else if (c == '}' || c == ']') {
                if (stack.isEmpty()) {
                    continue;
                }
                stack.pop();
                if (stack.isEmpty()) { // A complete JSON object/array found
                    return data.substring(startIndex, i + 1);
                }
            }
        }
        return null;
    }

    public static Object jsonParser(String messageContent) {
        try {
            String jsonString = extractFirstJson(messageContent);

            if (jsonString == null) {

                return "{\n" +
                        "  \"answer\": \"The query is not relevant to the provided document.\",\n" +
                        "  \"confidence\": 0,\n" +
                        "  \"source_document\": \"-1\",\n" +
                        "  \"isAnswerFound\": -1\n" +
                        "}";
            }

            jsonString = jsonString.replace("\\n", " ")
                    //.replace("\\", "")
                    .replace("'s ", "\\'s ")
                    .replace("'d ", "\\'d ")
                    .replace("'m ", "\\'m ")
                    .replace("'re ", "\\'re ")
                    .replace("'ve ", "\\'ve ")
                    .replace("'ll ", "\\'ll ")
                    .replace("'t ", "\\'t ")
                    .replace("\\'", "");

           /* // Determine if the JSON is an object or array
            char firstChar = jsonString.charAt(0);
            ObjectMapper mapper = new ObjectMapper();
            Object obj = null;

            if (firstChar == '{') {  // JSON Object
                obj = mapper.readValue(jsonString, Object.class);
            } else if (firstChar == '[') {  // JSON Array
                obj = mapper.readValue(jsonString, Object[].class);
            } else {
                return "No JSON Object found in text";
            }*/
            return jsonString;
        } catch (Exception e) {
            return "Invalid JSON response: " + e.getMessage();
        }
    }

    public static String replaceBulletsWithNewLine(String input) {
        String bulletPattern = "(^|\\s)([•*\\-‣◦▪○●❖◆◇✸✦]|[a-zA-Z]\\.|\\d+\\.|[IVXLCDMivxlcdm]+\\.)\\s*";

        // Compile the pattern and create a matcher for the input string
        Pattern pattern = Pattern.compile(bulletPattern);
        Matcher matcher = pattern.matcher(input);

        // Use a StringBuffer to store the result as we perform the replacement
        StringBuffer result = new StringBuffer();

        // Replace each match with "\\n" followed by the bullet
        while (matcher.find()) {
            // Get the matched bullet and trim surrounding whitespace
            String bullet = matcher.group(2).trim();  // group(2) holds the bullet character
            // Append "\\n" followed by the bullet to the result
            matcher.appendReplacement(result, matcher.group(1) + "\\\\n" + bullet);
        }
        // Append the remainder of the input string
        matcher.appendTail(result);

        return result.toString();
    }

    public static void main(String[] args){
        String s="{\n" +
                "  \"answer\": \"The solution will be deployed on GCP cloud and will be utilizing various Google cloud services, below are the solution requirement to deploy the project in GCP.\\n\\n•New GKE cluster with Kubernetes (1.25+) version on Google cloud.\\n\\n•Inbound internet connectivity to access the application from internet with SSL and DNS since the environment is customer POC.\\n\\n•Access of Google Cloud services for specific users:\\n\n" +
                "a. Access to see the deployed resource\\n\n" +
                "b. Access to see monitoring view of the resources\",\n" +
                "  \"confidence\": 100,\n" +
                "  \"source_document\": \"High\\n-Level Design (HLD) Document\",\n" +
                "  \"isAnswerFound\": 1\n" +
                "}";
        s=replaceBulletsWithNewLine(s);
        s=s.substring(s.indexOf("{"),s.indexOf("}")+1);
        System.out.println(s);
    }

    public static List<String> splitTextByMaxTokens(String text, int maxTokens) {
        System.out.println("Chunking started");
        List<String> chunks = new ArrayList<>();
        int textLength = text.length();  // Total length of the text
        int start = 0;  // Starting index for substring

        while (start < textLength) {
            // Determine the end index based on max tokens
            int end = findValidSubstringEnd(text, start, maxTokens);

            // Get the substring from start to end
            String chunk = text.substring(start, end);
            chunks.add(chunk);

            // Move the start to the next chunk
            start = end;
        }
        System.out.println("chunk list is "+chunks.toString());
        return chunks;
    }

    public static int findValidSubstringEnd(String text, int start, int maxTokens) {
        // Get the substring starting at 'start' and ending at max token length
        int end = Math.min(start + maxTokens, text.length());

        // Ensure the end doesn't break the middle of a token (if possible)
        // You can adjust this logic if your LLM needs tokens to end in specific places
        // For simplicity, it will just slice at maxTokens for now
        return end;
    }

    public static int getTokensFromText(String text) {
        int tokens = 0;
        try {
            EncodingRegistry registry = Encodings.newDefaultEncodingRegistry();
            Encoding enc = registry.getEncoding(EncodingType.CL100K_BASE);
            tokens = enc.countTokens(text);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tokens;
    }
    public static int estimateNumberOfPages(XWPFDocument document) {
        // Assume average words per page (this will vary depending on formatting)
        int averageWordsPerPage = 300;  // A common rough estimate

        // Count the total number of words in the document
        int totalWordCount = 0;
        for (var paragraph : document.getParagraphs()) {
            totalWordCount += paragraph.getText().split("\\s+").length;
        }

        // Estimate the number of pages based on total word count and average words per page
        return (int) Math.ceil((double) totalWordCount / averageWordsPerPage);
    }

    public static int estimateNumberOfPages(WordExtractor extractor) {
        // Use WordExtractor to extract the text content

        // Get the text from the document
        String text = extractor.getText();

        // Split text into words
        String[] words = text.split("\\s+");

        // Estimate the number of pages based on an average number of words per page
        int averageWordsPerPage = 300;  // You can adjust this estimate based on your needs
        int totalWordCount = words.length;

        // Calculate the estimated number of pages
        return (int) Math.ceil((double) totalWordCount / averageWordsPerPage);
    }
}
