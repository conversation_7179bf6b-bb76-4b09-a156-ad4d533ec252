package com.enttribe.x101.utils;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString

public class DocumentMetadataDTO {

    private String messageId;
    private String subject;
    public DocumentMetadataDTO(String messageId, String subject) {
        this.messageId = messageId;
        this.subject = subject;
    }

    @Getter
    @Setter
    @ToString
    public static class QueryResponseDTO {
        private String response;
        private int confidence;
        private int isAnswerFound;
        private List<DocumentMetadataDTO> documents;

        public QueryResponseDTO(String response, int confidence, int isAnswerFound, List<DocumentMetadataDTO> documents) {
            this.response = response;
            this.confidence = confidence;
            this.isAnswerFound = isAnswerFound;
            this.documents = documents;
        }
    }

}
