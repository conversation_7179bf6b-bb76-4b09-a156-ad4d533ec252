package com.enttribe.x101.utils;

import org.springframework.ai.document.Document;

import java.util.List;
import java.util.stream.Collectors;

public class DocumentConverter {

    public List<Document> convertStringsToDocuments(List<String> textChunks) {
        return textChunks.stream()
                .map(this::convertStringToDocument)
                .collect(Collectors.toList());
    }

    public Document convertStringToDocument(String text) {
        // Assuming Document class has a constructor that takes a string
        return new Document(text);
    }
}
