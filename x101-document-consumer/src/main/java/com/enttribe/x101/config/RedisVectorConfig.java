package com.enttribe.x101.config;

import java.io.FileInputStream;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;

import io.micrometer.observation.ObservationRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.embedding.BatchingStrategy;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.TokenCountBatchingStrategy;
import org.springframework.ai.vectorstore.RedisVectorStore;
import org.springframework.ai.vectorstore.observation.VectorStoreObservationConvention;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import redis.clients.jedis.DefaultJedisClientConfig;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisClientConfig;
import redis.clients.jedis.JedisPooled;

@Configuration
@EnableConfigurationProperties({RedisProperties.class})
public class RedisVectorConfig {

    private static final Logger log = LoggerFactory.getLogger(RedisVectorConfig.class);

    @Value("${vector.redis.trustStorePath:}")
    private String trustStorePath;

    @Value("${vector.redis.trustStorePassword:}")
    private String trustStorePassword;

    @Value("${commons.ai.sdk.keyStoreInstance:jks}")
    private String keyStoreInstance;

    @Value("${spring.ai.vectorstore.redis.index-name}")
    private String indexName;

    @Value("${spring.ai.vectorstore.redis.prefix}")
    private String redisPrefix;

    @Value("${summary.collection.name}")
    private String summaryCollectionName;

    @Value("${spring.data.redis.ssl.enable}")
    private boolean sslEnable;


    @Bean
    @ConditionalOnMissingBean(BatchingStrategy.class)
    BatchingStrategy batchingStrategy() {
        return new TokenCountBatchingStrategy();
    }

    @Bean
    public RedisVectorStore documentVectorStore(EmbeddingModel embeddingModel,
                                        JedisPooled jedisPooled, ObjectProvider<ObservationRegistry> observationRegistry,
                                        ObjectProvider<VectorStoreObservationConvention> customObservationConvention,
                                        BatchingStrategy batchingStrategy) {

        log.info("Creating redis vector store for document");
        var config = RedisVectorStore.RedisVectorStoreConfig.builder()
                .withIndexName(indexName)
                .withPrefix(redisPrefix)
                .withMetadataFields(
                        RedisVectorStore.MetadataField.text("file_id"),
                        RedisVectorStore.MetadataField.text("source"),
                        RedisVectorStore.MetadataField.text("title"),
                        RedisVectorStore.MetadataField.numeric("date"),
                        RedisVectorStore.MetadataField.text("fileId")
                )
                .build();

        return new RedisVectorStore(config, embeddingModel,
                jedisPooled,
                true, observationRegistry.getIfUnique(() -> ObservationRegistry.NOOP),
                customObservationConvention.getIfAvailable(() -> null), batchingStrategy);
    }

    @Bean
    public RedisVectorStore emailSummaryVectorStore(EmbeddingModel embeddingModel,
                                        JedisPooled jedisPooled, ObjectProvider<ObservationRegistry> observationRegistry,
                                        ObjectProvider<VectorStoreObservationConvention> customObservationConvention,
                                        BatchingStrategy batchingStrategy) {

        log.info("Creating redis vector store for email summary");
        var config = RedisVectorStore.RedisVectorStoreConfig.builder()
                .withIndexName(summaryCollectionName)
                .withPrefix("email_summary_")
                .withMetadataFields(
                        RedisVectorStore.MetadataField.text("file_id"),
                        RedisVectorStore.MetadataField.text("source"),
                        RedisVectorStore.MetadataField.text("title"),
                        RedisVectorStore.MetadataField.numeric("date"),
                        RedisVectorStore.MetadataField.text("fileId")
                )
                .build();

        return new RedisVectorStore(config, embeddingModel,
                jedisPooled,
                true, observationRegistry.getIfUnique(() -> ObservationRegistry.NOOP),
                customObservationConvention.getIfAvailable(() -> null), batchingStrategy);
    }

    @Bean
    public JedisPooled jedisPooled(RedisProperties redisProperties) throws CertificateException, KeyStoreException, IOException,
            NoSuchAlgorithmException, KeyManagementException {
        JedisConnectionFactory jedisConnectionFactory = this.getJedisConnectionFactory(redisProperties);
        return this.jedisPooled(jedisConnectionFactory);
    }

    private JedisPooled jedisPooled(JedisConnectionFactory jedisConnectionFactory) {
        String host = jedisConnectionFactory.getHostName();
        int port = jedisConnectionFactory.getPort();

        if (sslEnable) {
            SSLSocketFactory sslSocketFactory =
                    jedisConnectionFactory
                            .getClientConfiguration()
                            .getSslSocketFactory()
                            .orElseThrow(() -> new IllegalStateException("SSL socket factory is not available"));
            JedisClientConfig clientConfig =
                    DefaultJedisClientConfig.builder()
                            .ssl(jedisConnectionFactory.isUseSsl())
                            .sslSocketFactory(sslSocketFactory)
                            .clientName(jedisConnectionFactory.getClientName())
                            .timeoutMillis(jedisConnectionFactory.getTimeout())
                            .password(jedisConnectionFactory.getPassword())
                            .build();

            return new JedisPooled(new HostAndPort(host, port), clientConfig);
        } else {
            JedisClientConfig clientConfig =
                    DefaultJedisClientConfig.builder()
                            .clientName(jedisConnectionFactory.getClientName())
                            .timeoutMillis(jedisConnectionFactory.getTimeout())
                            .password(jedisConnectionFactory.getPassword())
                            .build();

            return new JedisPooled(new HostAndPort(host, port), clientConfig);
        }
    }

    private JedisConnectionFactory getJedisConnectionFactory(RedisProperties redisProperties)
            throws KeyStoreException,
            IOException,
            NoSuchAlgorithmException,
            KeyManagementException,
            CertificateException {

        RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration();
        standaloneConfig.setHostName(redisProperties.getHost());
        standaloneConfig.setPort(redisProperties.getPort());
        standaloneConfig.setDatabase(redisProperties.getDatabase());

        if (redisProperties.getPassword() != null) {
            standaloneConfig.setPassword(redisProperties.getPassword());
        }

        if (sslEnable) {
            log.info("setting up ssl for redis connection");
            KeyStore trustStore = KeyStore.getInstance(keyStoreInstance);
            trustStore.load(new FileInputStream(trustStorePath), trustStorePassword.toCharArray());

            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance("X509");
            trustManagerFactory.init(trustStore);

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustManagerFactory.getTrustManagers(), null);

            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            JedisClientConfiguration clientConfig =
                    JedisClientConfiguration.builder().useSsl().sslSocketFactory(sslSocketFactory).build();

            return new JedisConnectionFactory(standaloneConfig, clientConfig);
        }

        JedisClientConfiguration clientConfig = JedisClientConfiguration.builder().build();

        return new JedisConnectionFactory(standaloneConfig, clientConfig);
    }
}
