package com.enttribe.x101.repository;

import com.enttribe.x101.model.UserMailAttachment;
import com.enttribe.x101.repository.generic.GenericRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface UserMailAttachmentRepository extends GenericRepository<UserMailAttachment> {

    List<UserMailAttachment> findByProcessingStatusAndBatchId(UserMailAttachment.ProcessingStatus processingStatus, Pageable pageable,String batchId);
    UserMailAttachment findByAttachmentId(String attachmentId);
}
    
