/*
 *
 */
package com.enttribe.x101;

import com.enttribe.product.pii.filter.PropertyFilter;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.FilterProvider;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;
import io.milvus.param.IndexType;
import io.milvus.param.MetricType;
import org.springframework.ai.autoconfigure.vectorstore.pgvector.PgVectorStoreAutoConfiguration;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.TokenCountBatchingStrategy;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.vectorstore.MilvusVectorStore;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.web.client.RestTemplate;


/**
 * The Class X101Apprunner.
 */
@EnableFeignClients(basePackages = "com.enttribe")
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, PgVectorStoreAutoConfiguration.class})
//@EnableJpaAuditing(auditorAwareRef = "auditorAware")
@EnableJpaRepositories(basePackages = {"com.enttribe"})
@ComponentScan(basePackages = {"com.enttribe"})
@EntityScan(basePackages = {"com.enttribe", "com.enttribe.commons"})
public class Apprunner {

    public static void main(String[] args) {

        SpringApplication.run(Apprunner.class, args);
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

//    @Value("${MILVUS_EMBEDDING_URL}")
//    private String MILVUS_EMBEDDING_URL;
//
//    @Value("${MILVUS_USERNAME}")
//    private String milvusUserName;
//
//    @Value("${MILVUS_PASSWORD}")
//    private String milvusPassword;
//
//    @Value("${MILVUS_SECURE:false}")
//    private Boolean milvusSecure;
//
//    @Value("${MILVUS_SERVER_NAME}")
//    private String milvusServerName;
//
//    @Value("${MILVUS_SERVER_PEM_PATH}")
//    private String milvusServerPemPath;
//
//    @Value("${MILVUS_COLLECTION_NAME}")
//    private String MILVUS_COLLECTION_NAME;
//
//    @Value("${SUMMARY_COLLECTION_NAME}")
//    private String SUMMARY_COLLECTION_NAME;
//
//
//    String packageToScan = "com.enttribe.x101";

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // Create a SimpleFilterProvider
        objectMapper.registerModule(new Jdk8Module());
        SimpleFilterProvider filterProvider = new SimpleFilterProvider();
        // Add your custom filter to the filter provider
        filterProvider.setFailOnUnknownId(false);
        FilterProvider filters = filterProvider.addFilter("propertyFilter", new PropertyFilter());
        objectMapper.setFilterProvider(filters);
        return objectMapper;
    }

//    @Bean
//    public VectorStore documentVectorStore(MilvusServiceClient milvusClient, EmbeddingModel embeddingModel) {
//        MilvusVectorStore.MilvusVectorStoreConfig config = MilvusVectorStore.MilvusVectorStoreConfig.builder()
//                .withCollectionName(MILVUS_COLLECTION_NAME)
//                .withDatabaseName("default")
//                .withIndexType(IndexType.IVF_FLAT)
//                .withMetricType(MetricType.COSINE)
//                .build();
//        return new MilvusVectorStore(milvusClient, embeddingModel, config, false, new TokenCountBatchingStrategy());
//    }
//
//    @Bean
//    public VectorStore emailSummaryVectorStore(MilvusServiceClient milvusClient, EmbeddingModel embeddingModel) {
//        MilvusVectorStore.MilvusVectorStoreConfig config = MilvusVectorStore.MilvusVectorStoreConfig.builder()
//                .withCollectionName(SUMMARY_COLLECTION_NAME)
//                .withDatabaseName("default")
//                .withIndexType(IndexType.IVF_FLAT)
//                .withMetricType(MetricType.COSINE)
//                .withEmbeddingDimension(1536)
//                .build();
//
//        return new MilvusVectorStore(milvusClient, embeddingModel,config, true, new TokenCountBatchingStrategy());
//    }
//
//
//    @Bean
//    public MilvusServiceClient milvusClient() {
//        return new MilvusServiceClient(ConnectParam.newBuilder()
//                .withAuthorization(milvusUserName, milvusPassword)
//                .withUri(MILVUS_EMBEDDING_URL)
//                .withSecure(milvusSecure)
//                .withServerName(milvusServerName)
//                .withServerPemPath(milvusServerPemPath)
//                .build());
//    }

}
