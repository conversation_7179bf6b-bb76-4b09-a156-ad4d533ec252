package com.enttribe.x101.model;


import com.enttribe.platform.umapi.product.generic.utils.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "USER_MAIL_ATTACHMENT")
public class UserMailAttachment extends BaseEntity {

    @GeneratedValue(strategy = jakarta.persistence.GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;

    @Column(name = "NAME", length = 200)
    private String name;

    @Column(name = "TYPE", length = 100)
    private String type;    //contentType

    @Column(name = "MESSAGE_ID", length = 2000)
    private String messageId;

    @Column(name = "SHORT_SUMMARY", columnDefinition = "text")
    private String shortSummary;

    @Column(name = "LONG_SUMMARY", columnDefinition = "text")
    private String longSummary;

    @Column(name = "ATTACHMENT_ID", length = 2000)
    private String attachmentId;

    @Column(name = "RAG_DOCUMENT_ID", length = 50)
    private String ragDocumentId;

    @Column(name = "CONVERSATION_ID", length = 2000)
    private String conversationId;

    @Column(name = "INTERNET_MESSAGE_ID", length = 2000)
    private String internetMessageId;

    @Enumerated(EnumType.STRING)
    @Column(name="PROCESSING_STATUS")
    private ProcessingStatus processingStatus; // NEW , IN_PROGRESS , COMPLETED , ERROR

    @Column(name="PROCESSING_ERROR", length = 4000)
    private String processingError;

    @Column(name="DOC_PATH", length = 2000)
    private String docPath;

    public enum ProcessingStatus {
        NEW,
        IN_PROGRESS,
        COMPLETED,
        ERROR
    }


    @Column(name="UNIQUE_NAME", length = 200)
    private String uniqueName;

    @Column(name="USER_ID", length = 1000)
    private String userId;

    @Column(name = "BATCH_ID", length = 50)
    private String batchId;

    @Column(name = "ERROR_DISPLAY", length = 1000)
    private String errorDisplay;
    
}

 