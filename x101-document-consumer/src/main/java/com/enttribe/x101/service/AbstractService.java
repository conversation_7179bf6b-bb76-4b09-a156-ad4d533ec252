/*
 * 
 */
package com.enttribe.x101.service;

import com.enttribe.core.generic.dao.impl.CustomRsqlVisitor;
import com.enttribe.core.generic.dao.impl.HibernateGenericDao;
import com.enttribe.x101.repository.generic.GenericRepository;
import com.enttribe.x101.service.impl.GenericService;
import cz.jirutka.rsql.parser.RSQLParser;
import cz.jirutka.rsql.parser.ast.Node;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * The Class AbstractService.
 *
 * @param <T> the generic type
 */
@Slf4j
public abstract class AbstractService<T> implements GenericService<T> {



/** The repository. */
	private GenericRepository<T> repository;

/** The entity manager. */
	@PersistenceContext
	private EntityManager entityManager;
	
	/** The entity type. */
	private Class<T> entityType;
	
	 /**
 	 * Instantiates a new abstract service.
 	 *
 	 * @param repository the repository
 	 * @param entityType the entity type
 	 */
	protected AbstractService(GenericRepository<T> repository, Class<T> entityType) {
		this.repository = repository;
		this.entityType = entityType;
	}
	
		/**
		 * Find by id.
		 *
		 * @param id the id
		 * @return the optional
		 */
	


    /**
 * Finds and retrieves an entity by its ID.
 *
 * @param id The ID of the entity to be retrieved.
 * @return An Optional containing the found entity, or empty if no entity with the given ID is found.
 */
    @Override
	public Optional<T> findById(Integer id) {
		return repository.findById(id);
	}

 /**
  * Find all by id.
  *
  * @param id the id
  * @return the list
  */
	@Override
	public List<T> findAllById(List<Integer> id) {
		return repository.findAllById(id);
	}

 /**
  * Delete all.
  *
  * @param entities the entities
  */

/**
 * Deletes a list of entities.
 *
 * @param entities A List of entities to be deleted.
 */
	@Override
	public void deleteAll(List<T> entities) {
		repository.deleteAll(entities);
	}

  /**
   * Audit history.
   *
   * @param id the id
   * @param limit the limit
   * @param skip the skip
   * @return the string
   */
  	@Override
		public String auditHistory(int id, Integer limit, Integer skip) {
			HibernateGenericDao hibernateGenericDao = new HibernateGenericDao(entityType, entityManager);
			try {
				List list = hibernateGenericDao.findAudit(id);
				return list.toString();
			} catch (Exception e) {
				log.error("Error Inside @class: " + this.getClass().getName() + " @Method: auditHistory()" + e.getMessage());
				return null;
			}
		}
  
  /**
   * Search.
   *
   * @param query the query
   * @param offset the offset
   * @param size the size
   * @param orderby the orderby
   * @param orderType the order type
   * @return the list
   */
   @Override
	@SuppressWarnings("unchecked")
	public List<T> search(String query, Integer offset, Integer size, String orderby, String orderType) {
		if(query==null)
		{
			return Collections.emptyList();
		}

		return searchByFilter(entityType,query,orderby,orderType,offset,size);
	}
    
    
    
    /**
     * Search by filter.
     *
     * @param type the type
     * @param query the query
     * @param orderBy the order by
     * @param orderType the order type
     * @param llimit the llimit
     * @param ulimit the ulimit
     * @return the list
     */
    @SuppressWarnings("unchecked")
	private List<T> searchByFilter(Class<?> type, String query, String orderBy, String orderType, Integer llimit, Integer ulimit) {
	
    CriteriaBuilder builder = entityManager.getCriteriaBuilder();
	CriteriaQuery<?> criteria = builder.createQuery(type);
	Root<?> root = criteria.from(type);
	Node rootNode = new RSQLParser().parse(query);
	Specification<T> specification = rootNode.accept(new CustomRsqlVisitor<T>());
	Predicate predicate = specification.toPredicate((Root<T>)root, criteria, builder);
	criteria.where(predicate);
	if (orderBy != null && orderType != null) {
	if ("desc".equalsIgnoreCase(orderType)) {
	criteria.orderBy(entityManager.getCriteriaBuilder().desc(root.get(orderBy)));
	} else {
	criteria.orderBy(entityManager.getCriteriaBuilder().asc(root.get(orderBy)));
	}
	}
	TypedQuery<?> typedQuery = entityManager.createQuery(criteria);
	if (llimit != null && ulimit >= 0) {
		typedQuery.setMaxResults(ulimit + 1);
		typedQuery.setFirstResult(llimit);
	}
	return (List<T>) typedQuery.getResultList();
    }
    
    
	/**
	 * Count.
	 *
	 * @param query the query
	 * @return the long
	 */
	@Override
	@SuppressWarnings("unchecked")
	public Long count(String query) {
		if(query==null)
		{
			return null;
		}
		

		return countByFilter(entityType, query);
	}
	
	/**
	 * Count by filter.
	 *
	 * @param template the t
	 * @param query the query
	 * @return the long
	 */
	@SuppressWarnings("unchecked")
	private Long countByFilter(Class<?> template, String query) {
	CriteriaBuilder builder =entityManager.getCriteriaBuilder();
	CriteriaQuery<Long> criteria = builder.createQuery(Long.class);
	Root<?> root = criteria.from(template);
	Node rootNode = new RSQLParser().parse(query);
	Specification<T> specification = rootNode.accept(new CustomRsqlVisitor<T>());
	Predicate predicate = specification.toPredicate((Root<T>)root, criteria, builder);
	criteria.where(predicate);
	criteria.select(entityManager.getCriteriaBuilder().countDistinct(root));
	return entityManager.createQuery((criteria)).getSingleResult();
	}
	
    
}
