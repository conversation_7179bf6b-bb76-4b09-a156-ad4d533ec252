package com.enttribe.x101.service;

import com.enttribe.x101.utils.DocumentAIResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.core.io.Resource;

import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class AIService {

    @Value("classpath:system_prompts/document.st")
    private Resource documentSearchSysPrompt;

    @Value("classpath:user_prompts/document.st")
    private Resource documentSearchUserPrompt;
    private final ChatModel chatModel;

    @Value("classpath:system_prompts/json_correction_prompt.st")
    private Resource jsonCorrectionSystemPrompt;


    @Value("classpath:user_prompts/json_correction_prompt.st")
    private Resource jsonCorrectionUserPrompt;




    public DocumentAIResponse getDocumentResponse(String context, String question) {
        BeanOutputConverter<DocumentAIResponse> outputConverter = new BeanOutputConverter<>(DocumentAIResponse.class);
        String format = outputConverter.getFormat();

        Map<String, Object> map = Map.of("context", context, "question", question, "format", format);

        String userPrompt = getPromptString(documentSearchUserPrompt, map);

        String systemPrompt=getPromptString(documentSearchSysPrompt);
        System.out.println("system prompt is {}"+systemPrompt);
        ChatResponse chatResponse = ChatClient.create(chatModel).prompt()
                .system("Hi")
                .user(userPrompt)
                .call()
                .chatResponse();
        String response = chatResponse.getResult().getOutput().getContent();

        // Process the response with the output converter and return the typed response
        return getTypedResponse(outputConverter, response, true);
    }



    public static String getPromptString(Resource resource, Map<String, Object> map) {
        PromptTemplate promptTemplate = new PromptTemplate(resource);
        Prompt prompt = promptTemplate.create(map);
        return prompt.getContents();
    }

    public static String getPromptString(Resource resource) {
        PromptTemplate promptTemplate = new PromptTemplate(resource);
        Prompt prompt = promptTemplate.create();
        return prompt.getContents();
    }

    public String rectifyJSON(String format, String jsonInput) {

        Map<String, Object> map = Map.of("jsonInput", jsonInput, "format", format);
        String userPrompt = getPromptString(jsonCorrectionUserPrompt, map);

        String response = ChatClient.create(chatModel).prompt()
                .system(jsonCorrectionSystemPrompt)
                .user(userPrompt)
                .call()
                .content();


        return extractJsonString(response);
    }

    public <T> T getTypedResponse(BeanOutputConverter<T> outputConverter, String jsonString, boolean isObject) {
        T convert;
        jsonString = isObject ? extractJsonObjectString(jsonString) : extractJsonArrayString(jsonString);
        try {
            convert = outputConverter.convert(jsonString);
        } catch (Exception e) {

            try {
                if (isObject) {
                    convert = outputConverter.convert(jsonString + "}");
                } else {
                    convert = outputConverter.convert("[" + jsonString + "]");
                }
            } catch (Exception ex) {
                jsonString = rectifyJSON(outputConverter.getFormat(), jsonString);
                try {
                    convert = outputConverter.convert(jsonString);
                } catch (Exception exc) {
                    try {
                        if (isObject) {
                            convert = outputConverter.convert(jsonString + "}");
                        } else {
                            convert = outputConverter.convert("[" + jsonString + "]");
                        }
                    } catch (Exception exception) {
                           log.error("Error inside @method getTypedResponse.\n\n @param : outputConverter -> {}\n\n jsonString : {}", outputConverter.getFormat(), jsonString);
                        convert = isObject ? outputConverter.convert("{}") : outputConverter.convert("[]");
                    }
                }
            }
        }
        return convert;
    }
    private String extractJsonString(String inputString) {
        int firstObjectIndex = inputString.indexOf('{');
        int firstArrayIndex = inputString.indexOf('[');

        int firstIndex;
        if (firstObjectIndex == -1 && firstArrayIndex == -1) {
            firstIndex = -1;
        } else if (firstObjectIndex == -1) {
            firstIndex = firstArrayIndex;
        } else if (firstArrayIndex == -1) {
            firstIndex = firstObjectIndex;
        } else {
            firstIndex = Math.min(firstObjectIndex, firstArrayIndex);
        }

        int lastObjectIndex = inputString.lastIndexOf('}');
        int lastArrayIndex = inputString.lastIndexOf(']');

        int lastIndex = Math.max(lastObjectIndex, lastArrayIndex);

        if (firstIndex != -1 && lastIndex != -1 && firstIndex < lastIndex) {
            inputString = inputString.substring(firstIndex, lastIndex + 1);
        }

        return inputString;
    }

    private String extractJsonObjectString(String inputString) {
        int firstIndex = inputString.indexOf('{');
        int lastIndex = inputString.lastIndexOf('}');

        if (firstIndex != -1 && lastIndex != -1 && firstIndex < lastIndex) {
            inputString = inputString.substring(firstIndex, lastIndex + 1);
        }

        return inputString;
    }

    private String extractJsonArrayString(String inputString) {
        int firstIndex = inputString.indexOf('[');
        int lastIndex = inputString.lastIndexOf(']');

        if (firstIndex != -1 && lastIndex != -1 && firstIndex < lastIndex) {
            inputString = inputString.substring(firstIndex, lastIndex + 1);
        }

        return inputString;
    }



}
