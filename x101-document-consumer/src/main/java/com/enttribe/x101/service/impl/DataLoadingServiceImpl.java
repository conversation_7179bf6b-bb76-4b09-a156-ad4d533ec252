package com.enttribe.x101.service.impl;

import com.enttribe.commons.storage.StorageSystem;
import com.enttribe.core.generic.utils.ApplicationContextProvider;
import com.enttribe.utils.Utils;
import com.enttribe.x101.model.UserMailAttachment;
import com.enttribe.x101.repository.UserMailAttachmentRepository;
import com.enttribe.x101.service.S3Service;
import com.enttribe.x101.utils.DocumentConverter;
import com.enttribe.x101.wrapper.EmailAgentPromptAuditWrapper;
import com.google.json.JsonSanitizer;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.encryption.InvalidPasswordException;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.ExtractedTextFormatter;
import org.springframework.ai.reader.pdf.PagePdfDocumentReader;
import org.springframework.ai.reader.pdf.config.PdfDocumentReaderConfig;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

@Service

public class DataLoadingServiceImpl {

    public DataLoadingServiceImpl() {
    }

    private static final Logger logger = LoggerFactory.getLogger(DataLoadingServiceImpl.class);

    @Value("classpath:/data/medicaid-wa-faqs.pdf")
    private Resource pdfResource;

    @Value("${EMAIL_DOC_BUCKET_NAME}")
    private String EMAIL_DOC_BUCKET_NAME;

    @Value("${DELETE_EMAIL_DOC_FLAG}")
    private boolean DELETE_EMAIL_DOC_FLAG;

    @Value("${PROMPT_AUDIT_URL}")
    private String PROMPT_AUDIT_URL;

    @Value("${SPRING_AI_MODEL_NAME}")
    private String SPRING_AI_MODEL_NAME;

    @Autowired
    private S3Service s3Service;

    public static final String PDF_EXTENSION = "pdf";
    public static final String DOC_EXTENSION = "doc";
    public static final String DOCS_EXTENSION = "docs";
    public static final String DOCX_EXTENSION = "docx";
    public static final String FILE_EXTENSION_NOT_SUPPORTED = "File extension not supported";
    public static final String NON_PARSABLE_CONTENT = "Content Not Parsable";


    private VectorStore documentVectorStore;
    private VectorStore emailSummaryVectorStore;

    private static final SecureRandom random = new SecureRandom();

    private ChatModel chatModelForMailAgent;
    private RestTemplate restTemplate;

    @Autowired
    private UserMailAttachmentRepository userMailAttachmentRepository;

    @Autowired
    public DataLoadingServiceImpl(VectorStore documentVectorStore, VectorStore emailSummaryVectorStore, ChatModel chatModelForMailAgent, RestTemplate restTemplate) {
        this.emailSummaryVectorStore = emailSummaryVectorStore;
        this.restTemplate = restTemplate;
        Assert.notNull(documentVectorStore, "VectorStore must not be null.");
        this.documentVectorStore = documentVectorStore;
        this.chatModelForMailAgent = chatModelForMailAgent;
    }

    public void load() {
        PagePdfDocumentReader pdfReader = new PagePdfDocumentReader(this.pdfResource,
                PdfDocumentReaderConfig.builder()
                        .withPageExtractedTextFormatter(ExtractedTextFormatter.builder()
                                .withNumberOfBottomTextLinesToDelete(3)
                                .withNumberOfTopPagesToSkipBeforeDelete(1)
                                .build())
                        .withPagesPerDocument(1)
                        .build());

        var tokenTextSplitter = new TokenTextSplitter(2000, 700, 5, 10000, true);

        logger.info(
                "Parsing document, splitting, creating embeddings and storing in vector store...  this will take a while.");
        this.documentVectorStore.accept(tokenTextSplitter.apply(pdfReader.get()));
        logger.info("Done parsing document, splitting, creating embeddings and storing in vector store");
    }

    // Method to extract text from a PDF and split it into chunks
    public String splitPDFText(String fullText, UserMailAttachment userMailAttachment, int chunkSize, Integer pageCount) throws Exception {
        try {
            List<String> textChunks = normalChunking(fullText);
            logger.info("chunks are {}", textChunks.toString());
            DocumentConverter converter = new DocumentConverter();
            List<Document> documents = converter.convertStringsToDocuments(textChunks);
            String request_id = generateReqId();
            logger.info("request id is {}", request_id);
            userMailAttachment.setAttachmentId(request_id);
            Map<String, String> stringStringMap = generateMetadata(request_id, userMailAttachment);
            logger.info("metadata is {}", stringStringMap.toString());
            for (Document document : documents) {
                document.getMetadata().put("file_id", request_id);
                document.getMetadata().putAll(stringStringMap);

                logger.info("document metadata is {}", document.getMetadata().toString());
            }
            String chatId = generateChatId();
            String summaryDetails = "";
            if (pageCount > 100) {
                summaryDetails = generateSummery(documents, chatId, userMailAttachment);
            } else {
                List<Document> document = List.of(converter.convertStringToDocument(fullText));
                summaryDetails = generateSummery(document, chatId, userMailAttachment);
            }
            //var tokenTextSplitter = new TokenTextSplitter(2000,700,5,10000,true);
            logger.debug("Going to store on Vector database ");
            this.documentVectorStore.accept(documents);
            return summaryDetails;

        } catch (Exception e) {
            logger.error("Getting exception at splitPDFText {}", Utils.getStackTrace(e));
            if (Utils.getStackTrace(e).contains("content must not be null")) {
                userMailAttachment.setShortSummary("This document contains unsupported characters or formatting.");
                userMailAttachment.setLongSummary("This document contains unsupported characters or formatting.");
                userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.COMPLETED);

            } else {
                userMailAttachment.setProcessingError(Utils.getStackTrace(e));
                userMailAttachment.setShortSummary("Unable to process Document");
                userMailAttachment.setLongSummary("Unable to process Document");
                userMailAttachment.setErrorDisplay("Unable to process Document");
                userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.ERROR);
            }
            userMailAttachmentRepository.save(userMailAttachment);
            return null;
        }
    }

    private String generateSummery(List<Document> documents, String chatId, UserMailAttachment userMailAttachment) throws Exception {

        try {
            if (documents.size() == 1) {
                logger.info("inside one chunks");
                String longSummary = getBatchSummary(documents, chatId);
                String shortSummary = getShortSummary(longSummary, chatId);
                userMailAttachment.setLongSummary(longSummary);
                userMailAttachment.setShortSummary(shortSummary);
                userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.COMPLETED);
                userMailAttachmentRepository.save(userMailAttachment);
                return shortSummary;
            }
            logger.debug("Document length : " + documents.size());
            List<String> batchSummList = batchChunkSummarization(documents, chatId);
            logger.info("Batch summary length: {}", batchSummList.size());

            if (batchSummList.size() > 50) {
                logger.info("Batch summary length exceeds 50, further summarizing the batch summaries.");
                List<String> reducedSummaries = summarizeBatchSummaries(batchSummList, chatId);
                String longSummary = finalSummarization(reducedSummaries); // Recursively summarize the reduced summaries
                logger.info("Final longSummary after recursive summarization: {}", longSummary);
                String shortSummary = getShortSummary(longSummary, chatId);
                logger.info("Final shortSummary after recursive summarization: {}", shortSummary);

                // Step 3: Save summaries to the database
                userMailAttachment.setLongSummary(longSummary);
                userMailAttachment.setShortSummary(shortSummary);
                userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.COMPLETED);
                userMailAttachmentRepository.save(userMailAttachment);
                return shortSummary;
            } else {
                // Normal summarization if batch summaries are within limits
                String longSummary = finalSummarization(batchSummList);
                logger.info("longSummary: {}", longSummary);
                String shortSummary = getShortSummary(longSummary, chatId);
                logger.info("shortSummary: {}", shortSummary);

                // Save summaries to the database
                userMailAttachment.setLongSummary(longSummary);
                userMailAttachment.setShortSummary(shortSummary);
                userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.COMPLETED);
                userMailAttachmentRepository.save(userMailAttachment);
                return shortSummary;
            }
        } catch (Exception e) {
            logger.error("Exception during process document {}", Utils.getStackTrace(e));
            throw new Exception("Unable to process document");
        }
    }


    public List<String> summarizeBatchSummaries(List<String> batchSummList, String chatId) {
        logger.info("Summarizing batch summaries into shorter 3-4 line summaries.");

        List<String> reducedSummaries = new ArrayList<>();

        // Split the batch summaries into chunks of 10 for LLM processing
        int batchSize = 5;
        for (int i = 0; i < batchSummList.size(); i += batchSize) {
            List<String> subBatch = batchSummList.subList(i, Math.min(i + batchSize, batchSummList.size()));

            // Combine sub-batch summaries
            String combinedSubBatch = String.join("\n", subBatch);

            logger.debug("Combined batch Token length {} ", com.enttribe.x101.utils.Utils.getTokensFromText(combinedSubBatch));

            String prompt = "The following is a set of batch summaries from a large document:\n" +
                    "{summaries}\n" +
                    "Please summarize these into a concise 3-4 line summary, capturing the key points concisely and professionally.\n" +
                    "Concise summary:";

            prompt = prompt.replace("{summaries}", combinedSubBatch);

            // Call LLM to summarize the batch summaries
            ChatResponse response = this.chatModelForMailAgent.call(new Prompt(prompt));
            String reducedSummary = response.getResults().get(0).getOutput().getContent();
            reducedSummaries.add(reducedSummary);

            logger.info("Reduced batch summary: {}", reducedSummary);
        }

        return reducedSummaries;
    }

    public Map<String, String> generateMetadata(String fileId, UserMailAttachment userMailAttachment) {
        Map<String, String> finalMetadata = new HashMap<>();
        logger.info("file path: " + userMailAttachment.getDocPath());
        finalMetadata.put("source", userMailAttachment.getDocPath());
        finalMetadata.put("title", userMailAttachment.getDocPath());
        finalMetadata.put("fileId", fileId);
        return finalMetadata;
    }

    public static String generateReqId() {
        int numDigits = 5;
        int bound = (int) Math.pow(10, numDigits);
        int randomNumber = random.nextInt(bound);
        return String.format("%0" + numDigits + "d", randomNumber);
    }


    public List<String> batchChunkSummarization(List<Document> docs, String chatId) throws Exception {
        int n = docs.size();
        int i = Math.min(10, n);
        int lastIndex = 0;

        List<String> summaryList = new ArrayList<>();

        while (i <= n) {
            logger.info("{}------{}", lastIndex, i);
            List<Document> currBatch = docs.subList(lastIndex, i);
            String summary = null;
            try {
                summary = getBatchSummary(currBatch, chatId);
            } catch (Exception e) {
                throw new Exception(e);
            }

            summaryList.add(summary);
            lastIndex = i;

            if (i + 10 < n) {
                i += 10;
            } else {
                break;
            }
        }

        if (i < n) {
            logger.info("Getting the summary for remaining chunks: {}", i);
            List<Document> currBatch = docs.subList(i, n);
            String summary = getBatchSummary(currBatch, chatId);
            summaryList.add(summary);
        }

        return summaryList;
    }

    private String generateChatId() {
        long currentTimeMillis = System.currentTimeMillis();
        String chatId = "a_" + currentTimeMillis;
        chatId = chatId.replace(".", "_"); // Replace periods with underscores if needed
        return chatId;
    }


    private static final String USER_KEY = "user";

    public String getShortSummary(String longSumm, String chatId) {
        logger.info("Inside shorten summarization");
        List<String> docChunk = com.enttribe.x101.utils.Utils.splitTextByMaxTokens(longSumm, 20000);
        String finalSummary = "";
        for (int i = 0; i < docChunk.size(); i++) {
            String content =
                    "The following are chunk summaries for text from a document. Your task is to shorten this into a concise summary withing 100 words.\n" +
                            "\n" +
                            "**long-summary:**\n" +
                            "{long_summary}\n" +
                            "\n" +
                            "**previous-summary:**\n" +
                            "{previous_summary}\n" +
                            "\n" +
                            "The short summary should:\n" +
                            "- Be concise and capture only the essential points.\n" +
                            "- ***Contain no more than 100 words***.\n" +
                            "- Maintain a professional tone.\n" +
                            "- Use both the **long-summary** and the **previous-summary** to generate the short summary.\n" +
                            "- Focus strictly on the content provided without any additional commentary, explanation, or phrasing.\n" +
                            "\n" +
                            "**Do not include**:\n" +
                            "- Any introductory or concluding statements (e.g., \"This is a summary of..., Here is Summary or anything else\").\n" +
                            "- Any additional interpretations, rephrasing like \"this is polished,\" or opinions from your side.\n" +
                            "- Irrelevant or generic information that does not directly come from the summaries.\n" +
                            "\n" +
                            "Only provide the summary itself. No extra words or phrases are allowed.\n" +
                            "\n" +
                            "short_summary:\n"; // Define your short summary template here

            content = content.replace("{long_summary}", docChunk.get(i));
            content = content.replace("{previous_summary}", finalSummary);
            ChatResponse response = this.chatModelForMailAgent.call(
                    new Prompt(content));
            finalSummary = response.getResults().get(0).getOutput().getContent();
            logger.info("getShortSummary=> {}", response.getResults().get(0).getOutput().getContent());
        }


        return finalSummary;
    }


    public String getBatchSummary(List<Document> docSubList, String chatId) throws Exception {
        logger.info("Inside batch document summarization, Document count: {}", docSubList.size());
        try {
            String docText = docSubList.stream()
                    .map(Document::getContent)
                    .collect(Collectors.joining("\n"));

            String content =
                    """
                                <system-prompt>
                                <description>You are an expert document summarizer.</description>
                                <context>
                                    The following text is extracted from a document and may include a Table of Contents or other non-essential sections.
                                </context>
                                <task>
                                    <point>Create a detailed and cohesive summary that captures the main ideas, critical information, and overall insights from the document.</point>
                                    <point>Ensure all topics are included to support RAG (Retrieval-Augmented Generation) queries.</point>
                                    <point>Do not include any content from the Table of Contents, headers, footers, page numbers, or references.</point>
                                    <point>Ignore any section that appears to be a Table of Contents or index.</point>
                                    <point>Focus solely on the main body content and substantive information.</point>
                                    <point>Provide a comprehensive understanding of all key aspects, avoiding redundancy.</point>
                                    <point>Be concise yet informative, aiming for a summary between 200 to 250 words.</point>
                                    <point>Maintain a formal and professional tone.</point>
                                    <point>Do not include introductory or concluding statements (e.g., 'This is a summary of...').</point>
                                    <point>Do not mention the exclusion of certain elements in the summary.</point>
                                    <point>Do not include any page numbers in the summary text.</point>
                                </task>
                                <instruction>Please provide the summary for the following:</instruction>
                            </system-prompt>
                            
                            """;

            String finalResult = "";
            List<String> docChunk = com.enttribe.x101.utils.Utils.splitTextByMaxTokens(docText, 20000);
            logger.debug("chunks are {}", docChunk.toString());
            for (int i = 0; i < docChunk.size(); i++) {
                logger.debug("Doc Chunk length is " + docChunk.get(i).length());
                if (!docChunk.get(i).isEmpty()) {

                    ChatResponse response = ChatClient.create(this.chatModelForMailAgent).prompt()
                            .system(content)
                            .user(docText)
                            .call().chatResponse();

//                ChatResponse response = this.chatModelForMailAgent.call(
//                        new Prompt(docChunk.get(i)));
                    logger.info("getBatchSummary=> {}", response.getResults().get(0).getOutput().getContent());
                    finalResult += response.getResults().get(0).getOutput().getContent();
                }
            }

            return finalResult;
        } catch (Exception e) {
            logger.error("Error processing document {}", Utils.getStackTrace(e));
            throw new Exception("Document not processed");
        }
    }

    public String finalSummarization(List<String> summList) {


        // Reduce the max token size further to prevent exceeding token limits
        String finalResult = "";
        // List<String> docChunks = com.enttribe.x101.utils.Utils.splitTextByMaxTokens(content, 6000); // Reduce chunk size to 4000 tokens
        logger.debug("Final Summary chunk size is {} ", summList.size());

        for (int i = 0; i < summList.size(); i++) {
            String content = reduceTemplate(); // Assuming this is a method or a constant
            logger.debug("Passing Final Chunk size {} for summary {} ", i, summList.get(i));
            try {
                // Attempt to get the summary for each chunk
                //    content=content.replace("{docs}",summList.get(i));
                ChatResponse response = ChatClient.create(this.chatModelForMailAgent).prompt()
                        .system(content)
                        .user(summList.get(i))
                        .call().chatResponse();


//                ChatResponse response = this.chatModelForMailAgent.call(
//                        new Prompt(content));
                logger.info("finalSummarization=> {}", response.getResults().get(0).getOutput().getContent());
                finalResult += response.getResults().get(0).getOutput().getContent();
            } catch (Exception e) {
                if (e.getMessage().contains("context_length_exceeded")) {
                    logger.error("Chunk size exceeded the model's token limit, retrying with smaller chunks...");
                    // Retry with smaller chunks
                    List<String> smallerChunks = com.enttribe.x101.utils.Utils.splitTextByMaxTokens(summList.get(i), 20000); // Reduce chunk size further
                    for (String smallerChunk : smallerChunks) {
                        ChatResponse retryResponse = this.chatModelForMailAgent.call(
                                new Prompt(smallerChunk));
                        finalResult += retryResponse.getResults().get(0).getOutput().getContent();
                    }
                } else {
                    // Handle other exceptions accordingly
                    logger.error("Error during summarization: {}", e.getMessage());
                    throw e;
                }
            }
        }
        return finalResult;
    }


    private String reduceTemplate() {
        return """
                <system-prompt>
                    <description>The following is a chunk of summaries for text of a document.</description>
                    <task>
                        <instruction>
                            As an expert in analyzing and understanding document structures, please create a final, consolidated summary from these chunks, within 450 to 500 words.
                        </instruction>
                        <guidelines>
                            <point>Do not introduce any additional content, explanations, or page numbers.</point>
                            <point>Avoid generic words or phrases. Only use information directly from the chunk summaries.</point>
                            <exclusions>
                                <item>Headers and footers</item>
                                <item>The Table of Contents</item>
                                <item>The Index</item>
                                <item>Any references to page numbers or section numbers</item>
                            </exclusions>
                            <point>Ensure content is organized logically and cohesively, leveraging your expertise in document analysis to distill only the essential insights.</point>
                            <point>Present the summary in short bullet points, each point concise and not overly long.</point>
                            <point>Do not include any introductory or concluding phrases (e.g., "Here is the summary of the document").</point>
                        </guidelines>
                        <summary-sections>
                            <section>Factual: Facts or information that contains numbers, dates, events, etc., that are mostly quantitative or qualitative data</section>
                            <section>SWOT: Key Strengths, weaknesses, opportunities, or threats that are mentioned in the case study</section>
                            <section>Decisions and Outcomes: Key decisions taken and their successful or failed outcomes, including reasons</section>
                            <section>Ethical and Governance: Key considerations from an ethical and governance perspective</section>
                        </summary-sections>
                    </task>
                    <output>Summary:</output>
                </system-prompt>
                
                """;
    }


    public void testGroqAi() {
    /* var openAiApi = new OpenAiApi("https://api.groq.com/openai", "********************************************************");
       var openAiChatOptions = OpenAiChatOptions.builder()
             .withModel("llama3-70b-8192")
             .withTemperature(0.4f)
             .withMaxTokens(200)
             .build();
       var chatModel = new OpenAiChatClient(openAiApi, openAiChatOptions);*/
        ChatResponse response = this.chatModelForMailAgent.call(
                new Prompt("Generate the names of 5 famous pirates."));
        logger.info("response=> {}", response.getResults().get(0).getOutput().getContent());
    }


    @Async
    public void processDocument(String batchId) {
        try {
            Pageable pageable = PageRequest.of(0, 10);
            List<UserMailAttachment> attachmentList = userMailAttachmentRepository.findByProcessingStatusAndBatchId(UserMailAttachment.ProcessingStatus.NEW, pageable, batchId);

            for (UserMailAttachment userMailAttachment : attachmentList) {
                userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.IN_PROGRESS);
                userMailAttachmentRepository.save(userMailAttachment);
                String fileFromObjectStorage = getFileFromObjectStorage(userMailAttachment);
                if (fileFromObjectStorage == null) {
                    userMailAttachmentRepository.save(userMailAttachment);
                    continue;
                }
                Map<String, String> response = readFileData(fileFromObjectStorage);
                String error = response.get("error");
                logger.error("Error while processing document: {} {}", userMailAttachment.getName(), error);
                /*if(response.get("content")==null || response.get("content").isEmpty() || response.get("content").isBlank() ){
                    logger.error("Empty document: {}",userMailAttachment.getName());
                    userMailAttachment.setShortSummary("This document contains unsupported characters or formatting.");
                    userMailAttachment.setLongSummary("This document contains unsupported characters or formatting.");
                    userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.COMPLETED);
                    userMailAttachment.setModifiedTime(new Date());
                    userMailAttachmentRepository.save(userMailAttachment);
                    continue;
                }*/

                if (error != null) {
                    logger.debug("Setting Error display message {}", error);
                    if (error.contains("InvalidPasswordException") || error.contains("The supplied data appears to be in the OLE2 Format")) {
                        userMailAttachment.setShortSummary("Oops! The document is password protected and can't be summarized.");
                        userMailAttachment.setLongSummary("Oops! The document is password protected and can't be summarized.");
                        userMailAttachment.setErrorDisplay("Oops! The document is password protected and can't be summarized.");
                        userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.ERROR);
                    } else if (error.contains("content must not be null")) {
                        userMailAttachment.setShortSummary("This document contains unsupported characters or formatting.");
                        userMailAttachment.setLongSummary("This document contains unsupported characters or formatting.");
                        userMailAttachment.setErrorDisplay("This document contains unsupported characters or formatting.");
                        userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.ERROR);
                    } else {
                        userMailAttachment.setProcessingError(error);
                        userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.ERROR);
                    }
                    userMailAttachment.setModifiedTime(new Date());
                    userMailAttachmentRepository.save(userMailAttachment);
                    continue;
                }
                logger.info("pageCount is {}", response.get("pageCount"));
                if (Integer.parseInt(response.get("pageCount")) > 100) {
                    userMailAttachment.setShortSummary("It looks document having more than 100 pages, Have patience I am working!!");
                    userMailAttachment.setLongSummary("It looks document having more than 100 pages, Have patience I am working!!");
                    userMailAttachment.setModifiedTime(new Date());
                    userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.NEW);
                    userMailAttachment.setBatchId("Long_Queue");
                    userMailAttachmentRepository.save(userMailAttachment);
                }
                if (!(Integer.parseInt(response.get("pageCount")) > 100)) {
                    logger.debug("Page size less then 100 going to process");
                    String content = response.get("content");
                    try {
                        String textChunks = splitPDFText(content, userMailAttachment, 1000, Integer.parseInt(response.get("pageCount")));
                        logger.info("textChunks: {}", textChunks);
                        deleteFileFromObjectStorage(userMailAttachment, fileFromObjectStorage);
                    } catch (Exception e) {
                        logger.error("Error in document process {}", Utils.getStackTrace(e));
                        deleteFileFromObjectStorage(userMailAttachment, fileFromObjectStorage);
                    }
                    //
                }
            }
        } catch (Exception e) {
            logger.error("Getting exception at processDocument {}", Utils.getStackTrace(e));
        }
    }


    @Async
    public void processLargeDocument() {
        try {
            Pageable pageable = PageRequest.of(0, 10);
            List<UserMailAttachment> attachmentList = userMailAttachmentRepository.findByProcessingStatusAndBatchId(UserMailAttachment.ProcessingStatus.NEW, pageable, "Long_Queue");

            for (UserMailAttachment userMailAttachment : attachmentList) {
                userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.IN_PROGRESS);
                userMailAttachmentRepository.save(userMailAttachment);
                String fileFromObjectStorage = getFileFromObjectStorage(userMailAttachment);
                if (fileFromObjectStorage == null) {
                    userMailAttachmentRepository.save(userMailAttachment);
                    continue;
                }
                Map<String, String> response = readFileData(fileFromObjectStorage);
                String error = response.get("error");
                logger.error("Error while processing document: {} {}", userMailAttachment.getName(), error);
                if (error != null) {
                    if (error.contains("InvalidPasswordException") || error.contains("The supplied data appears to be in the OLE2 Format")) {
                        userMailAttachment.setShortSummary("Oops! The Document is password protected, Can't summarize it.");
                        userMailAttachment.setLongSummary("Oops! The Document is password protected, Can't summarize it.");
                        userMailAttachment.setErrorDisplay("Oops! The document is password protected and can't be summarized.");
                        userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.COMPLETED);
                    } else if (error.contains("content must not be null")) {
                        userMailAttachment.setShortSummary("This document contains unsupported characters or formatting.");
                        userMailAttachment.setLongSummary("This document contains unsupported characters or formatting.");
                        userMailAttachment.setErrorDisplay("This document contains unsupported characters or formatting.");
                        userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.COMPLETED);

                    } else {
                        userMailAttachment.setProcessingError(error);
                        userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.ERROR);
                    }
                    userMailAttachment.setModifiedTime(new Date());
                    userMailAttachmentRepository.save(userMailAttachment);
                    continue;
                }
                logger.info("pageCount is {}", response.get("pageCount"));

                logger.debug("Page size less then 100 going to process");
                String content = response.get("content");
                String textChunks = splitPDFText(content, userMailAttachment, 1000, Integer.parseInt(response.get("pageCount")));
                logger.info("textChunks: {}", textChunks);
                deleteFileFromObjectStorage(userMailAttachment, fileFromObjectStorage);

            }
        } catch (Exception e) {
            logger.error("Getting exception at processDocument {}", Utils.getStackTrace(e));
        }
    }

    public void deleteFileFromObjectStorage(UserMailAttachment userMailAttachment, String fileFromObjectStorage) {
        try {
            Path filePath = Paths.get(fileFromObjectStorage);
            Files.delete(filePath);
            if (DELETE_EMAIL_DOC_FLAG) {
                StorageSystem storageRest = ApplicationContextProvider.getApplicationContext().getBean(StorageSystem.class);
                storageRest.delete(EMAIL_DOC_BUCKET_NAME, userMailAttachment.getDocPath());
            }
        } catch (Exception e) {
            logger.error("Getting exception at deleteFileFromObjectStorage {}", Utils.getStackTrace(e));
        }
    }

    public String getFileFromObjectStorage(UserMailAttachment userMailAttachment) {
        try {
            String docPath = userMailAttachment.getDocPath();
            logger.info("docPath is for file processing => {}", docPath);
            int lastSlashIndex = docPath.lastIndexOf('/');
            long timestampInSeconds = System.currentTimeMillis();

            String fileName = docPath.substring(lastSlashIndex + 1);
            logger.info("fileName is for file processing => {}", fileName);
            int dotIndex = fileName.lastIndexOf('.');
            String extension = dotIndex != -1 ? fileName.substring(dotIndex + 1) : "";
            String fileNameWithoutExtension = fileName.substring(0, dotIndex);
            logger.info("extension is for file processing => {}", extension);
            logger.info("fileNameWithoutExtension is for file processing => {}", fileNameWithoutExtension);
            String newFileName = fileNameWithoutExtension + "_" + timestampInSeconds + "." + extension;
            logger.info("newFileName is for file processing => {}", newFileName);
            s3Service.downloadFile(EMAIL_DOC_BUCKET_NAME,docPath,newFileName);
//            StorageSystem storageRest = ApplicationContextProvider.getApplicationContext().getBean(StorageSystem.class);
//            storageRest.copyToLocalFile(EMAIL_DOC_BUCKET_NAME, docPath, newFileName);
            return newFileName;
        } catch (Exception e) {
            logger.error("Exception occurred during  => {}", e.getMessage());
            logger.error("Exception occurred during  => {}", Utils.getStackTrace(e));
            userMailAttachment.setProcessingStatus(UserMailAttachment.ProcessingStatus.ERROR);
            userMailAttachment.setProcessingError("Document not found on object storage");

            return null;
        }

    }

    public String getNewQuestionForDocument(String longSummary, String question) {
        String prompt = "Using the context of the document and the user's original question, generate the top 3 most directly relevant follow-up questions. These questions should align closely with the user's intent, focusing precisely on the same topic, theme, or area of interest indicated by the user's question. Avoid straying from the user's core question and ensure each follow-up deepens understanding of that specific inquiry.\n" +
                "\n" +
                "Document Context:\n" +
                "{longSummary}\n" +
                "\n" +
                "User Question:\n" +
                "{question}\n" +
                "\n" +
                "Output Format:\n" +
                "[\"question 1\", \"question 2\", \"question 3\"]";

        prompt = prompt.replace("{longSummary}", longSummary);
        prompt = prompt.replace("{question}", question);


        long executionStartTime = System.currentTimeMillis();
        ChatResponse response = this.chatModelForMailAgent.call(
                new Prompt(prompt));
        long executionEndTime = System.currentTimeMillis();
        String content = response.getResults().get(0).getOutput().getContent();
        String responseText = com.enttribe.x101.utils.Utils.jsonParser(content).toString();
        logger.info("responseText for new question =======> {}", com.enttribe.x101.utils.Utils.replaceBulletsWithNewLine(content));
        if (responseText.indexOf("[") != -1 && responseText.indexOf("]") != -1) {
            responseText = responseText.substring(responseText.indexOf("["), responseText.indexOf("]") + 1);
        }
        createPromptAudit(response, question, executionStartTime, executionEndTime, prompt, responseText);
        return responseText;
    }

    public String getAnswerOnDocument(String fileId, String question) {

        FilterExpressionBuilder b = new FilterExpressionBuilder();
        Filter.Expression expression = b.eq("file_id", fileId).build();
        SearchRequest searchRequest = SearchRequest.defaults().withQuery(question).
                withSimilarityThreshold(SearchRequest.SIMILARITY_THRESHOLD_ACCEPT_ALL).
                withTopK(100).
                withFilterExpression(expression);

        List<Document> documents1 = this.documentVectorStore.similaritySearch(searchRequest);
        //shortUsingBM25(documents1);
        if (documents1.size() == 0) {
            UserMailAttachment attachment = userMailAttachmentRepository.findByAttachmentId(fileId);
            String documentQuestion = getNewQuestionForDocument(attachment.getLongSummary(), question);
            JSONArray questionArray = new JSONArray(documentQuestion);
            for (int i = 0; i < questionArray.length(); i++) {
                SearchRequest newSearchRequest = SearchRequest.defaults().withQuery(questionArray.getString(i)).
                        withSimilarityThreshold(SearchRequest.SIMILARITY_THRESHOLD_ACCEPT_ALL).
                        withTopK(100).
                        withFilterExpression(expression);
                documents1 = this.documentVectorStore.similaritySearch(newSearchRequest);
                if (documents1.size() > 0) {
                    break;
                }
            }
        }
        for (Document document : documents1) {
            System.out.println("Document ID: " + document.getId());
            System.out.println("Semantic Search Score: " + document.getMetadata());
        }

        int batchSize = 10;
        String finalResponse = "{\n" +
                "  \"answer\": \"The query is not relevant to the provided document.\",\n" +
                "  \"confidence\": 0,\n" +
                "  \"source_document\": \"0\",\n" +
                "  \"isAnswerFound\": 0\n" +
                "}";
        for (int i = 0; i < documents1.size(); i += batchSize) {
            int end = Math.min(i + batchSize, documents1.size());
            List<Document> batch = documents1.subList(i, end);

            String formattedDocs = formatDocs(batch);
            String prompts = "You are an expert in answering questions related to document. you have been trained in giving answers to user query on thousands of document. Your task is to understand user query and and create a professional answer based on provided documents.\n" +
                    "\n" +
                    "**Document**:\n" +
                    "```\n" +
                    "[{context}]\n" +
                    "```\n" +
                    "---\n" +
                    "\n" +
                    "**User Query**: \n" +
                    "```\n" +
                    "[{question}]\n" +
                    "```\n" +
                    "\n" +
                    "\n" +

                    "**Response Instructions**:\n" +
                    "\n" +
                    "1. If the RAG retriever finds relevant passages in the document, the language model must generate an answer based solely on those passages.\n" +
                    "2. If no relevant passages are found in the document, provide a message indicating that the query is not relevant to the provided document and '-1' in the \"isAnswerFound\" key.\n" +
                    "\n" +

                    "**Additional Constraints**:\n" +
                    "\n" +
                    "**Please strictly adhere to the following constraints**:\n" +
                    "\n" +
                    "1. The language model must generate responses solely based on the content of the provided document.\n" +
                    "2. Do not utilize external knowledge sources or the language model's own knowledge base.\n" +
                    "3. Do not add any bullets or bulleted formatting in the response in answer key. \n" +
                    "4. Any response should be directly derived from the information contained within the document.\n" +
                    "5. Any deviation from these constraints is not permissible and will result in inaccurate responses.\n" +
                    "6. For queries that require summarization, provide a concise summary as the response; for other queries, stick to presenting factual information from the document.\n" +
                    "7. Escape all double quotes within the answer value by preceding them with a backslash (\\\")." +
                    "8. Output format for responses must always be in below JSON format,\n" +
                    "\n" +
                    "**Output format**:\n" +
                    "```\n" +
                    "{\n" +
                    "  \"answer\": \"<This is the generated response from the language model based on the user query and the content of the provided document.>\",\n" +
                    "  \"confidence\": <confidence score for answer based on user query and Documents from 0 to 100. **If no relevant answer is found confidence then must be 0 and this is very important to handle in this case**>,\n" +
                    "  \"source_document\": \"<source_document if document found else '0'>\",\n" +
                    "  \"isAnswerFound\":<0 if No Relevant Document Found>\n" +
                    "}\n" +
                    "```\n" +
                    "Response:";

            prompts = prompts.replace("{context}", formattedDocs);
            prompts = prompts.replace("{question}", question);

            long executionStartTime = System.currentTimeMillis();
            ChatResponse response = this.chatModelForMailAgent.call(
                    new Prompt(prompts));
            long executionEndTime = System.currentTimeMillis();
            String content = response.getResults().get(0).getOutput().getContent();
            String responseText = com.enttribe.x101.utils.Utils.jsonParser(content).toString();
            /* responseText = com.enttribe.x101.utils.Utils.replaceBulletsWithNewLine(content);
            if (responseText.indexOf("{") != -1 && responseText.indexOf("}") != -1) {
                responseText = responseText.substring(responseText.indexOf("{"), responseText.indexOf("}") + 1);
            }*/
            responseText = JsonSanitizer.sanitize(responseText);
            logger.info("responseText  =======> {}", responseText);

            createPromptAudit(response, question, executionStartTime, executionEndTime, prompts, responseText);
            JSONObject obj = new JSONObject(responseText);
            if (obj.getInt("confidence") > 75) {
                finalResponse = responseText;
                break;
            }
        }
        return finalResponse;
    }

    private void createPromptAudit(ChatResponse response, String question, Long executionStartTime, Long executionEndTime, String prompts, String responseText) {
        try {
            EmailAgentPromptAuditWrapper auditWrapper = new EmailAgentPromptAuditWrapper();
            Long promptTokens = response.getMetadata().getUsage().getPromptTokens();
            Long totalTokens = response.getMetadata().getUsage().getTotalTokens();
            Long generationTokens = response.getMetadata().getUsage().getGenerationTokens();
            logger.info("PromptTokens  =======> {}", promptTokens);
            logger.info("TotalTokens  =======> {}", totalTokens);
            logger.info("GenerationTokens  =======> {}", generationTokens);
            auditWrapper.setPromptToken(promptTokens);
            auditWrapper.setTotalToken(totalTokens);
            auditWrapper.setCompletionToken(generationTokens);
            auditWrapper.setQuestion(question);
            auditWrapper.setRequestStartTime(executionStartTime);
            auditWrapper.setRequestEndTime(executionEndTime);
            auditWrapper.setRequestText(prompts);
            auditWrapper.setResponseText(responseText);
            auditWrapper.setEngine(SPRING_AI_MODEL_NAME);
            auditWrapper.setRequestId(generateChatId());
            auditWrapper.setTags("Email Document Prompt");
            auditWrapper.setAgentName("Email Document");
            auditWrapper.setStatus("SUCCESS");
            restTemplate.postForEntity(PROMPT_AUDIT_URL, auditWrapper, String.class);
        } catch (Exception e) {
            logger.error("Exception while createPromptAudit : {}", e.getMessage());
        }
    }

    /*private void shortUsingBM25(List<Document> documents1) throws IOException {

        Directory index = new RAMDirectory();
        StandardAnalyzer analyzer = new StandardAnalyzer();

        // Create IndexWriter (using the same index as above)
        IndexWriterConfig config = new IndexWriterConfig(analyzer);
        IndexWriter writer = new IndexWriter(index, config);

        writer.addDocuments(documents1);
        writer.close();

        // Create IndexReader
        DirectoryReader reader = DirectoryReader.open(index);
        IndexSearcher searcher = new IndexSearcher(reader);
        searcher.setSimilarity(new BM25Similarity());

        // Query
        QueryParser parser = new QueryParser("content", analyzer);
        Query query = parser.parse("search library");

        // Search
        TopDocs results = searcher.search(query, 10);
        System.out.println("Total Hits: " + results.totalHits);

        for (ScoreDoc scoreDoc : results.scoreDocs) {
            org.apache.lucene.document.Document document = searcher.doc(scoreDoc.doc);
            System.out.println("Document: " + document.get("content") + " Score: " + scoreDoc.score);
        }
    }
*/
    public String formatDocs(List<Document> docs) {
        List<String> formattedDocs = docs.stream()
                .map(doc -> "-----------------------------------\n" + doc.getContent() + "\n")
                .collect(Collectors.toList());
        return String.join("\n", formattedDocs);
    }


    public List<String> recursiveChunking(String text, String[] primarySeparators, String[] secondarySeparators) {
        List<String> chunks = new ArrayList<>();
        String[] initialChunks = text.split(String.join("|", primarySeparators));

        for (String chunk : initialChunks) {
            if (chunk.length() <= 3000) {
                chunks.add(chunk);
            } else {
                chunks.addAll(recursiveChunking(chunk, secondarySeparators, new String[]{}));
            }
        }

        return chunks;
    }


    public List<String> normalChunking(String fullText) {
        List<String> textChunks = new ArrayList<>();
        int length = fullText.length();
        for (int start = 0; start < length; start += 6000) {
            textChunks.add(fullText.substring(start, Math.min(length, start + 6000)).trim().lines().map(String::trim).filter(line -> !line.isEmpty()).reduce((line1, line2) -> line1 + "\n" + line2).orElse(""));
        }
        return textChunks;
    }

    public Map<String, String> readFileData(String path) {

        int dotIndex = path.lastIndexOf('.');
        String extension = dotIndex != -1 ? path.substring(dotIndex + 1) : "";
        switch (extension) {
            case PDF_EXTENSION -> {
                return readPdfFile(path);
            }
            case DOC_EXTENSION -> {
                return readWordDocFile(path);
            }
            case DOCS_EXTENSION, DOCX_EXTENSION -> {
                return readWordFile(path);
            }
            default -> {
                Map<String, String> response = new HashMap<>();
                response.put("error", FILE_EXTENSION_NOT_SUPPORTED);
                return response;
            }
        }
    }


    public static void main(String[] args) {
//        String docPath = "/Users/<USER>/Downloads/CRM_System_Requirements_Consolidated.docx";
//        DataLoadingServiceImpl dataLoadingService = new DataLoadingServiceImpl();
//        System.out.println(dataLoadingService.readWordFile(docPath).toString());

//        String sanitizedJson = JsonSanitizer.sanitize("{\"key\":\"value\"}");
//        JSONObject obj=new JSONObject(sanitizedJson);
//        System.out.println(obj.toString());


    }

    public Map<String, String> readPdfFile(String path) {
        File pdfFile = new File(path);
        Map<String, String> response = new HashMap<>();
        try (PDDocument document = PDDocument.load(pdfFile)) {
            int pageCount = document.getNumberOfPages();
            response.put("pageCount", String.valueOf(pageCount));

            StringBuilder contentBuilder = new StringBuilder();

            PDFTextStripper pdfStripper = new PDFTextStripper();

            // Extract and process the first 5 pages
            int tocPages = Math.min(5, pageCount);
            pdfStripper.setStartPage(1);
            pdfStripper.setEndPage(tocPages);
            String firstFivePagesText = pdfStripper.getText(document);
            if ((firstFivePagesText == null || firstFivePagesText.isEmpty()) || firstFivePagesText.length() <= 1) {
                response.put("error", NON_PARSABLE_CONTENT);
                return response;
            }
            // Call removeToc method on the first 5 pages
            String cleanedFirstFivePagesText = preprocessTextToRemoveTOC(firstFivePagesText);

            contentBuilder.append(cleanedFirstFivePagesText);

            // Extract the rest of the pages (pages 6 to end)
            if (pageCount > 5) {
                pdfStripper.setStartPage(6);
                pdfStripper.setEndPage(pageCount);
                String restOfPagesText = pdfStripper.getText(document);

                contentBuilder.append(restOfPagesText);
            }

            if (contentBuilder != null && !contentBuilder.isEmpty()) {
                response.put("content", contentBuilder.toString());
            } else {
                response.put("error", "content must not be null");
            }

        } catch (InvalidPasswordException invalidPasswordException) {
            logger.error("Exception coming in Processing document " + Utils.getStackTrace(invalidPasswordException));
            response.put("error", "InvalidPasswordException");
        } catch (IOException e) {
            logger.error("Exception coming in Processing document " + Utils.getStackTrace(e));
            response.put("error", Utils.getStackTrace(e));
        }
        return response;
    }

    public String preprocessTextToRemoveTOC(String docText) {
        // Split the text into manageable chunks if necessary
        List<String> chunks = com.enttribe.x101.utils.Utils.splitTextByMaxTokens(docText, 20000); // Adjust token size as needed
        StringBuilder cleanedText = new StringBuilder();

        for (String chunk : chunks) {
            String prompt =
                    "You are a document processing assistant.\n" +
                            "\n" +
                            "The following text is extracted from a document. It may include a Table of Contents, numerical summaries, headers, footers, or other non-essential sections. Your task is to:\n" +
                            "- Remove any content that is part of the Table of Contents, headers, footers, or page numbers.\n" +
                            "- Retain relevant numerical information (e.g., totals, invoices, and charges) that is part of the main body content.\n" +
                            "- Ensure structured data such as invoice summaries are preserved while non-essential formatting or repetition is removed.\n" +
                            "\n" +
                            "Do not add any explanations or comments. Maintain only the necessary document content.\n" +
                            "\n" +
                            "Here is the document text:\n" +
                            "\"\"\"\n" +
                            "{docText}\n" +
                            "\"\"\"\n" +
                            "\n" +
                            "Please provide the cleaned text below:\n";

            String promptWithChunk = prompt.replace("{docText}", chunk);

            ChatResponse response = this.chatModelForMailAgent.call(new Prompt(promptWithChunk));

            String cleanedChunk = response.getResults().get(0).getOutput().getContent();
            logger.info("Cleaned text obtained from LLM.");

            cleanedText.append(cleanedChunk).append("\n");
        }

        return cleanedText.toString();
    }


//    public Map<String, String> readPdfFile(String path) {
//        File pdfFile = new File(path);
//        Map<String, String> response = new HashMap<>();
//        try (PDDocument document = PDDocument.load(pdfFile)) {
//            PDFTextStripper pdfStripper = new PDFTextStripper();
//            int pageCount=document.getPages().getCount();
//            response.put("pageCount", String.valueOf(pageCount));
//            response.put("content", pdfStripper.getText(document));
//        } catch (IOException e) {
//            logger.error("Exception coming in Processing document "+Utils.getStackTrace(e));
//            response.put("error", Utils.getStackTrace(e));
//        }
//        return response;
//    }


    public Map<String, String> readWordFile(String path) {
        StringBuilder fullText = new StringBuilder();
        Map<String, String> response = new HashMap<>();
        try (FileInputStream fis = new FileInputStream(path);
             XWPFDocument document = new XWPFDocument(fis)) {
            int pageCount = com.enttribe.x101.utils.Utils.estimateNumberOfPages(document);
            List<XWPFParagraph> paragraphs = document.getParagraphs();

            for (XWPFParagraph paragraph : paragraphs) {
                fullText.append(paragraph.getText()).append("\n");
            }
            response.put("pageCount", String.valueOf(pageCount));
            if (fullText != null && (!fullText.isEmpty() && fullText.length() > 1)) {
                response.put("content", fullText.toString());
            } else {
                response.put("error", "content must not be null");
            }
            //response.put("content", fullText.toString());

        } catch (Exception e) {
            logger.error("Exception coming in readWordFile document " + Utils.getStackTrace(e));
            response.put("error", Utils.getStackTrace(e));
        }
        return response;
    }

    public Map<String, String> readWordDocFile(String path) {
        StringBuilder fullText = new StringBuilder();
        Map<String, String> response = new HashMap<>();
        try (FileInputStream fis = new FileInputStream(path);
             HWPFDocument document = new HWPFDocument(fis)) {
            WordExtractor extractor = new WordExtractor(document);
            int pageCount = com.enttribe.x101.utils.Utils.estimateNumberOfPages(extractor);
            String[] paragraphs = extractor.getParagraphText();
            for (String paragraph : paragraphs) {
                fullText.append(paragraph).append("\n");
            }
            response.put("pageCount", String.valueOf(pageCount));
            if (fullText != null && !fullText.isEmpty()) {
                response.put("content", fullText.toString());
            } else {
                response.put("error", "content must not be null");
            }
            // response.put("content",  fullText.toString());
        } catch (IOException e) {
            logger.error("Exception coming in readWordFile document " + Utils.getStackTrace(e));
            response.put("error", Utils.getStackTrace(e));
        }
        return response;
    }

    public String testSplitPDFText(String fullText) throws Exception {
        try {
            List<String> textChunks = normalChunking(fullText);
            DocumentConverter converter = new DocumentConverter();
            List<Document> documents = converter.convertStringsToDocuments(textChunks);


            String request_id = generateReqId();
            for (Document document : documents) {
                document.getMetadata().put("file_id", request_id);
                document.getMetadata().put("source", "attachments/20240824145907_online-term-plan-plus-policy-contract.pdf");
                document.getMetadata().put("title", "attachments/20240824145907_online-term-plan-plus-policy-contract.pdf");
                document.getMetadata().put("fileId", request_id);
            }

            System.out.println("request_id=>" + request_id);
            for (Document document : documents) {
                System.out.println("chunks==>" + document.getContent());
                //document.getMetadata().put("file_id", "17742");
            }
            this.documentVectorStore.accept(documents);
            String chatId = generateChatId();
            return chatId;
            // return testGenerateSummery(documents, chatId);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Getting exception at splitPDFText {}", Utils.getStackTrace(e));
            return null;
        }
    }

    private String testGenerateSummery(List<Document> documents, String chatId) throws Exception {

        if (documents.size() == 1) {
            System.out.println("inside one chunks");
            String longSummary = getBatchSummary(documents, chatId);
            System.out.println("longSummary=>" + longSummary);
            String shortSummary = getShortSummary(longSummary, chatId);
            System.out.println("shortSummary=>" + shortSummary);
            return shortSummary;
        }
        List<String> batchSummList = null;
        try {
            batchSummList = batchChunkSummarization(documents, chatId);
        } catch (Exception e) {
            throw new Exception(e);
        }
        System.out.println("Batch summary length: " + batchSummList.size());
        for (String summary : batchSummList) {
            System.out.println("Batch==>" + summary);
        }
      /*  String longSummary = finalSummarization(batchSummList);
        System.out.println("longSummary: "+ longSummary);
        String shortSummary = getShortSummary(longSummary, chatId);
        System.out.println("shortSummary: "+ shortSummary);*/
        return null;
    }

    public void loadMailText(Map<String, String> body) {
        String text = body.get("text");
        Document document = new Document(text);

        String request_id = generateReqId();
        document.getMetadata().put("file_id", request_id);
        List<Document> documents = new ArrayList<>();
        documents.add(document);
        this.emailSummaryVectorStore.accept(documents);
    }

    public String getAnswerOnMailText(String fileId, String question) {

        FilterExpressionBuilder b = new FilterExpressionBuilder();
        Filter.Expression expression = b.eq("file_id", fileId).build();
        SearchRequest searchRequest = SearchRequest.defaults().withQuery(question).
                withSimilarityThreshold(SearchRequest.SIMILARITY_THRESHOLD_ACCEPT_ALL).
                withTopK(SearchRequest.DEFAULT_TOP_K).
                withFilterExpression(expression);

        List<Document> documents1 = this.emailSummaryVectorStore.similaritySearch(searchRequest);
        String formattedDocs = formatDocs(documents1);

        String prompts = "Title: Language Interface Chat Bot Documentation\n" +
                "\n" +
                "**Document**:\n" +
                "```\n" +
                "[{context}]\n" +
                "```\n" +
                "---\n" +
                "\n" +
                "**User Query**: \n" +
                "```\n" +
                "[{question}]\n" +
                "```\n" +
                "\n" +
                "\n" +
                "**Response Instructions**:\n" +
                "\n" +
                "1.If the RAG retriever finds relevant passages in the document, the language model will generate an answer based on those passages. Ensure that the response is generated solely from the content of the document provided.\n" +
                "\n" +
                "2. If no relevant passages are found in the document, provide a message indicating that the query is not relevant to the provided document and '-1' in \"isAnswerFound\" key\n" +
                "\n" +
                "\n" +
                "**Tagged entities**:\n" +
                "```\n" +
                "[]\n" +
                "```\n" +
                "\n" +
                "**Objects selected from previous conversation response**:\n" +
                "```\n" +
                "[]\n" +
                "```\n" +
                "\n" +
                "**Additional Constraints**:\n" +
                "\n" +
                "**Please strictly adhere to the following constraints**:\n" +
                "\n" +
                "1. The language model must generate responses solely based on the content of the provided document.\n" +
                "2. Do not utilize external knowledge sources or the language model's own knowledge base.\n" +
                "3. Any response should be directly derived from the information contained within the document.\n" +
                "4. Any deviation from these constraints is not permissible and will result in inaccurate responses.\n" +
                "5. For queries that require summarization, provide a concise summary as the response; for other queries, stick to presenting factual information from the document.\n" +
                "6. Escape all double quotes within the answer value by preceding them with a backslash (\\\")." +
                "7. Output format for responses must always be in below JSON format,\n" +
                "\n" +
                "**Output format**:\n" +
                "```\n" +
                "{\n" +
                "  \"answer\": \"<This is the generated response from the language model based on the user query and the content of the provided document.>\",\n" +
                "  \"confidence\": <confidence score from 0 to 100>,\n" +
                "  \"source_document\": \"<source_document if document found else '-1'>\",\n" +
                "  \"isAnswerFound\":<-1 if No Relevant Document Found>\n" +
                "}\n" +
                "```\n" +
                "Response:";

        prompts = prompts.replace("{context}", formattedDocs);
        prompts = prompts.replace("{question}", question);

        ChatResponse response = this.chatModelForMailAgent.call(
                new Prompt(prompts));

        logger.info("answers  =======> {}", response.getResults().get(0).getOutput().getContent());
        String content = response.getResults().get(0).getOutput().getContent();
        return com.enttribe.x101.utils.Utils.jsonParser(content).toString();
    }


}
