/*
 * 
 */
package com.enttribe.x101.service.impl;

import java.util.List;
import java.util.Optional;

/**
 * The Interface GenericService.
 *
 * @param <T> the generic type
 */
public interface GenericService<T> {



  /**
   * Search.
   *
   * @param query the query
   * @param offset the offset
   * @param size the size
   * @param orderby the orderby
   * @param orderType the order type
   * @return the list
   */
  List<T> search(String query, Integer offset, Integer size, String orderby, String orderType);

/**
 * Count.
 *
 * @param query the query
 * @return the long
 */
  Long count(String query);

/**
 * Creates the.
 *
 * @param entity the entity
 * @return the t
 */
  T create(T entity);

/**
 * Update.
 *
 * @param entity the entity
 * @return the t
 */
  T update(T entity);

/**
 * Find by id.
 *
 * @param id the id
 * @return the optional
 */
  Optional<T> findById(Integer id);	

/**
 * Find all by id.
 *
 * @param id the id
 * @return the list
 */
  List<T> findAllById(List<Integer> id);

/**
 * Delete all.
 *
 * @param entities the entities
 */

/**

Delete all entities.
@param entities list of entities to delete
*/
  void deleteAll(List<T> entities);
  
  /**
   * Audit history.
   *
   * @param id the id
   * @param limit the limit
   * @param skip the skip
   * @return the string
   */
  String auditHistory(int id, Integer limit, Integer skip);
  
    
}
