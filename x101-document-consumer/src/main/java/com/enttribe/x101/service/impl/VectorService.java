package com.enttribe.x101.service.impl;

import com.enttribe.x101.service.AIService;
import com.enttribe.x101.utils.DocumentAIResponse;
import com.enttribe.x101.utils.DocumentMetadataDTO;
import com.google.json.JsonSanitizer;
import org.json.JSONObject;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.MilvusVectorStore;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Date;
@Service
public class VectorService {


    private VectorStore emailSummaryVectorStore;


    @Autowired
    private AIService aiService;

    private final ChatModel chatModelForMailAgent;

    @Autowired
    public VectorService(VectorStore emailSummaryVectorStore,ChatModel chatModelForMailAgent) {
        this.emailSummaryVectorStore = emailSummaryVectorStore;
        this.chatModelForMailAgent = chatModelForMailAgent;
    }

    public void storePluginMessageSummary(String date, String email, String summaryText) {
        Document document = new Document(summaryText);
        document.getMetadata().put("email", email);
        long epochDays = LocalDate.parse(date).toEpochDay();
        document.getMetadata().put("date", Long.toString(epochDays));


        List<Document> documents = new ArrayList<>();
        documents.add(document);
        emailSummaryVectorStore.accept(documents);
    }

    public String queryPluginMessageSummary(String email, String queryText) {
        FilterExpressionBuilder builder = new FilterExpressionBuilder();
        long sixDaysAgo = LocalDate.now().minusDays(6).toEpochDay();
        System.out.println("date is"+LocalDate.now().minusDays(6).toString());

        Filter.Expression expression = builder.and(
                builder.eq("email", email.replace("@", "\\@")),
                builder.gte("date", sixDaysAgo)
        ).build();

        SearchRequest searchRequest = SearchRequest.defaults()
                .withQuery(queryText)
                .withSimilarityThreshold(0.1)
                .withTopK(100)
                .withFilterExpression(expression);

        List<Document> documents = emailSummaryVectorStore.similaritySearch(searchRequest);

        int batchSize = 10;
        String finalResponse="{\n" +
                "  \"answer\": \"The query is not relevant to the provided document.\",\n" +
                "  \"confidence\": 0,\n" +
                "  \"source_document\": \"0\",\n" +
                "  \"isAnswerFound\": 0\n" +
                "}";

        for (int i = 0; i < documents.size(); i += batchSize) {
            int end = Math.min(i + batchSize, documents.size());
            List<Document> batch = documents.subList(i, end);


            String document = formatDocs(batch);
            // DocumentAIResponse response = aiService.getDocumentResponse(document, queryText);
            String prompts = "You are an expert in answering questions related to mail box of user {email}. you have been trained in giving answers to user query on thousands of emails. Your task is to understand user query and and create a professional answer based on provided emails.\n" +
                    "\n" +
                    "**Document**:\n" +
                    "```\n" +
                    "[{context}]\n" +
                    "```\n" +
                    "---\n" +
                    "\n" +
                    "**User Query**: \n" +
                    "```\n" +
                    "[{question}]\n" +
                    "```\n" +
                    "\n" +
                    "\n" +
                    "**Response Instructions**:\n" +
                    "\n" +
                    "1. If the RAG retriever finds relevant passages in the document, the language model must generate an answer based solely on those passages.\n" +
                    "2. If the answer contains multiple sentences or distinct points, format the response as bullet points with each point on a new line separated by a '\\n'. Otherwise, return the answer in a regular text format.\n" +
                    "3. If no relevant passages are found in the document, provide a message indicating that the query is not relevant to the provided document and '-1' in the \"isAnswerFound\" key.\n" +
                    "\n" +
                    "**Tagged entities**:\n" +
                    "```\n" +
                    "[]\n" +
                    "```\n" +
                    "\n" +
                    "**Objects selected from previous conversation response**:\n" +
                    "```\n" +
                    "{previousData}\n" +
                    "```\n" +
                    "\n" +
                    "**Additional Constraints**:\n" +
                    "\n" +
                    "**Please strictly adhere to the following constraints**:\n" +
                    "\n" +
                    "1. The language model must generate responses solely based on the content of the provided document.\n" +
                    "2. Do not utilize external knowledge sources or the language model's own knowledge base.\n" +
                    "3. Do not add any bullets or bulleted formatting in the response in answer key. \n" +
                    "4. Any response should be directly derived from the information contained within the document.\n" +
                    "5. Any deviation from these constraints is not permissible and will result in inaccurate responses.\n" +
                    "6. For queries that require summarization, provide a concise summary as the response; for other queries, stick to presenting factual information from the document.\n" +
                    "7. Escape all double quotes within the answer value by preceding them with a backslash (\\\")." +
                    "8. Output format for responses must always be in below JSON format,\n" +
                    "9. If there are multiple document matching the query of user add all of them in mailObject array,\n" +
                    "9. If you receive Objects selected from previous conversation response then update it according to output format. Do not remove anything from Objects selected from previous conversation response." +
                    "\n" +
                    "**Output format**:\n" +
                    "```\n" +
                    "{\n" +
                    "  \"answer\": \"<This is the generated response from the language model based on the user query and the content of the provided document.>\",\n" +
                    "  \"confidence\": <confidence score from 0 to 100 based on user query. 0 if nor relevant answer is found>,\n" +
                    "  \"source_document\": \"<source_document if document found else '0'>\",\n" +
                    "  \"isAnswerFound\":<0 if No Relevant Document Found>\n" +
                    "\"mailObject\":[{" +
                    "\"subject\":\"<subjectkey coming in document that matches the query>\",\n" +
                    "\"messageId\":\"<messageId coming in document that matches the query>\",\n" +
                    "\"mailReceivedTime\":\"<MailReceivedTime coming in document that matches the query>\",\n" +
                    "\"folderName\":\"<FolderName coming in document that matches the query>\",\n" +
                    "}\n" +
                    "```\n" +
                    "Response:";

            prompts = prompts.replace("{context}", document);
            prompts = prompts.replace("{email}", email);
            prompts = prompts.replace("{question}", queryText);
            ChatResponse response = this.chatModelForMailAgent.call(
                    new Prompt(prompts));


            String content = response.getResult().getOutput().getContent();
            String responseText = com.enttribe.x101.utils.Utils.jsonParser(content).toString();
            responseText= JsonSanitizer.sanitize(responseText);
            System.out.println("responseText  =======> {}"+ responseText);

            JSONObject obj=new JSONObject(responseText);
            if(obj.getInt("confidence")>75){
                finalResponse=responseText;
                break;
            }
        }
        return finalResponse;

    }

    public void storeVoiceConversationSummary(String conversationId, String date, String email, String summaryText) {
        Document document = new Document(summaryText);
        document.getMetadata().put("conversation_id", conversationId);
        long epochDays = LocalDate.parse(date).toEpochDay();
        document.getMetadata().put("date", Long.toString(epochDays));
        document.getMetadata().put("email", email);

        List<Document> documents = new ArrayList<>();
        documents.add(document);
        emailSummaryVectorStore.accept(documents);
    }


    public String queryVoiceConversationSummary(String email, String queryText) {
        FilterExpressionBuilder builder = new FilterExpressionBuilder();
        Filter.Expression expression = builder.eq("email", email).build();

        SearchRequest searchRequest = SearchRequest.defaults()
                .withQuery(queryText)
                .withSimilarityThreshold(SearchRequest.SIMILARITY_THRESHOLD_ACCEPT_ALL)
                .withTopK(SearchRequest.DEFAULT_TOP_K)
                .withFilterExpression(expression);

        List<Document> documents = emailSummaryVectorStore.similaritySearch(searchRequest);
        String document = formatDocs(documents);
        DocumentAIResponse response = aiService.getDocumentResponse(document, queryText);

        return response.toString();
    }

    public String formatDocs(List<Document> docs) {
        List<String> formattedDocs = docs.stream()
                .map(doc -> "-----------------------------------\n\n" + doc.getContent() + "\n")
                .collect(Collectors.toList());
        return String.join("\n", formattedDocs);
    }
}

