package com.enttribe.x101.service.impl;

import com.enttribe.x101.model.UserMailAttachment;
import com.enttribe.x101.repository.UserMailAttachmentRepository;
import com.enttribe.x101.repository.generic.GenericRepository;
import com.enttribe.x101.service.AbstractService;
import com.enttribe.x101.service.UserMailAttachmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserMailAttachmentServiceImpl extends AbstractService<UserMailAttachment> implements UserMailAttachmentService {


    public UserMailAttachmentServiceImpl(
            GenericRepository<UserMailAttachment> repository
    ) {
        super(repository, UserMailAttachment.class);
    }

    private Logger logger = LoggerFactory.getLogger(
            UserMailAttachmentServiceImpl.class
    );

    @Autowired
    private UserMailAttachmentRepository userMailAttachmentRepository;

    @Override
    public UserMailAttachment create(UserMailAttachment entity) {
        return userMailAttachmentRepository.save(entity);
    }

    @Override
    public UserMailAttachment update(UserMailAttachment entity) {
        return userMailAttachmentRepository.save(entity);
    }
}
