FROM eclipse-temurin:21-alpine
# Install additional packages
RUN apk update && apk add --no-cache curl bash vim net-tools procps htop && apk upgrade
RUN apk add --no-cache expat-dev
#Argument
ENV APP_NAME x101-document-service
#Env variables

ENV SERVICE_PATH=/opt/visionwaves/$APP_NAME

#Creating Service Path

RUN mkdir -p $SERVICE_PATH

ENV SSL_VAULT_PATH=/opt/visionwaves/sql_ssl
RUN addgroup -S visionwaves && adduser -S -G visionwaves visionwaves
RUN mkdir -p "$SSL_VAULT_PATH" && \
    chown -R visionwaves:visionwaves "$SSL_VAULT_PATH"

#Adding TAR to servicePath

ADD ./$APP_NAME.tar $SERVICE_PATH
ADD ./melodyposthook.sh $SERVICE_PATH
RUN apk add curl
#changing Workspace

WORKDIR $SERVICE_PATH

#CMD

CMD sh run.sh start ; sleep 15s ; tail -f  logs/x101-document-service.log
