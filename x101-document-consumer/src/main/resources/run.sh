#!/bin/sh

NAME=x101-document-service
APP_DIR=.
LOG_DIR=${APP_DIR}/logs
JAR=${APP_DIR}/${NAME}.jar
source /vault/secrets/secrets.env
MEMORY_LIMIT_MB=$(expr $MEMORY_LIMIT / 1024 / 1024)
JAVA_XMX=$(expr $MEMORY_LIMIT_MB \* 90 / 100)

if [ -z "$JAVA_XMX" ]; then
 CMD="java -jar -XX:+UseParallelGC -XX:TieredStopAtLevel=1 -noverify -Xverify:none -Dspring.config.location=file:./application.properties -XX:ErrorFile=logs/error.log -XX:HeapDumpPath=$LOG_DIR/dumps -Dcom.couchbase.client.core.deps.io.netty.transport.noNative=true $JAR"
else
 CMD="java -jar -Xms${JAVA_XMX}m -Xmx${JAVA_XMX}m -XX:+UseParallelGC -XX:TieredStopAtLevel=1 -noverify -Xverify:none -Dspring.config.location=file:./application.properties -XX:ErrorFile=$LOG_DIR/error.log -XX:HeapDumpPath=$LOG_DIR/dumps -Dcom.couchbase.client.core.deps.io.netty.transport.noNative=true $JAR"
fi

#CMD="java -jar -Xms1048m -Xmx1808m -XX:+UseParallelGC -XX:TieredStopAtLevel=1 -noverify -Xverify:none -XX:InitialHeapSize=2048m -XX:MaxHeapSize=2508m -XX:NewRatio=2 -Dspring.config.location=file:./application.properties -XX:ErrorFile=logs/error.log -XX:HeapDumpPath=logs/dumps $JAR"
echo $CMD

LOG_FILE="$LOG_DIR/$NAME.log"
STDERR_LOG="$LOG_DIR/$NAME.err"
PID_FILE="$LOG_DIR/$NAME.pid"

# Create SSL directory
mkdir -p /opt/visionwaves/sql_ssl
chmod 700 /opt/visionwaves/sql_ssl

if [[ -n "$MILVUS_SECRET" ]]; then
    echo "$MILVUS_SECRET" | base64 -d > /opt/visionwaves/sql_ssl/milvus_secret.pem || { echo "Error decoding MILVUS_SECRET"; exit 1; }
    chmod 600 /opt/visionwaves/sql_ssl/milvus_secret.pem
fi

if [[ -n "$REDIS_CA_CERT" ]]; then
    echo "$REDIS_CA_CERT" | base64 -d > /opt/visionwaves/sql_ssl/redis_ca.pem || { echo "Error decoding REDIS_CA_CERT"; exit 1; }
    chmod 600 /opt/visionwaves/sql_ssl/redis_ca.pem
fi

#make the log directory if it doesn't exist
if [ ! -d "$LOG_DIR" ] ; then
        mkdir -p $LOG_DIR
        chmod 777 -R $LOG_DIR
fi

isRunning() {
        [ -f "$PID_FILE" ] && ps `cat $PID_FILE` > /dev/null 2>&1
}

case $1 in
        start)
                if isRunning; then
                        echo "Already started"
                else
                        echo "Starting $NAME"
                        #sudo -u "$USER" $CMD > "$LOG_FILE" 2> "$STDERR_LOG" & echo $! > "$PID_FILE"
                        $CMD > "$LOG_FILE" 2> "$STDERR_LOG" & echo $! > "$PID_FILE"
                        if ! isRunning; then
                                echo "Unable to start, see $LOG_FILE and $stderr_log"
                                exit 1
                        fi
                fi
        ;;
        stop)
                if isRunning; then
                        echo "Stopping $NAME"
                        #sudo -u "$USER" kill `cat $PID_FILE`
                        kill -9 `cat $PID_FILE`
                        rm "$PID_FILE"
                else
                        echo "Not running"
                fi
        ;;
        restart)
                $0 stop
                $0 start
        ;;
        status)
                if isRunning; then
                        echo "Running"
                else
                        echo "Not running"
                fi
        ;;
        *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
    ;;
esac

exit 0
