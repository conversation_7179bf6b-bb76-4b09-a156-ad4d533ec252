## BOOT
#spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration,org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.cassandra.CassandraDataAutoConfiguration,org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration,org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration,org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration,org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration,org.springframework.boot.autoconfigure.cassandra.CassandraAutoConfiguration,org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration,org.springframework.boot.actuate.autoconfigure.metrics.mongo.MongoMetricsAutoConfiguration,org.springframework.boot.autoconfigure.groovy.template.GroovyTemplateAutoConfiguration,org.springframework.cloud.vault.config.VaultAutoConfiguration
spring.main.allow-bean-definition-overriding=true
#Audit kafka
audit.kafka.service.enable=false


security.ignored=/**
security.basic.enable: false
kakfa.bootstrap.servers=localhost:6667
# commons.storage.type=local
commons.storage.type=${COMMONS_STORAGE_TYPE:s3}
commons.storage.endPoint=${MINIO_ENDPOINT:DdIx8R28GX7J5kGZj812rA==:cJgIZi6Kk4LarPBtU7kXNw==:c5wz0lHKYQP6IjmdR9fzTRKo+S2GAq+ei5WORQ+NynAhV2CkmDESGlFS6nsylBXB}
commons.storage.accessKey=${MINIO_ACCESS_KEY:DJcx9p/PHhvN82GGcbg+kw==:RV/q24aTIMmNICvk/9Nl8Q==:bpeE0jS2DaVNNT3rXeyufA==}
commons.storage.secretKey=${MINIO_SECRET_KEY:/QvUeTTAcBh5rMoHapCh2w==:iLgUXb/h5s8nGLR4BGrYbw==:ND1iFtoqWk6sRbdUZ5h/BA==}


spring.kafka.producer.bootstrap-servers=localhost:6667,localhost:6667
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serialize=org.apache.kafka.common.serialization.StringSerializer
#spring.kafka.consumer.bootstrap-servers=localhost:6667,localhost:6667
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

kafka.auditable.topic=AuditKafka
kafka.auditable.listen.auto.start=false
kafka.auditable.group=auditJson

# application.properties

# Kafka consumer properties
#spring.kafka.consumer.bootstrap-servers=localhost:9092
#spring.kafka.consumer.group-id=email-assistant-group-id
#spring.kafka.consumer.auto-offset-reset=earliest
#spring.kafka.consumer.enable-auto-commit=false
#spring.kafka.listener.ack-mode=manual
#spring.kafka.consumer.max-poll-records=10
#spring.kafka.consumer.session-timeout-ms=15000
#spring.kafka.consumer.auto-commit-interval-ms=1000

# Kafka configuration
kafka.bootstrap-servers=localhost:9092
kafka.consumer.group-id=email-assistant-group-id
kafka.listener.concurrency=5



spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true


spring.data.cassandra.contact-points=${CASSANDRA_URL:cassandra.performance.svc.cluster.local}
spring.data.cassandra.port=${CASSANDRA_PORT:9042}
spring.data.cassandra.local-datacenter=${CASSANDRA_DATA_CENTER:datacenter1}
spring.data.cassandra.keyspace-name=${CASSANDRA_KEYSPACE:my_keyspace}
spring.data.cassandra.username=${CASSANDRA_USERNAME:cassandra}
#dev
#spring.data.cassandra.password=${CASSANDRA_PASSWORD:WPn128ktlt}
#demo
spring.data.cassandra.password=${CASSANDRA_PASSWORD:EraszuBQDe}

s3Region=${S3_REGION:me-central2}
s3User=${S3_USER:GOOG1EF5KECMTKALSTGGGUMFMTBMJHPPZUOIXRYTODFDI3RSLEANWFQ3O2YTU}
s3Checksum=${S3_CHECKSUM:O+ezFj9r0eEUtlo0MBdiipjpcS4vvmuCkYwAMpI5}
s3Url=${S3_URL:https://storage.googleapis.com}

## WEB
server.servlet.context-path=/x101-document-service


## DATASOURCE
commons.datasource.driverClassName=org.mariadb.jdbc.Driver

# Dev Database

commons.datasource.url=${MYSQL_URL:4jt71Mi1B+tBSICiQ9ceDg==:VzKliiAA1drWaj32/uDCXA==:DDBXC61rhDB92Bxm8BROrHONnr8jCrX9LT4GKsifqNUX6gY9cJ49IwAGGFdxGssaaziQqKxTq9rWoMn01Jvfd9uuGv15ARgTNAJcpKcRYtROwH4tqGei1xxH4ldSkwWcrqrFusrzCvsSWlJtoEtV0w==}
commons.datasource.username=${MYSQL_USERNAME:veILJ7KseDVvOXjO/BzZ7Q==:Z4Tf5mZReoffwVBFP2YEjQ==:9ZNZRWGxAYtaQxao8mzTqA==}
commons.datasource.checkSum=${MYSQL_CHECKSUM:veILJ7KseDVvOXjO/BzZ7Q==:Z4Tf5mZReoffwVBFP2YEjQ==:9ZNZRWGxAYtaQxao8mzTqA==}
commons.datasource.connectionTimeout=10000
commons.datasource.maxPoolSize=50
commons.datasource.idleTimeout=1800000
commons.datasource.minIdle=10

commons.jpa.properties.hibernate.enable_lazy_load_no_trans=true
commons.jpa.show-sql=false


## JPA

spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
#spring.jpa.hibernate.ddl-auto=validate
spring.jpa.hibernate.jdbc.factory_class=net.bull.javamelody.HibernateBatcherFactory
spring.jpa.hibernate.cache.use_query_cache=true
spring.jpa.hibernate.cache.use_second_level_cache=true
spring.jpa.hibernate.cache.region.factory_class=org.hibernate.cache.ehcache.EhCacheRegionFactory
spring.jpa.properties.javax.persistence.sharedCache.mode=ENABLE_SELECTIVE
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.cache.jcache.config=classpath:ehcache.xml
#spring.jpa.properties.hibernate.metadata_builder_contributor = com.inn.core.generic.utils.SqlFunctionsMetadataBuilderContributor
spring.jpa.properties.hibernate.proc.param_null_passing=true



spring.jackson.serialization.write-dates-as-timestamps=true

#REDIS
REDIS_SERVER_URL=thZckeBoJzgDT267hyAKNtkOh4Ay2AdHKA4hE49oJ9s=
redis.hostname=pBLSk1SpFpF2hFsxbyIqNg==
redis.port=5loP/Rb5Y6Ljn6K1wCDZNw==
redis.master=tHSoqN4LH8Ae9+HrbpKIXg==
redis.senital.list=gkHHnTz3riWSpCmKl9pcwTnnbKdbDhNmmtL0JTmpFuHs6lozCF0diem2Z9RMn1a+
redis.master.auth=qWTDOxtM9n1v0uKwxfGodg==
redis.senital.enable=B6V+uDmE1LvqI+UAcULNtg==
redis.idle.min=a19Kx9VgfwyuXUnv0rL29w==
redis.idle.max=HDlw71nze5lQXpvD534idw==
redis.total.max=HDlw71nze5lQXpvD534idw==


# Redis Config

spring.cache.type=redis
spring.redis.host=localhost
spring.redis.port=6379

IS_KEYCLOAK_TOKEN_ENABLED=

## Email
commons.mail.host=YdvwvTp0SNlMlegr54woww==
commons.mail.username=DEyLAF+6jybeWoyPzRKlRg==
commons.mail.checksum=iBANy1ds3k3mloqmqRyHiQ==
commons.mail.properties.mail.smtp.auth=true
commons.mail.properties.mail.smtp.connectiontimeout=10000
commons.mail.properties.mail.smtp.timeout=10000
commons.mail.properties.mail.smtp.writetimeout=10000
commons.mail.properties.mail.smtp.starttls.enable=true
commons.mail.properties.mail.smtp.port=25


## BPMN
activiti.mail.host=
activiti.mail.username=
activiti.mail.checksum=
activiti.jpaCloseEntityManager=
activiti.activiti5CompatibilityEnabled=
activiti.history=
activiti.asyncExecutorActivate=
activiti.databaseSchemaUpdate=
activiti.asyncExecutorMaxTimerJobsPerAcquisition=
activiti.jpaHandleTransaction=
BPMN_JOB_EXECUTION=
commissioning.gc.cvim.naming.management.role.name=

tcp.server.port=8090
netty.port=8089
server.port=8081
nv-service.url=
ipam-service.url=
naming-service.url=
um-service.url=${UM_URL:http://base-utility-service/base/util/rest}
meta-service.url=${META_SERVICE_URL:http://base-utility-service/base/util/rest}
attachment-service-url=${ATTACHMENT_SERVICE_URL:http://base-utility-service/base/util/rest}
um.url=${UM_URL:http://base-utility-service/base/util/rest}
geo-service.url=${GEO_URL:http://base-utility-service/base/util/rest}
inventory-service.url=${INVENTORY_URL:http://microservice/foresight/rest}
base-plateform-service.url=${BASE_PLATFORM_URL:http://base-utility-service/base/util/rest}
fm-service.url=${FM_URL:http://fm-microservice/fm/rest}
pm-url=${PM_URL:localhost:8080}
naming-rakuten-service.url=
foresight-nv.url=
nv-enterprise-service.url=
nv-report-service.url=
changeImpact-service.url=
cm-service.url=
livy-service.url=
tribe-notification-url=
changerequest-service.url=
incident-service.url=
security-service.url=
app.conf.swagger.base.package=localhost
lcm-service.url=
workflow-service.url =
cov-service.url=
cmaas-service.url=
bi-service.url=
topology-service.url=
rmop-base.url=
notification.url=
layer-service.url=
capacity-url=
bmaas-service.url=
core-service.url=
view-builder.url =${VIEW_BUILDER_URL:http://form-builder/fb/rest}
document-management.url=${DOCUMENT_MANAGEMENT_URL:http://document-document-service.ansible.svc.cluster.local/document-management/rest}
search.url=${DOCUMENT_MANAGEMENT_URL:http://document-document-service.ansible.svc.cluster.local/document-management/rest}

spring.jpa.properties.hibernate.transaction.jta.platform=org.hibernate.engine.transaction.jta.platform.internal.JBossAppServerJtaPlatform


#Prometheus
#management.endpoint.metrics.enabled=true
management.endpoints.web.exposure.include=loggers, health
management.endpoint.prometheus.enabled=true
management.metrics.tags.application=${opentracing.jaeger.service-name}
#management.metrics.export.prometheus.enabled=true
#management.security.enabled=false
logging.level.org.hibernate.hql=ERROR
logging.level.com.enttribe=DEBUG
logging.level.org.springframework=DEBUG

#jwt
jwt.filter.enable=QGEz1iVJSeyTW1dL+HosCg==
jwt.bypass.rest=
jwt.expiration.time=
jwt.encryption.enabled=QGEz1iVJSeyTW1dL+HosCg==
jwt.certificate.path=
jwt.rsa.private.key.path=
jwt.filter.rest=


#multipart file
spring.servlet.multipart.enabled=true

spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=215MB
fileUpload.path=/protected/uploads
orchestrator.fileUpload.path=/protected/uploads/orchestrator
#base upload path

server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.directory=/dev
server.tomcat.accesslog.prefix=stdout
server.tomcat.accesslog.file-date-format=
server.tomcat.accesslog.suffix=
server.tomcat.accesslog.pattern={"@version" : "1","@type" : "access", "request-time": "%t","client-host" : "%h","remote-user":"%u","request-method":"%m","request-url":"%r","status-code":%s" ,"bytes-sent" :%b","elapsed-time": "%T","thread" : "%I"}

springdoc.packagesToScan=com.enttribe.x101,com.enttribe.x101.controller

detrace.filename=fktE8J5YPYfY2lvzxFAieg==:QVYnjdXYTf/22ex4qQSuHg==:mjVXQG4XkcqD9EzF+N6gtA==
detrace.filepath=tBvu16fhz+PPk0RPhsOsmg==:ow1OPWcdeYv33UkfPY8CUQ==:nolaM3NqjbLGGuNiwwjYt2yitgPBgJ5X2lTKNyfJBdnHLS2p5Izm2AmLIIQ0ONI/enuoak0z01pkhlyHVlwyLhEbr7DsBIvdwZgHyl9hvFrRUSIGrYlSYu89AVnwE9EJ
has context menu

spring.cloud.vault.enabled=${VAULT_ENABLED:false}



#spring.datasource.url=****************************************
#spring.datasource.username=root
#spring.datasource.password=root@123
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

#spring.ai.azure.openai.api-key=OhYKgNJDcNFpFKYVA2ACAVgOJM3mdZFDZYB30fwdoU8t8KfU
#spring.ai.azure.openai.endpoint=https://api.fireworks.ai/inference/v1
#spring.ai.azure.openai.embedding.options.model=nomic-ai/nomic-embed-text-v1.5


EMAIL_DOC_BUCKET_NAME=${EMAIL_DOC_BUCKET:x101}

#spring.ai.openai.base-url=${SPRING_AI_CHAT_BASE_URL:http://**************:8000}
spring.ai.openai.base-url=${SPRING_AI_CHAT_BASE_URL:https://api.groq.com/openai}
spring.ai.openai.temperature=0.5
spring.ai.openai.api-key=${SPRING_AI_CHAT_API_KEY:********************************************************}
spring.ai.openai.chat.options.model=${AI_MODEL:llama-3.3-70b-versatile}

#spring.ai.openai.embedding.base-url=${SPRING_AI_EMBEDDING_URL:http://**************:80}
#spring.ai.openai.embedding.options.model=${SPRING_AI_EMBEDDING_MODEL:nomic-embed-text-v1.5}
#spring.ai.openai.embedding.api-key=

spring.ai.openai.embedding.base-url=${SPRING_AI_EMBEDDING_URL:https://api.groq.com/openai/}
spring.ai.openai.embedding.api-key=${SPRING_AI_EMBEDDING_API_KEY:********************************************************}
spring.ai.openai.embedding.options.model=${SPRING_AI_EMBEDDING_MODEL:nomic-embed-text-v1_5-preview1}

#spring.ai.vectorstore.milvus.client.host=${SPRING_AI_MILVUS_CLIENT_HOST:localhost}
#spring.ai.vectorstore.milvus.client.port=${SPRING_AI_MILVUS_CLIENT_PORT:19531}
#spring.ai.vectorstore.milvus.client.username=
#spring.ai.vectorstore.milvus.client.password=
#spring.ai.vectorstore.milvus.databaseName=default
#spring.ai.vectorstore.milvus.collectionName=${SPRING_AI_EMBEDDING_COLLECTION:vector_store_test}
#spring.ai.vectorstore.milvus.embeddingDimension=768
#spring.ai.vectorstore.milvus.indexType=IVF_FLAT
#spring.ai.vectorstore.milvus.metricType=COSINE

DELETE_EMAIL_DOC_FLAG=${DELETE_EMAIL_DOC:true}
#MILVUS_USERNAME=${MILVUS_USERNAME:humainos}
#MILVUS_PASSWORD=${MILVUS_PASSWORD:Vision@123}
#MILVUS_SECURE=${MILVUS_SECURE:true}
#MILVUS_SERVER_NAME=${MILVUS_SERVER_NAME:humainos}
#MILVUS_SERVER_PEM_PATH=${MILVUS_SERVER_PEM_PATH:/opt/visionwaves/sql_ssl/milvus_secret.pem}
#MILVUS_EMBEDDING_URL=${MILVUS_URL:http://milvus.milvus-tls.svc.cluster.local:19530}
#MILVUS_COLLECTION_NAME=${SPRING_AI_EMBEDDING_COLLECTION:email_agent_chunking}
#SUMMARY_COLLECTION_NAME=${EMAIL_SUMMARY_COLLECTION:emailSummaryTest}
SPRING_AI_MODEL_NAME=${AI_MODEL:llama-3.3-70b-versatile}
PROMPT_AUDIT_URL=${EMAIL_PROMPT_AUDIT_URL:http://x101-service.ansible.svc.cluster.local/admin-service/message/createPromptAudit}

vector.redis.trustStorePath=${REDIS_TRUST_STORE_PATH:/opt/visionwaves/sql_ssl/redis_ca.pem}
vector.redis.trustStorePassword=${REDIS_TRUST_STORE_PASS:promptRunner}
spring.data.redis.ssl.enable=${REDIS_SSL:false}

spring.data.redis.username=${REDIS_USERNAME:default}
spring.data.redis.password=${REDIS_PASSWORD:}
spring.data.redis.host=${REDIS_HOST:localhost}
spring.data.redis.port=${REDIS_PORTT:6379}

spring.ai.vectorstore.redis.index-name=${REDIS_INDEX_NAME:email_agent_document}
spring.ai.vectorstore.redis.prefix=${REDIS_PREFIX:email_document_}

summary.collection.name=${SUMMARY_COLLECTION_NAME:email_agent_summary}

AOA_ENABLED=false
kafka.user.topic=
kafka.sasl-jaas-config=
email.body.content.token.limit=8192