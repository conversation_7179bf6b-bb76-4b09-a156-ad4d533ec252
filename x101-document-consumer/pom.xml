<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.enttribe.platform</groupId>
		<artifactId>enttribe-parent</artifactId>
		<version>4.0.0</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>
	<artifactId>x101-document-service</artifactId>
	<version>1.4.0</version>
	<packaging>jar</packaging>
	<name>x101-document-service</name>
	<properties>
		<java.version>21</java.version>
		<spring-ai.version>1.0.0-M3</spring-ai.version>
		<platform.version>4.0.0</platform.version>
		<startup.class>com.enttribe.x101.Apprunner</startup.class>
		<output.dir>target/docker/</output.dir>
		<jacoco.version>0.8.8</jacoco.version>
		<jacoco.outputDir>${project.basedir}/../target/site/jacoco</jacoco.outputDir>
		<sonar.coverage.jacoco.xmlReportPaths>${project.basedir}/target/site/jacoco/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>
	</properties>

	<distributionManagement>
		<repository>
			<id>reposilite-repository-releases</id>
			<url>
				https://artifact.visionwaves.com/releases</url>
		</repository>
	</distributionManagement>
	<repositories>
		<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>spring-snapshots</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/snapshot</url>
			<releases>
				<enabled>false</enabled>
			</releases>
		</repository>
		<repository>
			<id>reposilite-repository-releases</id>
			<name>bootnext maven repo</name>
			<url>
				https://artifact.visionwaves.com/releases</url>
			<releases>
				<updatePolicy>always</updatePolicy>
			</releases>
		</repository>
		<repository>
			<id>internal-repository</id>
			<url>https://repo.maven.apache.org/maven2</url>
			<releases>
				<updatePolicy>always</updatePolicy>
			</releases>
		</repository>
	</repositories>


	<dependencies>
		<!-- https://mvnrepository.com/artifact/com.googlecode.json-simple/json-simple -->
		<!-- https://mvnrepository.com/artifact/com.mikesamuel/json-sanitizer -->
		<dependency>
			<groupId>com.mikesamuel</groupId>
			<artifactId>json-sanitizer</artifactId>
			<version>1.2.3</version>
		</dependency>



		<dependency>
			<groupId>com.enttribe.platform</groupId>
			<artifactId>base-utility-api</artifactId>
			<version>${platform.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.enttribe.platform.workflowmanagement</groupId>
					<artifactId>bootnext-workflowmanagement-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.squareup.okio</groupId>
					<artifactId>okio-jvm</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.projectlombok</groupId>
					<artifactId>lombok</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.json</groupId>
					<artifactId>json</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.opencsv</groupId>
					<artifactId>opencsv</artifactId>
				</exclusion>
			</exclusions>

		</dependency>
		<!--		<dependency>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                    <version>3.9.0</version>
                </dependency>-->
		<dependency>
			<groupId>com.enttribe.platform</groupId>
			<artifactId>enttribe-core</artifactId>
			<version>${platform.version}</version>
			<exclusions>
				<exclusion>
					<groupId>io.opentracing.contrib</groupId>
					<artifactId>opentracing-spring-jaeger-cloud-starter</artifactId>
				</exclusion>

				<exclusion>
					<groupId>com.nimbusds</groupId>
					<artifactId>nimbus-jose-jwt</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.projectlombok</groupId>
					<artifactId>lombok</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.json</groupId>
					<artifactId>json</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.opencsv</groupId>
					<artifactId>opencsv</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.enttribe.platform</groupId>
			<artifactId>enttribe-mysql-impl</artifactId>
			<version>${platform.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.github.jsqlparser</groupId>
					<artifactId>jsqlparser</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.projectlombok</groupId>
					<artifactId>lombok</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.json</groupId>
					<artifactId>json</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.opencsv</groupId>
					<artifactId>opencsv</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>-->

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>4.11.0</version> <!-- or any latest stable version -->
		</dependency>

		<dependency>
			<groupId>com.squareup.okio</groupId>
			<artifactId>okio</artifactId>
			<version>3.7.0</version> <!-- or any latest stable version -->
		</dependency>



<!--		<dependency>-->
<!--			<groupId>com.alibaba</groupId>-->
<!--			<artifactId>fastjson</artifactId>-->
<!--			<version>1.2.83</version>-->
<!--		</dependency>&ndash;&gt;-->

		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-openai-spring-boot-starter</artifactId>
		</dependency>


<!--		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-pgvector-store-spring-boot-starter</artifactId>
		</dependency>-->

	<!--	<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-milvus-store-spring-boot-starter</artifactId>
		</dependency>-->

		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-milvus-store</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-redis-store-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>5.1.0</version>
		</dependency>


		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-pdf-document-reader</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.29</version>
		</dependency>

		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>nimbus-jose-jwt</artifactId>
			<version>9.38-rc5</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/io.github.hamawhitegg/langchain-core -->
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-spring-boot-starter</artifactId>
			<version>0.33.0</version>
		</dependency>

		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-open-ai-spring-boot-starter</artifactId>
			<version>0.33.0</version>
		</dependency>


		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>5.2.3</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.2.3</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-scratchpad</artifactId>
			<version>5.2.3</version>
		</dependency>

		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-core</artifactId>
			<version>9.6.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-queryparser</artifactId>
			<version>9.6.0</version>
		</dependency>

	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.ai</groupId>
				<artifactId>spring-ai-bom</artifactId>
				<version>${spring-ai.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>


	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${output.dir}/lib</outputDirectory>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>false</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
							<useBaseVersion>false</useBaseVersion>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- Plugin to make executable jar file -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.0.2</version>
				<configuration>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<classpathPrefix>lib/</classpathPrefix>
							<mainClass>${startup.class}</mainClass>
						</manifest>
						<manifestEntries>
							<Class-Path>.</Class-Path>
						</manifestEntries>
					</archive>
					<excludes>
						<exclude>**/application.properties</exclude>
						<exclude>**/config.properties</exclude>
						<exclude>**/Dockerfile</exclude>
					</excludes>
					<finalName>x101-document-service</finalName>
					<!-- Automation for client purpose (Variable) -->
					<outputDirectory>${output.dir}</outputDirectory>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resource</id>
						<phase>validate</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${output.dir}</outputDirectory>
							<resources>
								<resource>
									<directory>src/main/resources</directory>
									<includes>
										<include>application.properties</include>
										<include>config.properties</include>
										<include>run.sh</include>
										<include>Dockerfile</include>
										<include>melodyposthook.sh</include>
									</includes>
									<filtering>true</filtering>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.7</version>
				<executions>
					<execution>
						<id>prepare-and-report</id>
						<goals>
							<goal>prepare-agent</goal>
							<goal>report</goal>
						</goals>
					</execution>
					<execution>
						<id>report-aggregate</id>
						<phase>verify</phase>
						<goals>
							<goal>report-aggregate</goal>
						</goals>
						<configuration>
							<outputDirectory>${jacoco.outputDir}</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.owasp</groupId>
				<artifactId>dependency-check-maven</artifactId>
				<version>9.0.9</version>
				<configuration>
					<ossindexAnalyzerEnabled>false</ossindexAnalyzerEnabled>
					<retireJsAnalyzerEnabled>false</retireJsAnalyzerEnabled>
					<nuspecAnalyzerEnabled>false</nuspecAnalyzerEnabled>
					<assemblyAnalyzerEnabled>false</assemblyAnalyzerEnabled>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
