mvn clean install -DskipTests
cd target
java -jar  -agentlib:native-image-agent=config-output-dir=./config-directory  -Xms1048m -Xmx1048m -XX:+UseParallelGC -XX:TieredStopAtLevel=1 -noverify -Xverify:none -XX:InitialHeapSize=2048m -XX:MaxHeapSize=2508m -XX:NewRatio=2 -Dspring.config.location=file:./application.properties -XX:ErrorFile=logs/error.log -XX:HeapDumpPath=logs/dumps emailagent-1.0.0.jar
rm -rf /tmp/config-directory/
cp -r config-directory /tmp/
cd ..
./mvnw native:compile -Pnative -DskipTests -Dproguard.skip=true -Ddependency-check.skip=true -Dmaven.test.skip -Dmaven.javadoc.skip=true
#tar -czf emailagent.tar emailagent-1.0.0.jar application.properties run.sh templates/ system_prompts/ user_prompts/
#docker build --platform=linux/amd64 --build-arg APP_NAME=emailagent . -t registry.visionwaves.com/emailagent:$1
#docker push registry.visionwaves.com/emailagent:$1

