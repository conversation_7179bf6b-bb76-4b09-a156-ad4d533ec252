#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

mvn clean package -DskipTests
cd target
tar -czf emailagent.tar emailagent-1.0.0.jar application.properties notification.json run.sh system_prompts/ user_prompts/
docker build --platform=linux/amd64 --build-arg APP_NAME=emailagent . -t registry.visionwaves.com/emailagent:$1 --no-cache
docker push registry.visionwaves.com/emailagent:$1

