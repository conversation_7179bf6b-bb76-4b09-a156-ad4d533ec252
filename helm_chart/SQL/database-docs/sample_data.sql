INSERT INTO BATCH (ID, BATCH_ID, QUEUE_NAME) VALUES (4, 'Batch-4', 'email-messages-queue1');

INSERT INTO BATCH (ID, BATCH_ID, QUEUE_NAME) VALUES (3, 'Batch-3', 'email-messages-queue1');

INSERT INTO BATCH (ID, BATCH_ID, QUEUE_NAME) VALUES (2, 'Batch-1', 'email-messages-queue1');

INSERT INTO CONTACT_BOOK (id, CONTACTS, USER_ID, CONTACT_NAME) 
VALUES (26897, '<EMAIL>', '<EMAIL>', 'aakash verma');

INSERT INTO CONTACT_BOOK (id, CONTACTS, USER_ID, CONTACT_NAME) 
VALUES (26896, '<EMAIL>', '<EMAIL>', 'himanshu bhati');

INSERT INTO CONTACT_BOOK (id, CONTACTS, USER_ID, CONTACT_NAME) 
VALUES (26895, '<EMAIL>', '<EMAIL>', 'mohammed azim');

INSERT INTO EMAIL_PREFERENCES 
(ID, CHECKIN, CHECKOUT, CONVERSATION_ID, CREATED_TIME, EMAIL_SENDER, EMAIL_SUBJECT, IMPORTANT_TAGS, MODIFIED_TIME, 
 SENDER_COMPANY, TIME_ZONE, USER_ID, FONT_SIZE, FONT_FAMILY, DISPLAY_NAME, FONT_COLOR, BLACK_LISTED_DOMAIN, 
 BLACK_LISTED_SUBJECT, CONTACT_NUMBER, BLACK_LISTED_SENDER, MASK_CONTENT, MEETING_TYPE, DEBUG_MODE, ON_DEMAND_ENABLED, 
 PREFERRED_MEETING_DURATION, DATE_FORMAT, KEYBOARD_SHORTCUTS, TIME_FORMAT, IS_CATEGORY_ENABLED, IS_PRIORITY_ENABLED, 
 IS_TONE_ENABLED, ALLOW_NOTIFICATION, DEVICE_ID, GCM_ID) 
VALUES 
(875, '11:00:00.000000', '21:00:00.000000', NULL, '2025-02-25 16:40:36.777000', NULL, NULL, NULL, '2025-02-25 16:40:36.777000',
 NULL, 'Asia/Kolkata', '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0x00', 'Teams', NULL, '0x00', 
 30, 'DD-MMM-YY', NULL, '24Hrs', 1, 1, 1, '0x00', NULL, NULL);

INSERT INTO EMAIL_PREFERENCES 
(ID, CHECKIN, CHECKOUT, CONVERSATION_ID, CREATED_TIME, EMAIL_SENDER, EMAIL_SUBJECT, IMPORTANT_TAGS, MODIFIED_TIME, 
 SENDER_COMPANY, TIME_ZONE, USER_ID, FONT_SIZE, FONT_FAMILY, DISPLAY_NAME, FONT_COLOR, BLACK_LISTED_DOMAIN, 
 BLACK_LISTED_SUBJECT, CONTACT_NUMBER, BLACK_LISTED_SENDER, MASK_CONTENT, MEETING_TYPE, DEBUG_MODE, ON_DEMAND_ENABLED, 
 PREFERRED_MEETING_DURATION, DATE_FORMAT, KEYBOARD_SHORTCUTS, TIME_FORMAT, IS_CATEGORY_ENABLED, IS_PRIORITY_ENABLED, 
 IS_TONE_ENABLED, ALLOW_NOTIFICATION, DEVICE_ID, GCM_ID) 
VALUES 
(874, '11:00:00.000000', '21:00:00.000000', NULL, '2025-02-18 08:26:46.466000', NULL, NULL, NULL, '2025-02-18 08:26:46.466000',
 NULL, 'Asia/Kolkata', '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0x00', 'Teams', NULL, '0x00', 
 30, 'DD-MMM-YY', NULL, '24Hrs', 1, 1, 1, '0x00', NULL, NULL);

INSERT INTO EMAIL_PREFERENCES 
(ID, CHECKIN, CHECKOUT, CONVERSATION_ID, CREATED_TIME, EMAIL_SENDER, EMAIL_SUBJECT, IMPORTANT_TAGS, MODIFIED_TIME, 
 SENDER_COMPANY, TIME_ZONE, USER_ID, FONT_SIZE, FONT_FAMILY, DISPLAY_NAME, FONT_COLOR, BLACK_LISTED_DOMAIN, 
 BLACK_LISTED_SUBJECT, CONTACT_NUMBER, BLACK_LISTED_SENDER, MASK_CONTENT, MEETING_TYPE, DEBUG_MODE, ON_DEMAND_ENABLED, 
 PREFERRED_MEETING_DURATION, DATE_FORMAT, KEYBOARD_SHORTCUTS, TIME_FORMAT, IS_CATEGORY_ENABLED, IS_PRIORITY_ENABLED, 
 IS_TONE_ENABLED, ALLOW_NOTIFICATION, DEVICE_ID, GCM_ID) 
VALUES 
(873, '11:00:00.000000', '21:00:00.000000', NULL, '2025-01-29 08:00:11.829000', NULL, NULL, NULL, '2025-01-29 08:00:11.829000',
 NULL, 'Asia/Kolkata', '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0x00', 'Teams', NULL, '0x00', 
 30, 'DD-MMM-YY', NULL, '24Hrs', 1, 1, 1, '0x00', NULL, NULL);

INSERT INTO EMAIL_PREFERENCES_AUD (
    ID, REV, REVTYPE, BLACK_LISTED_DOMAIN, BLACK_LISTED_SENDER, BLACK_LISTED_SUBJECT, 
    CHECKIN, CHECKOUT, CONTACT_NUMBER, CONVERSATION_ID, CREATED_TIME, DISPLAY_NAME, 
    EMAIL_SENDER, EMAIL_SUBJECT, FONT_COLOR, FONT_FAMILY, FONT_SIZE, IMPORTANT_TAGS, 
    MODIFIED_TIME, SENDER_COMPANY, TIME_ZONE, USER_ID, MASK_CONTENT, MEETING_TYPE, 
    DEBUG_MODE, ON_DEMAND_ENABLED, PREFERRED_MEETING_DURATION, DATE_FORMAT, TIME_FORMAT, 
    KEYBOARD_SHORTCUTS, IS_CATEGORY_ENABLED, IS_PRIORITY_ENABLED, IS_TONE_ENABLED, 
    ALLOW_NOTIFICATION, DEVICE_ID, GCM_ID
) VALUES 
(875, 20402, 0, NULL, NULL, NULL, '11:00:00', '21:00:00', NULL, NULL, 
 '2025-02-25 16:40:36.777000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 
 '2025-02-25 16:40:36.777000', NULL, 'Asia/Kolkata', '<EMAIL>', 
 '0x00', 'Teams', NULL, '0x00', 30, 'DD-MMM-YY', '24Hrs', NULL, 1, 1, 1, '0x00', NULL, NULL);

INSERT INTO EMAIL_PREFERENCES_AUD (
    ID, REV, REVTYPE, BLACK_LISTED_DOMAIN, BLACK_LISTED_SENDER, BLACK_LISTED_SUBJECT, 
    CHECKIN, CHECKOUT, CONTACT_NUMBER, CONVERSATION_ID, CREATED_TIME, DISPLAY_NAME, 
    EMAIL_SENDER, EMAIL_SUBJECT, FONT_COLOR, FONT_FAMILY, FONT_SIZE, IMPORTANT_TAGS, 
    MODIFIED_TIME, SENDER_COMPANY, TIME_ZONE, USER_ID, MASK_CONTENT, MEETING_TYPE, 
    DEBUG_MODE, ON_DEMAND_ENABLED, PREFERRED_MEETING_DURATION, DATE_FORMAT, TIME_FORMAT, 
    KEYBOARD_SHORTCUTS, IS_CATEGORY_ENABLED, IS_PRIORITY_ENABLED, IS_TONE_ENABLED, 
    ALLOW_NOTIFICATION, DEVICE_ID, GCM_ID
) VALUES 
(874, 20398, 0, NULL, NULL, NULL, '11:00:00', '21:00:00', NULL, NULL, 
 '2025-02-18 08:26:46.466000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 
 '2025-02-18 08:26:46.466000', NULL, 'Asia/Kolkata', '<EMAIL>', 
 '0x00', 'Teams', NULL, '0x00', 30, 'DD-MMM-YY', '24Hrs', NULL, 1, 1, 1, '0x00', NULL, NULL);

INSERT INTO EMAIL_PREFERENCES_AUD (
    ID, REV, REVTYPE, BLACK_LISTED_DOMAIN, BLACK_LISTED_SENDER, BLACK_LISTED_SUBJECT, 
    CHECKIN, CHECKOUT, CONTACT_NUMBER, CONVERSATION_ID, CREATED_TIME, DISPLAY_NAME, 
    EMAIL_SENDER, EMAIL_SUBJECT, FONT_COLOR, FONT_FAMILY, FONT_SIZE, IMPORTANT_TAGS, 
    MODIFIED_TIME, SENDER_COMPANY, TIME_ZONE, USER_ID, MASK_CONTENT, MEETING_TYPE, 
    DEBUG_MODE, ON_DEMAND_ENABLED, PREFERRED_MEETING_DURATION, DATE_FORMAT, TIME_FORMAT, 
    KEYBOARD_SHORTCUTS, IS_CATEGORY_ENABLED, IS_PRIORITY_ENABLED, IS_TONE_ENABLED, 
    ALLOW_NOTIFICATION, DEVICE_ID, GCM_ID
) VALUES 
(873, 20368, 0, NULL, NULL, NULL, '11:00:00', '21:00:00', NULL, NULL, 
 '2025-01-29 08:00:11.829000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 
 '2025-01-29 08:00:11.829000', NULL, 'Asia/Kolkata', '<EMAIL>', 
 '0x00', 'Teams', NULL, '0x00', 30, 'DD-MMM-YY', '24Hrs', NULL, 1, 1, 1, '0x00', NULL, NULL);

INSERT INTO EMAIL_STATS (ID, UNUSED_COUNT, STATS_DATE, TYPE, USED_COUNT, USER_ID) 
VALUES (2568, 24, '2025-03-03', 'email', 0, '<EMAIL>');

INSERT INTO EMAIL_STATS (ID, UNUSED_COUNT, STATS_DATE, TYPE, USED_COUNT, USER_ID) 
VALUES (2567, 53, '2025-03-02', 'email', 0, '<EMAIL>');

INSERT INTO EMAIL_STATS (ID, UNUSED_COUNT, STATS_DATE, TYPE, USED_COUNT, USER_ID) 
VALUES (2566, 23, '2025-03-01', 'email', 0, '<EMAIL>');

INSERT INTO EMAIL_THREAD (ID, CONVERSATION_ID, STAR_MARKED, STAR_REASON, SUBJECT, THREAD_SUMMARY, USER_ID, CREATED_TIME, MODIFIED_TIME, SHORT_SUMMARY, ACTION_DECRYPT_OBJECT, LONG_DECRYPT_OBJECT, SHORT_DECRYPT_OBJECT) 
VALUES (52278, 'AAQkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OAAQAJjg9oAvBL5HjZF-pEG9Dw4=', NULL, NULL, '❗ Withdrawal Alert: Check your A/c balance now!', 
'{"summaryPoints":["HDFC Bank sent multiple withdrawal alerts to the customer regarding a debit/withdrawal transaction of INR 37,901.00 from account XX3130 on 01-MAR-2025","The transaction was made on account of NEFT - ICIC0000041 - . - ************ - PARASH GUPTA","The customer’s available balance remains INR 68,85,719.47"],"actionItems":[]}', 
'<EMAIL>', '2025-03-01 14:36:35.186000', '2025-03-03 10:36:12.490000', 
'{"summary":"HDFC Bank notified the customer about a debit/withdrawal transaction of INR 37,901.00 from account XX3130 on 01-MAR-2025, with an available balance of INR 68,85,719.47."}', NULL, NULL, NULL);

INSERT INTO EMAIL_THREAD (ID, CONVERSATION_ID, STAR_MARKED, STAR_REASON, SUBJECT, THREAD_SUMMARY, USER_ID, CREATED_TIME, MODIFIED_TIME, SHORT_SUMMARY, ACTION_DECRYPT_OBJECT, LONG_DECRYPT_OBJECT, SHORT_DECRYPT_OBJECT) 
VALUES (52277, 'AAQkAGNjZjBjMmU2LTQwNGYtNGU1Ny04Yzk2LWUzMTE1YjEzNzUyMgAQAKgOBK0xKmFEg_BjPwXFFeE=', NULL, NULL, '❗ Withdrawal Alert: Check your A/c balance now!', 
'{"summaryPoints":["HDFC Bank notified the customer about a debit/withdrawal transaction of INR 37,901.00 from account XX3130 on 01/03/2025.","The transaction was made on account of NEFT - ICIC0000041 - ************ - PARASH GUPTA","The available balance in the account is INR 68,85,719.47."],"actionItems":[]}', 
'<EMAIL>', '2025-03-01 14:36:28.588000', '2025-03-03 10:36:07.139000', 
'{"summary":"HDFC Bank notified a debit of INR 37,901.00 from account XX3130 on 01/03/2025, with an available balance of INR 68,85,719.47, and provided information on service charges and fees."}', NULL, NULL, NULL);

INSERT INTO EMAIL_THREAD (ID, CONVERSATION_ID, STAR_MARKED, STAR_REASON, SUBJECT, THREAD_SUMMARY, USER_ID, CREATED_TIME, MODIFIED_TIME, SHORT_SUMMARY, ACTION_DECRYPT_OBJECT, LONG_DECRYPT_OBJECT, SHORT_DECRYPT_OBJECT) 
VALUES (52276, 'AAQkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OAAQAD3w3cgvoEZHnlInRqrH_uI=', NULL, NULL, '❗ Withdrawal Alert: Check your A/c balance now!', 
'{"summaryPoints":["HDFC Bank sent a debit/withdrawal transaction notification to the customer with details of INR 37,901.00 from their account XX3130 on 01-MAR-2025.","The customer’s available balance is INR 1,86,16,000.47."],"actionItems":["Customer: Verify the transaction and take action if required","Customer: Modify or unsubscribe from these alerts by logging in to NetBanking or visiting the nearest branch"]}', 
'<EMAIL>', '2025-03-01 14:11:06.597000', '2025-03-03 10:36:01.015000', 
'{"summary":"HDFC Bank notified a debit/withdrawal of INR 37,901.00 from account XX3130 on 01-MAR-2025, with an available balance of INR 1,86,16,000.47."}', NULL, NULL, NULL);

INSERT INTO EMAIL_USER (ID, DELETED, EMAIL, NAME, PHONE_NUMBER, TYPE, USER_ID, BATCH_ID, CREATED_TIME, MODIFIED_TIME, USER_OID, QUEUE_NAME)  
VALUES (106, 0x01, '<EMAIL>', 'PD', '**********', 'Office365', '<EMAIL>', 'Batch-4', NULL, '2025-01-23 11:42:59.015000', NULL, 'email-messages-queue1');

INSERT INTO EMAIL_USER (ID, DELETED, EMAIL, NAME, PHONE_NUMBER, TYPE, USER_ID, BATCH_ID, CREATED_TIME, MODIFIED_TIME, USER_OID, QUEUE_NAME)  
VALUES (105, 0x01, '<EMAIL>', 'Piyush Sethiya', NULL, 'Office365', '<EMAIL>', 'Batch-4', '2024-11-25 16:18:12.322000', '2024-11-25 16:18:12.322000', NULL, 'email-messages-priority-queue1');

INSERT INTO EMAIL_USER (ID, DELETED, EMAIL, NAME, PHONE_NUMBER, TYPE, USER_ID, BATCH_ID, CREATED_TIME, MODIFIED_TIME, USER_OID, QUEUE_NAME)  
VALUES (104, 0x01, '<EMAIL>', 'Praveen Dubey', NULL, 'Office365', '<EMAIL>', 'Batch-4', '2024-11-25 16:17:02.685000', '2024-11-25 16:17:02.685000', NULL, 'email-messages-priority-queue1');

INSERT INTO MAIL_SUMMARY (
    ID, CATEGORY, CONVERSATION_ID, CREATED_TIME, FROM_USER, MESSAGE_ID, MESSAGE_SUMMARY, 
    MODIFIED_TIME, OBJECTIVE, PRIORITY, STAR_MARK_REASON, STAR_MARKED, TAG, TO_USER, USER_ID, 
    CC_USER, SUBJECT, ACTION_OWNER, STATUS, FLAG_STATUS, TYPE, MAIL_RECEIVED_TIME, 
    CATEGORY_REASON, PRIORITY_REASON, INTERNET_MESSAGE_ID, ACTION_TAKEN, DELETED, DECRYPT_SUMMARY, 
    FOLDER_NAME, EMAIL_TONE, EMAIL_TONE_REASON, ACTION_CLEAR_REASON, NOTIFICATION_STATUS, MEETING_PREVIEW
) VALUES (
    103130, 'Attention', 'AAQkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OAAQAJjg9oAvBL5HjZF-pEG9Dw4=',
    '2025-03-01 14:36:35.188000', '<EMAIL>', 
    'AAMkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OABGAAAAAACzlCv1ukXBTbWKLy9nIfV3BwBhmxRn9bjHS4gkv1AdYjsrAAAAAAEMAABhmxRn9bjHS4gkv1AdYjsrAADBVFrlAAA=',
    '{"subject":"❗ Withdrawal Alert: Check your A/c balance now!","participants":["<EMAIL>","<EMAIL>"],"summaryObject":{"createdTime":*************,"sender":"<EMAIL>","content":"HDFC Bank notified the customer about a debit/withdrawal transaction of INR 37,901.00 from their account.","messageId":"AAMkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OABGAAAAAACzlCv1ukXBTbWKLy9nIfV3BwBhmxRn9bjHS4gkv1AdYjsrAAAAAAEMAABhmxRn9bjHS4gkv1AdYjsrAADBVFrlAAA=","internetMessageId":"<<EMAIL>>"},"actionOwner":[]}', 
    '2025-03-03 10:36:12.489000', '[{"quickReply":"Acknowledged","replyCategory":"mail"},{"quickReply":"Need More Info","replyCategory":"mail"},{"quickReply":"Call Customer","replyCategory":"meeting"}]', 
    'High', NULL, '0x00', '["HDFC Bank","INR 37,901.00"]', '<EMAIL>', '<EMAIL>', NULL, 
    '❗ Withdrawal Alert: Check your A/c balance now!', '[]', NULL, NULL, 'Email', '2025-03-01 13:47:54.000000', 
    'The mailbox user is in the ''To'' field and the email requires immediate attention regarding a debit/withdrawal transaction from their account.', 
    'The email notifies the user about a recent debit/withdrawal transaction and prompts them to check their account balance, implying a need for immediate attention within 1-3 days.', 
    '<<EMAIL>>', '0x00', '0x00', NULL, 
    'Inbox', 'Neutral', 
    'The email is informative, objective, and impartial, providing details about the transaction and the available balance without expressing personal opinions or emotions.', 
    NULL, 'NOT_SENT', NULL
);

INSERT INTO MAIL_SUMMARY (
    ID, CATEGORY, CONVERSATION_ID, CREATED_TIME, FROM_USER, MESSAGE_ID, MESSAGE_SUMMARY, 
    MODIFIED_TIME, OBJECTIVE, PRIORITY, STAR_MARK_REASON, STAR_MARKED, TAG, TO_USER, USER_ID, 
    CC_USER, SUBJECT, ACTION_OWNER, STATUS, FLAG_STATUS, TYPE, MAIL_RECEIVED_TIME, 
    CATEGORY_REASON, PRIORITY_REASON, INTERNET_MESSAGE_ID, ACTION_TAKEN, DELETED, DECRYPT_SUMMARY, 
    FOLDER_NAME, EMAIL_TONE, EMAIL_TONE_REASON, ACTION_CLEAR_REASON, NOTIFICATION_STATUS, MEETING_PREVIEW
) VALUES (
    103129, 'Attention', 'AAQkAGNjZjBjMmU2LTQwNGYtNGU1Ny04Yzk2LWUzMTE1YjEzNzUyMgAQAKgOBK0xKmFEg_BjPwXFFeE=',
    '2025-03-01 14:36:28.589000', '<EMAIL>', 
    'AAMkAGNjZjBjMmU2LTQwNGYtNGU1Ny04Yzk2LWUzMTE1YjEzNzUyMgBGAAAAAACKXlG2d5nsQJjXeLELW0XIBwDnHW0s_HIQRIPLGGoIMoycAAAAAAEMAADnHW0s_HIQRIPLGGoIMoycAADBj4WcAAA=',
    '{"subject":"❗ Withdrawal Alert: Check your A/c balance now!","participants":["<EMAIL>","<EMAIL>"],"summaryObject":{"createdTime":*************,"sender":"<EMAIL>","content":"HDFC Bank notified the customer about a debit transaction of INR 37,901.00 from their account.","messageId":"AAMkAGNjZjBjMmU2LTQwNGYtNGU1Ny04Yzk2LWUzMTE1YjEzNzUyMgBGAAAAAACKXlG2d5nsQJjXeLELW0XIBwDnHW0s_HIQRIPLGGoIMoycAAAAAAEMAADnHW0s_HIQRIPLGGoIMoycAADBj4WcAAA=","internetMessageId":"<<EMAIL>>"},"actionOwner":[]}', 
    '2025-03-03 10:36:07.138000', '[{"quickReply":"Acknowledge","replyCategory":"mail"},{"quickReply":"Call Bank","replyCategory":"mail"},{"quickReply":"Check Balance","replyCategory":"mail"}]', 
    'High', NULL, '0x00', '["HDFC Bank","INR 37,901.00"]', '<EMAIL>', '<EMAIL>', NULL, 
    '❗ Withdrawal Alert: Check your A/c balance now!', '[]', NULL, NULL, 'Email', '2025-03-01 13:47:54.000000', 
    'The mailbox user is in the ''To'' field and the email requires immediate attention regarding a debit/withdrawal transaction from their account.', 
    'The email is about a recent transaction (on 01-MAR-2025) and the user is advised to check their account balance, indicating a need for immediate attention.', 
    '<<EMAIL>>', '0x00', '0x00', NULL, 
    'Inbox', 'Neutral', 
    'The email is informative, providing details about the transaction and the current account balance, without expressing any emotions or personal opinions.', 
    NULL, 'NOT_ALLOWED', NULL
);

INSERT INTO MAIL_SUMMARY (
    ID, CATEGORY, CONVERSATION_ID, CREATED_TIME, FROM_USER, MESSAGE_ID, MESSAGE_SUMMARY, 
    MODIFIED_TIME, OBJECTIVE, PRIORITY, STAR_MARK_REASON, STAR_MARKED, TAG, TO_USER, USER_ID, 
    CC_USER, SUBJECT, ACTION_OWNER, STATUS, FLAG_STATUS, TYPE, MAIL_RECEIVED_TIME, 
    CATEGORY_REASON, PRIORITY_REASON, INTERNET_MESSAGE_ID, ACTION_TAKEN, DELETED, DECRYPT_SUMMARY, 
    FOLDER_NAME, EMAIL_TONE, EMAIL_TONE_REASON, ACTION_CLEAR_REASON, NOTIFICATION_STATUS, MEETING_PREVIEW
) VALUES (
    103128, 'Attention', 'AAQkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OAAQAD3w3cgvoEZHnlInRqrH_uI=',
    '2025-03-01 14:11:06.605000', '<EMAIL>', NULL, NULL, 
    '2025-03-03 10:11:06.605000', NULL, 'High', NULL, '0x00', '["HDFC Bank"]', '<EMAIL>', '<EMAIL>', NULL, 
    '❗ Urgent: Large Withdrawal Alert', '[]', NULL, NULL, 'Email', '2025-03-01 13:47:54.000000', 
    NULL, NULL, NULL, '0x00', '0x00', NULL, 
    'Inbox', 'Neutral', NULL, NULL, 'NOT_SENT', NULL
);

INSERT INTO MEETING_SUMMARY (ID, MEETING_ID, CREATION_TIME, LONG_SUMMARY, SHORT_SUMMARY, USER_ID, INTERNET_MESSAGE_ID, MODIFIED_TIME, PROCESSING_ERROR, STATUS, SUBJECT, ACTION_ITEMS) 
VALUES (776, ***********, '2025-02-14 06:40:04.416000', NULL, NULL, '<EMAIL>', '<<EMAIL>>', '2025-02-14 06:40:04.416000', NULL, 'NEW', 'Development and Optimization of a Prompt Analyser', NULL);

INSERT INTO MEETING_SUMMARY (ID, MEETING_ID, CREATION_TIME, LONG_SUMMARY, SHORT_SUMMARY, USER_ID, INTERNET_MESSAGE_ID, MODIFIED_TIME, PROCESSING_ERROR, STATUS, SUBJECT, ACTION_ITEMS) 
VALUES (157, 487, '2025-01-07 15:28:02.361000', NULL, NULL, '<EMAIL>', '<<EMAIL>>', '2025-01-07 15:28:02.361000', NULL, 'NEW', 'Team Meeting with NourOS team', NULL);

INSERT INTO MEETING_SUMMARY (ID, MEETING_ID, CREATION_TIME, LONG_SUMMARY, SHORT_SUMMARY, USER_ID, INTERNET_MESSAGE_ID, MODIFIED_TIME, PROCESSING_ERROR, STATUS, SUBJECT, ACTION_ITEMS) 
VALUES (156, 86953019129, '2024-12-03 10:28:01.965000', NULL, NULL, '<EMAIL>', '<<EMAIL>>', '2024-12-03 10:28:01.965000', NULL, 'NEW', 'Call', NULL);

INSERT INTO ORGANISATION (ID, CHECKIN, CHECKOUT, BLACKLISTED, ORGANISATION_NAME, TIME_ZONE, MEETING_TYPE, CREATED_TIME, MODIFIED_TIME, ACTIVE) 
VALUES (3, '11:00:00.000000', '23:00:00.000000', NULL, 'vision', 'Asia/Kolkata', 'Teams,Zoom', NULL, '2024-10-07 11:28:43.688000', 1);

INSERT INTO ORGANISATION (ID, CHECKIN, CHECKOUT, BLACKLISTED, ORGANISATION_NAME, TIME_ZONE, MEETING_TYPE, CREATED_TIME, MODIFIED_TIME, ACTIVE) 
VALUES (2, '11:00:00.000000', '21:00:00.000000', NULL, 'bootnext', 'Asia/Kolkata', 'Teams,Zoom', NULL, NULL, 1);

INSERT INTO ORGANISATION (ID, CHECKIN, CHECKOUT, BLACKLISTED, ORGANISATION_NAME, TIME_ZONE, MEETING_TYPE, CREATED_TIME, MODIFIED_TIME, ACTIVE) 
VALUES (1, '11:00:00.000000', '21:00:00.000000', NULL, 'visionwaves', 'Asia/Kolkata', 'Teams,Zoom', NULL, '2024-11-05 09:14:15.082000', 1);

INSERT INTO POLL_TIME_INFO (ID, EMAIL, END_TIME, START_TIME, STATUS) 
VALUES (6314506, '<EMAIL>', '2025-03-03 10:56:00.774154', NULL, 'SUCCESS');

INSERT INTO POLL_TIME_INFO (ID, EMAIL, END_TIME, START_TIME, STATUS) 
VALUES (6314505, '<EMAIL>', '2025-03-03 10:56:00.720032', NULL, 'SUCCESS');

INSERT INTO POLL_TIME_INFO (ID, EMAIL, END_TIME, START_TIME, STATUS) 
VALUES (6314504, '<EMAIL>', '2025-03-03 10:56:00.643100', NULL, 'SUCCESS');

INSERT INTO REVINFO (REV, REVTSTMP) 
VALUES (20403, *************);

INSERT INTO REVINFO (REV, REVTSTMP) 
VALUES (20402, *************);

INSERT INTO REVINFO (REV, REVTSTMP) 
VALUES (20401, *************);

INSERT INTO USER_ACTIONS (ID, ACTION_OWNER, ACTION_OWNER_REASON, ACTION_TAKEN, ACTION_TAKEN_REASON, CONVERSATION_ID, MAIL_RECEIVED_TIME, MAIL_SUMMARY_ID, CREATED_AT, INTERNET_MESSAGE_ID, DELETED, SUBJECT) 
VALUES (12481, '<EMAIL>', 'The mailbox user needs to review the withdrawal alert and check the account balance.', '0x00', NULL, 'AAQkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OAAQAAGeF5k3jNRBnDvpQth2pUQ=', '2025-03-01 13:26:39.000000', 103110, '2025-03-01 14:09:07.136637', '<<EMAIL>>', '0x00', '❗ Withdrawal Alert: Check your A/c balance now!');

INSERT INTO USER_ACTIONS (ID, ACTION_OWNER, ACTION_OWNER_REASON, ACTION_TAKEN, ACTION_TAKEN_REASON, CONVERSATION_ID, MAIL_RECEIVED_TIME, MAIL_SUMMARY_ID, CREATED_AT, INTERNET_MESSAGE_ID, DELETED, SUBJECT) 
VALUES (12480, '<EMAIL>', 'the mailbox user is notified about a debit/withdrawal transaction and needs to verify the transaction details', '0x00', NULL, 'AAQkAGNjZjBjMmU2LTQwNGYtNGU1Ny04Yzk2LWUzMTE1YjEzNzUyMgAQAKnhCaY-gNZKnFvJ_CT7Brk=', '2025-03-01 13:17:08.000000', 103092, '2025-03-01 13:43:39.525228', '<<EMAIL>>', '0x00', '❗ Withdrawal Alert: Check your A/c balance now!');

INSERT INTO USER_ACTIONS (ID, ACTION_OWNER, ACTION_OWNER_REASON, ACTION_TAKEN, ACTION_TAKEN_REASON, CONVERSATION_ID, MAIL_RECEIVED_TIME, MAIL_SUMMARY_ID, CREATED_AT, INTERNET_MESSAGE_ID, DELETED, SUBJECT) 
VALUES (12479, '<EMAIL>', 'The mailbox user is tasked with connecting the sender to the agency.', '0x00', NULL, 'AAQkADAyM2VjYjBhLWI0YzQtNDkwNC1hNWM4LTRkNDM1YTYyOTBjNQAQALRHIbDm1mtPtqarI3lEQh0=', '2025-03-01 13:02:40.000000', 103089, '2025-03-01 13:43:13.145828', '<<EMAIL>>', '0x00', 'Re: Urgent requirements - QA positions under telecom domain');

INSERT INTO USER_FOLDERS (ID, ACTIVE, DISPLAY_NAME, EMAIL, FOLDER_ID) 
VALUES (439, '0x00', 'NourOS', '<EMAIL>', 'AAMkADJlMTI1ZmJmLWYzZGEtNDFlNS1iN2FjLTA3N2ZmOTlhODc2NQAuAAAAAADEG4ApxMfZT7vvV9Fs6ZioAQCQpVPoi4OzQ5b6i_92jOn2AAAhrwCWAAA=');

INSERT INTO USER_FOLDERS (ID, ACTIVE, DISPLAY_NAME, EMAIL, FOLDER_ID) 
VALUES (438, '0x00', 'Jira Mail', '<EMAIL>', 'AAMkADJlMTI1ZmJmLWYzZGEtNDFlNS1iN2FjLTA3N2ZmOTlhODc2NQAuAAAAAADEG4ApxMfZT7vvV9Fs6ZioAQCQpVPoi4OzQ5b6i_92jOn2AAAGb6xmAAA=');

INSERT INTO USER_FOLDERS (ID, ACTIVE, DISPLAY_NAME, EMAIL, FOLDER_ID) 
VALUES (437, '0x01', 'Sent Items', '<EMAIL>', 'AAMkADJlMTI1ZmJmLWYzZGEtNDFlNS1iN2FjLTA3N2ZmOTlhODc2NQAuAAAAAADEG4ApxMfZT7vvV9Fs6ZioAQCQpVPoi4OzQ5b6i_92jOn2AAAAAAEJAAA=');

INSERT INTO USER_MAIL_ATTACHMENT (ID, ATTACHMENT_ID, CONVERSATION_ID, LONG_SUMMARY, MESSAGE_ID, NAME, RAG_DOCUMENT_ID, SHORT_SUMMARY, TYPE, USER_ID, DOC_PATH, INTERNET_MESSAGE_ID, PROCESSING_ERROR, PROCESSING_STATUS, UNIQUE_NAME, CREATION_TIME, MODIFIED_TIME, RETRY_COUNT, SUBJECT, creationtime, modificationtime, creatorid_fk, lastModifierid_FK, retryCount, BATCH_ID, ERROR_DISPLAY) 
VALUES (12270, 'AAMkADAyM2VjYjBhLWI0YzQtNDkwNC1hNWM4LTRkNDM1YTYyOTBjNQBGAAAAAACr3O_ubfzcSLSGX5wcYlK1BwAz7--je160Qq508t1DOn1bAAAAAAEJAAAz7--je160Qq508t1DOn1bAADCXJ6uAAABEgAQAIQgpSC0dfVAg52-Phu15rM=', 'AAQkADAyM2VjYjBhLWI0YzQtNDkwNC1hNWM4LTRkNDM1YTYyOTBjNQAQAGHW8mwFTEvUlW5AYQ2c31M=', NULL, 'AAMkADAyM2VjYjBhLWI0YzQtNDkwNC1hNWM4LTRkNDM1YTYyOTBjNQBGAAAAAACr3O_ubfzcSLSGX5wcYlK1BwAz7--je160Qq508t1DOn1bAAAAAAEJAAAz7--je160Qq508t1DOn1bAADCXJ6uAAA=', 'SAI Unified AI Platform & HumAIneOS Marketplace Activation_Accenture Proposal_2nd March.pdf', NULL, NULL, 'application/pdf', '<EMAIL>', '<EMAIL>/attachments/20250303104001_SAI Unified AI Platform & HumAIneOS Marketplace Activation_Accenture Proposal_2nd March.pdf', '<<EMAIL>>', NULL, 'NEW', '<<EMAIL>>_SAI Unified AI Platform & HumAIneOS Marketplace Activation_Accenture Proposal_2nd March.pdf', '2025-03-03 10:40:01.414000', '2025-03-03 10:40:01.414000', NULL, 'FW: SAI AI PoP and HumAIneOS Marketplace Activation Proposal', NULL, NULL, NULL, NULL, NULL, 'Batch-1', NULL);

INSERT INTO USER_MAIL_ATTACHMENT (ID, ATTACHMENT_ID, CONVERSATION_ID, LONG_SUMMARY, MESSAGE_ID, NAME, RAG_DOCUMENT_ID, SHORT_SUMMARY, TYPE, USER_ID, DOC_PATH, INTERNET_MESSAGE_ID, PROCESSING_ERROR, PROCESSING_STATUS, UNIQUE_NAME, CREATION_TIME, MODIFIED_TIME, RETRY_COUNT, SUBJECT, creationtime, modificationtime, creatorid_fk, lastModifierid_FK, retryCount, BATCH_ID, ERROR_DISPLAY) 
VALUES (12269, 'AAMkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OABGAAAAAACzlCv1ukXBTbWKLy9nIfV3BwBhmxRn9bjHS4gkv1AdYjsrAAAAAAEMAABhmxRn9bjHS4gkv1AdYjsrAADCabvBAAABEgAQAMOXkKwz_ypLtmDagJTOaAU=', 'AAQkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OAAQAJcz3ELOSE1ErGE_sydZSY0=', NULL, 'AAMkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OABGAAAAAACzlCv1ukXBTbWKLy9nIfV3BwBhmxRn9bjHS4gkv1AdYjsrAAAAAAEMAABhmxRn9bjHS4gkv1AdYjsrAADCabvBAAA=', 'Firewall Access Staging.docx', NULL, NULL, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', '<EMAIL>', '<EMAIL>/attachments/20250303081601_Firewall Access Staging.docx', '<<EMAIL>>', NULL, 'NEW', '<<EMAIL>>_Firewall Access Staging.docx', '2025-03-03 08:16:01.254000', '2025-03-03 08:16:01.254000', NULL, 'RE: nourOS integration with NCB Bank - IP whitelisting', NULL, NULL, NULL, NULL, NULL, 'Batch-1', NULL);

INSERT INTO USER_MAIL_ATTACHMENT (ID, ATTACHMENT_ID, CONVERSATION_ID, LONG_SUMMARY, MESSAGE_ID, NAME, RAG_DOCUMENT_ID, SHORT_SUMMARY, TYPE, USER_ID, DOC_PATH, INTERNET_MESSAGE_ID, PROCESSING_ERROR, PROCESSING_STATUS, UNIQUE_NAME, CREATION_TIME, MODIFIED_TIME, RETRY_COUNT, SUBJECT, creationtime, modificationtime, creatorid_fk, lastModifierid_FK, retryCount, BATCH_ID, ERROR_DISPLAY) 
VALUES (12268, 'AAMkADRmYzI3YWIzLTU1ZDEtNDA4ZS1hZjhmLWNmNWQ0NDA5MzViZgBGAAAAAAAxvtOpDHY0S4Njy0AcaOAxBwApccyjZNL0SJC_FtpAkijxAAAAAAEMAAApccyjZNL0SJC_FtpAkijxAAC42vksAAABEgAQAM9uNr6J16RCuk_kZ0H0qMA=', 'AAQkADRmYzI3YWIzLTU1ZDEtNDA4ZS1hZjhmLWNmNWQ0NDA5MzViZgAQACEGKb5hnKxIi5orMHqliR4=', NULL, 'AAMkADRmYzI3YWIzLTU1ZDEtNDA4ZS1hZjhmLWNmNWQ0NDA5MzViZgBGAAAAAAAxvtOpDHY0S4Njy0AcaOAxBwApccyjZNL0SJC_FtpAkijxAAAAAAEMAAApccyjZNL0SJC_FtpAkijxAAC42vksAAA=', 'Openai_Invoice-023F12D6-0011.pdf', NULL, NULL, 'application/pdf', '<EMAIL>', '<EMAIL>/attachments/20250303073004_Openai_Invoice-023F12D6-0011.pdf', '<<EMAIL>>', NULL, 'NEW', '<<EMAIL>>_Openai_Invoice-023F12D6-0011.pdf', '2025-03-03 07:30:04.134000', '2025-03-03 07:30:04.134000', NULL, 'Feb bills', NULL, NULL, NULL, NULL, NULL, 'Batch-1', NULL);

INSERT INTO USER_TEMPLATE (ID, DATA, EMAIL) 
VALUES (9, '{"valediction":"Thanks, Aniruddh Sonsale"}', '<EMAIL>');

INSERT INTO USER_TEMPLATE (ID, DATA, EMAIL) 
VALUES (8, '{"Signature":"Shiv Pathak \nSenior solution architect(VisionWaves India) \nWhatsApp - +************ \nLinkedIN - https://www.linkedin.com/in/shivchandrapathak/"}', '<EMAIL>');

INSERT INTO USER_TEMPLATE (ID, DATA, EMAIL) 
VALUES (7, '{"bday":"happy bday"}', '<EMAIL>');

INSERT INTO FAILURE_LOGS 
(ID, ATTACHMENT, ATTENDEES, CONVERSATION_ID, EMAIL, EMAIL_SUBJECT, ERROR_DATE, 
EXCEPTION_MESSAGE, INTENT, INTERNET_MESSAGE_ID, MESSAGE_ID, TYPE, 
CUSTOM_EXCEPTION_MESSAGE, USER_MAIL_ATTACHMENT, STATUS)
VALUES
(197357, NULL, NULL, 'AAQkAGNjZjBjMmU2LTQwNGYtNGU1Ny04Yzk2LWUzMTE1YjEzNzUyMgAQAFYjH8P91PZGhYig1s1m13o=', 
'<EMAIL>', '❗ Withdrawal Alert: Check your A/c balance now!', '2025-03-03 10:52:31.395000', 
'I/O error on POST request for "https://api.groq.com/openai/v1/chat/completions": null', 
'{"internetMessageId":"<<EMAIL>>","subject":"❗ Withdrawal Alert: Check your A/c balance now!","userPrompt":null,"conversationId":"AAQkAGNjZjBjMmU2LTQwNGYtNGU1Ny04Yzk2LWUzMTE1YjEzNzUyMgAQAFYjH8P91PZGhYig1s1m13o=","messageId":"AAMkAGNjZjBjMmU2LTQwNGYtNGU1Ny04Yzk2LWUzMTE1YjEzNzUyMgBGAAAAAACKXlG2d5nsQJjXeLELW0XIBwDnHW0s_HIQRIPLGGoIMoycAAAAAAEMAADnHW0s_HIQRIPLGGoIMoycAADBj4VfAAA=","previousPrompt":null,"folderName":"Inbox","type":"CATEGORY","userId":"<EMAIL>"}',
'<<EMAIL>>',
'AAMkAGNjZjBjMmU2LTQwNGYtNGU1Ny04Yzk2LWUzMTE1YjEzNzUyMgBGAAAAAACKXlG2d5nsQJjXeLELW0XIBwDnHW0s_HIQRIPLGGoIMoycAAAAAAEMAADnHW0s_HIQRIPLGGoIMoycAADBj4VfAAA=',
'GENERATE_CATEGORY', 'Error while getting category response', NULL, 'NEW');

INSERT INTO FAILURE_LOGS 
(ID, ATTACHMENT, ATTENDEES, CONVERSATION_ID, EMAIL, EMAIL_SUBJECT, ERROR_DATE, 
EXCEPTION_MESSAGE, INTENT, INTERNET_MESSAGE_ID, MESSAGE_ID, TYPE, 
CUSTOM_EXCEPTION_MESSAGE, USER_MAIL_ATTACHMENT, STATUS)
VALUES
(197356, NULL, NULL, 'AAQkADRmYzI3YWIzLTU1ZDEtNDA4ZS1hZjhmLWNmNWQ0NDA5MzViZgAQAHWwS_Ee-KxJuxe1fNCs8pg=', 
'<EMAIL>', 'Re: Plan expenses for feb month of BN and VW', '2025-03-03 10:32:12.170000', 
'Mail not summarised since sender is black listed', 
'{"internetMessageId":"<<EMAIL>>","subject":"Re: Plan expenses for feb month of BN and VW","userPrompt":null,"conversationId":"AAQkADRmYzI3YWIzLTU1ZDEtNDA4ZS1hZjhmLWNmNWQ0NDA5MzViZgAQAHWwS_Ee-KxJuxe1fNCs8pg=","messageId":"AAMkADRmYzI3YWIzLTU1ZDEtNDA4ZS1hZjhmLWNmNWQ0NDA5MzViZgBGAAAAAAAxvtOpDHY0S4Njy0AcaOAxBwApccyjZNL0SJC_FtpAkijxAAAAAAEMAAApccyjZNL0SJC_FtpAkijxAAC4PTWuAAA=","previousPrompt":null,"folderName":"Inbox","type":null,"userId":"<EMAIL>"}',
'<<EMAIL>>',
'AAMkADRmYzI3YWIzLTU1ZDEtNDA4ZS1hZjhmLWNmNWQ0NDA5MzViZgBGAAAAAAAxvtOpDHY0S4Njy0AcaOAxBwApccyjZNL0SJC_FtpAkijxAAAAAAEMAAApccyjZNL0SJC_FtpAkijxAAC4PTWuAAA=',
'BLACKLISTED_SENDER', 'Mail not summarised since sender is black listed', NULL, 'NEW');

INSERT INTO FAILURE_LOGS 
(ID, ATTACHMENT, ATTENDEES, CONVERSATION_ID, EMAIL, EMAIL_SUBJECT, ERROR_DATE, 
EXCEPTION_MESSAGE, INTENT, INTERNET_MESSAGE_ID, MESSAGE_ID, TYPE, 
CUSTOM_EXCEPTION_MESSAGE, USER_MAIL_ATTACHMENT, STATUS)
VALUES
(197355, NULL, NULL, 'AAQkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OAAQALQB51E8Rj5PrxQ-Jk18ti8=', 
'<EMAIL>', '❗ Withdrawal Alert: Check your A/c balance now!', '2025-03-03 10:27:16.203000', 
'I/O error on POST request for "https://api.groq.com/openai/v1/chat/completions": null', 
'{"internetMessageId":"<<EMAIL>>","subject":"❗ Withdrawal Alert: Check your A/c balance now!","userPrompt":null,"conversationId":"AAQkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OAAQALQB51E8Rj5PrxQ-Jk18ti8=","messageId":"AAMkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OABGAAAAAACzlCv1ukXBTbWKLy9nIfV3BwBhmxRn9bjHS4gkv1AdYjsrAAAAAAEMAABhmxRn9bjHS4gkv1AdYjsrAADBVFquAAA=","previousPrompt":null,"folderName":"Inbox","type":"CATEGORY","userId":"<EMAIL>"}',
'<<EMAIL>>',
'AAMkAGNjNGIwM2I1LTk1MjAtNDYwYi05NzIxLTEzM2YxODBhZGQ0OABGAAAAAACzlCv1ukXBTbWKLy9nIfV3BwBhmxRn9bjHS4gkv1AdYjsrAAAAAAEMAABhmxRn9bjHS4gkv1AdYjsrAADBVFquAAA=',
'GENERATE_CATEGORY', 'Error while getting category response', NULL, 'NEW');


