CREATE TABLE `BATCH` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each batch',
  `BATCH_ID` varchar(20) DEFAULT NULL COMMENT 'Unique identifier for the batch',
  `QUEUE_NAME` varchar(50) DEFAULT NULL COMMENT 'Name of the queue associated with the batch',
  PRIMARY KEY (`ID`)
);

CREATE TABLE `BUILD_EMAILS` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each record in BUILD_EMAILS',
  `APPROVED_BY` varchar(255) DEFAULT NULL COMMENT 'Name or identifier of the approver',
  `BLUE_OCEAN_URL` varchar(255) DEFAULT NULL COMMENT 'URL to the Blue Ocean view',
  `BUILD_PARAMETERS` varchar(255) DEFAULT NULL COMMENT 'Parameters used in the build',
  `CONTENT` varchar(255) DEFAULT NULL COMMENT 'Content related to the build email',
  `CONVERSATION_ID` varchar(255) DEFAULT NULL COMMENT 'Identifier for the related conversation',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the record was created',
  `DEPLOYED_DATE` datetime(6) DEFAULT NULL COMMENT 'Date when the deployment occurred',
  `EMAIL_FROM` varchar(255) DEFAULT NULL COMMENT 'Email address of the sender',
  `IS_DEPLOYED` bit(1) DEFAULT NULL COMMENT 'Flag indicating if the build is deployed',
  `MESSAGE_ID` varchar(255) DEFAULT NULL COMMENT 'Unique message identifier',
  `STATUS` varchar(255) DEFAULT NULL COMMENT 'Current status of the build email',
  PRIMARY KEY (`ID`)
);

CREATE TABLE `CONTACT_BOOK` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each contact entry',
  `CONTACTS` varchar(255) DEFAULT NULL COMMENT 'Contact details, typically an identifier or email',
  `USER_ID` varchar(255) DEFAULT NULL COMMENT 'User ID associated with the contact',
  `CONTACT_NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the contact',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_contact` (`USER_ID`,`CONTACTS`),
  UNIQUE KEY `unique_contact_user` (`CONTACTS`,`USER_ID`)
);

CREATE TABLE `EMAIL_PREFERENCES` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each email preference entry',
  `CHECKIN` time(6) DEFAULT NULL COMMENT 'Preferred check-in time',
  `CHECKOUT` time(6) DEFAULT NULL COMMENT 'Preferred check-out time',
  `CONVERSATION_ID` text COMMENT 'Identifier for related conversations',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the email preference was created',
  `EMAIL_SENDER` varchar(255) DEFAULT NULL COMMENT 'Email address of the sender',
  `EMAIL_SUBJECT` text COMMENT 'Subject of the email',
  `IMPORTANT_TAGS` varchar(255) DEFAULT NULL COMMENT 'Tags considered important by the user',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Last modified time of the email preference',
  `SENDER_COMPANY` varchar(255) DEFAULT NULL COMMENT 'Company associated with the sender',
  `TIME_ZONE` varchar(255) DEFAULT NULL COMMENT 'Preferred time zone of the user',
  `USER_ID` varchar(255) DEFAULT NULL COMMENT 'User ID associated with the preferences',
  `FONT_SIZE` varchar(255) DEFAULT NULL COMMENT 'Preferred font size for emails',
  `FONT_FAMILY` varchar(255) DEFAULT NULL COMMENT 'Preferred font family for emails',
  `DISPLAY_NAME` varchar(100) DEFAULT NULL COMMENT 'Display name associated with the user',
  `FONT_COLOR` varchar(30) DEFAULT NULL COMMENT 'Preferred font color for emails',
  `BLACK_LISTED_DOMAIN` text COMMENT 'Domains blacklisted by the user',
  `BLACK_LISTED_SUBJECT` text COMMENT 'Subjects blacklisted by the user',
  `CONTACT_NUMBER` varchar(15) DEFAULT NULL COMMENT 'Contact number of the user',
  `BLACK_LISTED_SENDER` text COMMENT 'Email addresses blacklisted by the user',
  `MASK_CONTENT` bit(1) DEFAULT NULL COMMENT 'Flag indicating if content masking is enabled',
  `MEETING_TYPE` varchar(50) DEFAULT NULL COMMENT 'Preferred type of meeting for notifications',
  `DEBUG_MODE` bit(1) DEFAULT NULL COMMENT 'Flag indicating if debug mode is enabled',
  `ON_DEMAND_ENABLED` bit(1) DEFAULT NULL COMMENT 'Flag indicating if on-demand preferences are enabled',
  `PREFERRED_MEETING_DURATION` int DEFAULT NULL COMMENT 'Preferred duration for meetings',
  `DATE_FORMAT` varchar(50) DEFAULT NULL COMMENT 'Preferred date format',
  `KEYBOARD_SHORTCUTS` varchar(2000) DEFAULT NULL COMMENT 'User-defined keyboard shortcuts',
  `TIME_FORMAT` varchar(50) DEFAULT '24Hrs' COMMENT 'Preferred time format (e.g., 12Hrs or 24Hrs)',
  `IS_CATEGORY_ENABLED` tinyint(1) DEFAULT '1' COMMENT 'Flag indicating if category functionality is enabled',
  `IS_PRIORITY_ENABLED` tinyint(1) DEFAULT '1' COMMENT 'Flag indicating if priority functionality is enabled',
  `IS_TONE_ENABLED` tinyint(1) DEFAULT '1' COMMENT 'Flag indicating if tone detection is enabled',
  `ALLOW_NOTIFICATION` bit(1) DEFAULT b'0' COMMENT 'Flag indicating if notifications are allowed',
  `DEVICE_ID` varchar(255) DEFAULT NULL COMMENT 'Device ID associated with preferences',
  `GCM_ID` varchar(255) DEFAULT NULL COMMENT 'Google Cloud Messaging ID for notifications',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_user_id` (`USER_ID`),
  KEY `idx_email_subject` (`EMAIL_SUBJECT`(255)),
  KEY `idx_sender_company` (`SENDER_COMPANY`),
  KEY `idx_email_sender` (`EMAIL_SENDER`),
  KEY `idx_blacklisted_domain` (`BLACK_LISTED_DOMAIN`(255)),
  KEY `idx_blacklisted_subject` (`BLACK_LISTED_SUBJECT`(255)),
  KEY `idx_conversation_id` (`CONVERSATION_ID`(255)),
  KEY `idx_contact_number` (`CONTACT_NUMBER`),
  KEY `idx_font_color` (`FONT_COLOR`),
  KEY `idx_display_name` (`DISPLAY_NAME`),
  KEY `idx_user_id` (`USER_ID`),
  KEY `idx_font_family` (`FONT_FAMILY`),
  KEY `idx_font_size` (`FONT_SIZE`),
  KEY `idx_time_zone` (`TIME_ZONE`),
  KEY `idx_modified_time` (`MODIFIED_TIME`)
);

CREATE TABLE `EMAIL_PREFERENCES_AUD` (
  `ID` int NOT NULL COMMENT 'Primary key identifier for each email preference audit entry',
  `REV` int NOT NULL COMMENT 'Revision number for the audit entry',
  `REVTYPE` tinyint DEFAULT NULL COMMENT 'Type of revision (e.g., insert, update, delete)',
  `BLACK_LISTED_DOMAIN` text COMMENT 'Domains blacklisted for this audit record',
  `BLACK_LISTED_SENDER` text COMMENT 'Senders blacklisted for this audit record',
  `BLACK_LISTED_SUBJECT` text COMMENT 'Subjects blacklisted for this audit record',
  `CHECKIN` time(6) DEFAULT NULL COMMENT 'Preferred check-in time for the audit record',
  `CHECKOUT` time(6) DEFAULT NULL COMMENT 'Preferred check-out time for the audit record',
  `CONTACT_NUMBER` varchar(255) DEFAULT NULL COMMENT 'Contact number for the audit record',
  `CONVERSATION_ID` text COMMENT 'Conversation identifier for this audit entry',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the audit entry was created',
  `DISPLAY_NAME` varchar(255) DEFAULT NULL COMMENT 'Display name for the user in this audit record',
  `EMAIL_SENDER` text COMMENT 'Email address of the sender in the audit record',
  `EMAIL_SUBJECT` text COMMENT 'Subject of the email in the audit record',
  `FONT_COLOR` varchar(255) DEFAULT NULL COMMENT 'Font color used in the email in the audit record',
  `FONT_FAMILY` varchar(255) DEFAULT NULL COMMENT 'Font family used in the email in the audit record',
  `FONT_SIZE` varchar(255) DEFAULT NULL COMMENT 'Font size used in the email in the audit record',
  `IMPORTANT_TAGS` text COMMENT 'Important tags associated with the email in the audit record',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the audit entry was last modified',
  `SENDER_COMPANY` text COMMENT 'Company of the sender in the audit record',
  `TIME_ZONE` varchar(255) DEFAULT NULL COMMENT 'Time zone associated with the user in the audit record',
  `USER_ID` varchar(255) DEFAULT NULL COMMENT 'User ID associated with the audit record',
  `MASK_CONTENT` bit(1) DEFAULT NULL COMMENT 'Flag indicating if content is masked in this audit record',
  `MEETING_TYPE` varchar(50) DEFAULT NULL COMMENT 'Meeting type for the email preference in the audit record',
  `DEBUG_MODE` bit(1) DEFAULT NULL COMMENT 'Flag indicating if debug mode is enabled for the email preference in the audit record',
  `ON_DEMAND_ENABLED` bit(1) DEFAULT NULL COMMENT 'Flag indicating if on-demand functionality is enabled for the email preference in the audit record',
  `PREFERRED_MEETING_DURATION` int DEFAULT NULL COMMENT 'Preferred meeting duration for the user in this audit record',
  `DATE_FORMAT` varchar(50) DEFAULT NULL COMMENT 'Preferred date format for the user in this audit record',
  `TIME_FORMAT` varchar(50) DEFAULT '24Hrs' COMMENT 'Preferred time format for the user in this audit record',
  `KEYBOARD_SHORTCUTS` varchar(2000) DEFAULT NULL COMMENT 'User-defined keyboard shortcuts for the email preferences in this audit record',
  `IS_CATEGORY_ENABLED` tinyint(1) DEFAULT '1' COMMENT 'Flag indicating if category functionality is enabled for the email preferences in the audit record',
  `IS_PRIORITY_ENABLED` tinyint(1) DEFAULT '1' COMMENT 'Flag indicating if priority functionality is enabled for the email preferences in the audit record',
  `IS_TONE_ENABLED` tinyint(1) DEFAULT '1' COMMENT 'Flag indicating if tone functionality is enabled for the email preferences in the audit record',
  `ALLOW_NOTIFICATION` bit(1) DEFAULT NULL COMMENT 'Flag indicating if notifications are allowed in the email preferences audit record',
  `DEVICE_ID` varchar(255) DEFAULT NULL COMMENT 'Device ID associated with the email preferences in this audit record',
  `GCM_ID` varchar(255) DEFAULT NULL COMMENT 'Google Cloud Messaging ID for notifications in this audit record',
  PRIMARY KEY (`REV`,`ID`)
);

CREATE TABLE `EMAIL_STATS` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each email stats entry',
  `UNUSED_COUNT` int DEFAULT NULL COMMENT 'Count of unused emails for the given stats period',
  `STATS_DATE` date DEFAULT NULL COMMENT 'The date the statistics were recorded for',
  `TYPE` varchar(255) DEFAULT NULL COMMENT 'Type of email statistics (e.g., sent, received)',
  `USED_COUNT` int DEFAULT NULL COMMENT 'Count of used emails for the given stats period',
  `USER_ID` varchar(255) DEFAULT NULL COMMENT 'User ID associated with the email stats entry',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UNIQUE_USER_DATE` (`USER_ID`,`STATS_DATE`,`TYPE`)
);

CREATE TABLE `EMAIL_THREAD` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each email thread',
  `CONVERSATION_ID` varchar(255) DEFAULT NULL COMMENT 'Identifier for the email conversation',
  `STAR_MARKED` bit(1) DEFAULT NULL COMMENT 'Flag indicating if the thread is marked as important',
  `STAR_REASON` text COMMENT 'Reason for marking the email thread as starred',
  `SUBJECT` varchar(255) DEFAULT NULL COMMENT 'Subject of the email thread',
  `THREAD_SUMMARY` text COMMENT 'Summary of the email thread',
  `USER_ID` varchar(255) DEFAULT NULL COMMENT 'User ID associated with the email thread',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the email thread was created',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the email thread was last modified',
  `SHORT_SUMMARY` text COMMENT 'Short summary of the email thread',
  `ACTION_DECRYPT_OBJECT` text COMMENT 'Decrypted object for action related to the email thread',
  `LONG_DECRYPT_OBJECT` text COMMENT 'Decrypted object for long-term details related to the email thread',
  `SHORT_DECRYPT_OBJECT` text COMMENT 'Decrypted object for short-term details related to the email thread',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_conversation_user` (`CONVERSATION_ID`,`USER_ID`),
  KEY `idx_modified_time` (`MODIFIED_TIME`),
  KEY `idx_created_time` (`CREATED_TIME`),
  FULLTEXT KEY `ft_summary` (`THREAD_SUMMARY`),
  FULLTEXT KEY `ft_short_summary` (`SHORT_SUMMARY`),
  FULLTEXT KEY `ft_subject` (`SUBJECT`),
  FULLTEXT KEY `ft_combined` (`SUBJECT`,`THREAD_SUMMARY`,`SHORT_SUMMARY`)
);

CREATE TABLE `EMAIL_USER` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each email user',
  `DELETED` bit(1) DEFAULT NULL COMMENT 'Flag indicating whether the email user is deleted',
  `EMAIL` varchar(50) DEFAULT NULL COMMENT 'Email address of the user',
  `NAME` varchar(255) DEFAULT NULL COMMENT 'Full name of the email user',
  `PHONE_NUMBER` varchar(20) DEFAULT NULL COMMENT 'Phone number associated with the email user',
  `TYPE` varchar(50) DEFAULT NULL COMMENT 'Type of the email user (e.g., personal, business)',
  `USER_ID` varchar(1000) DEFAULT NULL COMMENT 'Unique identifier for the user',
  `BATCH_ID` varchar(20) DEFAULT NULL COMMENT 'Batch identifier associated with the email user',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the email user was created',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the email user was last modified',
  `USER_OID` varchar(1000) DEFAULT NULL COMMENT 'Object identifier for the user in another system',
  `QUEUE_NAME` varchar(50) DEFAULT NULL COMMENT 'Name of the queue associated with the email user',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_email` (`EMAIL`)
);

CREATE TABLE `FAILURE_LOGS` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each failure log entry',
  `ATTACHMENT` bit(1) DEFAULT NULL COMMENT 'Flag indicating whether there is an attachment in the failure log',
  `ATTENDEES` varchar(255) DEFAULT NULL COMMENT 'List of attendees related to the failure log entry',
  `CONVERSATION_ID` varchar(255) DEFAULT NULL COMMENT 'Identifier for the conversation associated with the failure log',
  `EMAIL` varchar(255) DEFAULT NULL COMMENT 'Email address related to the failure log entry',
  `EMAIL_SUBJECT` text COMMENT 'Subject of the email associated with the failure log',
  `ERROR_DATE` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the error occurred',
  `EXCEPTION_MESSAGE` text COMMENT 'Detailed message describing the exception',
  `EXCEPTION_TRACE` text COMMENT 'Trace of the exception for debugging purposes',
  `INTENT` text COMMENT 'Intent associated with the email or action related to the failure',
  `INTERNET_MESSAGE_ID` varchar(255) DEFAULT NULL COMMENT 'Unique identifier for the message in the internet protocol',
  `MESSAGE_ID` varchar(255) DEFAULT NULL COMMENT 'Message identifier for the failure log entry',
  `TYPE` varchar(255) DEFAULT NULL COMMENT 'Type of failure (e.g., SMTP error, processing error)',
  `CUSTOM_EXCEPTION_MESSAGE` text COMMENT 'Custom message describing the exception in detail',
  `USER_MAIL_ATTACHMENT` varchar(255) DEFAULT NULL COMMENT 'Name or path of the user mail attachment related to the failure',
  `STATUS` varchar(50) DEFAULT NULL COMMENT 'Current status of the failure log entry (e.g., pending, resolved)',
  PRIMARY KEY (`ID`),
  KEY `idx_type` (`TYPE`),
  KEY `idx_error_date` (`ERROR_DATE`),
  KEY `idx_email` (`EMAIL`)
);

CREATE TABLE `MAIL_SUMMARY` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each mail summary',
  `CATEGORY` varchar(255) DEFAULT NULL COMMENT 'Category of the email (e.g., work, personal)',
  `CONVERSATION_ID` varchar(255) DEFAULT NULL COMMENT 'Identifier for the conversation associated with the email',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the mail summary was created',
  `FROM_USER` varchar(255) DEFAULT NULL COMMENT 'Email address of the sender',
  `MESSAGE_ID` varchar(255) DEFAULT NULL COMMENT 'Unique identifier for the message',
  `MESSAGE_SUMMARY` text COMMENT 'Summary of the email message',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the mail summary was last modified',
  `OBJECTIVE` text COMMENT 'Objective or purpose of the email',
  `PRIORITY` varchar(255) DEFAULT NULL COMMENT 'Priority level of the email (e.g., high, medium, low)',
  `STAR_MARK_REASON` varchar(255) DEFAULT NULL COMMENT 'Reason for marking the email as important or starred',
  `STAR_MARKED` bit(1) DEFAULT b'0' COMMENT 'Flag indicating if the email is starred',
  `TAG` text COMMENT 'Tags or labels associated with the email',
  `TO_USER` text COMMENT 'Recipient(s) of the email',
  `USER_ID` varchar(255) DEFAULT NULL COMMENT 'User identifier associated with the mail summary',
  `CC_USER` text COMMENT 'List of users who are ccd on the email',
  `SUBJECT` varchar(255) DEFAULT NULL COMMENT 'Subject line of the email',
  `ACTION_OWNER` text COMMENT 'User responsible for taking action on the email',
  `STATUS` varchar(255) DEFAULT NULL COMMENT 'Current status of the email (e.g., pending, resolved)',
  `FLAG_STATUS` varchar(25) DEFAULT 'notFlagged' COMMENT 'Status of the flag (e.g., flagged or not)',
  `TYPE` varchar(50) DEFAULT NULL COMMENT 'Type of email (e.g., regular, automated)',
  `MAIL_RECEIVED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the email was received',
  `CATEGORY_REASON` varchar(7000) DEFAULT NULL COMMENT 'Reason for the category classification of the email',
  `PRIORITY_REASON` varchar(700) DEFAULT NULL COMMENT 'Reason for the priority classification of the email',
  `INTERNET_MESSAGE_ID` varchar(255) DEFAULT NULL COMMENT 'Unique identifier for the message in the internet protocol',
  `ACTION_TAKEN` bit(1) NOT NULL COMMENT 'Flag indicating if an action has been taken on the email',
  `DELETED` bit(1) DEFAULT NULL COMMENT 'Flag indicating if the email has been deleted',
  `DECRYPT_SUMMARY` text COMMENT 'Summary of the decrypted content of the email',
  `FOLDER_NAME` varchar(50) DEFAULT NULL COMMENT 'Folder where the email is stored (e.g., inbox, sent)',
  `EMAIL_TONE` varchar(30) DEFAULT NULL COMMENT 'Tone of the email (e.g., formal, casual)',
  `EMAIL_TONE_REASON` varchar(1024) DEFAULT NULL COMMENT 'Reason for the tone classification of the email',
  `ACTION_CLEAR_REASON` varchar(255) DEFAULT NULL COMMENT 'Reason for clearing the action on the email',
  `NOTIFICATION_STATUS` enum('SENT','NOT_SENT','NOT_ALLOWED','FAILED') DEFAULT NULL COMMENT 'Status of the email notification (sent, failed, etc.)',
  `MEETING_PREVIEW` text,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_user_internet_message_id` (`USER_ID`,`INTERNET_MESSAGE_ID`),
  KEY `idx_priority` (`PRIORITY`),
  KEY `idx_mail_received_time` (`MAIL_RECEIVED_TIME`),
  KEY `idx_mail_summary_type` (`TYPE`),
  KEY `idx_user_mail_time` (`USER_ID`,`MAIL_RECEIVED_TIME`),
  KEY `idx_from_user` (`FROM_USER`),
  KEY `idx_user_priority_mail_time` (`USER_ID`,`PRIORITY`,`MAIL_RECEIVED_TIME`),
  FULLTEXT KEY `ft_message_summary` (`MESSAGE_SUMMARY`)
);

CREATE TABLE `MEETING_SUMMARY` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each meeting summary',
  `MEETING_ID` varchar(20) DEFAULT NULL COMMENT 'Unique identifier for the meeting',
  `CREATION_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the meeting summary was created',
  `LONG_SUMMARY` longtext COMMENT 'Detailed summary of the meeting content',
  `SHORT_SUMMARY` longtext COMMENT 'Brief summary of the meeting content',
  `USER_ID` varchar(100) DEFAULT NULL COMMENT 'User identifier associated with the meeting summary',
  `INTERNET_MESSAGE_ID` varchar(500) DEFAULT NULL COMMENT 'Internet message identifier for the meeting email',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the meeting summary was last modified',
  `PROCESSING_ERROR` longtext COMMENT 'Details of any errors encountered during the processing of the meeting summary',
  `STATUS` varchar(255) DEFAULT NULL COMMENT 'Current status of the meeting summary (e.g., completed, pending)',
  `SUBJECT` varchar(500) DEFAULT NULL COMMENT 'Subject line or title of the meeting',
  `ACTION_ITEMS` text COMMENT 'Action items or tasks to be followed up from the meeting',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `meeting_summary_key` (`MEETING_ID`)
);

CREATE TABLE `ORGANISATION` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each organisation record',
  `CHECKIN` time(6) DEFAULT NULL COMMENT 'Time when the organisation checks in (e.g., work hours start)',
  `CHECKOUT` time(6) DEFAULT NULL COMMENT 'Time when the organisation checks out (e.g., work hours end)',
  `BLACKLISTED` varchar(255) DEFAULT NULL COMMENT 'Indicates if the organisation is blacklisted',
  `ORGANISATION_NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the organisation',
  `TIME_ZONE` varchar(255) DEFAULT NULL COMMENT 'Time zone of the organisation',
  `MEETING_TYPE` varchar(50) DEFAULT NULL COMMENT 'Type of meetings associated with the organisation',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the organisation record was created',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the organisation record was last modified',
  `ACTIVE` tinyint(1) DEFAULT NULL COMMENT 'Indicates whether the organisation is active (1 = active, 0 = inactive)',
  PRIMARY KEY (`ID`)
);

CREATE TABLE `POLL_TIME_INFO` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each poll time record',
  `EMAIL` varchar(255) DEFAULT NULL COMMENT 'Email address associated with the poll',
  `END_TIME` datetime(6) DEFAULT NULL COMMENT 'End time of the poll process',
  `START_TIME` datetime(6) DEFAULT NULL COMMENT 'Start time of the poll process',
  `STATUS` enum('PROCESSING','SUCCESS','FAILED') DEFAULT NULL COMMENT 'Current status of the poll process (PROCESSING, SUCCESS, FAILED)',
  PRIMARY KEY (`ID`),
  KEY `idx_email` (`EMAIL`),
  KEY `idx_end_time` (`END_TIME`),
  KEY `idx_start_time` (`START_TIME`)
);

CREATE TABLE `REVINFO` (
  `REV` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key identifier for each revision record',
  `REVTSTMP` bigint DEFAULT NULL COMMENT 'Timestamp of the revision',
  PRIMARY KEY (`REV`)
);

CREATE TABLE `USER_ACTIONS` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Unique identifier for each user action record',
  `ACTION_OWNER` varchar(255) DEFAULT NULL COMMENT 'Owner of the action being recorded',
  `ACTION_OWNER_REASON` varchar(1000) DEFAULT NULL COMMENT 'Reason for the action owner assignment',
  `ACTION_TAKEN` bit(1) NOT NULL COMMENT 'Flag indicating whether the action was taken (1 = yes, 0 = no)',
  `ACTION_TAKEN_REASON` varchar(1000) DEFAULT NULL COMMENT 'Reason for the action being taken',
  `CONVERSATION_ID` varchar(255) DEFAULT NULL COMMENT 'Unique identifier for the email conversation',
  `MAIL_RECEIVED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the email was received',
  `MAIL_SUMMARY_ID` int NOT NULL COMMENT 'Reference to the associated mail summary record',
  `CREATED_AT` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the action was created',
  `INTERNET_MESSAGE_ID` varchar(500) DEFAULT NULL COMMENT 'Unique identifier for the email message',
  `DELETED` bit(1) DEFAULT NULL COMMENT 'Flag indicating whether the record has been deleted (1 = deleted, NULL = not deleted)',
  `SUBJECT` varchar(500) DEFAULT NULL COMMENT 'Subject of the email associated with the action',
  PRIMARY KEY (`ID`),
  KEY `FKmqfoq29ej5alybrlk91gl8ug3` (`MAIL_SUMMARY_ID`),
  CONSTRAINT `FKmqfoq29ej5alybrlk91gl8ug3` FOREIGN KEY (`MAIL_SUMMARY_ID`) REFERENCES `MAIL_SUMMARY` (`ID`)
);

CREATE TABLE `USER_FOLDERS` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Unique identifier for each folder record',
  `ACTIVE` bit(1) NOT NULL COMMENT 'Indicates whether the folder is active (1 = active, 0 = inactive)',
  `DISPLAY_NAME` varchar(255) DEFAULT NULL COMMENT 'Display name of the folder for the user',
  `EMAIL` varchar(255) DEFAULT NULL COMMENT 'Email address associated with the folder',
  `FOLDER_ID` varchar(255) DEFAULT NULL COMMENT 'Unique identifier for the folder',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_folder` (`DISPLAY_NAME`,`EMAIL`)
);

CREATE TABLE `USER_MAIL_ATTACHMENT` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Unique identifier for each attachment record',
  `ATTACHMENT_ID` varchar(2000) DEFAULT NULL COMMENT 'Identifier for the attachment file',
  `CONVERSATION_ID` varchar(2000) DEFAULT NULL COMMENT 'ID of the conversation the attachment belongs to',
  `LONG_SUMMARY` longtext COMMENT 'Detailed summary of the attachment content',
  `MESSAGE_ID` varchar(2000) DEFAULT NULL COMMENT 'ID of the email message containing the attachment',
  `NAME` varchar(200) DEFAULT NULL COMMENT 'Name of the attachment file',
  `RAG_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT 'ID for the related document (if applicable)',
  `SHORT_SUMMARY` longtext COMMENT 'Brief summary of the attachment content',
  `TYPE` varchar(100) DEFAULT NULL COMMENT 'Type or category of the attachment',
  `USER_ID` varchar(50) DEFAULT NULL COMMENT 'User ID associated with the attachment',
  `DOC_PATH` varchar(2000) DEFAULT NULL COMMENT 'Path where the document is stored',
  `INTERNET_MESSAGE_ID` varchar(500) DEFAULT NULL COMMENT 'Internet Message ID for the email',
  `PROCESSING_ERROR` longtext COMMENT 'Error message if processing the attachment failed',
  `PROCESSING_STATUS` varchar(255) DEFAULT NULL COMMENT 'Current processing status of the attachment',
  `UNIQUE_NAME` varchar(700) DEFAULT NULL COMMENT 'Unique name for the attachment',
  `CREATION_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the attachment was created',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the attachment was last modified',
  `RETRY_COUNT` int DEFAULT NULL COMMENT 'Number of retry attempts for processing the attachment',
  `SUBJECT` varchar(500) DEFAULT NULL COMMENT 'Subject of the email the attachment belongs to',
  `creationtime` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the record was created',
  `modificationtime` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the record was last modified',
  `creatorid_fk` int DEFAULT NULL COMMENT 'ID of the user who created the record',
  `lastModifierid_FK` int DEFAULT NULL COMMENT 'ID of the user who last modified the record',
  `retryCount` int DEFAULT NULL COMMENT 'Number of retry attempts for processing the attachment (duplicate column)',
  `BATCH_ID` varchar(50) DEFAULT NULL COMMENT 'Batch ID for processing the attachment',
  `ERROR_DISPLAY` varchar(1000) DEFAULT NULL COMMENT 'Display name of error',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_name` (`UNIQUE_NAME`),
  KEY `idx_internet_message_id` (`INTERNET_MESSAGE_ID`)
);

CREATE TABLE `USER_TEMPLATE` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Unique identifier for each template record',
  `DATA` text COMMENT 'Content of the template',
  `EMAIL` varchar(255) DEFAULT NULL COMMENT 'Email address associated with the template',
  PRIMARY KEY (`ID`)
);
