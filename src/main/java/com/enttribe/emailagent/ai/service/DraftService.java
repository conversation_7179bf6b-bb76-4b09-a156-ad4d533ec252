package com.enttribe.emailagent.ai.service;

import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.emailagent.ai.dto.draft.DraftResponse;
import com.enttribe.emailagent.ai.dto.draft.FreshDraftAIResponse;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.service.PromptAuditService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.EmailConstants;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.*;
import static com.enttribe.emailagent.utils.ExceptionUtils.getStackTraceAsString;


import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Draft service.
 * <AUTHOR> Sonsale
 */
@Service("ds")
@RequiredArgsConstructor
@Slf4j
public class DraftService {

    private final ChatModel chatModel;
    private final AIService aiService;
    private final PromptAuditService promptAuditService;
    private final AiChatModel aiChatModel;

    @Autowired
    private IEmailPreferencesDao emailPreferencesDao;

    @Value("${spring.ai.mistralai.chat.options.model}")
    private String engine;

    @Autowired
    private UserContextHolder userContextHolder;

    private static final Logger auditLog = EmailAgentLogger.getLogger(DraftService.class);

    @Value("${generatedBy}")
    private String generatedBy;

    @Value("${forwardDraftPromptId}")
    private String forwardDraft;

    @Value("${freshDraftPromptId}")
    private String freshDraft;

    @Value("${generateDraftFromContentPromptId}")
    private String generateDraftFromContent;

    @Value("${generateDraftPromptId}")
    private String generateDraft;

    @Value("${generateFreshMeetingPromptId}")
    private String generateFreshMeeting;

    @Value("${improveDraftPromptId}")
    private String improveDraft;

    @Value("${improveFreshDraftPromptId}")
    private String improveFreshDraft;

    @Value("${improveSelectionPromptId}")
    private String improveSelection;

    @Value("${regenerateDraftPromptId}")
    private String regenerateDraft;

    /**
     * Fetches the display name for the sender's user ID if it exists, otherwise returns the original sender.
     *
     * @param sender the email address of the sender
     * @return the display name if available, otherwise the original sender
     */
    public String getDisplayNameOfUser(String sender) {
        String displayName = emailPreferencesDao.getDisplayNameByUserId(sender);
        return displayName != null ? displayName : sender;
    }

    /**
     * Generates a fresh draft email based on the provided parameters.
     *
     * @param intent The purpose or intention of the email.
     * @param sender The email address of the sender.
     * @param length The desired length of the email (e.g., "short", "medium", "long").
     * @param tone   The desired tone of the email (e.g., "formal", "casual", "friendly").
     * @return A FreshDraftAIResponse object containing the generated email draft, or null if an error occurs.
     */
    public FreshDraftAIResponse getFreshDraft(String intent, String sender, String length, String tone) {
        log.debug("Inside @method getFreshDraft with intent: {}, sender: {}, length: {}, tone: {}", intent, sender, length, tone);
        UserInfo userInfo = userContextHolder.getCurrentUser();

        String displayName = getDisplayNameOfUser(sender);
        displayName = displayName == null ? sender : displayName;

        try {
            Map<String, Object> map = Map.of(EmailConstants.INTENT, intent,
                    EmailConstants.LENGTH, getLengthGuideLine(length), "tone", getToneGuideLine(tone), EmailConstants.USER_NAME, displayName);

            FreshDraftAIResponse freshDraftAIResponse = aiChatModel.chatCompletion(freshDraft,
                    map,
                    FreshDraftAIResponse.class);
           log.debug("@class DraftService @method getFreshDraft freshDraftAIResponse : {} ",freshDraftAIResponse);
            if (freshDraftAIResponse == null) {
                log.error("Failed to parse AI response into FreshDraftAIResponse");
                return null;
            }

            String emailContent = freshDraftAIResponse.getEmailContent() == null ? null : freshDraftAIResponse.getEmailContent() + generatedBy;
            freshDraftAIResponse.setEmailContent(emailContent);
            List<String> emails = CommonUtils.extractEmails(intent);
            freshDraftAIResponse.setParticipants(emails);
            log.debug("Successfully generated fresh draft");
            return freshDraftAIResponse;
        } catch (Exception e) {
            log.error("Error while generating fresh draft", e);
            auditLog.error("Error while generating fresh draft", e.getMessage(), getStackTraceAsString(e), null, null, FRESH_DRAFT, userInfo.getEmail(), null, null, null, null, intent, null);
            return null;
        }
    }

    /**
     * Improves an existing fresh draft email based on the provided parameters.
     *
     * @param draft  The existing draft email content to be improved.
     * @param intent The purpose or intention of the email.
     * @param sender The email address of the sender.
     * @param length The desired length of the email (e.g., "short", "medium", "long").
     * @param tone   The desired tone of the email (e.g., "formal", "casual", "friendly").
     * @return A FreshDraftAIResponse object containing the improved email draft, or null if an error occurs.
     */
    public FreshDraftAIResponse improveFreshDraft(String draft, String intent, String sender, String length, String tone) {
        log.debug("Inside @method improveFreshDraft");
        String displayName = getDisplayNameOfUser(sender);
        UserInfo userInfo = userContextHolder.getCurrentUser();
        try {
            Map<String, Object> map = Map.of("draft", draft, EmailConstants.INTENT, intent, EmailConstants.LENGTH, getLengthGuideLine(length), "tone", getToneGuideLine(tone),
                    "currentDate", LocalDate.now().toString(), EmailConstants.USER_NAME, displayName);
            FreshDraftAIResponse freshDraftAIResponse = aiChatModel.chatCompletion(improveFreshDraft,
                    map, FreshDraftAIResponse.class);

            log.debug("@clas DraftService @method improveFreshDraft freshDraftAIResponse : {} ", freshDraftAIResponse);

            String emailContent = freshDraftAIResponse.getEmailContent() == null ? null : freshDraftAIResponse.getEmailContent() + generatedBy;
            freshDraftAIResponse.setEmailContent(emailContent);
            List<String> emails = CommonUtils.extractEmails(intent);
            freshDraftAIResponse.setParticipants(emails);
            return freshDraftAIResponse;
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("draft", draft, "intent", intent);
            log.error("Error while improve Fresh draft: {}", e.getMessage(), e);
            auditLog.error("Error while improving fresh draft", e.getMessage(), getStackTraceAsString(e), null, null, IMPROVE_FRESH_DRAFT, userInfo.getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            return null;
        }
    }

    /**
     * Generates a draft email based on the provided parameters and context.
     *
     * @param objective     The main objective or goal of the email.
     * @param summary       A summary of the context or previous communication.
     * @param recipient     The email address of the recipient.
     * @param sender        The email address of the sender.
     * @param length        The desired length of the email (e.g., "short", "medium", "long").
     * @param tone          The desired tone of the email (e.g., "formal", "casual", "friendly").
     * @param userNames     The name of the user generating the draft.
     * @param auditMap      A map containing audit information.
     * @return A DraftResponse object containing the generated email draft, or null if an error occurs.
     */
    public DraftResponse generateDraft(String objective, String summary, String recipient, String sender, String length, String tone, String userNames, Map<String, String> auditMap) {
        log.debug("Inside @method generateDraft with objective: {}, recipient: {}, sender: {}, length: {}, tone: {}", objective, recipient, sender, length, tone);
        String displayName = getDisplayNameOfUser(sender);
        try {
            Map<String, Object> map = Map.of("summary", summary, EmailConstants.OBJECTIVE, objective,
                    "recipient", recipient, EmailConstants.LENGTH, getLengthGuideLine(length),
                    "tone", getToneGuideLine(tone), EmailConstants.USER_NAME, displayName);

            DraftResponse draftResponse = aiChatModel.chatCompletion(generateDraft,
                    map, DraftResponse.class);
            log.debug("@class DraftService @method generateDraft draftResponse : {}", draftResponse);
            if (draftResponse == null) {
                log.error("Failed to get typed response from AI service");
                return null;
            }
            String emailContent = draftResponse.getEmail() == null ? null : draftResponse.getEmail() + generatedBy;
            draftResponse.setEmail(emailContent);
            List<String> emails = CommonUtils.extractEmails(objective);
            draftResponse.setParticipants(emails);
            log.debug("Draft generated successfully");
            return draftResponse;
        } catch (Exception e) {
            log.error("Error while generating draft: {}", e.getMessage(), e);
            auditLog.error("Error while generating draft", e.getMessage(), getStackTraceAsString(e), null, null, GENERATE_DRAFT, userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            return null;
        }
    }

    /**
     * Generate forward draft draft response.
     *
     * @param objective     the objective
     * @param summary       the summary
     * @param recipient     the recipient
     * @param sender        the sender
     * @param length        the length
     * @param tone          the tone
     * @param previousDraft the previous draft
     * @param auditMap      the audit map
     * @return the draft response
     */
    public DraftResponse generateForwardDraft(String objective, String summary, String recipient, String sender, String length, String tone, String previousDraft, Map<String, String> auditMap) {
        log.debug("Inside @method generateForwardDraft with objective: {}, recipient: {}, sender: {}, length: {}, tone: {}", objective, recipient, sender, length, tone);

        String displayName = getDisplayNameOfUser(sender);
        previousDraft = (previousDraft != null) ? previousDraft : "";
        log.debug("Previous draft initialized to: {}", previousDraft);

        try {
            Map<String, Object> map = new HashMap<>();
            map.put("summary", summary);
            map.put(EmailConstants.OBJECTIVE, objective);
            map.put(EmailConstants.LENGTH, getLengthGuideLine(length));
            map.put("tone", getToneGuideLine(tone));
            map.put(EmailConstants.PREVIOUS_DRAFT, previousDraft);
            map.put(EmailConstants.USER_NAME, displayName);
            map.put("recipient", recipient);

            DraftResponse draftResponse = aiChatModel.chatCompletion(forwardDraft,
                    map, DraftResponse.class);
            log.debug("@class DraftService @method generateForwardDraft draftResponse : {} ",draftResponse);
            if (draftResponse == null || draftResponse.getParticipants().isEmpty()) {
                log.error("Received empty response from AI model");
                return null;
            }

            String emailContent = draftResponse.getEmail();
            if (emailContent != null) {

                emailContent += generatedBy;
                draftResponse.setEmail(emailContent);
            }
            List<String> emails = CommonUtils.extractEmails(objective);
            draftResponse.setParticipants(emails);
            log.debug("Draft generated successfully");
            return draftResponse;

        } catch (Exception e) {
            log.error("Error while generating forward draft: {}", e.getMessage(), e);
            auditLog.error("Error while generating forward draft", e.getMessage(), getStackTraceAsString(e), null, null, GENERATE_DRAFT, userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            return null;
        }
    }


    /**
     * Re generate draft draft response.
     *
     * @param objective     the objective
     * @param summary       the summary
     * @param recipient     the recipient
     * @param sender        the sender
     * @param length        the length
     * @param tone          the tone
     * @param previousDraft the previous draft
     * @param auditMap      the audit map
     * @return the draft response
     */
    public DraftResponse reGenerateDraft(String objective, String summary, String recipient, String sender, String length, String tone, String previousDraft, Map<String, String> auditMap) {
        log.debug("Inside @method reGenerateDraft with objective: {}, summary: {}, recipient: {}, sender: {}, length: {}, tone: {}, previousDraft: {}", objective, summary, recipient, sender, length, tone, previousDraft);
        String displayName = getDisplayNameOfUser(sender);

        if (previousDraft != null) {
            int index = previousDraft.indexOf(generatedBy);
            if (index != -1) {
                previousDraft = previousDraft.substring(0, index);
            }
        }
        try {

            Map<String, Object> map = Map.of("summary", summary, EmailConstants.OBJECTIVE, objective,
                    "recipient", recipient, EmailConstants.LENGTH, getLengthGuideLine(length),
                    "tone", getToneGuideLine(tone), EmailConstants.USER_NAME, displayName, EmailConstants.PREVIOUS_DRAFT, previousDraft);

            DraftResponse draftResponse = aiChatModel.chatCompletion(regenerateDraft,
                    map,
                    DraftResponse.class);


            if (draftResponse == null) {
                log.error("Failed to get typed response from AI service");
                return null;
            }

            String emailContent = draftResponse.getEmail() == null ? null : draftResponse.getEmail() + generatedBy;
            draftResponse.setEmail(emailContent);
            List<String> emails = CommonUtils.extractEmails(objective);
            draftResponse.setParticipants(emails);
            log.debug("Draft generated successfully");
            return draftResponse;
        } catch (Exception e) {
            log.error("Error while generating draft: {}", e.getMessage(), e);
            auditLog.error("Error while generating draft", e.getMessage(), getStackTraceAsString(e), null, null, GENERATE_DRAFT, userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            return null;
        }
    }


    /**
     * Generates a draft email based on the provided parameters and context.
     *
     * @param objective The main objective or goal of the email.
     * @param content   A content of the context or previous communication.
     * @param recipient The email address of the recipient.
     * @param sender    The email address of the sender.
     * @param length    The desired length of the email (e.g., "short", "medium", "long").
     * @param tone      The desired tone of the email (e.g., "formal", "casual", "friendly").
     * @param userName  The name of the user generating the draft.
     * @param auditMap  A map containing audit information.
     * @return A DraftResponse object containing the generated email draft, or null if an error occurs.
     */
    public DraftResponse generateDraftFromContent(String objective, String content, String recipient, String sender, String length, String tone, String userName, Map<String, String> auditMap) {
        log.debug("Inside @method generateDraft with objective: {}, recipient: {}, sender: {}, length: {}, tone: {}", objective, recipient, sender, length, tone);
        String displayName = getDisplayNameOfUser(sender);
        try {
            Map<String, Object> map = Map.of("content", content, EmailConstants.OBJECTIVE, objective,
                    "recipient", recipient, EmailConstants.LENGTH, getLengthGuideLine(length),
                    "tone", getToneGuideLine(tone), EmailConstants.USER_NAME, displayName);

            DraftResponse draftResponse = aiChatModel.chatCompletion(generateDraftFromContent,
                    map, DraftResponse.class);

            log.debug("@class DraftService @method generateDraftFromContent draftResponse : {} ",draftResponse);
            String emailContent = draftResponse.getEmail() == null ? null : draftResponse.getEmail() + generatedBy;
            draftResponse.setEmail(emailContent);
            List<String> emails = CommonUtils.extractEmails(objective);
            draftResponse.setParticipants(emails);

            return draftResponse;
        } catch (Exception e) {
            log.error("Error while generating draft: {}", e.getMessage(), e);
            auditLog.error("Error while generating draft from content", e.getMessage(), getStackTraceAsString(e), null, null, "GENERATE_DRAFT_V1", userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            return null;
        }
    }

    /**
     * Improves an existing draft email based on the provided parameters.
     *
     * @param previousDraft The content of the previous draft to be improved.
     * @param intent        The purpose or intention of the email.
     * @param recipient     The email address of the recipient.
     * @param sender        The email address of the sender.
     * @param length        The desired length of the email (e.g., "short", "medium", "long").
     * @param tone          The desired tone of the email (e.g., "formal", "casual", "friendly").
     * @param userName      The name of the user improving the draft.
     * @param auditMap      A map containing audit information.
     * @return A DraftResponse object containing the improved email draft, or null if an error occurs.
     */
    public DraftResponse improveDraft(String previousDraft, String intent, String recipient, String sender, String length, String tone, String userName, Map<String, String> auditMap) {
        log.debug("Inside @method improveDraft");
        String displayName = getDisplayNameOfUser(sender);
        try {
            Map<String, Object> map = Map.of("previousDraft", previousDraft, EmailConstants.INTENT, intent,
                    "recipient", recipient, EmailConstants.LENGTH, length,
                    "tone", getToneGuideLine(tone), EmailConstants.USER_NAME, displayName);

            DraftResponse draftResponse = aiChatModel.chatCompletion(improveDraft,
                    map,
                    DraftResponse.class);
            log.debug("@class DraftService @method improveDraft draftResponse : {} ",draftResponse);
            String emailContent = draftResponse.getEmail() == null ? null : draftResponse.getEmail() + generatedBy;
            draftResponse.setEmail(emailContent);
            List<String> emails = CommonUtils.extractEmails(intent);
            draftResponse.setParticipants(emails);
            return draftResponse;
        } catch (Exception e) {
            auditLog.error("Error while improving draft", e.getMessage(), getStackTraceAsString(e), null, null, IMPROVE_DRAFT, userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            return null;
        }
    }

    /**
     * Improves a selected portion of an existing draft email based on the provided parameters.
     *
     * @param previousDraft The content of the entire previous draft.
     * @param selection     The selected portion of the draft to be improved.
     * @param direction     The direction or aspect of improvement (e.g., "elaborate", "summarize", "formalize").
     * @param length        The desired length of the improved selection (e.g., "short", "medium", "long").
     * @param tone          The desired tone of the improved selection (e.g., "formal", "casual", "friendly").
     * @param auditMap      A map containing audit information.
     * @return A DraftResponse object containing the improved email draft with the selected portion updated, or null if an error occurs.
     */
    public DraftResponse improveSelection(String previousDraft, String selection, String direction, String length, String tone, Map<String, String> auditMap) {
        log.debug("Inside @method improveSelection");

        if (selection.equals(direction)) direction = "Pay attention to length and tone guidelines.";

        try {
             int index = previousDraft.indexOf("Generated by");
            String cleanPreviousDraft;
            if (index != -1) {
                cleanPreviousDraft = previousDraft.substring(0, index);
            } else {
                cleanPreviousDraft = previousDraft;
            }

            Map<String, Object> map = Map.of("previousDraft", cleanPreviousDraft, "selection", selection,
                    "direction", direction, EmailConstants.LENGTH, length, "tone", tone);

            DraftResponse draftResponse = aiChatModel.chatCompletion(improveSelection,
                    map,
                    DraftResponse.class);

            log.debug("@class DraftService @method improveSelection draftResponse : {} ", draftResponse);

            String improvedPart = draftResponse.getEmail() == null ? "" : draftResponse.getEmail() + generatedBy;

            draftResponse.setEmail(improvedPart);
            List<String> emails = CommonUtils.extractEmails(direction);
            draftResponse.setParticipants(emails);
            return draftResponse;
        } catch (Exception e) {
            log.error("Error while generating improve Selection: {}", e.getMessage(), e);
            auditLog.error("Error while improving selection", e.getMessage(), getStackTraceAsString(e), null, null, IMPROVE_SELECTION, userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            return null;
        }
    }

    /**
     * Generates a draft for a meeting based on the provided objective and mail summary.
     *
     * @param objective   The main objective or goal of the meeting.
     * @param mailSummary A summary of the relevant email communication, if available.
     * @param auditMap    A map containing audit information.
     * @return A DraftResponse object containing the generated meeting draft, or null if an error occurs.
     */
    public DraftResponse generateMeetingDraft(String objective, String mailSummary, Map<String, String> auditMap) {
        log.debug("Inside @method generateMeetingDraft");
        try {
            Map<String, Object> map;
            log.debug("Objective is : {}", objective);

            if (mailSummary == null || mailSummary.isEmpty()) {
                map = Map.of(EmailConstants.OBJECTIVE, objective);
                log.debug("Map is : {}", map);

                DraftResponse draftResponse = aiChatModel.chatCompletion(generateFreshMeeting,
                        map, DraftResponse.class);

                String emailContent = draftResponse.getEmail() == null ? null : draftResponse.getEmail() + generatedBy;
                draftResponse.setEmail(emailContent);
                return draftResponse;
            } else {
                map = Map.of(EmailConstants.OBJECTIVE, objective, "mailSummary", mailSummary);
                DraftResponse draftResponse = aiChatModel.chatCompletion(generateFreshMeeting,
                        map, DraftResponse.class);
                String emailContent = draftResponse.getEmail() == null ? null : draftResponse.getEmail() + generatedBy;
                draftResponse.setEmail(emailContent);
                return draftResponse;
            }
        } catch (Exception e) {
            log.error("Error while generating Meeting draft: {}", e.getMessage(), e);
            auditLog.error("Error while generating meeting draft", e.getMessage(), getStackTraceAsString(e), null, null, GENERATE_MEETING_DRAFT, userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            return null;
        }
    }

    private String getLengthGuideLine(String lengthType) {
        return switch (lengthType) {
            case "Short" -> """
                    - **Short (25-50 words):** Focus on delivering the essential points. Avoid unnecessary elaboration, but ensure clarity and completeness.
                    """;
            case "Medium" -> """
                    - **Medium (50-100 words):** Provide additional context or explanation, but stay focused on the subject. Ensure all essential details are covered.
                      """;
            case "Long" -> """
                    - **Long (100-300 words):** Use for more complex messages that require a comprehensive explanation or discussion of multiple points. Include all necessary details while maintaining structure and readability.
                        """;
            default -> "";
        };
    }

    private String getToneGuideLine(String tonality) {
        return switch (tonality) {
            case "Professional" -> """
        <tone-guidelines>
            <tone type="Professional">
                <purpose>Use for formal communication, especially for business updates, requests, or official information sharing.</purpose>
                <salutation>
                    Use only "Hi" or "Hello". Extract the person's name from user intent if mentioned and use it with the salutation.
                </salutation>
                <language-style>Formal, clear, and concise. Avoid contractions. Use complete sentences.</language-style>
                <closing>Include a polite closing such as "Best regards" or "Thank you".</closing>
                <example>
                    "Hi [Recipient], I wanted to follow up on our discussion from last week regarding the budget review. Could you please provide the latest numbers by [DATE] to finalize our report? Thank you for your help in this matter. Best regards, [Sender]"
                </example>
            </tone>
        </tone-guidelines>
        """;
            case "Casual" -> """
        <tone-guidelines>
            <tone type="Casual">
                <purpose>Use for friendly and informal messages between familiar colleagues.</purpose>
                <salutation>
                    Use "Hey". Extract the person's name from user intent if mentioned and use it with the salutation.
                </salutation>
                <language-style>Informal, friendly, and conversational. Use contractions and colloquial expressions.</language-style>
                <closing>Use a casual closing like "See you there!" or omit if appropriate.</closing>
                <example>
                    "Hey [Recipient Name], just a quick reminder about our team’s coffee chat tomorrow at [TIME]. It’ll be fun to catch up with everyone. See you there!"
                </example>
            </tone>
        </tone-guidelines>
        """;
            case "Urgent" -> """
        <tone-guidelines>
            <tone type="Urgent">
                <purpose>Use when immediate action is required. The tone should be direct and emphasize urgency.</purpose>
                <salutation>
                    Use "Hi". Extract the person's name from user intent if mentioned and use it with the salutation.
                </salutation>
                <language-style>Direct and to the point. Use short sentences to convey urgency. Polite but firm.</language-style>
                <closing>A brief "Thanks!" or "Please prioritize this."</closing>
                <example>
                    "Hi [Recipient Name], I need your feedback on the proposal by [DAY] to meet the submission deadline. Please prioritize this. Thanks!"
                </example>
            </tone>
        </tone-guidelines>
        """;
            case "Appreciative" -> """
        <tone-guidelines>
            <tone type="Appreciative">
                <purpose>Use to express gratitude or acknowledge someone’s efforts.</purpose>
                <salutation>
                    Use "Dear". Extract the person's name from user intent if mentioned and use it with the salutation.
                </salutation>
                <language-style>Warm, sincere, and positive. Use appreciative language.</language-style>
                <closing>Use a heartfelt closing like "I appreciate all your hard work!" or "Thank you again."</closing>
                <example>
                    "Dear [Recipient Name], I wanted to thank you for your continued support on the project. Your insights have been invaluable in keeping us on track. I appreciate all your hard work! Best regards, [Sender]"
                </example>
            </tone>
        </tone-guidelines>
        """;
            case "Funny" -> """
        <tone-guidelines>
            <tone type="Humorous">
                <purpose>Use in appropriate contexts where a light-hearted tone is acceptable and won't undermine the message.</purpose>
                <salutation>
                    Use "Hey". Extract the person's name from user intent if mentioned and use it with the salutation.
                </salutation>
                <language-style>Light-hearted, with appropriate humor. Ensure the joke is suitable for a professional environment.</language-style>
                <closing>A friendly sign-off or humorous remark.</closing>
                <example>
                    "Hey [Recipient Name], any chance you can finish that report before the coffee machine runs out? I promise to save you a cup!"
                </example>
            </tone>
        </tone-guidelines>
        """;
            default -> "";
        };
    }



}
