package com.enttribe.emailagent.ai.service;

import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.emailagent.ai.dto.DocumentAnswer.FormatAnswerAIResponse;
import com.enttribe.emailagent.ai.dto.category.CategoryAIResponse;
import com.enttribe.emailagent.ai.dto.mail_summary.MailSummaryAIResponse;
import com.enttribe.emailagent.ai.dto.meeting_summary.MeetingSummaryAiResponse;
import com.enttribe.emailagent.ai.dto.objective.ObjectiveAIResponse;
import com.enttribe.emailagent.ai.dto.summary.Summary;
import com.enttribe.emailagent.ai.dto.thread_summary.ThreadSummaryAIResponse;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.dto.ActionTakenResponse;
import com.enttribe.emailagent.dto.DaySummary;
import com.enttribe.emailagent.dto.EmailProfileResponse;
import com.enttribe.emailagent.dto.QueryTable;
import com.enttribe.emailagent.dto.UserEmailDto;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.service.PromptAuditService;
import com.enttribe.emailagent.utils.EmailConstants;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.google.common.base.Throwables.getStackTraceAsString;

/**
 * The type Ai service.
 * <AUTHOR> Pathak
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AIService {

    private final ChatModel chatModel;
    private final PromptAuditService promptAuditService;
    private final AiChatModel aiChatModel;

    @Value("${spring.ai.mistralai.chat.options.model}")
    private String engine;

    @Value("${actionTakenPromptId}")
    private String actionTaken;

    @Value("${categoryPromptId}")
    private String category;

    @Value("${emailProfilePromptId}")
    private String emailProfile;

    @Value("${formatAnswerPromptId}")
    private String formatAnswer;

    @Value("${mailSummaryPromptId}")
    private String mailSummary;

    @Value("${objectivePromptId}")
    private String objective;

    @Value("classpath:system_prompts/json_correction_prompt.st")
    private Resource jsonCorrectionSystemPrompt;

    @Value("classpath:user_prompts/json_correction_prompt.st")
    private Resource jsonCorrectionUserPrompt;

    @Value("${regenerateObjectivePromptId}")
    private String regenerateObjective;

    @Value("${threadSummaryPromptId}")
    private String threadSummary;

    @Value("${tagPromptId}")
    private String tag;

    @Value("${tableInquiryPromptId}")
    private String tableInquiry;

    @Value("${shortSummaryPromptId}")
    private String shortSummary;

    @Value("${meetingSummaryPromptId}")
    private String meetingSummary;

    @Value("${chatMessage}")
    private String chatMessage;

    private static final Logger auditLog = EmailAgentLogger.getLogger(AIService.class);


    /**
     * Generates a response using the chat model.
     *
     * @param message The input message to generate a response for.
     * @return A Map containing the generated response with the key "generation".
     */
    public Map<String, Object> generate(String message) {
        return Map.of("generation", chatModel.call(message));
    }

    /**
     * Retrieves an email profile response based on the provided email content and user.
     *
     * @param emailContent The content of the email.
     * @param emailUser The user associated with the email.
     * @return An EmailProfileResponse object containing the profile information.
     */
    public EmailProfileResponse getProfileResponse(String emailContent, String emailUser) {
        Map<String, Object> map = Map.of(EmailConstants.EMAIL_CONTENT, emailContent, EmailConstants.EMAIL_USER, emailUser, "currentDate", LocalDate.now().toString());
        EmailProfileResponse emailProfileResponse = aiChatModel.chatCompletion(emailProfile,
                map, EmailProfileResponse.class);
        log.debug("@class AIService @method getProfileResponse emailProfileResponse: {}", emailProfileResponse);
        return emailProfileResponse;
    }


    /**
     * Retrieves a category response based on the provided email content, user, and audit map.
     *
     * @param userQuery The user query for search.
     * @param emailUser The user associated with the email.
     * @param auditMap A map containing audit information.
     * @return A CategoryAIResponse object containing the category information.
     */
    public QueryTable getQueryTableResponse(String userQuery, String emailUser, Map<String, String> auditMap) {
        Map<String, Object> map = Map.of(EmailConstants.USER_QUERY, userQuery,
                EmailConstants.EMAIL_USER, emailUser);
        try {
            QueryTable queryTable = aiChatModel.chatCompletion(tableInquiry, map, QueryTable.class);
            log.debug("@class AIService @method getQueryTableResponse queryTable : {} ", queryTable);

            return queryTable;
        } catch (Exception e) {
            log.error("Error while generating Query Table Response: {}", e.getMessage(), e);
            auditLog.error("Error inside @method getQueryTableResponse", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), auditMap.get("type"), auditMap.get("userId"), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
        }
        return null;
    }


    public static void main(String[] args) {
        BeanOutputConverter<MailSummaryAIResponse> outputConverter = new BeanOutputConverter<>(MailSummaryAIResponse.class);
        String format = outputConverter.getFormat();
        System.out.println(format);
    }


    /**
     * Retrieves a category response based on the provided email content, user, and audit map.
     *
     * @param emailContent The content of the email.
     * @param emailUser The user associated with the email.
     * @param auditMap A map containing audit information.
     * @return A CategoryAIResponse object containing the category information.
     */

    public CategoryAIResponse getCategoryResponse(String emailContent, String emailUser, Map<String, String> auditMap) {
        Map<String, Object> map = Map.of(EmailConstants.EMAIL_CONTENT, emailContent, EmailConstants.EMAIL_USER, emailUser, "currentDate", LocalDate.now().toString());
        CategoryAIResponse categoryAIResponse = aiChatModel.chatCompletion(category, map, CategoryAIResponse.class);
        log.debug("@class AIService @method getCategoryResponse categoryAIResponse : {} ", categoryAIResponse);
        return categoryAIResponse;
    }

    /**
     * Retrieves a tag response based on the provided email content, user, and audit map.
     *
     * @param emailContent The content of the email.
     * @param emailUser The user associated with the email.
     * @param auditMap A map containing audit information.
     * @return A CategoryAIResponse object containing the tag information.
     */
    public CategoryAIResponse getTagResponse(String emailContent, String emailUser, Map<String, String> auditMap) {
              Map<String, Object> map = Map.of(EmailConstants.EMAIL_CONTENT, emailContent,
                EmailConstants.EMAIL_USER, emailUser, "currentDate", LocalDate.now().toString());
        try {
            CategoryAIResponse categoryAIResponse = aiChatModel.chatCompletion(tag,
                    map,
                    CategoryAIResponse.class);
            log.debug("@class AIService @method getTagResponse categoryAIResponse : {} ", categoryAIResponse);
            return categoryAIResponse;
        } catch (Exception e) {
            log.error("Error while generating Tag Response: {}", e.getMessage(), e);
            auditLog.error("Error inside @method getTagResponse", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), auditMap.get("type"), auditMap.get("userId"), auditMap.get("subject"), auditMap.get("conversationId"), null, null,
                    AIUtils.convertToJSON(auditMap, true), null);
        }
        return null;
    }

    /**
     * Retrieves objective responses based on the provided email content, conversation summary, and mailbox user.
     *
     * @param emailContent The content of the email.
     * @param conversationSummary A summary of the conversation.
     * @param mailboxUser The user of the mailbox.
     * @param auditMap A map containing audit information.
     * @return A list of ObjectiveAIResponse objects, or null if an error occurs.
     */
    public List<ObjectiveAIResponse> getObjectiveResponses(String emailContent, String conversationSummary, String mailboxUser, Map<String, String> auditMap) {
//        log.debug("going to get objective response {}", auditMap.toString());
        Map<String, Object> map = Map.of(EmailConstants.EMAIL_CONTENT, emailContent, "conversationSummary", conversationSummary, "mailboxUser", mailboxUser);
        try {

            List<ObjectiveAIResponse> objectiveAIResponsesList = aiChatModel.chatCompletion(objective, map, new ParameterizedTypeReference<List<ObjectiveAIResponse>>() {
            });
            log.debug("@class AIService @method getObjectiveResponses objectiveAIResponsesList :{}",objectiveAIResponsesList);
            return objectiveAIResponsesList;
        } catch (Exception e) {
            log.error("Error while generating ObjectiveAIResponse List: {}", e.getMessage(), e);
            auditLog.error("Error inside @method getObjectiveResponses", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), auditMap.get("type"), auditMap.get("userId"), auditMap.get("subject"), auditMap.get("conversationId"), null, null,
                    AIUtils.convertToJSON(auditMap, true), null);
        }
        return null;
    }


    /**
     * Retrieves a mail summary response based on the provided email content, sender, and category.
     *
     * @param emailContent The content of the email.
     * @param sender The sender of the email.
     * @param category The category of the email.
     * @param auditMap A map containing audit information.
     * @return A MailSummaryAIResponse object, or null if an error occurs.
     */
    public MailSummaryAIResponse getMailSummaryResponse(UserEmailDto emailContent, String sender, String category, Map<String, String> auditMap,String userId) {
        String emailXmlString="";
        emailXmlString = AIUtils.convertToXMLWithComments(emailContent);

        Map<String, Object> map = Map.of(EmailConstants.EMAIL_CONTENT, emailXmlString, "sender", sender,"category", category,"userId",userId);

        try {
            MailSummaryAIResponse mailSummaryAIResponse = aiChatModel.chatCompletion(mailSummary,
                    map,
                    MailSummaryAIResponse.class);

            log.debug("@class AIService @method getMailSummaryResponse mailSummaryAIResponse :{}",mailSummaryAIResponse);
            return mailSummaryAIResponse;

        } catch (Exception e) {
            log.error("Error while generating mail summary : {}", e.getMessage(), e);
            auditLog.error("Error inside @method getMailSummaryResponse", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), auditMap.get("type"), auditMap.get("userId"), auditMap.get("subject"), auditMap.get("conversationId"), null, null,
                    AIUtils.convertToJSON(auditMap, true), null);
        }
        return null;
    }


    /**
     * Check action taken list.
     *
     * @param emailContent the email content
     * @param sender       the sender
     * @param actionReason the action reason
     * @param auditMap     the audit map
     * @return the list
     */
    public List<ActionTakenResponse> checkActionTaken(String emailContent, String sender, String actionReason, Map<String, String> auditMap) {
        log.debug("Inside @method checkActionTaken. @param : actionReason -> {}", actionReason);

        // Initialize the map with existing entries
        Map<String, Object> map = new HashMap<>();
        map.put(EmailConstants.EMAIL_CONTENT, emailContent);
        map.put("sender", sender);
        map.put("reason", actionReason);

        try {

            List<ActionTakenResponse> list = aiChatModel.chatCompletion(actionTaken, map, new ParameterizedTypeReference<List<ActionTakenResponse>>() {
            });

            log.debug("@class AIService @method checkActionTaken list : {} ", list);

            return list;
        } catch (Exception e) {
            log.error("Error inside @method checkActionTaken", e);
            auditLog.error("Error inside @method checkActionTaken", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), auditMap.get("type"), auditMap.get("userId"), auditMap.get("subject"), auditMap.get("conversationId"), null, null,
                    AIUtils.convertToJSON(auditMap, true), null);
        }
        return null;
    }


    /**
     * Retrieves a thread summary response based on the provided email content, recipient, and previous summary.
     *
     * @param emailContent The content of the email.
     * @param recipient The recipient of the email.
     * @param previousSummary The previous summary of the thread.
     * @param auditMap A map containing audit information.
     * @return A ThreadSummaryAIResponse object, or null if an error occurs.
     */
    public ThreadSummaryAIResponse getThreadSummaryResponse(String emailContent, String recipient, String previousSummary, Map<String, String> auditMap) {

        Map<String, Object> map = Map.of(EmailConstants.EMAIL_CONTENT, emailContent, "recipient", recipient, "previousSummary", previousSummary);

        try {

            ThreadSummaryAIResponse threadSummaryAIResponse = aiChatModel.chatCompletion(threadSummary, map, ThreadSummaryAIResponse.class);
            log.debug("@class AIService @method getThreadSummaryResponse threadSummaryAIResponse : {} ", threadSummaryAIResponse);

            return threadSummaryAIResponse;
        } catch (Exception e) {
            log.error("Error while generating Thread Summary AI Response: {}", e.getMessage(), e);
            auditLog.error("Error inside @method getThreadSummaryResponse", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), auditMap.get("type"), auditMap.get("userId"), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
        }
        return null;

    }

    /**
     * Retrieves a short summary response based on the provided email content and previous summary.
     *
     * @param emailContent The content of the email.
     * @param previousSummary The previous summary.
     * @param auditMap A map containing audit information.
     * @return A Summary object, or null if an error occurs.
     */
    public Summary getShortSummaryResponse(String emailContent, String previousSummary, Map<String, String> auditMap) {
        Map<String, Object> map = Map.of(EmailConstants.EMAIL_CONTENT, emailContent, "previousSummary", previousSummary);
        Summary summary = null;
        try {
            summary = aiChatModel.chatCompletion(shortSummary, map, Summary.class);
            log.debug("@class AIService @method getShortSummaryResponse summary : {} ", summary);

        } catch (Exception e) {

            log.error("Error while generating Short Summary Response: {}", e.getMessage(), e);
            auditLog.error("Error inside @method getShortSummaryResponse", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), auditMap.get("type"), auditMap.get("userId"), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
        }
        return summary;
    }



    /**
     * Extracts a JSON string from the input string.
     *
     * @param inputString The input string containing JSON.
     * @return The extracted JSON string.
     */
    private String extractJsonString(String inputString) {
        int firstObjectIndex = inputString.indexOf('{');
        int firstArrayIndex = inputString.indexOf('[');

        int firstIndex;
        if (firstObjectIndex == -1 && firstArrayIndex == -1) {
            firstIndex = -1;
        } else if (firstObjectIndex == -1) {
            firstIndex = firstArrayIndex;
        } else if (firstArrayIndex == -1) {
            firstIndex = firstObjectIndex;
        } else {
            firstIndex = Math.min(firstObjectIndex, firstArrayIndex);
        }

        int lastObjectIndex = inputString.lastIndexOf('}');
        int lastArrayIndex = inputString.lastIndexOf(']');

        int lastIndex = Math.max(lastObjectIndex, lastArrayIndex);

        if (firstIndex != -1 && lastIndex != -1 && firstIndex < lastIndex) {
            inputString = inputString.substring(firstIndex, lastIndex + 1);
        }

        return inputString;
    }

    /**
     * Extracts a JSON object string from the input string.
     *
     * @param inputString The input string containing a JSON object.
     * @return The extracted JSON object string.
     */
    private String extractJsonObjectString(String inputString) {
        int firstIndex = inputString.indexOf('{');
        int lastIndex = inputString.lastIndexOf('}');

        if (firstIndex != -1 && lastIndex != -1 && firstIndex < lastIndex) {
            inputString = inputString.substring(firstIndex, lastIndex + 1);
        }

        return inputString;
    }

    /**
     * Extracts a JSON array string from the input string.
     *
     * @param inputString The input string containing a JSON array.
     * @return The extracted JSON array string.
     */
    private String extractJsonArrayString(String inputString) {
        int firstIndex = inputString.indexOf('[');
        int lastIndex = inputString.lastIndexOf(']');

        if (firstIndex != -1 && lastIndex != -1 && firstIndex < lastIndex) {
            inputString = inputString.substring(firstIndex, lastIndex + 1);
        }

        return inputString;
    }

    /**
     * Gets the type string for response parse failure.
     *
     * @param type The original type string.
     * @return The type string with "_RESPONSE_PARSE_FAILURE" appended.
     */
    public String getType(String type) {
        return type + "_RESPONSE_PARSE_FAILURE";
    }

    public List<ObjectiveAIResponse> regenerateObjective(String emailContent, String conversationSummary, String mailboxUser, Map<String, String> auditMap,String lastObjectives, String internetMessageId) {

        Map<String, Object> map = Map.of(EmailConstants.EMAIL_CONTENT, emailContent, "conversationSummary", conversationSummary, "mailboxUser", mailboxUser,"previousObjectives",lastObjectives);

        try {
            String chatId = mailboxUser + internetMessageId;
            List<ObjectiveAIResponse> ObjectiveAIResponseList = aiChatModel.chatCompletion(regenerateObjective, chatId, map, new ParameterizedTypeReference<List<ObjectiveAIResponse>>() {});
            return ObjectiveAIResponseList;

        } catch (Exception e) {
            log.error("Error while regenerate Objective : {}", e.getMessage(), e);
            auditLog.error("Error inside @method getObjectiveResponses", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), auditMap.get("type"), auditMap.get("userId"), auditMap.get("subject"), auditMap.get("conversationId"), null, null,
                    AIUtils.convertToJSON(auditMap, true), null);
        }
        return null;
    }


    /**
     * Format answer format answer ai response.
     *
     * @param answer the answer
     * @return the format answer ai response
     */
    public FormatAnswerAIResponse formatAnswer(String answer) {
        Map<String, Object> map = Map.of("input_text", answer);
        FormatAnswerAIResponse formatAnswerAIResponse = aiChatModel.chatCompletion(formatAnswer,
                map, FormatAnswerAIResponse.class);
        log.debug("@class AIService @method formatAnswer formatAnswerAIResponse: {}", formatAnswerAIResponse);
        return formatAnswerAIResponse;

    }


    /**
     * Gets meeting summary response.
     *
     * @param content the content
     * @return the meeting summary response
     */
    public MeetingSummaryAiResponse getMeetingSummaryResponse(String content) {

        Map<String, Object> map = Map.of("transcript", content);

        try {
            MeetingSummaryAiResponse meetingSummaryAiResponse = aiChatModel.chatCompletion(meetingSummary, map, MeetingSummaryAiResponse.class);

            log.debug("@class AIService @method getMeetingSummaryResponse meetingSummaryAiResponse : {} ", meetingSummaryAiResponse);

            return meetingSummaryAiResponse;

        } catch (Exception e) {
            log.error("Error while generating Meeting Summary Response: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public String generateChatMessage(List<DaySummary> messages) {
        log.debug("Inside @method generateMessage");
        try {
            List<String> messageString = messages.stream().map(DaySummary::getContent).toList();
            Map<String, Object> map = Map.of("input", messageString);
            return aiChatModel.chatCompletion(chatMessage, map, String.class);
        } catch (Exception e) {
            log.error("Error while generating chat message: {}", e.getMessage(), e);
            return "";
        }
    }
}
