package com.enttribe.emailagent.ai.service;

import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.emailagent.ai.dto.meeting.IntentResponseNew;

import com.enttribe.emailagent.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


import java.time.LocalDate;
import java.util.Map;

/**
 * The type Meeting service.
 * <AUTHOR> <PERSON>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MeetingService {

    private final ChatModel chatModel;
    private final AIService aiService;

    @Value("${intentPromptNewPromptId}")
    private String intentPromptNew;

    private final AiChatModel aiChatModel;

    /**
     * Intent response intent response new.
     *
     * @param currentPrompt   the current prompt
     * @param meetingDuration the meeting duration
     * @param auditMap        the audit map
     * @param timeZone        the time zone
     * @return the intent response new
     */
    public IntentResponseNew intentResponse(String currentPrompt, Integer meetingDuration, Map<String, String> auditMap, String timeZone) {
        log.debug("userPrompt for intentResponse: {}", currentPrompt);
        Map<String, Object> map = Map.of("userMessage", currentPrompt, "meetingDuration", meetingDuration, "currentDate", DateUtils.getUserLocalDate(timeZone), "dayName", LocalDate.now().getDayOfWeek().toString());

        try {
            IntentResponseNew intentResponseNew = aiChatModel.chatCompletion(intentPromptNew, map, IntentResponseNew.class);
            log.debug("@class MeetingService @method intentResponse intentResponseNew : {} ", intentResponseNew);
            return intentResponseNew;
        } catch (Exception e) {
            log.error("Error while generating Intent Response: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

}
