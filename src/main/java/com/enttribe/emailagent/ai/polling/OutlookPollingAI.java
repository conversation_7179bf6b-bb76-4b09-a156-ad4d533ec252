package com.enttribe.emailagent.ai.polling;

import static com.enttribe.emailagent.ai.utils.AIUtils.convertToJSON;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.BLACKLISTED_DOMAIN;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.BLACKLISTED_SENDER;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.BLACKLISTED_SUBJECT;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.EWS_NETWORK_CALL;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.GENERATE_CATEGORY;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.GENERATE_MAIL_SUMMARY;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.GENERATE_OBJECTIVE;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.GENERATE_TAG;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.GENERATE_THREAD_LONG_SUMMARY;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.GENERATE_THREAD_SHORT_SUMMARY;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.GRAPH_NETWORK_CALL;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.POLLING;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.TAG;
import static com.google.common.base.Throwables.getStackTraceAsString;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.dao.MeetingSummaryDao;
import com.enttribe.emailagent.dto.*;
import com.enttribe.emailagent.entity.*;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.UserActionsDao;
import com.enttribe.emailagent.service.ImailSummaryService;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.enttribe.emailagent.ai.dto.category.CategoryAIResponse;
import com.enttribe.emailagent.ai.dto.mail_summary.MailSummaryAIResponse;
import com.enttribe.emailagent.ai.dto.objective.ObjectiveAIResponse;
import com.enttribe.emailagent.ai.dto.summary.Summary;
import com.enttribe.emailagent.ai.dto.thread_summary.ThreadSummaryAIResponse;
import com.enttribe.emailagent.ai.service.AIService;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.repository.ContactBookRepository;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IEmailThreadDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.IOrganisationRepository;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.service.EwsService;
import com.enttribe.emailagent.service.GraphIntegrationService;
import com.enttribe.emailagent.service.PollTimeInfoService;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.EmailConstants;
import com.enttribe.emailagent.utils.KafkaUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.enttribe.emailagent.masking.DataMasking;

/**
 * The type Outlook polling ai.
 * <AUTHOR> Pathak
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OutlookPollingAI {

    private final AIService aiService;
    private final IEmailThreadDao threadDao;
    private final IMailSummaryDao mailSummaryDao;
    private final UserActionsDao userActionsDao;
    private final IEmailPreferencesDao preferencesDao;
    private final IOrganisationRepository orgRepo;
    private final GraphIntegrationService graphIntegrationService;
    private final EwsService ewsService;
    private final UserFoldersRepository userFoldersRepository;
    private final KafkaUtils kafkaUtil;
    private final GmailIntegration gmailIntegration;
    private final MeetingSummaryDao meetingSummary;
    private final IEmailThreadDao emailThreadDao;

    private final ImailSummaryService imailSummaryService;
    @Autowired
    private PollTimeInfoService pollTimeInfoService;
    @Autowired
    private ContactBookRepository contactBookRepository;


    @Value("${pollingMode}")
    private String pollingMode;

    @Value("${threadConcurency}")
    private Integer threadConcurency;

    private static final Logger auditLog = EmailAgentLogger.getLogger(OutlookPollingAI.class);


    /**
     * Poll outlook inbox.
     *
     * @param emailId    the email id
     * @param userId     the user id
     * @param timeFilter the time filter
     * @param source     the source
     */
    public void pollOutlookInbox(String emailId, String userId, String timeFilter, String source) {
        log.debug("Inside @method pollOutlookInbox");
        List<UserFolders> activeMailFolders = userFoldersRepository.getActiveMailFoldersByEmail(emailId);
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            activeMailFolders.forEach(folder -> executor.submit(() -> {
                try {
                    pollByFolderGraphAPI(emailId, userId, timeFilter, folder);
                } catch (Exception e) {
                    Map<String, String> auditMap = Map.of("timeFilter", timeFilter, "folder", folder.getDisplayName());
                    auditLog.error("Error polling for user", e.getMessage(), getStackTraceAsString(e), null, null, POLLING, emailId, null, null, null, null,
                            AIUtils.convertToJSON(auditMap, true), null);
//                    log.error("Error polling folder {} of user : {}", folder.getDisplayName(), emailId);
//                    log.error("Error trace : ", e);

                }
            }));
        }
    }

    private void pollByFolderGraphAPI(String emailId, String userId, String timeFilter, UserFolders folder) {
        log.debug("Inside @method pollByFolderGraphAPI. @param : emailId -> {} userId -> {} timeFilter -> {} folder -> {}",
                emailId, userId, timeFilter, folder);

        UserEmailResponseDto emailsOfUser = fetchEmails(emailId, userId, timeFilter, folder);
        List<List<UserEmailDto>> conversations = getConversations(emailsOfUser);

        if (conversations.isEmpty()) {
            log.info("No conversations found for user: {}", emailId);
            return;
        }

        EmailPreferences preference = preferencesDao.getEmailPreferencesByUserId(userId);
        Organisation orgObject = orgRepo.findByOrgName(CommonUtils.getOrganizationFromEmail(emailId));
        Result result = getResult(emailId, preference, orgObject);

        log.debug("Polling {} conversations for user : {} by thread : {}", conversations.size(), emailId, Thread.currentThread().getName());
        processConversations(conversations, emailId, userId, folder, result);
    }

    private UserEmailResponseDto fetchEmails(String emailId, String userId, String timeFilter, UserFolders folder) {
        log.debug("Fetching emails for emailId: {}, userId: {}, timeFilter: {}, folderId: {}", emailId, userId, timeFilter, folder.getFolderId());
        UserEmailResponseDto emailsOfUser = new UserEmailResponseDto();
        try {
            if (pollingMode.equalsIgnoreCase("EWS")) {
                emailsOfUser = ewsService.getEmailsOfUser(emailId, emailId, timeFilter, folder.getFolderId());
            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                try {
                    log.info("inside gmail polling");
                    emailsOfUser = gmailIntegration.pollGmailFolderWise(userId, timeFilter, folder.getFolderId());
                } catch (Exception e) {
                    Map<String, String> auditMap = Map.of("folderName", folder.getDisplayName());
                    auditLog.error("Error inside @method poll GMAIL mail {}", e.getMessage(), getStackTraceAsString(e), null, null, EWS_NETWORK_CALL, userId, null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
                }
            } else {
                emailsOfUser = graphIntegrationService.getEmailsOfUser(userId, emailId, timeFilter, null, null, folder.getFolderId(), 100, 0);
            }
            log.debug("Fetched {} emails for userId: {}", emailsOfUser.getMessages().size(), userId);
        } catch (Exception e) {
            log.error("Error in fetching emails", e);
        }
        return emailsOfUser;
    }

    private List<List<UserEmailDto>> getConversations(UserEmailResponseDto emailsOfUser) {
        log.debug("Extracting conversations from emails.");
        List<List<UserEmailDto>> conversations = new ArrayList<>();
        if (emailsOfUser != null && emailsOfUser.getMessages() != null) {
            conversations = emailsOfUser.getMessages();
            log.debug("Extracted {} conversations.", conversations.size());
        }
        return conversations;
    }

    private void processConversations(List<List<UserEmailDto>> conversations, String emailId, String userId, UserFolders folder, Result result) {
        for (List<UserEmailDto> emails : conversations) {
            log.debug("Processing conversation with subject: {}", emails.get(0).getSubject());
            if (isBlacklistedSubject(emails.get(0).getSubject(), result)) {
                log.debug("Skipping blacklisted subject: {}", emails.get(0).getSubject());
                continue;
            }

            EmailThread emailThread = findOrCreateEmailThread(emails.get(0), userId);
            processEmails(emails, emailId, userId, folder, result, emailThread);
        }
    }

    private EmailThread findOrCreateEmailThread(UserEmailDto email, String userId) {
        String conversationId = email.getConversationId();
        log.debug("Finding or creating email thread for conversationId: {}", conversationId);
        EmailThread emailThread = threadDao.findByConversationId(conversationId, userId);

        if (emailThread == null) {
            emailThread = new EmailThread();
            emailThread.setCreatedTime(new Date());
            emailThread.setUserId(userId);
            emailThread.setSubject(email.getSubject());
            emailThread.setConversationId(conversationId);
            log.debug("Created new email thread for conversationId: {}", conversationId);
        } else {
            log.debug("Found existing email thread for conversationId: {}", conversationId);
        }
        return emailThread;
    }

    private void processEmails(List<UserEmailDto> emails, String emailId, String userId, UserFolders folder, Result result, EmailThread emailThread) {
        for (UserEmailDto email : emails) {
            log.debug("Processing email with messageId: {}", email.getId());
            if (isBlacklistedEmail(email, result)) {
                log.debug("Skipping email from blacklisted sender: {}", email.getFrom());
                continue;
            }

            handleEmailAttachments(email, emailId, userId);
            MailSummary mailSummary = findOrCreateMailSummary(email, userId);
            handleEmailCategoryAndPriority(email, emailId, folder, result, mailSummary);
            handleEmailSummaryAndThread(email, emailId, emailThread, mailSummary);

            saveMailSummary(mailSummary);
            saveThreadSummary(emailThread, email.getId());
        }
    }

    private void handleEmailAttachments(UserEmailDto email, String emailId, String userId) {
        if (Boolean.TRUE.equals(email.getHasAttachments())) {
            log.debug("Processing attachments for email with messageId: {}", email.getId());
            try {
                if (pollingMode.equalsIgnoreCase("EWS")) {
                    ewsService.summarizeMailAttachment(email.getConversationId(), email.getInternetMessageId(), emailId, email.getId(), userId, email.getSubject());
                } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                    try {
                        gmailIntegration.summarizeMailAttachment(email.getConversationId(), emailId, email.getInternetMessageId());
                    } catch (Exception e) {
                        log.error("Error in summarising attachment GMAIL", e);
                    }
                } else {
                    graphIntegrationService.summarizeMailAttachment(email.getConversationId(), email.getInternetMessageId(), emailId, email.getId(), userId, email.getSubject());
                }
                log.debug("Attachments processed for email with messageId: {}", email.getId());
            } catch (Exception e) {
                log.error("Error in summarising attachment", e);
            }
        }
    }


    private void handleEmailCategoryAndPriority(UserEmailDto email, String emailId, UserFolders folder, Result result, MailSummary mailSummary) {
        log.debug("Handling category and priority for email with messageId: {}", email.getId());
        CategoryAIResponse categoryResponse = getCategoryResponse(email.toString(), emailId, folder);
        String priority = determinePriority(email, categoryResponse, mailSummary);

        mailSummary.setPriority(priority);
        mailSummary.setCategory(determineCategory(email, emailId, categoryResponse, mailSummary, result, folder));
        mailSummary.setCategoryReason(determineCategoryReason(email, emailId, categoryResponse, mailSummary, folder));
        log.debug("Category and priority set for email with messageId: {}", email.getId());
    }

    private MailSummary findOrCreateMailSummary(UserEmailDto email, String userId) {
        log.debug("Finding or creating mail summary for email with internetMessageId: {}", email.getInternetMessageId());
        MailSummary mailSummary = mailSummaryDao.findByInternetMessageId(email.getInternetMessageId(), userId);
        if (mailSummary == null) {
            mailSummary = new MailSummary();
            mailSummary.setCreatedTime(new Date());
            mailSummary.setSubject(email.getSubject());
            log.debug("Created new mail summary for email with internetMessageId: {}", email.getInternetMessageId());
        } else {
            log.debug("Found existing mail summary for email with internetMessageId: {}", email.getInternetMessageId());
        }
        return mailSummary;
    }

    private boolean isBlacklistedEmail(UserEmailDto email, Result result) {
        String fromUser = email.getFrom();
        log.debug("Checking if email from sender {} is blacklisted.", fromUser);
        return result.blackListedDomain().contains(CommonUtils.getDomainFromEmail(fromUser)) ||
                result.blackListedSender().contains(fromUser.toLowerCase());
    }

    private boolean isBlacklistedSubject(String subject, Result result) {
        log.debug("Checking if subject is blacklisted: {}", subject);
        return result.blackListedSubject().stream().anyMatch(blacklist -> subject.contains(blacklist));
    }


    private CategoryAIResponse getCategoryResponse(String emailContent, String emailId, UserFolders folder) {
        log.debug("Getting category response for emailId: {}", emailId);
        CategoryAIResponse categoryResponse = null;
        try {
            categoryResponse = aiService.getCategoryResponse(emailContent, emailId, null);
            if (!folder.getDisplayName().equals("Sent Items")) {
                aiService.getObjectiveResponses(emailContent, "", emailId, null);
            }
            log.debug("Category response obtained for emailId: {}", emailId);
        } catch (Exception e) {
            log.error("Error while getting category response", e);
        }
        return categoryResponse != null ? categoryResponse : getDummyCategoryAIResponse();
    }

    private String determinePriority(UserEmailDto email, CategoryAIResponse categoryResponse, MailSummary mailSummary) {
        String priority = categoryResponse.getPriority();
        if (email.getImportance().equals("high")) {
            priority = "High";
            mailSummary.setPriorityReason("Mail is of high importance.");
            log.debug("Priority set to High for email with messageId: {}", email.getId());
        } else {
            mailSummary.setPriorityReason(categoryResponse.getPriorityReason());
        }
        return priority;
    }

    private String determineCategory(UserEmailDto email, String emailId, CategoryAIResponse categoryResponse, MailSummary mailSummary, Result result, UserFolders folder) {
        String type = email.getType();
        log.debug("Determining category for email with messageId: {}", email.getId());
        if (type.equalsIgnoreCase(EmailConstants.MEETING)) {
            log.debug("Email is a meeting invitation.");
            return EmailConstants.EVENT;
        } else if (mailSummary.getStarMarked() != null && mailSummary.getStarMarked()) {
            log.debug("Email is star marked.");
            return EmailConstants.ATTENTION;
        } else if (mailSummary.getActionOwner() != null && mailSummary.getActionOwner().contains(emailId) && categoryResponse.getCategory().equals(EmailConstants.ATTENTION)) {
            log.debug("Recipient is an action owner in the mail.");
            return categoryResponse.getCategory();
        } else if (mailSummary.getActionOwner() == null || (!mailSummary.getActionOwner().contains(emailId) && categoryResponse.getCategory().equals(EmailConstants.ATTENTION))) {
            log.debug("Recipient is not an action owner in the mail.");
            return "FYI";
        } else {
            log.debug("Defaulting category for email with messageId: {}", email.getId());
            return categoryResponse.getCategory();
        }
    }


    private String determineCategoryReason(UserEmailDto email, String emailId, CategoryAIResponse categoryResponse, MailSummary mailSummary, UserFolders folder) {
        String type = email.getType();
        if (type.equalsIgnoreCase(EmailConstants.MEETING)) {
            return "Mail is a meeting invitation.";
        } else if (mailSummary.getStarMarked() != null && mailSummary.getStarMarked()) {
            return "Mail is star marked.";
        } else if (mailSummary.getActionOwner() != null && mailSummary.getActionOwner().contains(emailId) && categoryResponse.getCategory().equals(EmailConstants.ATTENTION)) {
            return "Recipient is an action owner in the mail.";
        } else if (mailSummary.getActionOwner() == null || (!mailSummary.getActionOwner().contains(emailId) && categoryResponse.getCategory().equals(EmailConstants.ATTENTION))) {
            return "Recipient is not an action owner in the mail.";
        } else {
            return categoryResponse.getCategoryReason();
        }
    }


    private void handleEmailSummaryAndThread(UserEmailDto email, String emailId, EmailThread emailThread, MailSummary mailSummary) {
        try {
            log.debug("Handling summary for email with messageId: {}", email.getId());
            MailSummaryAIResponse mailSummaryResponse = aiService.getMailSummaryResponse(email, email.getFrom(), email.getSubject(), new HashMap<>(), emailId);
            refactorMailSummary(email, mailSummaryResponse);

            String threadSummary = emailThread.getThreadSummary() != null ? emailThread.getThreadSummary() : "No previous thread summary";
            Summary shortSummaryResponse = aiService.getShortSummaryResponse(email.toString(), threadSummary, null);
            ThreadSummaryAIResponse threadSummaryResponse = aiService.getThreadSummaryResponse(email.toString(), emailId, threadSummary, null);

            emailThread.setShortSummary(convertToJSON(shortSummaryResponse, true));
            emailThread.setThreadSummary(convertToJSON(threadSummaryResponse, true));

            mailSummary.setMessageSummary(convertToJSON(mailSummaryResponse, true));
            log.debug("Summary handled for email with messageId: {}", email.getId());
        } catch (Exception e) {
            log.error("Error while handling summary for email with messageId: {}", email.getId(), e);
        }
    }

    private void saveMailSummary(MailSummary mailSummary) {
        log.debug("Saving mail summary for messageId: {}", mailSummary.getMessageId());
        mailSummary.setModifiedTime(new Date());
        mailSummaryDao.save(mailSummary);
        log.debug("Mail summary saved for messageId: {}", mailSummary.getMessageId());
    }

    private void saveThreadSummary(EmailThread emailThread, String messageId) {
        log.debug("Saving thread summary for messageId: {}", messageId);
        emailThread.setModifiedTime(new Date());
        threadDao.save(emailThread);
        log.debug("Thread summary saved for messageId: {}", messageId);
    }


    private void refactorMailSummary(UserEmailDto email, MailSummaryAIResponse mailSummary) {
        if (mailSummary == null) return;
        mailSummary.setSubject(email.getSubject());
        com.enttribe.emailagent.ai.dto.mail_summary.Summary summaryObject = mailSummary.getSummaryObject();
        if (summaryObject == null) return;
        summaryObject.setMessageId(email.getId());
        summaryObject.setInternetMessageId(email.getInternetMessageId());
        summaryObject.setCreatedTime(email.getCreatedTime().getTime());
    }


    private Result getResult(String userId, EmailPreferences preference, Organisation orgObject) {

        List<String> senderCompanyPreference = new ArrayList<>();
        List<String> emailSenderPreferences = new ArrayList<>();
        List<String> conversationPreference = new ArrayList<>();
        List<String> blacklistedDomains = new ArrayList<>();
        List<String> blacklistedSubject = new ArrayList<>();
        List<String> blackListedSender = new ArrayList<>();
        if (preference == null && orgObject != null) {
            preference = new EmailPreferences();
            preference.setCheckout(orgObject.getCheckout());
            preference.setCheckin(orgObject.getCheckin());
            preference.setTimeZone(orgObject.getTimeZone());
            preference.setUserId(userId);
            preference.setCreatedTime(new Date());
            preference.setModifiedTime(new Date());

            preferencesDao.save(preference);

        } else {
            if (preference != null && preference.getSenderCompany() != null) {
                List<String> senderCompanyList = Arrays.asList(preference.getSenderCompany().split(","));
                senderCompanyPreference = senderCompanyList.stream().map(String::toLowerCase).collect(Collectors.toList());

            }
            if (preference.getEmailSender() != null) {
                List<String> emailSenderList = Arrays.asList(preference.getEmailSender().split(","));
                emailSenderPreferences = emailSenderList.stream().map(String::toLowerCase).collect(Collectors.toList());
            }
            if (preference.getConversationId() != null) {
                conversationPreference = Arrays.asList(preference.getConversationId().split(","));
            }
            if (preference.getBlackListedDomain() != null) {
                List<String> domainList = Arrays.asList(preference.getBlackListedDomain().split(","));
                blacklistedDomains = domainList.stream().map(String::toLowerCase).collect(Collectors.toList());
            }
            if (preference.getBlackListedSubject() != null) {
                List<String> subjectList = Arrays.asList(preference.getBlackListedSubject().split(","));
                blacklistedSubject = subjectList.stream().map(String::toLowerCase).collect(Collectors.toList());
            }
            if (preference.getBlackListedSender() != null) {
                List<String> senderList = Arrays.asList(preference.getBlackListedSender().split(","));
                blackListedSender = senderList.stream().map(String::toLowerCase).collect(Collectors.toList());
            }
            if (preference.getCheckin() == null) {
                preference.setCheckin(orgObject.getCheckin());
            }
            if (preference.getCheckout() == null) {
                preference.setCheckout(orgObject.getCheckout());
            }
            if (preference.getTimeZone() == null) {
                preference.setTimeZone(orgObject.getTimeZone());
            }
            if (preference.getBlackListedDomain() == null) {
                preference.setBlackListedDomain(orgObject.getBlacklisted());
            }
            if (preference.getBlackListedSubject() == null) {
                preference.setBlackListedSubject(orgObject.getBlacklisted());
            }
            preferencesDao.save(preference);
        }
        return new Result(senderCompanyPreference, emailSenderPreferences, conversationPreference, blacklistedDomains, blacklistedSubject, blackListedSender);
    }

    private record Result(List<String> senderCompanyPreference, List<String> emailSenderPreferences,
                          List<String> conversationPreference, List<String> blackListedDomain,
                          List<String> blackListedSubject, List<String> blackListedSender) {
    }

    private CategoryAIResponse getDummyCategoryAIResponse() {
        CategoryAIResponse categoryAIResponse = new CategoryAIResponse();
        categoryAIResponse.setCategory("FYI");
        categoryAIResponse.setPriority("Medium");
        categoryAIResponse.setEmailTone("Neutral");
        categoryAIResponse.setTags(List.of());
        return categoryAIResponse;
    }

    private void tagEmails(String email, String messageId, List<String> tags, String pollingMode) {
        log.debug("adding tags");
        if (tags == null) tags = List.of();
        for (String tag : tags) {
            try {
                if (pollingMode.equalsIgnoreCase("EWS")) {
                    ewsService.addCustomTags(email, messageId, tag);
                } else {
                    graphIntegrationService.tagEmail(email, messageId, tag, true);
                }

            } catch (Exception e) {
                auditLog.error("Error while adding tags", e.getMessage(), getStackTraceAsString(e), messageId, null, TAG, email, null, null, null, null, tag, null);
            }
        }
    }


    /**
     * Poll outlook inbox kafka.
     *
     * @param emailId    the email id
     * @param userId     the user id
     * @param timeFilter the time filter
     * @param source     the source
     * @param batchId    the batch id
     * @param queueName  the queue name
     */
    public void pollOutlookInboxKafka(String emailId, String userId, String timeFilter, String source, String batchId, String queueName) {
        log.debug("Inside @method pollOutlookInboxKafka");
        List<UserFolders> activeMailFolders = userFoldersRepository.getActiveMailFoldersByEmail(emailId);
        Semaphore semaphore = new Semaphore(3);

        AtomicInteger threadCounter = new AtomicInteger(1);  // To track thread numbers
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            activeMailFolders.forEach(folder ->
                    executor.submit(() -> {
                        int threadNumber = threadCounter.getAndIncrement(); // Assign a thread number
                        try {
                            semaphore.acquire();  // Acquire a permit, limiting to 10 threads
                            log.info("Starting poll folder task for user: {} on thread: {}", userId, threadNumber);
                            pushMailToKafka(emailId, userId, timeFilter, folder, batchId, queueName);
                            log.info("Finished poll folder task for user: {} on thread: {}", userId, threadNumber);
                        } catch (InterruptedException e) {
                            Map<String, String> auditMap = Map.of("timeFilter", timeFilter, "folder", folder.getDisplayName());
                            auditLog.error("Error polling for user", e.getMessage(), getStackTraceAsString(e), null, null, "POLLING_FOLDER", emailId, null, null, null, null,
                                    AIUtils.convertToJSON(auditMap, true), null);
                            log.error("Poll Folder Thread was interrupted for user: {} on thread: {}", userId, threadNumber, e);
                        } finally {
                            semaphore.release();  // Release permit after task completion
                        }
                    })
            );
        } catch (Exception e) {
            log.error("Error occurred while polling emails: from folder ", e);
        }

    }


    private void pushMailToKafka(String emailId, String userId, String timeFilter, UserFolders folder, String batchId, String queueName) {
        log.debug("Inside @method pushMailToKafka. @param : emailId -> {} userId -> {} timeFilter -> {} folder -> {}",
                emailId, userId, timeFilter, folder);
        try {
            UserEmailResponseDto emailsOfUser = new UserEmailResponseDto();
            if (pollingMode.equalsIgnoreCase("EWS")) {
                try {
                    emailsOfUser = ewsService.getEmailsOfUser(emailId, emailId, timeFilter, folder.getFolderId());
                    if (emailsOfUser != null) {
                        pollTimeInfoService.setEndTime(emailId, LocalDateTime.now(), PollTimeInfo.Status.SUCCESS);
                    }
                } catch (Exception e) {
                    Map<String, String> auditMap = Map.of("folderName", folder.getDisplayName());
                    auditLog.error("Error inside @method poll mail", e.getMessage(), getStackTraceAsString(e), null, null, EWS_NETWORK_CALL, userId, null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    log.error("Error in EWS polling", e);
                }
            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                try {
                    log.info("inside gmail polling");
                    emailsOfUser = gmailIntegration.pollGmailFolderWise(userId, timeFilter, folder.getFolderId());
                    if (emailsOfUser != null) {
                        pollTimeInfoService.setEndTime(emailId, LocalDateTime.now(), PollTimeInfo.Status.SUCCESS);
                    }
                } catch (Exception e) {
                    Map<String, String> auditMap = Map.of("folderName", folder.getDisplayName());
                    auditLog.error("Error inside @method poll GMAIL mail {}", e.getMessage(), getStackTraceAsString(e), null, null, EWS_NETWORK_CALL, userId, null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
                }
            } else {
                try {
                    emailsOfUser = graphIntegrationService.getEmailsOfUser(userId, emailId, timeFilter, null, null, folder.getFolderId(), 100, 0);
                    if (emailsOfUser != null) {
                        pollTimeInfoService.setEndTime(emailId, LocalDateTime.now(), PollTimeInfo.Status.SUCCESS);
                    }
                } catch (Exception e) {
                    Map<String, String> auditMap = Map.of("folderName", folder.getDisplayName());
                    auditLog.error("Error inside @method poll mail", e.getMessage(), getStackTraceAsString(e), null, null, GRAPH_NETWORK_CALL, userId, null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    log.error("Error in polling Graph", e);
                }
            }
            List<List<UserEmailDto>> conversations = new ArrayList<List<UserEmailDto>>();
            if (emailsOfUser != null && emailsOfUser.getMessages() != null && emailsOfUser.getMessages().size() > 0) {
                conversations = emailsOfUser.getMessages();
            }
            EmailPreferences preference = preferencesDao.getEmailPreferencesByUserId(userId);
            Organisation orgObject = orgRepo.findByOrgName(CommonUtils.getOrganizationFromEmail(emailId));

            Result result = getResult(emailId, preference, orgObject);

            for (List<UserEmailDto> emails : conversations) {
                String conversationId = emails.getFirst().getConversationId();
                String subject = emails.getFirst().getSubject();

                log.info("Consume Kafka BlackListed Subject {} Actual Subject {} userId {}", result.blackListedSubject().toString(), emails.getFirst().getSubject(), userId);

                if (result.blackListedSubject().stream().anyMatch(blacklist -> emails.getFirst().getSubject().contains(blacklist))) {
                    Map<String, String> auditMap = CommonUtils.getAuditMap(userId, subject, null, null, null, null, null, conversationId);
                    auditLog.error("Mail not summarised since SUBJECT is black listed", "Mail not summarised since SUBJECT is black listed", "Mail not summarised since SUBJECT is black listed",
                            emails.getFirst().getId(), emails.getFirst().getInternetMessageId(), BLACKLISTED_SUBJECT, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    continue;
                }

                log.debug("Checking attachment for subject : {}, userId : {}", subject, userId);

                for (int i = 0; i < emails.size(); i++) {
                    UserEmailDto email = emails.get(i);

                    String messageId = email.getId();
                    String internetMessageId = email.getInternetMessageId();
                    String fromUser = email.getFrom();
                    Map<String, String> auditMap = CommonUtils.getAuditMap(userId, subject, internetMessageId, null, null, null, messageId, conversationId);
                    auditMap.put("folderName", folder.getDisplayName());

                    log.debug("Push to Kafka Blacklisted domain for preference {} for user {}", result.blackListedDomain().toString(), userId);

                    if (result.blackListedDomain().contains(CommonUtils.getDomainFromEmail(fromUser))) {
                        auditLog.error("Mail not summarised since the domain is black listed", "Mail not summarised since the domain is black listed", "Mail not summarised since the domain is black listed",
                                messageId, internetMessageId, BLACKLISTED_DOMAIN, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                        continue;
                    }
                    log.debug("Consume Kafka Blacklisted sender from preference {} for user {}", result.blackListedSender().toString(), userId);
                    if (result.blackListedSender().contains(fromUser)) {
                        auditLog.error("Mail not summarised since sender is black listed", "Mail not summarised since sender is black listed", "Mail not summarised since sender is black listed",
                                messageId, internetMessageId, BLACKLISTED_SENDER, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                        continue;
                    }

                    Boolean hasAttachments = email.getHasAttachments();
                    if (hasAttachments) {
                        log.debug("The email has attachments. messageId : {}, subject : {}, userId : {}", messageId, subject, userId);
                        if (pollingMode.equalsIgnoreCase("EWS")) {
                            try {
                                ewsService.summarizeMailAttachment(conversationId, internetMessageId, emailId, messageId, userId, email.getSubject());
                            } catch (Exception e) {
                                log.error("Error in summarising attachment EWS", e);
                            }
                        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                            try {
                                gmailIntegration.summarizeMailAttachment(conversationId, emailId, messageId);
                            } catch (Exception e) {
                                log.error("Error in summarising attachment GMAIL", e);
                            }
                        } else {
                            try {
                                graphIntegrationService.summarizeMailAttachment(conversationId, internetMessageId, emailId, messageId, userId, email.getSubject());
                            } catch (Exception e) {
                                log.error("Error in summarising attachment Graph", e);
                            }
                        }
                        log.debug("Attachment summarized messageId : {}, subject : {}, userId : {}", messageId, subject, userId);
                    }
                }
            }

            KafkaMailWrapper mailWrapper = new KafkaMailWrapper();
            mailWrapper.setEmailId(emailId);
            mailWrapper.setUserId(userId);
            mailWrapper.setUserConversation(conversations);
            mailWrapper.setFolderDisplayName(folder.getDisplayName());
            ObjectMapper mapper = new ObjectMapper();
            String mailWrapperString = "";

            mailWrapperString = mapper.writeValueAsString(mailWrapper);
            if (!mailWrapperString.isEmpty()) {
                kafkaUtil.produceMessage(mailWrapperString, batchId, queueName);
            }


        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("timeFilter", timeFilter, "folder", folder.getDisplayName());
            auditLog.error("Error polling for user", e.getMessage(), getStackTraceAsString(e), null, null, "PUSH_MESSAGE_TO_KAFKA", emailId, null, null, null, null,
                    AIUtils.convertToJSON(auditMap, true), null);
        }


    }

    /**
     * Consume kafka.
     *
     * @param emailId       the email id
     * @param userId        the user id
     * @param conversations the conversations
     * @param folderName    the folder name
     */
    public void consumeKafka(String emailId, String userId, List<List<UserEmailDto>> conversations, String folderName) {

        log.debug("Going to process mails for user {} ", userId);
        EmailPreferences preference = preferencesDao.getEmailPreferencesByUserId(userId);
        Organisation orgObject = orgRepo.findByOrgName(CommonUtils.getOrganizationFromEmail(emailId));

        Result result = getResult(emailId, preference, orgObject);

        log.error("Polling {} conversations for user : {} by thread : {}", conversations.size(), emailId, Thread.currentThread().getName());
        for (List<UserEmailDto> emails : conversations) {

            EmailThread emailThread;
            String conversationId = emails.getFirst().getConversationId();
            String subject = emails.getFirst().getSubject();
            emailThread = threadDao.findByConversationId(conversationId, userId);

            log.info("Consume Kafka BlackListed Subject {} Actual Subject {} userId {}", result.blackListedSubject().toString(), emails.getFirst().getSubject(), userId);

            if (result.blackListedSubject().stream().anyMatch(blacklist -> emails.getFirst().getSubject().contains(blacklist))) {
                Map<String, String> auditMap = CommonUtils.getAuditMap(userId, subject, null, null, null, null, null, conversationId);
                auditLog.error("Mail not summarised since SUBJECT is black listed", "Mail not summarised since SUBJECT is black listed", "Mail not summarised since SUBJECT is black listed",
                        emails.getFirst().getId(), emails.getFirst().getInternetMessageId(), BLACKLISTED_SUBJECT, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                continue;
            }

            log.debug("Consume Kafka for subject  : {} emailSize {} userId {}", subject, emails.size(), userId);

            if (emailThread == null) {
                emailThread = new EmailThread();
                emailThread.setCreatedTime(new Date());
                emailThread.setUserId(userId);
                emailThread.setSubject(subject);
                emailThread.setConversationId(conversationId);
            }

            Summary shortSummaryResponse = null;
            ThreadSummaryAIResponse threadSummaryResponse = null;

            for (int i = 0; i < emails.size(); i++) {
                UserEmailDto email = emails.get(i);
                MailSummary mailSummary;
                String messageId = email.getId();
                String internetMessageId = email.getInternetMessageId();
                String emailContent = email.toString();
                String fromUser = email.getFrom();


                Map<String, String> auditMap = CommonUtils.getAuditMap(userId, subject, internetMessageId, null, null, null, messageId, conversationId);
                auditMap.put("folderName", folderName);

                log.debug("Consume Kafka Blacklisted domain for preference {} for user {}", result.blackListedDomain().toString(), userId);
                if (result.blackListedDomain().contains(CommonUtils.getDomainFromEmail(fromUser))) {
                    auditLog.error("Mail not summarised since the domain is black listed", "Mail not summarised since the domain is black listed", "Mail not summarised since the domain is black listed",
                            messageId, internetMessageId, BLACKLISTED_DOMAIN, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    continue;
                }
                log.debug("Consume Kafka Blacklisted sender from preference {} for user {}", result.blackListedSender().toString(), userId);
                if (result.blackListedSender().contains(fromUser)) {
                    auditLog.error("Mail not summarised since sender is black listed", "Mail not summarised since sender is black listed", "Mail not summarised since sender is black listed",
                            messageId, internetMessageId, BLACKLISTED_SENDER, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    continue;
                }
                String type = email.getType();
                graphIntegrationService.generateStatsForEmail(fromUser, emailId, email.getBody(), type);
                mailSummary = mailSummaryDao.findByInternetMessageId(internetMessageId, userId);
                if (mailSummary == null) {
                    mailSummary = new MailSummary();
                    mailSummary.setCreatedTime(new Date());
                    mailSummary.setSubject(subject);
                }
                CategoryAIResponse categoryResponse = null;
                List<ObjectiveAIResponse> objectiveResponses = null;
                try {
                    auditMap.put("type", "CATEGORY");
                    categoryResponse = aiService.getCategoryResponse(emailContent, emailId, auditMap);

                    log.debug("category Response is {} subject {} userId {}", categoryResponse, subject, userId);
                } catch (Exception e) {
                    auditLog.error("Error while getting category response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_CATEGORY, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    log.error("Error while getting category for user {} for subject {}", userId, subject, e);
                }


                if (categoryResponse == null) {
                    categoryResponse = getDummyCategoryAIResponse();
                    mailSummary.setPriority(EmailConstants.TAGGED_MANUALLY);
                }
                MailSummaryAIResponse mailSummaryResponse = null;
                MailSummary savedSummary = mailSummaryDao.findFirstByInternetMessageId(internetMessageId);
                updateToneForSameEmail(savedSummary, categoryResponse);
                log.info("inside  savedSummary {}", savedSummary);
                log.info("inside  type {}", type);
                if (type != null && type.equalsIgnoreCase(EmailConstants.EMAIL)) {
                    try {
                        if (savedSummary != null && savedSummary.getMessageSummary() != null) {
                            String messageSummary = savedSummary.getMessageSummary();
                            ObjectMapper mapper = CommonUtils.getObjectMapper();
                            MailSummaryAIResponse mailSummaryAIResponse = mapper.readValue(messageSummary, MailSummaryAIResponse.class);
                            com.enttribe.emailagent.ai.dto.mail_summary.Summary summaryObject = mailSummaryAIResponse.getSummaryObject();
                            if (summaryObject != null && summaryObject.getContent() != null) {
                                summaryObject.setMessageId(messageId);
                                summaryObject.setSender(email.getFrom());
                                mailSummaryResponse = mailSummaryAIResponse;
                            } else {
                                try {
                                    auditMap.put("type", "MAIL_SUMMARY");
                                    mailSummaryResponse = aiService.getMailSummaryResponse(email, fromUser, categoryResponse.getCategory(), auditMap, emailId);
                                    refactorMailSummary(email, mailSummaryResponse);
                                } catch (Exception e) {
                                    auditLog.error("Error while getting mail summary response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_MAIL_SUMMARY, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                                    log.error("Error while getting mail summary response for subject {} for user {}", e, subject, userId);
                                }
                            }
                        } else {
                            try {
                                auditMap.put("type", "MAIL_SUMMARY");
                                mailSummaryResponse = aiService.getMailSummaryResponse(email, fromUser, categoryResponse.getCategory(), auditMap, emailId);
                                refactorMailSummary(email, mailSummaryResponse);
                            } catch (Exception e) {
                                auditLog.error("Error while getting mail summary response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_MAIL_SUMMARY, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                                log.error("Error while getting mail summary response for subject {} for user {}", e, subject, userId);
                            }
                        }
                        log.debug("mailSummary response for subject {} ofr userId is {}", subject, userId);
                        String threadSummary = emailThread.getThreadSummary();
                        if (threadSummary == null) threadSummary = "No previous thread summary";
                        try {
                            auditMap.put("type", "SHORT_THREAD_SUMMARY");
                            shortSummaryResponse = aiService.getShortSummaryResponse(emailContent, threadSummary, auditMap);
                        } catch (Exception e) {
                            auditLog.error("Error while getting thread short summary response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_THREAD_SHORT_SUMMARY, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                            log.error("Error while getting short thread summary response for subject {} for user {}", e, subject, userId);
                        }
                        try {
                            auditMap.put("type", "LONG_THREAD_SUMMARY");
                            threadSummaryResponse = aiService.getThreadSummaryResponse(emailContent, emailId, threadSummary, auditMap);
                        } catch (Exception e) {
                            auditLog.error("Error while getting thread short summary response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_THREAD_LONG_SUMMARY, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                            log.error("Error while getting long thrread summary response for subject {} for user {}", e, subject, userId);
                        }
                    } catch (Exception e) {
                        log.error("Error while getting summary response for subject {} for user {}", subject, userId, e);
                    }
                } else if (type != null && type.equalsIgnoreCase("Meeting")) {
                    String meetingId = CommonUtils.getMeetingId(email.getBody());
                    if (meetingId != null) {
                        MeetingSummary meeting = meetingSummary.findMeetingSummaryByMeetingId(emailId, meetingId);
                        if (meeting == null) {
                            MeetingSummary newMeetingObject = new MeetingSummary();
                            newMeetingObject.setInternetMessageId(internetMessageId);
                            newMeetingObject.setMeetingId(meetingId);
                            newMeetingObject.setStatus("NEW");
                            newMeetingObject.setSubject(subject);
                            newMeetingObject.setUserId(emailId);
                            newMeetingObject.setCreationTime(new Date());
                            newMeetingObject.setModifiedTime(new Date());
                            meetingSummary.save(newMeetingObject);

                        }
                    }
                }

                if (mailSummary == null) {
                    mailSummary = new MailSummary();
                    mailSummary.setCreatedTime(new Date());
                    mailSummary.setSubject(subject);
                }

                mailSummary.setMessageId(messageId);
                mailSummary.setConversationId(conversationId);
                mailSummary.setUserId(userId);
                mailSummary.setFromUser(fromUser);
                mailSummary.setInternetMessageId(internetMessageId);
                log.info("going to set email.getToRecipients() is {}", email.getToRecipients());
                //mailSummary.setToUser(String.join(",", email.getToRecipients()));
                mailSummary.setToUser(String.join(",", Optional.ofNullable(email.getToRecipients()).orElse(Collections.emptyList())));
//                mailSummary.setBccUser(String.join(",", email.getBccRecipients()));
                log.info("going to set email.getToRecipients() is {}", email.getCcRecipients());
                mailSummary.setCcUser(String.join(",", Optional.ofNullable(email.getCcRecipients()).orElse(Collections.emptyList())));
                log.info("going to set type is {}", type);
                mailSummary.setType(type);

//                Date meetingEndTime1 = email.getMeetingEndTime();
//                LocalDateTime meetingEndTime = meetingEndTime1 != null ?
//                        LocalDateTime.ofInstant(meetingEndTime1.toInstant(), ZoneId.systemDefault()) : null;
//                mailSummary.setMeetingEndTime(meetingEndTime);
                Instant mailRecTime = email.getCreatedTime().toInstant();
                LocalDateTime mailReceivedTime = mailRecTime != null ?
                        LocalDateTime.ofInstant(mailRecTime, ZoneId.systemDefault()) : null;
                mailSummary.setMailReceivedTime(mailReceivedTime);

                if (mailSummaryResponse != null) {
                    List<ActionOwnerResponse> actionOwner = mailSummaryResponse.getActionOwner();
                    mailSummary.setActionOwner(convertToJSON(actionOwner, false));
                    if (!folderName.equals("Sent Items")) {
                        Set<UserActions> userActionsSet = getUserActions(emailId, subject, conversationId, internetMessageId, mailReceivedTime,  actionOwner, mailSummary);
                        mailSummary.setUserActions(userActionsSet);
                    }
                }

                takeAction(userId, folderName, email, mailReceivedTime);

                String priority = categoryResponse.getPriority();

                log.debug("star marking... for user {} for subject", userId, subject);
                //Star mark related code
                if (result.emailSenderPreferences().contains(fromUser.toLowerCase())) {
                    mailSummary.setStarMarked(true);
                    mailSummary.setStarMarkReason(EmailConstants.SENDER + fromUser + EmailConstants.MATCHED_WITH_PREFERENCES);
                }
                if (result.senderCompanyPreference().contains((CommonUtils.getDomainFromEmail(fromUser)).toLowerCase())) {
                    mailSummary.setStarMarked(true);
                    mailSummary.setStarMarkReason(EmailConstants.COMPANY_DOMAIN + CommonUtils.getDomainFromEmail(fromUser) + EmailConstants.MATCHED_WITH_PREFERENCES);
                }
                if (result.conversationPreference().contains(emailThread.getConversationId())) {
                    mailSummary.setStarMarked(true);
                    mailSummary.setStarMarkReason(EmailConstants.CONVERSATION_ID + emailThread.getConversationId() + EmailConstants.MATCHED_WITH_PREFERENCES);

                    emailThread.setStarMarked(true);
                    emailThread.setStarReason(EmailConstants.CONVERSATION_ID + emailThread.getConversationId() + EmailConstants.MATCHED_WITH_PREFERENCES);
                }

                if (type.equalsIgnoreCase(EmailConstants.MEETING)) {
                    mailSummary.setCategory(EmailConstants.EVENT);
                    mailSummary.setCategoryReason("Mail is a meeting invitation.");
                } else if (mailSummary.getStarMarked() != null && mailSummary.getStarMarked()) {
                    mailSummary.setCategory(EmailConstants.ATTENTION);
                    mailSummary.setCategoryReason("Mail is star marked.");
                }
//                else if (mailSummary.getActionOwner() != null && mailSummary.getActionOwner().contains(emailId) && categoryResponse.getCategory().equals(EmailConstants.ATTENTION)) {
//                    mailSummary.setCategory(categoryResponse.getCategory());
//                    mailSummary.setCategoryReason("Recipient is an action owner in the mail.");
//                } else if (mailSummary.getActionOwner() == null || (!mailSummary.getActionOwner().contains(emailId) && categoryResponse.getCategory().equals(EmailConstants.ATTENTION))) {
//                    mailSummary.setCategory("FYI");
//                    mailSummary.setCategoryReason("Recipient is not an action owner in the mail.");
//                }
                else {
                    mailSummary.setCategory(categoryResponse.getCategory());
                    mailSummary.setCategoryReason(categoryResponse.getCategoryReason());
                }

                //check for FYI case as action owners gets assigned then wrong day summary is displayed
                if (mailSummary.getCategory().equalsIgnoreCase("FYI")) {
                    mailSummary.setActionOwner(null);
                }

                log.info("inside  check email.getImportance() {}", email.getImportance());
                if (email.getImportance() != null && email.getImportance().equals("high")) {
                    priority = "High";
                    mailSummary.setPriorityReason("Mail is of high importance.");
                } else {
                    mailSummary.setPriorityReason(categoryResponse.getPriorityReason());
                }
                mailSummary.setPriority(priority);
                mailSummary.setEmailTone(categoryResponse.getEmailTone());
                mailSummary.setEmailToneReason(categoryResponse.getEmailToneReason());

                List<String> categories = List.of(priority);

                if (preference != null && preference.getMaskContent() && mailSummaryResponse != null) {
                    JSONObject maskedMailSummary = DataMasking.analyzeSensitiveData(mailSummaryResponse.getSummaryObject().getContent());
                    mailSummaryResponse.getSummaryObject().setContent(maskedMailSummary.getString("text"));
                    if (maskedMailSummary.has("items")) {
                        JSONObject object = DataMasking.getDeAnonymizeObject(maskedMailSummary.getJSONArray("items"));
                        mailSummary.setDecryptSummary(object.toString());
                    }
                }
                mailSummary.setMessageSummary(convertToJSON(mailSummaryResponse, true));
                mailSummary.setModifiedTime(new Date());
                CategoryAIResponse tagResponse = null;
                List<String> tags = List.of();
                try {
                    auditMap.put("type", "TAG");
                    if (savedSummary != null) {
                        String tag = savedSummary.getTag();
                        tags = CommonUtils.getListFromString(tag);
                    } else {
                        if (mailSummaryResponse != null && mailSummaryResponse.getSummaryObject() != null && mailSummaryResponse.getSummaryObject().getContent() != null) {
                            tagResponse = aiService.getTagResponse(mailSummaryResponse.getSummaryObject().getContent(), emailId, auditMap);
                            tags = tagResponse.getTags();
                        }
                    }
                } catch (Exception e) {
                    tagResponse = new CategoryAIResponse();
                    auditLog.error("Error while getting tag response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_TAG, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    log.error("Error while getting tag response for user {} for subject {}", userId, subject, e);
                }

//                //Creating tags for the user...
                mailSummary.setTag(convertToJSON(tags, false));

                //   tagEmails(emailId, messageId, tags, pollingMode);

                log.debug("Going to save mail summary for subject {} for user {}", subject, userId);
                mailSummary.setFolderName(folderName);
                try {
                    // Map<String,Object> requestBody=new HashMap<>();
                    JSONObject requestObject = new JSONObject();
                    requestObject.put("email", emailId);
                    requestObject.put("mailReceivedDate", mailSummary.getMailReceivedTime().toLocalDate());
                    requestObject.put("summaryText", CommonUtils.getMailSummaryForVector(mailSummary, userId));
                    log.debug("going to store summary in vector {}", requestObject.toString());
                    graphIntegrationService.saveMailSummaryInVector(requestObject.toString());
                } catch (Exception e) {
                    log.error("Error while storing summary to Vector {}", e);
                }
                if (preference != null && preference.getMaskContent() && shortSummaryResponse != null) {
                    JSONObject shortSummary = DataMasking.analyzeSensitiveData(shortSummaryResponse.getSummary());
                    shortSummaryResponse.setSummary(shortSummary.getString("text"));
                    if (shortSummary.has("items")) {
                        JSONObject object = DataMasking.getDeAnonymizeObject(shortSummary.getJSONArray("items"));
                        emailThread.setShortDecryptObject(object.toString());
                    }
                }
                emailThread.setShortSummary(convertToJSON(shortSummaryResponse, true));
                if (preference != null && preference.getMaskContent() && threadSummaryResponse != null) {
                    List<String> actionItems = threadSummaryResponse.getActionItems();
                    List<String> tempActionItems = new ArrayList<String>();
                    for (int x = 0; x < actionItems.size(); x++) {
                        JSONObject actionItemString = DataMasking.analyzeSensitiveData(actionItems.get(x));
                        tempActionItems.add(actionItemString.getString("text"));
                        if (actionItemString.has("items")) {

                        }
                    }
                    threadSummaryResponse.setActionItems(tempActionItems);
                }
                if (preference != null && preference.getMaskContent() && threadSummaryResponse != null) {
                    List<String> summaryPoints = threadSummaryResponse.getSummaryPoints();
                    List<String> tempSummaryPoints = new ArrayList<String>();
                    for (int j = 0; j < summaryPoints.size(); j++) {
                        JSONObject summaryString = DataMasking.analyzeSensitiveData(summaryPoints.get(j));
                        tempSummaryPoints.add(summaryString.getString("text"));
                        if (summaryString.has("items")) {

                        }
                    }
                    threadSummaryResponse.setSummaryPoints(tempSummaryPoints);
                }

                if (threadSummaryResponse != null && threadSummaryResponse.getSummaryPoints() != null && !threadSummaryResponse.getSummaryPoints().isEmpty()) {
                    emailThread.setThreadSummary(convertToJSON(threadSummaryResponse, true));
                }

                emailThread.setModifiedTime(new Date());
                threadDao.save(emailThread);
                log.debug("Going to save thread summary for subject {} for user {}", subject, userId);
                try {
                    if (!folderName.equals("Sent Items")) {
                        auditMap.put("type", "OBJECTIVE");
                        String conversationSummary;
                        if (threadSummaryResponse != null && threadSummaryResponse.getSummaryPoints() != null && !threadSummaryResponse.getSummaryPoints().isEmpty()) {
                            conversationSummary = getConversationSummaryString(emailThread);
                        } else {
                            conversationSummary = getConversationSummaryString(emailThread);
                        }
                        objectiveResponses = aiService.getObjectiveResponses(emailContent, conversationSummary, emailId, auditMap);
                        log.debug("objective Response is {} subject {} userId {}", objectiveResponses, subject, userId);
                    }
                } catch (Exception e) {
                    auditLog.error("Error while getting objective response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_OBJECTIVE, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    log.error("Error while getting objective for user {} for subject {}", userId, subject, e);
                }
                mailSummary.setObjective(convertToJSON(objectiveResponses, false));
                mailSummary.setNotificationStatus(preference.getAllowNotification() ? MailSummary.NotificationStatus.NOT_SENT : MailSummary.NotificationStatus.NOT_ALLOWED);
                mailSummaryDao.save(mailSummary);

            }
        }
    }

    private Set<UserActions> getUserActions(String emailId, String subject, String conversationId, String internetMessageId, LocalDateTime mailReceivedTime, List<ActionOwnerResponse> actionOwner, MailSummary mailSummary) {
        if (actionOwner == null || actionOwner.isEmpty()) return null;
        long count = userActionsDao.getUserActionByInternetMessageId(emailId, internetMessageId);
        if (count != 0) return null;

        List<ActionOwnerResponse> usersActions = actionOwner.stream().filter(allActions -> allActions.getActionOwnerEmail().toLowerCase().contains(emailId.toLowerCase())).toList();
        Set<UserActions> userActionsSet = new HashSet<>();
        for (ActionOwnerResponse actionOwnerResponse: usersActions) {
            UserActions userActions = new UserActions();
            userActions.setActionOwner(emailId);
            userActions.setSubject(subject);
            userActions.setConversationId(conversationId);
            userActions.setActionOwnerReason(actionOwnerResponse.getActionOwnerReason());
            userActions.setMailSummary(mailSummary);
            userActions.setInternetMessageId(internetMessageId);
            userActions.setMailReceivedTime(mailReceivedTime);
            userActions.setCreatedAt(LocalDateTime.now());

            userActionsSet.add(userActions);
        }
        return userActionsSet;
    }

    private void takeAction(String userId, String folderName, UserEmailDto email, LocalDateTime mailReceivedTime) {
        log.debug("Inside @method takeAction. @param : userId -> {} folderName -> {}", userId, folderName);
        String conversationId = email.getConversationId();
        String subject = email.getSubject();
        if (folderName !=null && folderName.equals("Sent Items")) {
            List<ActionItemDto> actionItemDetails = imailSummaryService.getActionItemDetails(userId, conversationId, subject);
            if (actionItemDetails.isEmpty()) {
                log.debug("No action item is found for the conversation with subject : {}", subject);
                return;
            }
//            EmailThread emailThread = emailThreadDao.findByConversationId(conversationId, userId);
            List<ActionTakenResponse> actionOwnerResponses = aiService.checkActionTaken(email.getBody(), userId, actionItemDetails.toString(), Map.of("emailBody", email.getBody(), "userId", userId, "actionItemDetails", actionItemDetails.toString(), "type", "AUTO_ACTION"));
            log.debug("actionOwnerResponses from AI : {}", actionOwnerResponses);
            if (actionOwnerResponses != null && !actionOwnerResponses.isEmpty()) {
                log.debug("Action owner response for userid {} and response is {}", userId, actionOwnerResponses);
                for (ActionTakenResponse actionTakenResponse : actionOwnerResponses) {

                    UserActions userActions = userActionsDao.findById(actionTakenResponse.getMailSummaryId()).orElse(null);
                    if (userActions != null) {
                        log.debug("Updating user action for ActionTakenResponse : {}", actionTakenResponse);
                        userActions.setActionTaken(actionTakenResponse.getActionTaken());
                        userActions.setActionTakenReason(actionTakenResponse.getReasoning());
                        userActionsDao.save(userActions);
                    }
                }
            }
        }
    }

    public Map<String, Object> manualProcessEmail(UserEmailDto mailObject, String emailId, String folderName) {
        if (folderName == null) folderName = mailObject.getFolderName();
        log.debug("Folder name is : {}", folderName);

        Map<String, Object> responseMap = new HashMap<String, Object>();
        log.debug("Going to manually process mails for internetMessageId {} for emailId {} ", mailObject.getInternetMessageId(), emailId);
        EmailPreferences preference = preferencesDao.getEmailPreferencesByUserId(emailId);
        Organisation orgObject = orgRepo.findByOrgName(CommonUtils.getOrganizationFromEmail(emailId));

        Result result = getResult(emailId, preference, orgObject);
        EmailThread emailThread;
        String conversationId = mailObject.getConversationId();
        String subject = mailObject.getSubject();
        emailThread = threadDao.findByConversationId(conversationId, emailId);

        if (emailThread == null) {
            emailThread = new EmailThread();
            emailThread.setCreatedTime(new Date());
            emailThread.setUserId(emailId);
            emailThread.setSubject(subject);
            emailThread.setConversationId(conversationId);
        }

        Summary shortSummaryResponse = null;
        ThreadSummaryAIResponse threadSummaryResponse = null;
        MailSummary mailSummary;
        String messageId = mailObject.getId();
        String internetMessageId = mailObject.getInternetMessageId();
        String emailContent = mailObject.toString();
        String fromUser = mailObject.getFrom();

        String type = mailObject.getType();
        graphIntegrationService.generateStatsForEmail(fromUser, emailId, mailObject.getBody(), type);
        Map<String, String> auditMap = CommonUtils.getAuditMap(emailId, subject, internetMessageId, null, null, null, messageId, conversationId);
        auditMap.put("folderName", folderName);
        mailSummary = mailSummaryDao.findByInternetMessageId(internetMessageId, emailId);
        if (mailSummary == null) {
            mailSummary = new MailSummary();
            mailSummary.setCreatedTime(new Date());
            mailSummary.setSubject(subject);
        }
        if (type.equalsIgnoreCase("Meeting")) {
            mailSummary.setMeetingPreview(mailObject.getBodyPreview());
        }
        Boolean hasAttachments = mailObject.getHasAttachments();
        if (hasAttachments) {
            log.debug("The email has attachments. messageId : {}, subject : {}, userId : {}", messageId, subject, emailId);
            if (pollingMode.equalsIgnoreCase("EWS")) {
                try {
                    ewsService.summarizeMailAttachment(conversationId, internetMessageId, emailId, messageId, emailId, mailObject.getSubject());
                } catch (Exception e) {
                    log.error("Error in summarising attachment EWS", e);
                }
            } else {
                try {
                    graphIntegrationService.summarizeMailAttachment(conversationId, internetMessageId, emailId, messageId, emailId, mailObject.getSubject());
                } catch (Exception e) {
                    log.error("Error in summarising attachment Graph", e);
                }
            }
            log.debug("Attachment summarized messageId : {}, subject : {}, userId : {}", messageId, subject, emailId);
        }

        CategoryAIResponse categoryResponse = null;
        List<ObjectiveAIResponse> objectiveResponses = null;
        try {
            auditMap.put("type", "CATEGORY");
            categoryResponse = aiService.getCategoryResponse(emailContent, emailId, auditMap);

            log.debug("category Response is {} subject {} userId {}", categoryResponse, subject, emailId);
        } catch (Exception e) {
            log.error("Error while getting category for user {} for subject {}", emailId, subject, e);
        }


        if (categoryResponse == null) {
            categoryResponse = getDummyCategoryAIResponse();
            mailSummary.setPriority(EmailConstants.TAGGED_MANUALLY);
            mailSummary.setPriority(EmailConstants.TAGGED_MANUALLY);
        }
        MailSummaryAIResponse mailSummaryResponse = null;
        MailSummary savedSummary = mailSummaryDao.findFirstByInternetMessageId(internetMessageId);
        updateToneForSameEmail(savedSummary, categoryResponse);
        log.info("type is {}", type);
        if (type.equalsIgnoreCase(EmailConstants.EMAIL)) {
            try {
                if (savedSummary != null && savedSummary.getMessageSummary() != null) {
                    String messageSummary = savedSummary.getMessageSummary();
                    ObjectMapper mapper = CommonUtils.getObjectMapper();
                    MailSummaryAIResponse mailSummaryAIResponse = mapper.readValue(messageSummary, MailSummaryAIResponse.class);
                    com.enttribe.emailagent.ai.dto.mail_summary.Summary summaryObject = mailSummaryAIResponse.getSummaryObject();
                    if (summaryObject != null && summaryObject.getContent() != null) {
                        summaryObject.setMessageId(messageId);
                        summaryObject.setSender(mailObject.getFrom());
                        mailSummaryResponse = mailSummaryAIResponse;
                    } else {
                        try {
                            log.debug("going to get mail summary{}");
                            auditMap.put("type", "MAIL_SUMMARY");
                            mailSummaryResponse = aiService.getMailSummaryResponse(mailObject, fromUser, categoryResponse.getCategory(), auditMap, emailId);
                            refactorMailSummary(mailObject, mailSummaryResponse);
                        } catch (Exception e) {
                            log.error("Error while getting mail summary response for subject {} for user {}", e, subject, emailId);
                            auditLog.error("Error while getting mail summary response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_MAIL_SUMMARY, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);

                        }
                    }
                } else {
                    try {
                        auditMap.put("type", "MAIL_SUMMARY");
                        mailSummaryResponse = aiService.getMailSummaryResponse(mailObject, fromUser, categoryResponse.getCategory(), auditMap, emailId);
                        refactorMailSummary(mailObject, mailSummaryResponse);
                    } catch (Exception e) {
                        log.error("Error while getting mail summary response for subject {} for user {}", e, subject, emailId);
                        auditLog.error("Error while getting mail summary response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_MAIL_SUMMARY, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);

                    }
                }
                log.debug("mailSummary response for subject {} ofr userId is {}", subject, emailId);
                String threadSummary = emailThread.getThreadSummary();
                if (threadSummary == null) threadSummary = "No previous thread summary";
                try {
                    auditMap.put("type", "SHORT_THREAD_SUMMARY");
                    shortSummaryResponse = aiService.getShortSummaryResponse(emailContent, threadSummary, auditMap);
                } catch (Exception e) {
                    auditLog.error("Error while getting thread short summary response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_THREAD_SHORT_SUMMARY, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    log.error("Error while getting short thread summary response for subject {} for user {}", e, subject, emailId);
                }
                try {
                    auditMap.put("type", "LONG_THREAD_SUMMARY");
                    threadSummaryResponse = aiService.getThreadSummaryResponse(emailContent, emailId, threadSummary, auditMap);
                } catch (Exception e) {
                    auditLog.error("Error while getting thread short summary response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_THREAD_LONG_SUMMARY, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    log.error("Error while getting long thrread summary response for subject {} for user {}", e, subject, emailId);
                }
            } catch (Exception e) {
                log.error("Error while getting summary response for subject {} for user {}", e, subject, emailId);
            }
        } else if (type != null && type.equalsIgnoreCase("Meeting")) {
            String meetingId = CommonUtils.getMeetingId(mailObject.getBody());
            if (meetingId != null) {
                MeetingSummary meeting = meetingSummary.findMeetingSummaryByMeetingId(emailId, meetingId);
                if (meeting == null) {
                    MeetingSummary newMeetingObject = new MeetingSummary();
                    newMeetingObject.setInternetMessageId(internetMessageId);
                    newMeetingObject.setMeetingId(meetingId);
                    newMeetingObject.setStatus("NEW");
                    newMeetingObject.setSubject(subject);
                    newMeetingObject.setUserId(emailId);
                    newMeetingObject.setCreationTime(new Date());
                    newMeetingObject.setModifiedTime(new Date());
                    meetingSummary.save(newMeetingObject);

                }
            }
        }

        mailSummary.setMessageId(messageId);
        mailSummary.setConversationId(conversationId);
        mailSummary.setUserId(emailId);
        mailSummary.setFromUser(fromUser);
        mailSummary.setInternetMessageId(internetMessageId);
        mailSummary.setToUser(String.join(",", Optional.ofNullable(mailObject.getToRecipients()).orElse(Collections.emptyList())));
        mailSummary.setCcUser(String.join(",", Optional.ofNullable(mailObject.getCcRecipients()).orElse(Collections.emptyList())));

        mailSummary.setType(type);
        mailSummary.setEmailTone(categoryResponse.getEmailTone());
        mailSummary.setEmailToneReason(categoryResponse.getEmailToneReason());

//        Date meetingEndTime1 = mailObject.getMeetingEndTime();
//        LocalDateTime meetingEndTime = meetingEndTime1 != null ?
//                LocalDateTime.ofInstant(meetingEndTime1.toInstant(), ZoneId.systemDefault()) : null;
//        mailSummary.setMeetingEndTime(meetingEndTime);
        Instant mailRecTime = mailObject.getCreatedTime().toInstant();
        LocalDateTime mailReceivedTime = mailRecTime != null ?
                LocalDateTime.ofInstant(mailRecTime, ZoneId.systemDefault()) : null;
        mailSummary.setMailReceivedTime(mailReceivedTime);

        if (mailSummaryResponse != null) {
            List<ActionOwnerResponse> actionOwner = mailSummaryResponse.getActionOwner();
            mailSummary.setActionOwner(convertToJSON(actionOwner, false));
            if (folderName != null && !folderName.equals("Sent Items")) {
                Set<UserActions> userActionsSet = getUserActions(emailId, subject, conversationId, internetMessageId, mailReceivedTime, actionOwner, mailSummary);
                mailSummary.setUserActions(userActionsSet);
            }
        }

        takeAction(emailId, folderName, mailObject, mailReceivedTime);

        String priority = categoryResponse.getPriority();

        log.debug("star marking... for user {} for subject", emailId, subject);
        //Star mark related code
        if (result.emailSenderPreferences().contains(fromUser.toLowerCase())) {
            mailSummary.setStarMarked(true);
            mailSummary.setStarMarkReason(EmailConstants.SENDER + fromUser + EmailConstants.MATCHED_WITH_PREFERENCES);
        }
        if (result.senderCompanyPreference().contains((CommonUtils.getDomainFromEmail(fromUser)).toLowerCase())) {
            mailSummary.setStarMarked(true);
            mailSummary.setStarMarkReason(EmailConstants.COMPANY_DOMAIN + CommonUtils.getDomainFromEmail(fromUser) + EmailConstants.MATCHED_WITH_PREFERENCES);
        }
        if (result.conversationPreference().contains(emailThread.getConversationId())) {
            mailSummary.setStarMarked(true);
            mailSummary.setStarMarkReason(EmailConstants.CONVERSATION_ID + emailThread.getConversationId() + EmailConstants.MATCHED_WITH_PREFERENCES);

            emailThread.setStarMarked(true);
            emailThread.setStarReason(EmailConstants.CONVERSATION_ID + emailThread.getConversationId() + EmailConstants.MATCHED_WITH_PREFERENCES);
        }

        log.debug("ActionOwner() for subject {} for user {}" + mailSummary.getActionOwner(), subject, emailId);
        log.debug("Category() for user {} for subject {}" + categoryResponse.getCategory(), emailId, subject);

        if (type.equalsIgnoreCase(EmailConstants.MEETING)) {
            mailSummary.setCategory(EmailConstants.EVENT);
            mailSummary.setCategoryReason("Mail is a meeting invitation.");
        } else if (mailSummary.getStarMarked() != null && mailSummary.getStarMarked()) {
            mailSummary.setCategory(EmailConstants.ATTENTION);
            mailSummary.setCategoryReason("Mail is star marked.");
        }
//        else if (mailSummary.getActionOwner() != null && mailSummary.getActionOwner().contains(emailId) && categoryResponse.getCategory().equals(EmailConstants.ATTENTION)) {
//            mailSummary.setCategory(categoryResponse.getCategory());
//            mailSummary.setCategoryReason("Recipient is an action owner in the mail.");
//        } else if (mailSummary.getActionOwner() == null || (!mailSummary.getActionOwner().contains(emailId) && categoryResponse.getCategory().equals(EmailConstants.ATTENTION))) {
//            mailSummary.setCategory("FYI");
//            mailSummary.setCategoryReason("Recipient is not an action owner in the mail.");
//        }
        else {
            mailSummary.setCategory(categoryResponse.getCategory());
            mailSummary.setCategoryReason(categoryResponse.getCategoryReason());
        }

        //check for FYI case as action owners gets assigned then wrong day summary is displayed
        if (mailSummary.getCategory().equalsIgnoreCase("FYI")) {
            mailSummary.setActionOwner(null);
        }

        if (mailObject.getImportance().equals("high")) {
            priority = "High";
            mailSummary.setPriorityReason("Mail is of high importance.");
        } else {
            mailSummary.setPriorityReason(categoryResponse.getPriorityReason());
        }
        mailSummary.setPriority(priority);

        List<String> categories = List.of(priority);

        if (preference != null && preference.getMaskContent() && mailSummaryResponse != null) {
            JSONObject maskedMailSummary = DataMasking.analyzeSensitiveData(mailSummaryResponse.getSummaryObject().getContent());
            mailSummaryResponse.getSummaryObject().setContent(maskedMailSummary.getString("text"));
            if (maskedMailSummary.has("items")) {
                JSONObject object = DataMasking.getDeAnonymizeObject(maskedMailSummary.getJSONArray("items"));
                mailSummary.setDecryptSummary(object.toString());
            }
        }
        mailSummary.setMessageSummary(convertToJSON(mailSummaryResponse, true));
        mailSummary.setModifiedTime(new Date());
        CategoryAIResponse tagResponse = null;
        List<String> tags = List.of();
        try {
            auditMap.put("type", "TAG");
            if (savedSummary != null) {
                String tag = savedSummary.getTag();
                tags = CommonUtils.getListFromString(tag);
            } else {
                if (mailSummaryResponse != null && mailSummaryResponse.getSummaryObject() != null && mailSummaryResponse.getSummaryObject().getContent() != null) {
                    tagResponse = aiService.getTagResponse(mailSummaryResponse.getSummaryObject().getContent(), emailId, null);
                    tags = tagResponse.getTags();
                }
            }
        } catch (Exception e) {
            tagResponse = new CategoryAIResponse();
            log.error("Error while getting tag response for user {} for subject {}", emailId, subject, e);
        }
//
//        //Creating tags for the user...
        mailSummary.setTag(convertToJSON(tags, false));

        //    tagEmails(emailId, messageId, tags, pollingMode);

        log.debug("Going to save mail summary for subject {} for user {}", subject, emailId);
        mailSummary.setFolderName(folderName);
        mailSummaryDao.save(mailSummary);
        try {
            // Map<String,Object> requestBody=new HashMap<>();
            JSONObject requestObject = new JSONObject();
            requestObject.put("email", emailId);
            requestObject.put("mailReceivedDate", mailSummary.getMailReceivedTime().toLocalDate());
            requestObject.put("summaryText", CommonUtils.getMailSummaryForVector(mailSummary, emailId));
            log.debug("going to store summary in vector {}", requestObject.toString());
            graphIntegrationService.saveMailSummaryInVector(requestObject.toString());
        } catch (Exception e) {
            log.error("Error while storing summary to Vector {}", e);
        }
        if (preference != null && preference.getMaskContent() && shortSummaryResponse != null) {
            JSONObject shortSummary = DataMasking.analyzeSensitiveData(shortSummaryResponse.getSummary());

            shortSummaryResponse.setSummary(shortSummary.getString("text"));
            if (shortSummary.has("items")) {
                JSONObject object = DataMasking.getDeAnonymizeObject(shortSummary.getJSONArray("items"));
                emailThread.setShortDecryptObject(object.toString());
            }
        }
        emailThread.setShortSummary(convertToJSON(shortSummaryResponse, true));
        if (preference != null && preference.getMaskContent() && threadSummaryResponse != null) {
            List<String> actionItems = threadSummaryResponse.getActionItems();
            List<String> tempActionItems = new ArrayList<String>();
            String actionItemString = "";
            for (int x = 0; x < actionItems.size(); x++) {
                actionItemString = actionItems.get(x) + " @# ";
            }
            if (actionItems.size() > 0) {
                JSONObject actionItemObject = DataMasking.analyzeSensitiveData(actionItemString);
                Arrays.stream(actionItemObject.getString("text").split(" @# ")).forEach(s -> tempActionItems.add(s));
                if (actionItemObject.has("items")) {
                    JSONObject object = DataMasking.getDeAnonymizeObject(actionItemObject.getJSONArray("items"));
                    emailThread.setActionDecryptObject(object.toString());
                }

                threadSummaryResponse.setActionItems(tempActionItems);
            }
        }
        if (preference != null && preference.getMaskContent() && threadSummaryResponse != null) {
            List<String> summaryPoints = threadSummaryResponse.getSummaryPoints();
            List<String> tempSummaryPoints = new ArrayList<String>();
            String summaryPointString = "";
            log.debug(" summaryPoints is {} {}", summaryPoints.size(), summaryPoints.toString());
            for (int j = 0; j < summaryPoints.size(); j++) {
                summaryPointString = summaryPoints.get(j) + " @# ";
            }
            if (summaryPoints.size() > 0) {
                log.debug("summaryPointString is {}", summaryPointString);
                JSONObject summaryObject = DataMasking.analyzeSensitiveData(summaryPointString);
                Arrays.stream(summaryObject.getString("text").split(" @# ")).forEach(s -> tempSummaryPoints.add(s));
                if (summaryObject.has("items")) {
                    JSONObject object = DataMasking.getDeAnonymizeObject(summaryObject.getJSONArray("items"));
                    emailThread.setLongDecryptObject(object.toString());
                }
                threadSummaryResponse.setSummaryPoints(tempSummaryPoints);
            }
        }


        emailThread.setThreadSummary(convertToJSON(threadSummaryResponse, true));

        emailThread.setModifiedTime(new Date());
        threadDao.save(emailThread);
        log.debug("Going to save thread summary for subject {} for user {}", subject, emailId);
        try {
            if (!folderName.equals("Sent Items")) {
                auditMap.put("type", "OBJECTIVE");
                String conversationSummary;
                if (threadSummaryResponse != null && threadSummaryResponse.getSummaryPoints() != null && !threadSummaryResponse.getSummaryPoints().isEmpty()) {
                    conversationSummary = emailThread.getThreadSummary();
                } else {
                    conversationSummary = getConversationSummaryString(emailThread);
                }
                objectiveResponses = aiService.getObjectiveResponses(emailContent, conversationSummary, emailId, auditMap);
                log.debug("objective Response is {} subject {} userId {}", objectiveResponses, subject, emailId);
            }
        } catch (Exception e) {
            auditLog.error("Error while getting objective response", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, GENERATE_OBJECTIVE, emailId, subject, conversationId, null, null, AIUtils.convertToJSON(auditMap, true), null);
            log.error("Error while getting objective for user {} for subject {}", emailId, subject, e);
        }
        mailSummary.setObjective(convertToJSON(objectiveResponses, false));
        mailSummary.setNotificationStatus(preference.getAllowNotification() ? MailSummary.NotificationStatus.NOT_SENT : MailSummary.NotificationStatus.NOT_ALLOWED);
        mailSummaryDao.save(mailSummary);
        responseMap.put("result", "Success");
        return responseMap;
    }

    private void updateToneForSameEmail(MailSummary savedSummary, CategoryAIResponse categoryResponse) {
        if (savedSummary != null && savedSummary.getEmailTone() != null) {
            log.debug("Updating tone...");
            categoryResponse.setEmailTone(savedSummary.getEmailTone());
            categoryResponse.setEmailToneReason(savedSummary.getEmailToneReason());
        }
    }

    private String getConversationSummaryString(EmailThread emailThread) {
        String conversationSummary = "";
        if (emailThread.getThreadSummary() == null || emailThread.getThreadSummary().equals("{}"))
            return conversationSummary;

        conversationSummary = emailThread.getThreadSummary();
        int index = conversationSummary.indexOf("actionItems");
        if (index != -1) {
            conversationSummary = conversationSummary.substring(17, index);
        }
        return conversationSummary;
    }


    /**
     * Save contact book.
     *
     * @param fromUser the from user
     * @param toUser   the to user
     * @param ccUser   the cc user
     * @param userId   the user id
     */
    public void saveContactBook(String fromUser, String toUser, String ccUser, String userId) {
        List<ContactBook> contactBooks = new ArrayList<>();
        Set<String> uniqueContacts = new HashSet<>();
        if (fromUser != null) uniqueContacts.add(fromUser);
        if (toUser != null) {
            String[] toUsers = toUser.split(",");
            for (String to : toUsers) {
                String trimmedToUser = to.trim();
                if (!userId.equals(trimmedToUser)) {
                    uniqueContacts.add(trimmedToUser);
                }
            }
        }

        if (ccUser != null) {
            String[] ccUsers = ccUser.split(",");
            for (String cc : ccUsers) {
                String trimmedCcUser = cc.trim();
                if (!userId.equals(trimmedCcUser)) {
                    uniqueContacts.add(cc.trim());
                }
            }
        }
        for (String contact : uniqueContacts) {
            String contactName = preferencesDao.findDisplayNameByEmail(contact);
            if (contactName == null || contactName.trim().isEmpty()) {

                contactName = contact.split("@")[0];
                if (contactName.contains(".")) {
                    String[] split = contactName.split(".");
                    contactName = split[0] + split[1];
                }
            }
            log.info("Contact: {}, Display Name: {}", contact, contactName);

            ContactBook contactBook = new ContactBook();
            contactBook.setUserId(userId);
            contactBook.setContacts(contact);
            contactBook.setContactName(contactName);

            contactBooks.add(contactBook);
            try {
                contactBookRepository.saveAll(contactBooks);
                log.info("Successfully saved contacts for user ID: {}", userId);
            } catch (Exception e) {
                log.error("Error saving contacts for user ID {}: {}", userId, e.getMessage());
            }
        }
    }


}
