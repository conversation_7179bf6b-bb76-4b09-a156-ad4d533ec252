package com.enttribe.emailagent.ai.dto.meeting;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * The type Meeting chat dto.
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class MeetingChatDto {

    private String followUpQuestion;
    private String finalRequirement;
    private Boolean ready;
    private Boolean startTimeProvided;
    private String identifiedAttendees;
    private String identifiedSubject;
//    private Boolean meetingDateConfirmed;
    private String meetingDate;
//    private String meetingStartTime;
//    private String meetingEndTime;
//    private String timeZone;

}
