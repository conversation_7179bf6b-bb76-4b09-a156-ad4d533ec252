package com.enttribe.emailagent.ai.dto.meeting;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * The type Intent response new.
 * <AUTHOR> Sonsale
 */
@Getter
@Setter
@ToString
public class IntentResponseNew {

    private String subjectForMeeting;
//    private String body;
    private String timeZone;
    private String meetingAgenda;
    private Boolean startTimeProvided;
    private int meetingDuration;
    private String meetingStartTime;
    private String meetingEndTime;
    private String meetingDate;
    private List<String> requiredAttendees;
    private List<String> optionalAttendees;

}