package com.enttribe.emailagent.ai.dto.mail_summary;

import com.enttribe.emailagent.dto.ActionOwnerResponse;
import com.enttribe.emailagent.utils.Description;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * The type Mail summary ai response.
 * <AUTHOR> <PERSON>gi
 */
@Getter
@Setter
@ToString
@XmlRootElement(name = "MailSummaryAIResponse")
@Description("Mail Summary response from LLM")
public class MailSummaryAIResponse {

    @Description("This is subject of the email")
    private String subject;
    @Description("This is list of participants in email")
    private List<String> participants;
    @Description("This is email summary")
    private Summary summaryObject;
    @Description("This is response for action owner with unique actions and reason")
    private List<ActionOwnerResponse> actionOwner;
}
