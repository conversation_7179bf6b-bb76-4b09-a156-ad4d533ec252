package com.enttribe.emailagent.ai.dto.meeting;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * The type Available time slots.
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
public class AvailableTimeSlots {

    private Date startTime;
    private Date endTime;
    private List<String> available;
    private List<String> unavailable;
    @JsonIgnore
    private List<String> outOfOffice;
    private List<String> unknown;


    public AvailableTimeSlots(Date startTime, Date endTime, List<String> available, List<String> unavailable) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.available = available;
        this.unavailable = unavailable;
    }
}
