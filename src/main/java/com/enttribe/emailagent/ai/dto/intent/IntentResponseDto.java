package com.enttribe.emailagent.ai.dto.intent;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * The type Intent response dto.
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class IntentResponseDto {

    private String intent;
    private String subjectForMeeting;
    private String body;
    private String timeZone;
    private String durationType;
    private int meetingDuration;
    private String meetingStartTime;
    private String meetingEndTime;
    private String calendar;
    private List<String> attendees;
    private Boolean isHoursProvided;
    private Boolean participantsProvided;
    private Boolean recurring;

}
