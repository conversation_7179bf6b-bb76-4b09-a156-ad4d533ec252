package com.enttribe.emailagent.ai.dto.mail_summary;

import com.enttribe.emailagent.utils.Description;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * The type Summary.
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@XmlRootElement(name = "MailSummaryDTO")
@Description("This is email summary object")
public class Summary {
    @Description("created time of summary")
    private Long createdTime;
    @Description("sender email id")
    private String sender;
    @Description("summary content")
    private String content;
    @Description("messageId of email")
    private String messageId;
    @Description("internetMessageId of email")
    private String internetMessageId;

}