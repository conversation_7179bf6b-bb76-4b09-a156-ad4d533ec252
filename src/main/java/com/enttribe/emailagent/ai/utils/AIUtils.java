package com.enttribe.emailagent.ai.utils;

import com.enttribe.emailagent.ai.dto.mail_summary.MailSummaryAIResponse;
import com.enttribe.emailagent.ai.dto.mail_summary.Summary;
import com.enttribe.emailagent.dto.ActionOwnerResponse;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.Description;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.knuddels.jtokkit.Encodings;
import com.knuddels.jtokkit.api.Encoding;
import com.knuddels.jtokkit.api.EncodingRegistry;
import com.knuddels.jtokkit.api.EncodingType;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.json.XML;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.core.io.Resource;
import org.w3c.dom.Comment;
import org.w3c.dom.Document;
import org.w3c.dom.Element;


import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;

/**
 * The type Ai utils.
 * <AUTHOR> Pathak
 */
@Slf4j
public class AIUtils {

    private AIUtils() {

    }

    public static String getResolvedPrompt(Resource prompt, Map<String, Object> model) {
        PromptTemplate promptTemplate = PromptTemplate.builder()
                .resource(prompt)
                .variables(model)
                .build();
        return promptTemplate.render();
    }

    public static String getPromptString(Resource resource) {
        PromptTemplate promptTemplate = new PromptTemplate(resource);
        Prompt prompt = promptTemplate.create();
        return prompt.getContents();
    }

    public static String convertToJSON(Object object, boolean isObject) {
        try {
            if (object == null) return isObject ? "{}" : "[]";
            ObjectMapper mapper = CommonUtils.getObjectMapper();
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Error inside @method convertToJSON", e);
            return object.toString();
        }
    }

    public static int getTokensFromText(String text) {
        int tokens = 0;
        try {
            EncodingRegistry registry = Encodings.newDefaultEncodingRegistry();
            Encoding enc = registry.getEncoding(EncodingType.CL100K_BASE);
            tokens = enc.countTokens(text);
        } catch (Exception e) {
            log.error("Error getting token from text", e);
        }
        return tokens;
    }

    public static String reduceTextToMaxTokens(String text, int maxTokens) {
        // Step 1: Get the initial token count
        int tokenCount = getTokensFromText(text);

        // Step 2: If the token count is already below the limit, return the text as is
        if (tokenCount <= maxTokens) {
            return text;
        }

        // Step 3: Keep reducing the text by half from the end until it's under the maxTokens
        while (tokenCount > maxTokens) {
            // Reduce the text length by half
            text = text.substring(0, text.length() / 2);
            tokenCount = getTokensFromText(text);
        }

        // The loop ends when the token count is <= maxTokens.
        return text;
    }



    public static String convertJsonToXml(String json) {
        try {
            // Convert the JSON string to a JSONObject
            JSONObject jsonObject = new JSONObject(json);

            // Convert the JSONObject to an XML string
            String xml = XML.toString(jsonObject);

            // Wrap the result with a root element if needed
            return xml;
        } catch (Exception e) {
            e.printStackTrace();
            return null; // Handle the exception as needed
        }
    }

    public static String convertToXMLWithComments(Object object) {
        if (object == null) {
            return ""; // Skip processing if the object is null
        }
        try {
            JAXBContext context = JAXBContext.newInstance(object.getClass());
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);

            // Create a DOM document for manipulation
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.newDocument();

            // Marshal the object into the DOM document
            marshaller.marshal(object, document);

            // Add class-level description as a comment if present
            Class<?> clazz = object.getClass();
            if (clazz.isAnnotationPresent(Description.class)) {
                String classDescription = clazz.getAnnotation(Description.class).value();
                Comment classComment = document.createComment(classDescription);
                document.insertBefore(classComment, document.getDocumentElement());
            }

            // Traverse fields and add comments for annotated fields
            addFieldComments(object, document.getDocumentElement());

            // Transform the DOM document to an XML string
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(document), new StreamResult(writer));

            return writer.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    private static void addFieldComments(Object object, Element parentElement) {
        try {
            Class<?> clazz = object.getClass();
            Document document = parentElement.getOwnerDocument();

            for (Field field : clazz.getDeclaredFields()) {
                // Skip fields in the java.* packages to avoid reflection issues
                if (field.getDeclaringClass().getPackageName().startsWith("java.")) {
                    continue;
                }

                field.setAccessible(true);

                // Check if the field has the @Description annotation
                if (field.isAnnotationPresent(Description.class)) {
                    String description = field.getAnnotation(Description.class).value();
                    Comment comment = document.createComment(description);

                    // Find the corresponding child element in the DOM
                    for (int i = 0; i < parentElement.getChildNodes().getLength(); i++) {
                        if (parentElement.getChildNodes().item(i) instanceof Element) {
                            Element childElement = (Element) parentElement.getChildNodes().item(i);
                            if (childElement.getTagName().equals(field.getName())) {
                                // Insert the comment before the field's element
                                parentElement.insertBefore(comment, childElement);
                                break;
                            }
                        }
                    }

                    // Recursively handle nested objects
                    Object value = field.get(object);
                    if (value != null && !field.getType().isPrimitive() && !field.getType().getName().startsWith("java.lang")) {
                        for (int i = 0; i < parentElement.getChildNodes().getLength(); i++) {
                            if (parentElement.getChildNodes().item(i) instanceof Element) {
                                Element childElement = (Element) parentElement.getChildNodes().item(i);
                                if (childElement.getTagName().equals(field.getName())) {
                                    addFieldComments(value, childElement);
                                }
                            }
                        }
                    }
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }


    public static void main(String[] args) {

        Summary summary = new Summary();
        summary.setCreatedTime(System.currentTimeMillis());
        summary.setSender("<EMAIL>");
        summary.setContent("This is a summary of the email content.");
        summary.setMessageId("12345");
        summary.setInternetMessageId("67890");

        // Create instances of ActionOwnerResponse with sample data
        ActionOwnerResponse actionOwner1 = new ActionOwnerResponse();
        actionOwner1.setActionOwnerEmail("<EMAIL>");
        actionOwner1.setActionOwnerReason("Owner is the main point of contact.");

        ActionOwnerResponse actionOwner2 = new ActionOwnerResponse();
        actionOwner2.setActionOwnerEmail("<EMAIL>");
        actionOwner2.setActionOwnerReason("Owner is responsible for follow-up.");

        // Create an instance of MailSummaryAIResponse with sample data
        MailSummaryAIResponse mailSummary = new MailSummaryAIResponse();
        mailSummary.setSubject("My subject");
        mailSummary.setParticipants(Arrays.asList("<EMAIL>", "<EMAIL>"));
        mailSummary.setSummaryObject(summary);
        mailSummary.setActionOwner(Arrays.asList(actionOwner1, actionOwner2));


        System.out.println(convertToXMLWithComments(mailSummary));

    }
}
