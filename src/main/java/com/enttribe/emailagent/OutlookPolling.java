//package com.enttribe.emailagent;
//
//import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.*;
//import static com.google.common.base.Throwables.getStackTraceAsString;
//
//import java.time.LocalTime;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import com.enttribe.emailagent.ai.utils.AIUtils;
//import com.enttribe.emailagent.utils.ZoomClient;
//import org.slf4j.Logger;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//
//import com.enttribe.emailagent.ai.dto.draft.DraftResponse;
//import com.enttribe.emailagent.ai.dto.intent.IntentResponseDto;
//import com.enttribe.emailagent.ai.service.DraftService;
//import com.enttribe.emailagent.ai.service.MeetingService;
//import com.enttribe.emailagent.dto.Meeting;
//import com.enttribe.emailagent.dto.ZonedMeeting;
//import com.enttribe.emailagent.entity.EmailPreferences;
//import com.enttribe.emailagent.entity.EmailUser;
//import com.enttribe.emailagent.entity.MailSummary;
//import com.enttribe.emailagent.exception.EmailAgentLogger;
//import com.enttribe.emailagent.repository.EmailUserDao;
//import com.enttribe.emailagent.repository.IEmailPreferencesDao;
//import com.enttribe.emailagent.repository.IMailSummaryDao;
//import com.enttribe.emailagent.service.GraphIntegrationService;
//import com.enttribe.emailagent.utils.CommonUtils;
//import com.enttribe.emailagent.utils.DateUtils;
//import com.enttribe.emailagent.utils.EmailConstants;
//
//import lombok.extern.slf4j.Slf4j;
//
//@Service
//@Slf4j
//public class OutlookPolling {
//
//    @Autowired
//    EmailUserDao userDao;
//
//    @Autowired
//    private IEmailPreferencesDao preferencesDao;
//
//    @Autowired
//    IMailSummaryDao mailSummary;
//
//    @Autowired
//    private MeetingService meetingService;
//
//    @Value("${pollingMode}")
//    private String pollingMode;
//
//    @Autowired
//    private DraftService draftServiceAI;
//
//    @Autowired
//    private GraphIntegrationService graphIntegrationService;
//
//    private static final Logger auditLog = EmailAgentLogger.getLogger(OutlookPolling.class);
//
//
//    public Map<String, Object> intentBasedSearch1(String emailId, String userPrompt, String previousPrompt, String internetMessageId, String userId) {
//
//        try {
//
//            if (pollingMode.equalsIgnoreCase("EWS"))
//                return meetingService.intentBasedSearch(emailId, userPrompt, previousPrompt, internetMessageId, userId);
//            MailSummary mailSummaryObject = mailSummary.findByInternetMessageId(internetMessageId, userId);
//            Map<String, String> auditMap;
//            if (mailSummaryObject != null) {
//                auditMap = CommonUtils.getAuditMap(userId, mailSummaryObject.getSubject(), mailSummaryObject.getInternetMessageId(), MEETING_FROM_CHAT, userPrompt, previousPrompt, mailSummaryObject.getMessageId(), mailSummaryObject.getConversationId());
//            } else {
//                auditMap = CommonUtils.getAuditMap(userId, null, null, MEETING_FROM_CHAT, userPrompt, previousPrompt, null, null);
//            }
//            if (internetMessageId != null) {
//
//                if (mailSummaryObject != null) {
//                    userPrompt = userPrompt.concat((" " + mailSummaryObject.getToUser() + " " + mailSummaryObject.getFromUser()));
//                    if (mailSummaryObject.getCcUser() != null) {
//                        userPrompt = userPrompt.concat(" " + mailSummaryObject.getCcUser());
//                    }
//                }
//            }
//
//            if (previousPrompt == null) previousPrompt = "";
//            String promptToReturn = previousPrompt + " " + userPrompt;
//
//            log.info("userPrompt after update is {}", userPrompt);
//
//            log.debug("Inside @method intentBasedSearch.. @param : emailId -> {} userPrompt -> {}", emailId, userPrompt);
//            HashMap<String, Object> resultMap = new HashMap<>();
//            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
//
//            IntentResponseDto intentResponse = meetingService.intentResponse(userPrompt, previousPrompt, auditMap);
//            intentResponse.setBody("");
//
//            if (intentResponse.getAttendees().size() == 0) {
//                resultMap.put(EmailConstants.RESULT, "No participant is provided for the meeting.");
//                resultMap.put(EmailConstants.PREVIOUS_PROMPT, promptToReturn);
//                return resultMap;
//            }
//
//            if (intentResponse.getIsHoursProvided() != null && !intentResponse.getIsHoursProvided()) {
//                List<String> attendeesEmail = intentResponse.getAttendees();
//                attendeesEmail.add(emailId);
//
//                List<LocalTime> checkoutTimesOfUsers = getCheckoutTimesOfUsers(attendeesEmail);
//                String startTime = null;
//                String endTime = null;
//                LocalTime earliestTime = DateUtils.getEarliestTime(checkoutTimesOfUsers.toArray(new LocalTime[0]));
//
//                String dateString = intentResponse.getMeetingStartTime();
//                String calendar = intentResponse.getCalendar();
//                if (calendar.equalsIgnoreCase("today") || calendar.equalsIgnoreCase("none")) {
////                    startTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
//                    startTime = DateUtils.getUTCDateTime(false, preferences.getTimeZone());
//                    endTime = DateUtils.convertToUTCString(earliestTime, null, 0);
//                } else {
//                    startTime = DateUtils.convertToUTCString(dateString, preferences.getCheckin(), preferences.getTimeZone());
//                    endTime = DateUtils.convertToUTCString(dateString, earliestTime, null);
//                }
//
//
//                List<Meeting> availableMeetingSlots = graphIntegrationService.getAvailableMeetingSlots(attendeesEmail, startTime, endTime, intentResponse.getMeetingDuration());
//
//                if (availableMeetingSlots.isEmpty()) {
//                    resultMap.put(EmailConstants.RESULT, EmailConstants.THERE_IS_NO_SLOT_AVAILABLE_IN_WORKING_HOURS);
//                    resultMap.put(EmailConstants.PREVIOUS_PROMPT, promptToReturn);
//                } else {
//                    resultMap.put(EmailConstants.DESCRIPTION, "Please choose from the available slots to schedule a meeting.");
//                    resultMap.put(EmailConstants.PREVIOUS_PROMPT, promptToReturn);
//                    resultMap.put("type", intentResponse.getDurationType());
////                    List<ZonedMeeting> zonedMeetings = DateUtils.convertMeetingsToZonedMeetings(availableMeetingSlots, preferences.getTimeZone());
//                    resultMap.put(EmailConstants.RESULT, availableMeetingSlots);
//                }
//                return resultMap;
//            }
//
//            resultMap.put(EmailConstants.RESULT, intentResponse);
//            if (mailSummaryObject != null) {
//                resultMap.put("subject", mailSummaryObject.getSubject());
//                auditMap.put("type", "MEETING_DRAFT");
//                try {
//                    DraftResponse agenda = draftServiceAI.generateMeetingDraft(promptToReturn, mailSummaryObject.getMessageSummary(), auditMap);
//                    resultMap.put("agenda", agenda.getEmail());
//
//                } catch (Exception e) {
////                        Map<String, String> auditMap1 = Map.of("userPrompt", promptToReturn, "summary", mailSummaryObject.getMessageSummary());
//                    auditLog.error("Error inside @method meeting draft", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), internetMessageId, MEETING_DRAFT, emailId, auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
//                }
//            }
//            MeetingService.adjustEndTime(intentResponse);
//            resultMap.put(EmailConstants.DESCRIPTION, "final object");
//            return resultMap;
//        } catch (Exception e) {
//            auditLog.error("Error inside @method intentBasedSearch1", e.getMessage(), getStackTraceAsString(e), null, internetMessageId, MEETING_FROM_CHAT, emailId, null, null, null, null, userPrompt, null);
//            throw new RuntimeException(e);
//        }
//    }
//
//    private List<LocalTime> getCheckoutTimesOfUsers(List<String> emails) {
//        List<LocalTime> localTimes = new ArrayList<>();
//        for (String email : emails) {
//            EmailUser user = userDao.findByEmail(email);
//            if (user != null) {
//                String userId = user.getUserId();
//                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
//                if (preferences != null && preferences.getCheckout() != null) {
//                    LocalTime toUTCZone = DateUtils.adjustTimeToZone(preferences.getCheckout(), preferences.getTimeZone());
//                    localTimes.add(toUTCZone);
//                }
//            }
//        }
//        return localTimes;
//    }
//
//}
