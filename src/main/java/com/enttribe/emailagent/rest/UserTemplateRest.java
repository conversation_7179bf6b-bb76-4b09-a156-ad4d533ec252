package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.service.UserTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static com.enttribe.emailagent.utils.APIConstants.*;

/**
 * The type User template rest.
 *  <AUTHOR> <PERSON>
 */
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/template")
public class UserTemplateRest {

    @Autowired
    private UserTemplateService templateService;

    //used
    @PostMapping("/add")
    @Operation(summary = "Add new entry", tags = "Template", description = "Adds a new entry using the provided data in the request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_ADD_TEMPLATE}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Entry added successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while adding the entry.")
    })
    public Map<String, String> add(@RequestBody Map<String, String> requestBody) {
        return templateService.add(requestBody);
    }

    @PostMapping("/update")
    @Operation(summary = "Update an existing entry", tags = "Template", description = "Updates an existing entry using the provided data in the request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_UPDATE_TEMPLATE}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Entry updated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the entry.")
    })
    public Map<String, String> update(@RequestBody Map<String, String> requestBody) {
        return templateService.update(requestBody);
    }

    //used
    @GetMapping("/get")
    @Operation(summary = "Retrieve entry by key", tags = "Template", description = "Fetches an entry using the specified key as a query parameter.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_GET_TEMPLATE}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Entry retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the entry.")
    })
    public Map<String, Object> get(@RequestParam(required = false) String key) {
        return templateService.get(key);
    }

    //used
    @GetMapping("/delete")
    @Operation(summary = "Delete entry by key", tags = "Template", description = "Deletes an entry using the specified key as a query parameter.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_DELETE_TEMPLATE}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Entry deleted successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while deleting the entry.")
    })
    public Map<String, String> delete(@RequestParam(required = false) String key) {
        return templateService.delete(key);
    }

}
