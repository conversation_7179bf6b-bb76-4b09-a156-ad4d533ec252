package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.dto.*;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.UserFolders;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.service.EwsService;
import com.enttribe.emailagent.service.GraphIntegrationService;
import com.enttribe.emailagent.service.ImailSummaryService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.ConverterUtils;
import com.enttribe.emailagent.utils.DateUtils;
import com.enttribe.emailagent.utils.EmailConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;

import static com.enttribe.emailagent.utils.APIConstants.*;
import static com.enttribe.emailagent.utils.ExceptionUtils.getStackTraceAsString;

import com.enttribe.emailagent.integration.GmailIntegration;

/**
 * The type Voice rest.
 *  <AUTHOR> Pathak
 */
@RestController("voiceRest")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/emailservice")
@Slf4j
public class VoiceRest {

    @Autowired
    private UserContextHolder userContextHolder;

    @Autowired
    private EwsService ewsService;

    @Autowired
    private IEmailPreferencesDao preferencesDao;

    @Autowired
    private GmailIntegration gmailIntegration;

    @Autowired
    private GraphIntegrationService graphIntegrationService;

    @Autowired
    private ImailSummaryService imailSummaryService;

    @Value("${pollingMode}")
    private String pollingMode;

    @Autowired
    private UserFoldersRepository userFoldersRepository;

    private static final Logger auditLog = EmailAgentLogger.getLogger(VoiceRest.class);

    @PostMapping(path = "/v1/flagEmail")
    @Operation(summary = "Flag email (v1)", tags = "Email", description = "Flag an email using version 1 of the API based on the provided details in the request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_FLAG_EMAIL_V1}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email flagged successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public String flagEmailBot(@RequestBody Map<String, String> body) throws IOException, InterruptedException {
        String email = userContextHolder.getCurrentUser().getEmail();
        String messageId = body.get("messageId");
        String flagStatus = body.get("flagStatus");
        log.debug("Inside @method flagEmailBot. @param : email -> {} messageId -> {} flagStatus -> {}", email, messageId, flagStatus);

        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.flagEmail(email, messageId, flagStatus);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.starAndUnstarEmail(email, messageId, flagStatus) ? "success" : "failed";
        } else {
            String flag = String.format("{\"flag\":{\"flagStatus\":\"%s\"}}", flagStatus);
            return graphIntegrationService.flagEmail(email, messageId, flag);
        }
    }

    @PostMapping(path = "/v3/flagEmail")
    @Operation(summary = "Flag email (v3)", tags = "Email", description = "Flag an email using version 3 of the API based on the database ID of the record.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_FLAG_EMAIL_V3}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email flagged successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> flagEmail(@RequestBody Map<String, Object> body) {
        String email = userContextHolder.getCurrentUser().getEmail();
        Integer id = (Integer) body.get("id");
        String flagStatus = (String) body.get("flagStatus");
        log.debug("Inside @method flagEmail. @param : email -> {} id -> {} flagStatus -> {}", email, id, flagStatus);

        Map<String, String> response = new HashMap<>();
        String result;
        if (pollingMode.equalsIgnoreCase("EWS")) {
            result = ewsService.flagEmail(email, id, flagStatus);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            result = gmailIntegration.starAndUnstarEmail(email, id, flagStatus) ? "success" : "failed";
        } else {
            String flag = String.format("{\"flag\":{\"flagStatus\":\"%s\"}}", flagStatus);
            result = graphIntegrationService.flagEmail(email, id, flag);
        }
        response.put("result", result);
        return response;
    }

    @PostMapping("/createDraft")
    @Operation(summary = "Create draft", tags = "Email", description = "Create a draft email based on the provided request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_CREATE_DRAFT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft created successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public String createDraft(@RequestBody Map<String, String> requestBody) {
        String email;
        try {
            email = userContextHolder.getCurrentUser().getEmail();
        } catch (Exception e) {
            email = requestBody.get("emailUser");
        }

        String subject = requestBody.get("subject");
        String content = requestBody.get("content");
        Boolean sendDraft = Optional.ofNullable(requestBody.get("sendDraft"))
                .filter(value -> !value.isEmpty())
                .map(Boolean::parseBoolean)
                .orElse(true);
        String toEmail = requestBody.get("toEmail");
        String ccEmail = requestBody.get("ccEmail");
        String bccEmail = requestBody.get("bccEmail");
        log.debug("Inside @method createDraft. @param : email -> {} subject -> {}", email, subject);
        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.createDraft(email, subject, content, toEmail, ccEmail, bccEmail, sendDraft);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.createDraft(requestBody);
        } else {
            return graphIntegrationService.createDraft(email, subject, content, toEmail, ccEmail, bccEmail, sendDraft);
        }
    }

    @Deprecated
    @PostMapping("/getAvailableMeetingSlots")
    @Operation(summary = "Get available meeting slots", tags = "Meeting", description = "Retrieve available meeting slots for the specified email addresses within the optional date range.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_GET_AVAILABLE_MEETING_SLOTS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Available meeting slots retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public List<Meeting> getAvailableMeetingSlots(@RequestBody List<String> emails,
                                                  @RequestParam(required = false) String startDateTime,
                                                  @RequestParam(required = false) String endDateTime,
                                                  HttpServletRequest request) throws Exception {
        String email = userContextHolder.getCurrentUser().getEmail();
        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
        String timeZone = preferences.getTimeZone();
        request.setAttribute("timeZone", timeZone);

        if (pollingMode.equalsIgnoreCase("EWS")) {
            startDateTime = DateUtils.convertToUTCString(startDateTime, timeZone);
            endDateTime = DateUtils.convertToUTCString(endDateTime, timeZone);
            return ewsService.getAvailableMeetingSlots(emails, startDateTime, endDateTime, 60);
        }
        if (pollingMode.equalsIgnoreCase("GMAIL")) {
            startDateTime = DateUtils.convertToUTCString(startDateTime, timeZone);
            endDateTime = DateUtils.convertToUTCString(endDateTime, timeZone);
            return gmailIntegration.getAvailableMeetingSlots(emails, startDateTime, endDateTime, 60);
        } else {
            startDateTime = DateUtils.convertToUTCString(startDateTime, timeZone);
            endDateTime = DateUtils.convertToUTCString(endDateTime, timeZone);
            return graphIntegrationService.getAvailableMeetingSlots(emails, startDateTime, endDateTime, 60);
        }
    }

    @PostMapping("/createDraftReply")
    @Operation(summary = "Create draft reply", tags = "Email", description = "Create a draft reply based on the provided request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_CREATE_DRAFT_REPLY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft reply created successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public String createDraftReply(@RequestBody Map<String, String> requestBody) throws Exception {
        String email = userContextHolder.getCurrentUser().getEmail();
        String messageId = requestBody.get("messageId");
        String content = requestBody.get("content");
        String type = requestBody.get("type");
        boolean sendDraft = Boolean.parseBoolean(requestBody.get("sendDraft"));
        String contentType = requestBody.get("contentType");

        log.debug("Inside @method createDraftReply. @param : email -> {} messageId -> {}", email, messageId);
        if (content == null) content = "";

        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.createDraftReply(email, messageId, content, sendDraft, type, contentType);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.createDraftReply(requestBody, true);
        } else {
            return graphIntegrationService.createDraftReply(email, messageId, content, sendDraft, type, contentType);
        }
    }

    @PostMapping("/v1/createDraft")
    @Operation(summary = "Create draft", tags = "Email", description = "Create a draft email based on the provided request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_CREATE_DRAFT_V1}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft created successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> createDraftV1(@RequestBody Map<String, String> requestBody) {
        String email = userContextHolder.getCurrentUser().getEmail();
        String subject = requestBody.get("subject");
        String content = requestBody.get("content");
        Boolean sendDraft = Optional.ofNullable(requestBody.get("sendDraft"))
                .filter(value -> !value.isEmpty())
                .map(Boolean::parseBoolean)
                .orElse(true);
        String toEmail = requestBody.get("toEmail");
        String ccEmail = requestBody.get("ccEmail");
        String bccEmail = requestBody.get("bccEmail");

        Map<String, String> response = new HashMap<>();
        String result = null;
        log.debug("Inside @method createDraft. @param : email -> {} subject -> {}", email, subject);
        if (pollingMode.equalsIgnoreCase("EWS")) {
            result = ewsService.createDraft(email, subject, content, toEmail, ccEmail, bccEmail, sendDraft);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            result = gmailIntegration.createDraft(requestBody);
        } else {
            result = graphIntegrationService.createDraft(email, subject, content, toEmail, ccEmail, bccEmail, sendDraft);
        }
        response.put("result", result);
        return response;
    }

    @PostMapping("/v1/createDraftReply")
    @Operation(summary = "Create draft reply", tags = "Email", description = "Create a draft reply based on the provided request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_CREATE_DRAFT_REPLY_V1}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft reply created successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> createDraftReplyV1(@RequestBody Map<String, String> requestBody) throws Exception {
        String email = userContextHolder.getCurrentUser().getEmail();
        String messageId = requestBody.get("messageId");
        if (pollingMode.equalsIgnoreCase("EWS")) {
            messageId = messageId.replace(" ", "+");
            messageId = messageId.replace("_", "+");
            messageId = messageId.replace("-", "/");
        } else {
            messageId = messageId.replace("/", "-");
        }
        String content = requestBody.get("content");
        String type = requestBody.get("type");
        String contentType = "HTML";// Only meant for plugin
        boolean sendDraft = Boolean.parseBoolean(requestBody.get("sendDraft"));

        log.debug("Inside @method createDraftReply. @param : email -> {} messageId -> {}", email, messageId);
        if (content == null) content = "";

        Map<String, String> response = new HashMap<>();
        String result = null;
        if (pollingMode.equalsIgnoreCase("EWS")) {
            result = ewsService.createDraftReply(email, messageId, content, sendDraft, type, contentType);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            result = gmailIntegration.createDraftReply(requestBody, true);
        } else {
            result = graphIntegrationService.createDraftReply(email, messageId, content, sendDraft, type, contentType);
        }
        response.put("result", result);
        return response;
    }

    @PostMapping("/v1/createDraftForward")
    @Operation(summary = "Create draft forward", tags = "Email", description = "Create a draft forward based on the provided request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_CREATE_DRAFT_FORWARD_V1}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft forward created successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> createDraftForwardV1(@RequestBody Map<String, Object> requestBody) throws Exception {
        String email = userContextHolder.getCurrentUser().getEmail();
        String messageId = (String) requestBody.get("messageId");
        String comment = (String) requestBody.get("comment");
        String toRecipients = String.join(",", (List<String>) requestBody.get("toRecipients"));
        String ccRecipients = String.join(",", (List<String>) requestBody.get("ccRecipients"));
        boolean sendDraft = (Boolean) requestBody.get("sendDraft");

        if (pollingMode.equalsIgnoreCase("EWS")) {
            messageId = messageId.replace(" ", "+");
            messageId = messageId.replace("_", "+");
            messageId = messageId.replace("-", "/");
        } else {
            messageId = messageId.replace("/", "-");
        }

        log.debug("Inside @method createDraftForward. @param: email -> {} messageId -> {}", email, messageId);

        Map<String, String> response = new HashMap<>();
        String result;

        if (pollingMode.equalsIgnoreCase("EWS")) {
            result = ewsService.createDraftForward(email, messageId, comment, toRecipients, ccRecipients, sendDraft);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            result = gmailIntegration.forwardEmail(messageId, email, comment, toRecipients);
        } else {
            result = graphIntegrationService.createDraftForward(email, messageId, comment, toRecipients, ccRecipients, sendDraft);
        }

        response.put("result", result);
        return response;
    }

    @PostMapping("/getMailSummaries")
    @Operation(summary = "Get mail summaries within a date range", tags = "Mail Summary", description = "Retrieve the list of mail summaries between the specified start and end date.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_GET_MAIL_SUMMARIES}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Mail summaries retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getMailSummaries(@RequestBody Map<String, String> requestBody) throws URISyntaxException {
        String startDateStr = requestBody.get("startDate");
        String endDateStr = requestBody.get("endDate");
        UserInfo userInfo = userContextHolder.getCurrentUser();
        String email = userInfo.getEmail();

        log.debug("Inside @method getMailSummaries. @param : email -> {} startDate -> {} endDate -> {}", email, startDateStr, endDateStr);

        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
        List<MailSummary> mailSummaries = imailSummaryService.getMailSummaries(email, startDateStr, endDateStr, preferences.getTimeZone());
        imailSummaryService.addAttachmentDetails(mailSummaries);
        List<MailSummaryDto> mailSummaryDtos = ConverterUtils.convertToMailSummaryDtoList(mailSummaries);

        List<EventDto> calendarEvents;
        if (pollingMode.equalsIgnoreCase("EWS")) {
            calendarEvents = ewsService.getCalendarEvents(email, null, null, false);
        } else if (pollingMode.equalsIgnoreCase("Gmail")) {
            calendarEvents = gmailIntegration.getCalendarEvents(email, null, null);
        } else {
            calendarEvents = graphIntegrationService.getCalendarEventsV1(email, null, null, null);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("mails", mailSummaryDtos);
        response.put("todayEvents", calendarEvents);
        return response;
    }

    @PostMapping("/getAllSummaryForToday")
    @Operation(summary = "Get all summaries for today", tags = "Summary", description = "Retrieve all summaries for today based on the provided request data.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_GET_ALL_SUMMARY_FOR_TODAY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Day summaries retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getDaySummaryForBot(@RequestBody Map<String, String> requestBody,
                                                   HttpServletRequest request) {
        log.debug("Inside getDaySummaryForBot");
        List<DaySummary> message = new ArrayList<>();
        String email = userContextHolder.getCurrentUser().getEmail();
        try {
            String date = requestBody.get("date");
            log.debug("Inside @method getAllSummaryForToday. @param : email -> {} date -> {}", email, date);

            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            request.setAttribute("timeZone", preferences.getTimeZone());

            List<UserFolders> activeMailFolders = userFoldersRepository.getActiveMailFoldersByEmail(email);

            message = imailSummaryService.getDaySummary(email, email, date);
            log.debug("Day Summary count {} ", message.size());
            final List<UnReadEmail> unreadEmails = new ArrayList<>();

            if (!message.isEmpty()) {
                log.debug("Polling Unread emails for user {}", email);

                if (activeMailFolders != null && !activeMailFolders.isEmpty()) {
                    activeMailFolders.stream()
                            .filter(userFolder -> !userFolder.getDisplayName().equalsIgnoreCase("Sent Items")
                                    && !userFolder.getDisplayName().equalsIgnoreCase("Deleted Items"))
                            .flatMap(userFolder -> {
                                if (pollingMode.equalsIgnoreCase("EWS")) {
                                    return ewsService.getUnreadEmails(email, userFolder.getFolderId()).stream();
                                } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                                    return gmailIntegration.getUnreadEmails(email, userFolder.getFolderId()).stream();
                                } else {
                                    return graphIntegrationService.getUnreadEmails(email, userFolder.getFolderId()).stream();
                                }
                            })
                            .forEach(unreadEmails::add);
                }
            }
            log.debug("Unread email for user {} count {} ", email, unreadEmails.size());

            message.removeIf(Objects::isNull);

            log.debug("Messages email for user {} count {} ", email, message.size());

            if (!unreadEmails.isEmpty() && !message.isEmpty()) {
                log.debug("Processing {} unread emails for {} day summaries", unreadEmails.size(), message.size());

                message.stream()
                        .filter(daySummary -> daySummary.getMailObject() != null)
                        .forEach(daySummary -> {
                            List<MailSummary> mailSummaries = daySummary.getMailObject();
                            log.debug("Processing day summary with {} mail summaries", mailSummaries.size());

                            mailSummaries.stream()
                                    .forEach(mailSummary -> {
                                        log.debug("Checking mail summary with internetMessageId: {}", mailSummary.getInternetMessageId());

                                        boolean isUnread = unreadEmails.stream()
                                                .anyMatch(unreadEmail -> {
                                                    boolean matches = mailSummary.getInternetMessageId().equals(unreadEmail.getInternetMessageId());
                                                    if (matches) {
                                                        log.debug("Match found for internetMessageId: {}", unreadEmail.getInternetMessageId());
                                                    }
                                                    return matches;
                                                });

                                        if (isUnread) {
                                            mailSummary.setIsUnread(true);
                                            log.debug("Marked email as unread. internetMessageId: {}", mailSummary.getInternetMessageId());
                                        } else {
                                            log.debug("No match found for mail summary with internetMessageId: {}", mailSummary.getInternetMessageId());
                                        }
                                    });
                        });
            } else {
                log.debug("No unread emails or day summaries to process.");
            }

            log.debug("Checking calendar events for user {} ", email);

            List<EventDto> calendarEvents;
            if (pollingMode.equalsIgnoreCase("EWS")) {
                calendarEvents = ewsService.getCalendarEvents(email, null, null, false);
            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                calendarEvents = gmailIntegration.getCalendarEvents(email, null, null);
            } else {
                calendarEvents = graphIntegrationService.getCalendarEvents(email, null, null, false);
            }
            calendarEvents.forEach(event -> event.setBodyPreview(null));
            DaySummary daySummary = getTodayEvents(calendarEvents);

            message.add(daySummary);
        } catch (Exception e) {
            log.error("Error while getting summary. Exception message: {}", e.getMessage());
        }

        message.removeIf(Objects::isNull);

        Map<String, Object> obj = new HashMap<>();
        obj.put(EmailConstants.RESULT, message);
        obj.put("userEmail", email);
        return obj;
    }

    @PostMapping("/getAllSummaryBetweenDate")
    @Operation(summary = "Get all summaries between dates", tags = "Summary", description = "Retrieve all summaries between the specified start and end dates based on the provided request data.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_GET_ALL_SUMMARY_BETWEEN_DATE}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Summaries retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getAllSummaryBetweenDate(@RequestBody Map<String, String> requestBody,
                                                        HttpServletRequest request) {
        log.debug("Inside getAllSummaryBetweenDate");
        Map<String, Object> obj = new HashMap<>();
        String email = userContextHolder.getCurrentUser().getEmail();
        String startDate = requestBody.get("startDate");
        String endDate = requestBody.get("endDate");
        log.debug("Inside @method getAllSummaryBetweenDate. @param : email -> {} startDate -> {} endDate -> {}", email, startDate, endDate);

        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
        request.setAttribute("timeZone", preferences.getTimeZone());

        List<DaySummary> message = imailSummaryService.getDaySummaryBetweenDates(email, email, startDate, endDate);
        try {
            List<EventDto> calendarEvents;
            if (pollingMode.equalsIgnoreCase("EWS")) {
                calendarEvents = ewsService.getCalendarEvents(email, startDate, endDate, false);
            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                calendarEvents = gmailIntegration.getCalendarEvents(email, startDate, endDate);
            } else {
                calendarEvents = graphIntegrationService.getCalendarEvents(email, startDate, endDate, false);
            }
            DaySummary daySummary = getTodayEvents(calendarEvents);

            message.add(daySummary);

        } catch (Exception e) {
            log.error("Error while adding calendar events. Exception message: {}", e.getMessage());
        }
        message.removeIf(Objects::isNull);
        obj.put(EmailConstants.RESULT, message);
        obj.put("userEmail", email);
        return obj;
    }

    @PostMapping("/getDocSummary")
    @Operation(summary = "Get document summary", tags = "Document", description = "Retrieve both long and short summaries for a document based on the provided details.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_GET_DOC_SUMMARY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Document summaries retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public AttachmentsDto getLongAndShortSummaryForDocument(@RequestBody Map<String, String> body) {
        String docId = body.get("docId");
        log.debug("Inside @method getLongAndShortSummaryForDocument. @param : docId -> {}", docId);
        return graphIntegrationService.getLongAndShortSummaryForDocument(docId);
    }

    @PostMapping("/searchContacts")
    @Operation(summary = "Search contacts", tags = "Contacts", description = "Retrieve contact EmailIds from contact books.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_SEARCH_CONTACTS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of emailIds retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public List<String> searchContacts(@RequestBody Map<String, String> body) {
        String contactName = body.get("contactName");
        log.debug("Inside @method searchContacts. @param : contactName -> {}", contactName);
        return graphIntegrationService.searchContact(contactName);
    }

    @PostMapping("/searchMultipleContacts")
    @Operation(summary = "Search multiple contacts", tags = "Contacts", description = "Retrieve contact EmailIds from contact books for multiple contacts.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_SEARCH_MULTIPLE_CONTACTS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of emailIds retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, List<String>> searchMultipleContacts(@RequestBody List<String> emails) {
        log.debug("Inside @method searchMultipleContacts. @param : emails -> {}", emails);
        return graphIntegrationService.searchMultipleContacts(emails);
    }

    @PostMapping("/getAnswerFromAttachment")
    @Operation(summary = "Get answer from attachment", tags = "Attachment", description = "Retrieve an answer based on the provided attachment details.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_GET_ANSWER_FROM_ATTACHMENT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Answer retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> getAnswerFromAttachment(@RequestBody Map<String, String> requestBody) {
        try {
            String docId = requestBody.get("docId");
            String question = requestBody.get("question");
            String answerFromAttachment = graphIntegrationService.getAnswerFromAttachment(docId, question);
            return Map.of("answer", answerFromAttachment);
        } catch (Exception e) {
            log.error("Error inside @method getAnswerFromAttachment", e);
            auditLog.error("Error while getting answer from attachment", e.getMessage(), getStackTraceAsString(e), null, null, "VOICE_ATTACHMENT_ANSWER", userContextHolder.getCurrentUser().getEmail(), null, null, null, null, requestBody, null);
            return Map.of("answer", "Failed to get Answer");
        }
    }

    @PostMapping(path = "/v1/scheduleEvent")
    @Operation(summary = "Schedule event (v1)", tags = "Event", description = "Schedule an event based on the provided request body using version 1 of the API.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_SCHEDULE_EVENT_V1}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event scheduled successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public String scheduleEventV1(@RequestBody Map<String, Object> jsonBody) {
        String email = userContextHolder.getCurrentUser().getEmail();

        String timeZone = (String) jsonBody.get(EmailConstants.TIME_ZONE);
        if (timeZone == null || timeZone.isBlank()) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            timeZone = preferences.getTimeZone();
            jsonBody.put(EmailConstants.TIME_ZONE, timeZone);
        }

        String meetingType = (String) jsonBody.get("meetingType");
        if (meetingType == null || meetingType.isBlank()) {
            jsonBody.put("meetingType", "Teams");
        }

        log.debug("Inside @method /v1/scheduleEvent. @param : email -> {}", email);
        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.scheduleEvent(email, jsonBody);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.createMeetingEvent(email, jsonBody);
        } else {
            String meetingRequestJson = CommonUtils.convertToMeetingRequestJson(jsonBody, email);
            Map<String,Object> result = graphIntegrationService.scheduleEvent(email, meetingRequestJson);
            return (String) result.get(EmailConstants.RESULT);
        }
    }

    @PostMapping(path = "/v1/updateExistingEvent")
    @Operation(summary = "Update Existing Event (v1)", tags = "Event", description = "Update an existing event based on the provided request body using version 1 of the API.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_UPDATE_EXISTING_EVENT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event updated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public String updateExistingEvent(@RequestBody Map<String, Object> jsonBody) {
        String email = userContextHolder.getCurrentUser().getEmail();

        String timeZone = (String) jsonBody.get(EmailConstants.TIME_ZONE);
        if (timeZone == null || timeZone.isBlank()) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            timeZone = preferences.getTimeZone();
            jsonBody.put(EmailConstants.TIME_ZONE, timeZone);
        }

        String meetingType = (String) jsonBody.get("meetingType");
        if (meetingType == null || meetingType.isBlank()) {
            jsonBody.put("meetingType", "Teams");
        }

        String eventId = (String) jsonBody.get("eventId");

        log.debug("Inside @method /v1/updateExistingEvent. @param : email -> {}", email);
        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.scheduleEvent(email, jsonBody);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.createMeetingEvent(email, jsonBody);
        } else {
            String meetingRequestJson = CommonUtils.convertToMeetingRequestForUpdateEvent(jsonBody, email);
            Map<String,Object> result = graphIntegrationService.updateEvent(eventId, email, meetingRequestJson);
            return (String) result.get(EmailConstants.RESULT);
        }
    }

    @PostMapping("/v3/getDaySummary")
    @Operation(summary = "Get day summary (v3)", tags = "Summary", description = "Retrieve the summary for the current day, optionally filtered by the specified type and date, using version 3 of the API.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_GET_DAY_SUMMARY_V3}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Day summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getDaySummaryV2(@RequestBody Map<String, String> request) {
        String type = request.get("type");
        String date = request.get("date");
        UserInfo userInfo = userContextHolder.getCurrentUser();
        String email = userInfo.getEmail();
        log.debug("Inside @method getDaySummaryV3. @param : email -> {} type -> {} date -> {}", email, type, date);
        List<DaySummary> message = imailSummaryService.getDaySummaryV2(email, email, type, date);
        List<MailSummary> messageList = imailSummaryService.getDaySummaryV3(email, type, date);

        if (type == null) type = EmailConstants.TODAY;
        if (type.equalsIgnoreCase(EmailConstants.TODAY)) {
            try {
                List<EventDto> calendarEvents;
                if (pollingMode.equalsIgnoreCase("EWS")) {
                    calendarEvents = ewsService.getCalendarEvents(email, null, null, false);
                } else if (pollingMode.equalsIgnoreCase("Gmail")) {
                    log.info("inside getCalendarEvents for Gmail");
                    calendarEvents = gmailIntegration.getCalendarEvents(email, null, null);
                } else {
                    calendarEvents = graphIntegrationService.getCalendarEventsV1(email, null, null, null);
                }
                DaySummary daySummary = getTodayEvents(calendarEvents);

                message.add(daySummary);
            } catch (Exception e) {
                log.error("Error while adding calendar events. Exception message  : {}", e.getMessage());
            }
        }
        message.removeIf(Objects::isNull);
        long emailCount = imailSummaryService.countAllMailOfUser(email, type, date);
        Map<String, Object> obj = new HashMap<>();
        obj.put(EmailConstants.RESULT, message);
        obj.put("totalEmailCount", emailCount);
        obj.put("mailList", messageList);
        return obj;
    }

    @PostMapping("/sendDraft")
    @Operation(summary = "Send Draft Email", tags = "Email", description = "Send a draft email based on the provided messageId.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_SEND_DRAFT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft email sent successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> sendDraft(@RequestBody Map<String, String> requestBody) throws Exception {
        String email = userContextHolder.getCurrentUser().getEmail();
        String messageId = requestBody.get("messageId");

        if (pollingMode.equalsIgnoreCase("EWS")) {
            messageId = messageId.replace(" ", "+");
            messageId = messageId.replace("_", "+");
            messageId = messageId.replace("-", "/");
        } else {
            messageId = messageId.replace("/", "-");
        }

        log.debug("Inside @method sendDraft. @param: email -> {}, messageId -> {}", email, messageId);

        Map<String, String> response = new HashMap<>();
        String result;

        if (pollingMode.equalsIgnoreCase("EWS")) {
            result = ewsService.sendDraft(email, messageId);
        } else {
            result = graphIntegrationService.sendDraft(email, messageId);
        }

        response.put("result", result);
        return response;
    }

    private DaySummary getTodayEvents(List<EventDto> calendarEvents) {
        DaySummary daySummary = new DaySummary();
        if (calendarEvents == null || calendarEvents.isEmpty()) {
            daySummary.setContent(EmailConstants.getMeetingMessage());
            daySummary.setMeetings(null);
        } else if (calendarEvents.size() == 1) {
            daySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + calendarEvents.size() + " meeting</span> remaining today");
            daySummary.setAction("Meeting");
            daySummary.setMeetings(calendarEvents);
        } else {
            daySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + calendarEvents.size() + " meetings</span> remaining today");
            daySummary.setAction("Meeting");
            daySummary.setMeetings(calendarEvents);
        }
        return daySummary;
    }
}
