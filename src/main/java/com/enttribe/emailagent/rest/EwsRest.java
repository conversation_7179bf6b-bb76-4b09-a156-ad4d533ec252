package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.masking.DataMasking;
import com.enttribe.emailagent.service.EwsService;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.enttribe.emailagent.utils.AESDecryption;
import com.enttribe.emailagent.utils.APIConstants;
import com.enttribe.emailagent.utils.EWSUtils;
import com.enttribe.emailagent.utils.EmailConstants;
import com.enttribe.emailagent.utils.EwsTokenUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.enumeration.property.WellKnownFolderName;
import microsoft.exchange.webservices.data.core.service.folder.Folder;
import microsoft.exchange.webservices.data.core.service.item.Item;
import microsoft.exchange.webservices.data.property.complex.FolderId;
import microsoft.exchange.webservices.data.search.FindFoldersResults;
import microsoft.exchange.webservices.data.search.FindItemsResults;
import microsoft.exchange.webservices.data.search.FolderView;
import org.json.JSONObject;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Ews rest.
 *  <AUTHOR> Sonsale
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/ews")
@Slf4j
public class EwsRest {

    private final EwsService ewsService;

    @PostMapping("/login")
    @Operation(summary = "User Login", tags = "Authentication", description = "Authenticates the user by verifying the username and password and generates an access token if valid.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_EWS_LOGIN})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully authenticated the user and generated a token."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred during authentication.")
    })
    public Map<String, String> login(@RequestBody Map<String, String> request) {
        Map<String,String> responseMap = new HashMap<>();
        try {
           String username=request.get("username");
           String password=request.get("password");
           ExchangeService service= EWSUtils.getServiceObjectForLogin(AESDecryption.decrypt(username), AESDecryption.decrypt(password));
            if(service!=null){
                System.out.println("Service is not null");
                FolderId inboxFolderId = new FolderId(WellKnownFolderName.Inbox);
                Folder mailFolder = Folder.bind(service, inboxFolderId);
                if (mailFolder!=null) {
                    System.out.println("mailFolder is not null");
                    String token = EwsTokenUtils.generateAccessToken(AESDecryption.decrypt(username));
                    responseMap.put("token", token);
                    responseMap.put("status", "Authenticated");
                }
                else{
                    responseMap.put("status", "UnAuthorised");
                }
            }
            else {
                responseMap.put("status", "UnAuthorised");
            }
        } catch (Exception e) {
            log.error("Error while authentication", e);
            responseMap.put("status", "UnAuthorised");
        }
        return responseMap;
    }

    @PostMapping("/refresh-token")
    @Operation(summary = "Refresh Access Token", tags = "Authentication", description = "Generates a new refresh token using the provided access token.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_EWS_REFRESH_TOKEN})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully generated a new refresh token."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while generating refresh token.")
    })
    public Map<String,String> refreshToken(@RequestBody Map<String, String> request) {
        Map<String,String> responseMap = new HashMap<>();
        try {
            String accessToken=request.get("access_token");
            Claims claims = EwsTokenUtils.parseToken(accessToken);
            String username = claims.getSubject();

            String refreshToken = EwsTokenUtils.generateRefreshToken(username);
            responseMap.put("refreshToken", refreshToken);
            responseMap.put("status", "success");

        } catch (Exception e) {
           log.error("Error while generating referesh token", e);
            responseMap.put("status", "failed");
        }
        return responseMap;
    }

    @PostMapping("/introspect")
    @Operation(summary = "Introspect Token", tags = "Authentication", description = "Validates and retrieves information about the provided access token.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_EWS_INTROSPECT})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully introspected the token and returned its details."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while introspecting the token.")
    })
    public Map<String, Object> introspectToken(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();

        try {
            String token=request.get("access_token");
            Claims claims = EwsTokenUtils.parseToken(token);

            response.put("active", true);
            response.put("username", claims.getSubject());
            response.put("expiration", claims.getExpiration());
            // Add more token details as needed

        } catch (ExpiredJwtException e) {
            response.put("active", false);
            response.put("error", "Token expired");
        } catch (Exception e) {
            response.put("active", false);
            response.put("error", "Invalid token");
        }

        return response;
    }

}
