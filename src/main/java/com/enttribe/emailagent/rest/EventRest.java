// package com.enttribe.emailagent.rest;


// import com.enttribe.emailagent.dto.DaySummary;
// import com.enttribe.emailagent.dto.EventDto;
// import com.enttribe.emailagent.dto.Meeting;
// import com.enttribe.emailagent.entity.EmailPreferences;
// import com.enttribe.emailagent.entity.MailSummary;
// import com.enttribe.emailagent.integration.GmailIntegration;
// import com.enttribe.emailagent.repository.IEmailPreferencesDao;
// import com.enttribe.emailagent.repository.IMailSummaryDao;
// import com.enttribe.emailagent.service.EwsService;
// import com.enttribe.emailagent.service.GraphIntegrationService;
// import com.enttribe.emailagent.userinfo.UserContextHolder;
// import com.enttribe.emailagent.userinfo.UserInfo;
// import com.enttribe.emailagent.utils.DateUtils;
// import com.enttribe.emailagent.utils.EmailConstants;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import lombok.RequiredArgsConstructor;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RestController;

// import java.time.LocalDate;
// import java.time.LocalDateTime;
// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import java.util.Objects;
// import java.util.Optional;

// /**
//  * The type Event rest.
//  *  <AUTHOR> Pathak
//  */
// @RestController
// @RequiredArgsConstructor
// @RequestMapping("/events")
// @Slf4j
// public class EventRest {

//     private final UserContextHolder userContextHolder;
//     private final IEmailPreferencesDao preferencesDao;
//     private final EwsService ewsService;
//     private final GmailIntegration gmailIntegration;
//     private  final IMailSummaryDao mailSummaryDao;
//     private final GraphIntegrationService graphIntegrationService;

//     @Value("${pollingMode}")
//     private String pollingMode;

// //    /v1/getAvailableMeetingSlots
//     @PostMapping("/v1/getAvailableMeetingSlots")
//     @Operation(summary = "Get available meeting slots (v1)", tags = "Event", description = "Retrieve available meeting slots for the specified email addresses within the optional date range.")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Available meeting slots retrieved successfully."),
//             @ApiResponse(responseCode = "500", description = "Internal server error.")
//     })
//     public List<Meeting> getAvailableMeetingSlotsV1(@RequestBody Map<String, Object> request) {

//         List<String> emails = (List<String>) request.get("requiredAttendees");
//         String startDateTime = (String) request.get("startTime");
//         String endDateTime = (String) request.get("endTime");

//         if (startDateTime != null && !startDateTime.endsWith("Z")) {
//             EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userContextHolder.getCurrentUser().getEmail());
//             startDateTime = DateUtils.convertToUTCString(startDateTime, preferences.getTimeZone());
//             endDateTime = DateUtils.convertToUTCString(endDateTime, preferences.getTimeZone());
//         }

//         if (pollingMode.equalsIgnoreCase("EWS")) {
//             return ewsService.getAvailableMeetingSlots(emails, startDateTime, endDateTime, 60);
//         } else {

//             return graphIntegrationService.getAvailableMeetingSlots(emails, startDateTime, endDateTime, 60);
//         }
//     }

// //    /v1/getCalendarEventById
//     @PostMapping(path = "/getCalendarEventById")
//     @Operation(summary = "Get calendar event by ID (v1)", tags = "Event", description = "Retrieve calendar event details by the specified email ID.")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Event retrieved successfully."),
//             @ApiResponse(responseCode = "500", description = "Internal server error.")
//     })
//     public List<EventDto> getCalendarEventById(@RequestBody Map<String, String> request) {
//         String email = userContextHolder.getCurrentUser().getEmail();
//         String id = request.get("id");
//         String internetMessageId = request.get("internetMessageId");
//         log.debug("Inside @method getCalendarEventByIdV1. @param : email -> {} id -> {}", email, id);
//         if (pollingMode.equalsIgnoreCase("EWS")) {
//             return ewsService.getCalendarEventById(email, id);
//         } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
//             internetMessageId = internetMessageId.replace(" ", "+");
//             return gmailIntegration.getCalendarEventsByMessageId(email, Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, email)).map(MailSummary::getMessageId).orElse(id));
//         } else {
//             return graphIntegrationService.getCalendarEventById(email, id);
//         }
//     }

// //    /v3/getAvailableMeetingSlots
//     @PostMapping("/getAvailableMeetingSlots")
//     @Operation(summary = "Get available meeting slots", tags = "Event", description = "Retrieve available meeting slots for the specified email addresses within the optional date range. This api also factors in OUT OF OFFICE.")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Available meeting slots retrieved successfully."),
//             @ApiResponse(responseCode = "500", description = "Internal server error.")
//     })
//     public List<Meeting> getAvailableMeetingSlots(@RequestBody Map<String, Object> request) {
//         log.debug("Inside @method getAvailableMeetingSlots. @param : {}", request);
//         List<String> emails = (List<String>) request.get("participants");

//         String startDateTime = (String) request.get("startDateTime");
//         Integer slotDuration = (Integer) request.get("slotDuration");

//         String meetingDate = startDateTime.split("T")[0];

//         List<LocalDateTime> checkoutTimesOfUsers = getCheckoutTimesOfUsers(emails, meetingDate);
//         LocalDateTime earliestTime = DateUtils.getEarliestLocalDateTime(checkoutTimesOfUsers);
//         String startTime, endTime;
//         if (DateUtils.isToday(meetingDate)) {
//             startTime = DateUtils.getUTCDateStringWithOffset();
//         } else {
//             List<LocalDateTime> checkinTimesOfUsers = getCheckInTimesOfUsers(emails, meetingDate);
//             LocalDateTime latestTime = DateUtils.getLatestLocalDateTime(checkinTimesOfUsers);
//             startTime = DateUtils.getUTCStringFromLocalDateTime(latestTime);
//         }
//         endTime = DateUtils.getUTCStringFromLocalDateTime(earliestTime);


//         if (pollingMode.equalsIgnoreCase("EWS")) {
//             return ewsService.getAvailableMeetingSlotsOOF(emails, startTime, endTime, slotDuration);
//         } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
//             return gmailIntegration.getAvailableMeetingSlotsOFF(emails, startTime, endTime, slotDuration);
//         } else {
//             return graphIntegrationService.getAvailableMeetingSlotsOOF(emails, startTime, endTime, slotDuration);
//         }
//     }

//     @PostMapping(path = "/getCalendarEvents")
//     @Operation(summary = "Get calendar events", tags = "Calendar", description = "Retrieve calendar events based on the details provided in the request body.")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Calendar events retrieved successfully."),
//             @ApiResponse(responseCode = "500", description = "Internal server error.")
//     })
//     public Map<String, Object> getCalendarEvents(@RequestBody Map<String, String> request) throws Exception {

//         String startDateTime = request.get("startDateTime");
//         String endDateTime = request.get("endDateTime");

//         List<EventDto> calendarEvents;
//         String email = userContextHolder.getCurrentUser().getEmail();
//         if (pollingMode.equalsIgnoreCase("EWS")) {
//             calendarEvents = ewsService.getCalendarEvents(email, startDateTime, endDateTime, false);
//         } else {
//             calendarEvents = graphIntegrationService.getCalendarEventsV1(email, startDateTime, endDateTime,null);
//         }
//         Map<String, Object> response = new HashMap<>();
//         response.put("meetingCount", calendarEvents.size());
//         response.put("meetings", calendarEvents);
//         return response;
//     }

// //    /v1/getUpcomingEvents
//     @GetMapping("/getUpcomingEvents")
//     @Operation(summary = "Get upcoming events", tags = "Event", description = "Retrieve a list of upcoming events.")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Upcoming events retrieved successfully."),
//             @ApiResponse(responseCode = "500", description = "Internal server error.")
//     })
//     public Map<String, Object> getUpcomingEvents() {
//         UserInfo userInfo = userContextHolder.getCurrentUser();
//         String email = userInfo.getEmail();
//         List<DaySummary> message = new ArrayList<DaySummary>();
//         Integer totalMeetingCount = null;
//         try {

//             EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
//             String startDateTime = DateUtils.getUTCString(true, 0, preferences.getTimeZone());
//             String endDateTime = DateUtils.getUTCString(false, 6, preferences.getTimeZone());

//             log.info("start time is {}", startDateTime);
//             log.info("end time is {}", endDateTime);
//             List<EventDto> calendarEvents;
//             if (pollingMode.equalsIgnoreCase("EWS")) {
//                 calendarEvents = ewsService.getCalendarEvents(email, startDateTime, endDateTime, false);
//             } else if (pollingMode.equalsIgnoreCase("Gmail")) {
//                 log.info("inside get upcoming events for Gmail");
//                 calendarEvents = gmailIntegration.getCalendarEvents(email, startDateTime, endDateTime);
//             } else {
//                 calendarEvents = graphIntegrationService.getCalendarEventsV1(email, startDateTime, endDateTime,null);
//             }
//             totalMeetingCount = calendarEvents.size();
//             DaySummary daySummary = getUpcomingEvents(calendarEvents);

//             message.add(daySummary);
//         } catch (Exception e) {
//             log.error("Error while adding calendar events. Exception message  : {}", e.getMessage());
//         }
//         message.removeIf(Objects::isNull);
//         Map<String, Object> obj = new HashMap<>();
//         obj.put(EmailConstants.RESULT, message);
//         obj.put("totalMeetingCount", totalMeetingCount);
//         return obj;
//     }

//     private DaySummary getUpcomingEvents(List<EventDto> calendarEvents) {
//         DaySummary daySummary = new DaySummary();
//         if (calendarEvents == null || calendarEvents.isEmpty()) {
//             daySummary.setContent(EmailConstants.getMeetingMessage());
//             daySummary.setMobileContent(EmailConstants.getMeetingMessage());
//             daySummary.setValue(0);
//             daySummary.setMeetings(null);
//         } else if (calendarEvents.size() == 1) {
//             daySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + calendarEvents.size() + " meeting</span> scheduled for next week.");
//             daySummary.setMobileContent("Meeting scheduled for next week");
//             daySummary.setAction("Meeting");
//             daySummary.setValue(calendarEvents.size());
//             daySummary.setMeetings(calendarEvents);
//         } else {
//             daySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + calendarEvents.size() + " meetings</span> scheduled for next week");
//             daySummary.setMobileContent("Meetings scheduled for next week");
//             daySummary.setValue(calendarEvents.size());
//             daySummary.setAction("Meeting");
//             daySummary.setMeetings(calendarEvents);
//         }
//         return daySummary;
//     }


//     private List<LocalDateTime> getCheckInTimesOfUsers(List<String> emails, String meetingDate) {
//         if (meetingDate == null || meetingDate.isBlank()) meetingDate = LocalDate.now().toString();
//         List<LocalDateTime> localTimes = new ArrayList<>();
//         for (String email : emails) {
//             EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
//             if (preferences != null && preferences.getCheckout() != null) {
//                 //Here we convert user's checkout time to utc local time
//                 LocalDateTime toUTCZone = DateUtils.parseDateToUTC(meetingDate, preferences.getTimeZone(), preferences.getCheckin());
//                 localTimes.add(toUTCZone);
//             }

//         }
//         return localTimes;
//     }

//     private List<LocalDateTime> getCheckoutTimesOfUsers(List<String> emails, String meetingDate) {
//         if (meetingDate == null || meetingDate.isBlank()) meetingDate = LocalDate.now().toString();
//         List<LocalDateTime> localTimes = new ArrayList<>();
//         for (String email : emails) {
//             EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
//             if (preferences != null && preferences.getCheckout() != null) {
//                 try {
//                     //Here we convert user's checkout time to utc local time
//                     LocalDateTime toUTCZone = DateUtils.parseDateToUTC(meetingDate, preferences.getTimeZone(), preferences.getCheckout());
//                     localTimes.add(toUTCZone);
//                 } catch (Exception e) {
//                     log.error("Error inside @method getCheckoutTimesOfUsers. @param : emails -> {} meetingDate -> {}", emails, meetingDate);
//                     log.error("Error while getting getCheckoutTimesOfUsers", e);
//                 }
//             }

//         }
//         return localTimes;
//     }

// }
