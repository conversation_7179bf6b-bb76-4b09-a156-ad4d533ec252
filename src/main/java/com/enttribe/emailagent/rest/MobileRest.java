package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.UserMailAttachment;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.service.EwsService;
import com.enttribe.emailagent.service.GraphIntegrationService;
import com.enttribe.emailagent.service.UserMailAttachmentService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.EmailConstants;
import com.enttribe.emailagent.utils.S3Service;
import com.enttribe.emailagent.wrapper.MessageWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import static com.enttribe.emailagent.utils.CommonUtils.EWS;
import static com.enttribe.emailagent.utils.CommonUtils.GMAIL;
import static com.enttribe.emailagent.utils.APIConstants.*;


/**
 * The type Mobile rest.
 *  <AUTHOR> Dangi
 */
@RestController("mobile")
@Slf4j
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/mobile")
public class MobileRest {

    @Autowired
    private UserContextHolder userContextHolder;

    @Value("${pollingMode}")
    private String pollingMode;

    @Autowired
    private EwsService ewsService;

    @Autowired
    private GmailIntegration gmailIntegration;

    @Autowired
    private GraphIntegrationService graphIntegrationService;

    @Autowired
    private IMailSummaryDao mailSummaryDao;

    @Autowired
    private S3Service s3Service;

    @Autowired
    UserMailAttachmentService userMailAttachmentService;

    @GetMapping("/getMessageDetails")
    @Operation(summary = "Get message details", tags = "Mobile app", description = "Retrieve the get message details for a specific user based on their email address.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_GET_MESSAGE_DETAILS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get message details retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public MessageWrapper getMessageDetails(@RequestParam String integnetMessageId) throws Exception {
        String userId=userContextHolder.getCurrentUser().getId();
        String messageId = Optional.ofNullable(mailSummaryDao.findByInternetMessageId(integnetMessageId, userId)).map(MailSummary::getMessageId).orElse(integnetMessageId);
        if (pollingMode.equalsIgnoreCase(EWS)) return ewsService.getMailDetails(userId,messageId);
        else if (pollingMode.equalsIgnoreCase(GMAIL)) return gmailIntegration.getMessageWrapperById(messageId);
        else return graphIntegrationService.getMessageWrapperByMessageId(userId, messageId);
    }

    @GetMapping("/deleteMailById")
    @Operation(summary = "Delete mail", tags = "Mobile app", description = "Delete mail by mail ID",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_DELETE_MAIL}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Mail deleted successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Boolean deleteMailById(@RequestParam("internetMessageId") String internetMessageId,@RequestParam("softDelete") Boolean softDelete) throws Exception {
        String userId=userContextHolder.getCurrentUser().getId();
//        String userId = "<EMAIL>";
        String messageId = Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, userId)).map(MailSummary::getMessageId).orElse(internetMessageId);
        if (pollingMode.equalsIgnoreCase(EWS)) return  ewsService.deleteMailById(userId,messageId,softDelete);
        else if (pollingMode.equalsIgnoreCase(GMAIL)) return gmailIntegration.deleteMessageById(userId,messageId,softDelete);
        else return graphIntegrationService.deleteMessageById(userId,messageId,softDelete);
    }

    @PostMapping(path = "/v2/flagEmail")
    @Operation(summary = "Flag email (v2)", tags = "Email", description = "Flag an email using version 1 of the API based on the provided details in the request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_FLAG_EMAIL}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email flagged successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> flagEmailBot(@RequestBody Map<String, String> body) throws IOException, InterruptedException {
        String email = userContextHolder.getCurrentUser().getEmail();
        String messageId = body.get("messageId");
        String flagStatus = body.get("flagStatus");
        log.debug("Inside @method flagEmailBot. @param : email -> {} messageId -> {} flagStatus -> {}", email, messageId, flagStatus);
        Map<String, String> responseMap = new HashMap<>();
        if (pollingMode.equalsIgnoreCase("EWS")) {
           String response = ewsService.flagEmail(email, messageId, flagStatus);
              responseMap.put("Response",response);
              return responseMap;
        }
        else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            String response = gmailIntegration.starAndUnstarEmail(email, messageId, flagStatus)?"success":"failed";
             responseMap.put("Response",response);
             return responseMap;
        }
        else {
            String flag = String.format("{\"flag\":{\"flagStatus\":\"%s\"}}", flagStatus);
            String response = graphIntegrationService.flagEmail(email, messageId, flag);
            responseMap.put("Response",response);
            return responseMap;
        }
    }

    @GetMapping("/download/stream")
    @Operation(summary = "Download File as Stream", tags = "File Operations", description = "Downloads a file from the specified bucket as a stream.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_DOWNLOAD_FILE}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "File successfully downloaded." ),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while downloading the file.")
    })
    public ResponseEntity<byte[]> downloadFileAsStream(
            @RequestParam String bucketName,
            @RequestParam String key) throws IOException {

        byte[] fileBytes = s3Service.downloadFileAsBytes(bucketName, key);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + key + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(fileBytes);
    }


    @GetMapping("/V1/download/stream")
    @Operation(summary = "Download File as Stream by Doc ID", tags = "File Operations", description = "Downloads a file as a stream from the specified document ID.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_DOWNLOAD_FILE_BY_DOC_ID}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "File successfully downloaded."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while downloading the file.")
    })
    public ResponseEntity<byte[]> downloadFileAsStreamByDocId(
            @RequestParam String docId) throws IOException {
        UserMailAttachment userMailAttachment = userMailAttachmentService.userMailAttachmentByAttachmentId(docId);
        String s3BucketName = "emailAttachments";
        String key = userMailAttachment.getDocPath();
        byte[] fileBytes = s3Service.downloadFileAsBytes(s3BucketName,userMailAttachment.getDocPath());

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + key + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(fileBytes);
    }


    @PostMapping("/createDraft")
    @Operation(summary = "Create draft", tags = "Email", description = "Create a draft email based on the provided request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_CREATE_DRAFT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft created successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> createDraft(@RequestBody Map<String, String> requestBody) {
        Map<String, String> responseMap = new HashMap<>();
        String email = userContextHolder.getCurrentUser().getEmail();
        String subject = requestBody.get("subject");
        String content = requestBody.get("content");
        String attachmentList = requestBody.get("attachmentList");
        Boolean isDraft = !Optional.ofNullable(requestBody.get("sendDraft"))
                .filter(value -> !value.isEmpty())
                .map(Boolean::parseBoolean)
                .orElse(true);
        String toEmail = requestBody.get("toEmail");
        String ccEmail = requestBody.get("ccEmail");
        String bccEmail = requestBody.get("bccEmail");
        log.debug("Inside @method createDraft. @param : email -> {} subject -> {}", email, subject);
        if (pollingMode.equalsIgnoreCase("EWS")) {
            String response = ewsService.createDraftWithAttachment(email, subject, content, toEmail, ccEmail, bccEmail,isDraft,attachmentList);
            responseMap.put("Response",response);
            return responseMap;
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            String response = gmailIntegration.createDraft(requestBody);
            responseMap.put("Response",response);
            return responseMap;
        } else {
            String response = graphIntegrationService.createDraftWithAttachment(email, subject, content, toEmail, ccEmail, bccEmail,isDraft,attachmentList);
            responseMap.put("Response",response);
            return responseMap;
        }
    }

    @PostMapping("/createDraftReply")
    @Operation(summary = "Create draft reply", tags = "Email", description = "Create a draft reply based on the provided request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_CREATE_DRAFT_REPLY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft reply created successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> createDraftReply(@RequestBody Map<String, String> requestBody) throws Exception {
        Map<String, String> responseMap = new HashMap<>();
        String response;
        String email = userContextHolder.getCurrentUser().getEmail();
//        String email = "<EMAIL>";
        String messageId = requestBody.get("messageId");
        String content = requestBody.get("content");
        boolean sendDraft = Optional.ofNullable(requestBody.get("sendDraft"))
                .filter(value -> !value.isEmpty())
                .map(Boolean::parseBoolean)
                .orElse(false);
        boolean isReplyAll = Optional.ofNullable(requestBody.get("isReplyAll"))
                .filter(value -> !value.isEmpty())
                .map(Boolean::parseBoolean)
                .orElse(false);
        String attachmentList = requestBody.get("attachmentList");
        log.debug("Inside @method createDraftReply. @param : email -> {} messageId -> {}", email, messageId);
        if (content == null) content = "";

        if (pollingMode.equalsIgnoreCase("EWS")) {
            response = ewsService.createDraftReplyWithAttachment(email, messageId, content, sendDraft,attachmentList,isReplyAll);
            responseMap.put("Response",response);
            return responseMap;
        }
        else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            response = gmailIntegration.createDraftReply(requestBody,isReplyAll);
            responseMap.put("Response",response);
            return responseMap;
        }
        else {
        response = graphIntegrationService.createDraftReplyWithAttachment(email, messageId, content, sendDraft, attachmentList, isReplyAll);
            responseMap.put("Response",response);
            return responseMap;
        }
    }

    @PostMapping("/V1/upload/stream")
    @Operation(summary = "Upload Attachment for Mail", tags = "File Operations", description = "Uploads a file as an attachment to the specified S3 bucket using the provided file data.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_UPLOAD_ATTACHMENT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "File successfully uploaded."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while uploading the file.")
    })
    public Map<String, String> uploadAttachmentForMail(
            @RequestPart("fileData") MultipartFile fileInputStream) {
        Map<String, String> responseMap = new HashMap<>();
        try {
            String fileName = fileInputStream.getOriginalFilename();
            if (fileName == null || fileName.isEmpty()) {
                throw new IllegalArgumentException("File name cannot be empty");
            }

            String email = userContextHolder.getCurrentUser().getEmail();
            String s3BucketName = "emailAttachments";
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String s3Key = email + "/attachments/" + timestamp + "_" + fileName;

            String filePath = s3Service.uploadFile(s3BucketName, s3Key, fileInputStream.getInputStream(), fileInputStream.getSize());

            responseMap.put("Response", EmailConstants.SUCCESS);
            responseMap.put("filePath", s3Key);
        } catch (Exception e) {
            responseMap.put("Response", EmailConstants.FAILURE);
            log.error("Exception while uploading for mobile -->", e);
        }
        return responseMap;
    }

    @PostMapping("/forwardEmail")
    @Operation(summary = "Create draft reply", tags = "Email", description = "Create a draft reply based on the provided request body.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_FORWARD_EMAIL}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft reply created successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> forwardMail(@RequestBody Map<String, String> requestBody) throws Exception {
        Map<String, String> responseMap = new HashMap<>();
        String response;
        String email = userContextHolder.getCurrentUser().getEmail();
//        String email = "<EMAIL>";
        String messageId = requestBody.get("messageId");
        String content = requestBody.get("content");
        String toEmail = requestBody.get("toEmail");
        String ccEmail = requestBody.get("ccEmail");
        String bccEmail = requestBody.get("bccEmail");
        log.debug("Inside @method createDraftReply. @param : email -> {} messageId -> {}", email, messageId);
        if (content == null) content = "";

        if (pollingMode.equalsIgnoreCase("EWS")) {
            response = ewsService.forwardEmail(email, messageId,toEmail,ccEmail,bccEmail,content);
            responseMap.put("Response",response);
            return responseMap;
        }
        else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            response = gmailIntegration.forwardEmail(messageId,email,content,toEmail);
            responseMap.put("Response",response);
            return responseMap;
        }
        else {
            response = graphIntegrationService.forwardEmail(email, messageId,toEmail,ccEmail,bccEmail, content);
            responseMap.put("Response",response);
            return responseMap;
        }
    }

    @PostMapping("/setAutoReply")
    @Operation(summary = "Set auto-reply", tags = "Email", description = "Configure the auto-reply settings for a specific user or email based on the provided details.",
            security = @SecurityRequirement(name = DEFAULT, scopes = {ROLE_API_SET_AUTO_REPLY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Auto-reply settings configured successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String,String> setAutoReply(@RequestBody Map<String, String> body) {
        String userEmail =userContextHolder.getCurrentUser().getEmail();
        Map<String,String> responseMap = new HashMap<>();
        String internalReplyMessage = body.get("internalReplyMessage");
        String externalReplyMessage = body.get("externalReplyMessage");
        String scheduledStartDateTime = body.get("scheduledStartDateTime");
        String scheduledEndDateTime = body.get("scheduledEndDateTime");
        String timeZone = body.get(EmailConstants.TIME_ZONE); // Can be null or empty
        try {
            if (pollingMode.equalsIgnoreCase("EWS")) {
                ewsService.setAutoReplyForUser(userEmail, internalReplyMessage, externalReplyMessage,
                        scheduledStartDateTime, scheduledEndDateTime, timeZone);
            }
            else if(pollingMode.equalsIgnoreCase("GMAIL")){
                gmailIntegration.setAutoReplyForUser(userEmail, internalReplyMessage, externalReplyMessage,
                        scheduledStartDateTime, scheduledEndDateTime, timeZone);
            }
            else {
                graphIntegrationService.setAutoReplyForUser(userEmail, internalReplyMessage, externalReplyMessage,
                        scheduledStartDateTime, scheduledEndDateTime, timeZone);
            }
            responseMap.put("response",EmailConstants.SUCCESS);
        } catch (Exception e) {
            log.error("Error inside @method setAutoReply", e);
            responseMap.put("response",EmailConstants.FAILED);
        }
        return responseMap;
    }



}
