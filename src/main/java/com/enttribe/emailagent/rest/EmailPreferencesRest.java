package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.service.EmailPreferencesService;

import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.APIConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.util.*;
import com.enttribe.emailagent.entity.MailSummary;

/**
 * The type Email preferences rest.
 *  <AUTHOR> Dangi
 */
@RestController("emailPreferencesRest")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/preferences")
public class EmailPreferencesRest {

    @Value("${pollingMode}")
    private String pollingMode;

    @Autowired
    private UserContextHolder userContextHolder;

    @Autowired
    private EmailPreferencesService service;
@Autowired
    private IMailSummaryDao mailSummaryDao;


    @PostMapping("/updateTimeZone")
    @Operation(summary = "Update the user's time zone", tags = "User Preferences", description = "Updates the user's time zone based on the provided value in the request body.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_UPDATE_TIME_ZONE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Time zone updated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the time zone.")
    })
    public EmailPreferences updateTimeZone(@RequestBody Map<String, String> requestBody) {
        String timeZone = requestBody.get("timeZone");
        if (timeZone == null) {
            throw new IllegalArgumentException("TimeZone is required");
        }
        return service.updateTimeZone(timeZone);
    }

    @Deprecated
    @PostMapping("/updateDeviceId")
    @Operation(summary = "Update the device ID", tags = "Device Management", description = "Updates the device ID based on the provided value in the request body.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_UPDATE_DEVICE_ID})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Device ID updated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the device ID.")
    })
    public Map<String, String> updateDeviceId(@RequestBody Map<String, String> requestBody) {
        String deviceId = requestBody.get("deviceId");
        if (deviceId == null) {
            throw new IllegalArgumentException("TimeZone is required");
        }
        String result = service.updateDeviceId(deviceId);
        return Map.of("result", result);
    }


    @PostMapping("create")
    @Operation(summary = "Create new email preferences", tags = "Email Preferences", description = "Creates a new email preferences record based on the provided request body.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_CREATE_PREFERENCES})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email preferences created successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while creating email preferences.")
    })
    public EmailPreferences create(@RequestBody EmailPreferences emailPreferences) {
        return service.create(emailPreferences);
    }

    @PostMapping("update")
    @Operation(summary = "Update email preferences", tags = "Email Preferences", description = "Updates the existing email preferences based on the provided request body.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_UPDATE_PREFERENCES})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email preferences updated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating email preferences.")
    })
    public EmailPreferences update(@RequestBody EmailPreferences emailPreferences) {
        return service.update(emailPreferences);
    }


    @PostMapping(path = "/saveOrUpdate")
    @Operation(summary = "Save or update email preferences", tags = "Email Preferences", description = "Saves or updates email preferences based on the provided request body. If the preferences already exist, it will be updated.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_SAVE_UPDATE_PREFERENCES})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email preferences saved or updated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while saving or updating email preferences.")
    })
    public EmailPreferences saveOrUpdateEmailPreferences(@RequestBody EmailPreferences preferences) {
        return service.saveOrUpdateEmailPreferences(preferences);
    }


    @GetMapping(path = "/get")
    @Operation(summary = "Get email preferences", tags = "Email Preferences", description = "Retrieves the email preferences for the current user.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_GET_PREFERENCES})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved email preferences."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving email preferences.")
    })
    public EmailPreferences getEmailPreferences() {
        return service.getEmailPreferences();
    }


    @GetMapping(path = "/check")
    @Operation(summary = "Check a specific email preference", tags = "Email Preferences", description = "Checks if a specific email preference is enabled or not based on the provided preference and type.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_CHECK_PREFERENCE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully checked the preference."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while checking the preference.")
    })
    public Map<String, Boolean> checkPreference(@RequestParam String preference, @RequestParam String type) {
        boolean checked = service.checkPreference(preference, type);
        Map<String, Boolean> responseMap = new HashMap<>();
        responseMap.put("result", checked);
        return responseMap;
    }

    //used
    @GetMapping(path = "/addConversationId")
    @Operation(summary = "Add or update conversation ID", tags = "Conversation Management", description = "Adds or updates the conversation ID based on provided `conversationId` or `internetMessageId`.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_ADD_CONVERSATION_ID})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully added or updated the conversation ID."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while adding the conversation ID.")
    })
    public Map<String, Object> addConversationId(
            @RequestParam(name = APIConstants.CONVERSATION_ID, required = false) String conversationId,
            @RequestParam(name = APIConstants.INTERNET_MESSAGE_ID, required = false) String internetMessageId) {

        String userId = userContextHolder.getCurrentUser().getId();
        if (pollingMode.equalsIgnoreCase("GMAIL")) {
            conversationId = Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, userId))
                    .map(MailSummary::getConversationId)
                    .orElse(conversationId);
        }
        return service.addConversationId(conversationId);
    }

    //used
    @GetMapping(path = "/deleteConversationId")
    @Operation(summary = "Delete conversation ID", tags = "Conversation Management", description = "Deletes the conversation ID based on provided `conversationId` or `internetMessageId`.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_DELETE_CONVERSATION_ID})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully deleted the conversation ID."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while deleting the conversation ID.")
    })
    public Map<String, String> deleteConversationId(@RequestParam String conversationId,@RequestParam(name = APIConstants.INTERNET_MESSAGE_ID, required = false) String internetMessageId) {
        String userId = userContextHolder.getCurrentUser().getId();
        if (pollingMode.equalsIgnoreCase("GMAIL")) {
            conversationId = Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, userId))
                    .map(MailSummary::getConversationId)
                    .orElse(conversationId);
        }
        return service.deleteConversationId(conversationId);
    }


    //used
    @Deprecated
    @GetMapping(path = "/addPreferences")
    @Operation(summary = "Add email preference", tags = "Email Preferences", description = "Adds a new preference for the user based on the provided `preference` and `type`.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_ADD_PREFERENCES})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully added the preference."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while adding the preference.")
    })
    public EmailPreferences addPreferences(@RequestParam String preference,
                                           @RequestParam String type) {
        return service.addPreferences(preference, type);
    }

    //used
    @PostMapping(path = "/addPreferences")
    @Operation(summary = "Add email preference V1", tags = "Email Preferences", description = "Adds a new preference for the user based on the provided `preference` and `type` in the request body.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_ADD_PREFERENCES})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully added the preference."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while adding the preference.")
    })
    public EmailPreferences addPreferencesV1(@RequestBody Map<String, String> request) {
        String preference = request.get("preference");
        String type = request.get("type");
        return service.addPreferences(preference, type);
    }

    //used
    @GetMapping(path = "/deletePreferences")
    @Operation(summary = "Delete email preference", tags = "Email Preferences", description = "Deletes an existing email preference for the user based on the provided `preference` and `type`.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_DELETE_PREFERENCES})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully deleted the preference."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while deleting the preference.")
    })
    public EmailPreferences deletePreferences(@RequestParam String preference,
                                              @RequestParam String type) {
        return service.deletePreferences(preference, type);
    }

    @GetMapping(path = "/getTimeZones")
    @Operation(summary = "Get time zones like the provided zone", tags = "Time Zone", description = "Fetches a list of time zones that match the provided `zone` parameter.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_GET_TIME_ZONES})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved time zones."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching time zones.")
    })
    List<String> getTimeZonesLike(@RequestParam(required = true, name = "zone") String zone){
        return service.getTimeZonesLike(zone);
    }


    @GetMapping("/search")
    @Operation(summary = "Search for email preferences", tags = "Email Preferences", description = "Search for email preferences based on various optional query parameters.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_SEARCH_PREFERENCES})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved matching email preferences."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred during the search.")
    })
    public List<EmailPreferences> search(@RequestParam(name = "id", required = false) Integer id,
                              @RequestParam(name = "userId", required = false) String userId,
                              @RequestParam(name = "emailSubject", required = false) String emailSubject,
                              @RequestParam(name = "senderCompany", required = false) String senderCompany,
                              @RequestParam(name = "importantTags", required = false) String importantTags,
                              @RequestParam(name = "fontFamily", required = false) String fontFamily,
                              @RequestParam(name = "fontSize", required = false) String fontSize,
                              @RequestParam(name = "emailSender", required = false) String emailSender,
                              @RequestParam(name = "blackListedDomain", required = false) String blackListedDomain,
                              @RequestParam(name = "blackListedSubject", required = false) String blackListedSubject,
                              @RequestParam(name = "timeZone", required = false) String timeZone,
                              @RequestParam(name = "createdTime", required = false) Date createdTime,
                              @RequestParam(name = "modifiedTime", required = false) Date modifiedTime,
                              @RequestParam(name = "conversationId", required = false) String conversationId,
                              @RequestParam(name = "checkin", required = false) LocalTime checkin,
                              @RequestParam(name = "checkout", required = false) LocalTime checkout,
                              @RequestParam(name = "fontColor", required = false) String fontColor,
                              @RequestParam(name = "displayName", required = false) String displayName) {
    return service.search(id, userId, emailSubject, senderCompany, importantTags, fontFamily, fontSize,
                                   emailSender, blackListedDomain, blackListedSubject, timeZone, createdTime,
                                   modifiedTime, conversationId, checkin, checkout, fontColor, displayName);
}

@GetMapping("/count")
@Operation(summary = "Count email preferences based on search criteria", tags = "Email Preferences", description = "Count email preferences that match the given optional query parameters.",
        security = {
                @SecurityRequirement(
                        name = APIConstants.DEFAULT,
                        scopes = {APIConstants.ROLE_API_COUNT_PREFERENCES})})
@ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved count of matching email preferences."),
        @ApiResponse(responseCode = "500", description = "Internal server error occurred during counting.")
})
public long count(@RequestParam(name = "id", required = false) Integer id,
                          @RequestParam(name = "userId", required = false) String userId,
                          @RequestParam(name = "emailSubject", required = false) String emailSubject,
                          @RequestParam(name = "senderCompany", required = false) String senderCompany,
                          @RequestParam(name = "importantTags", required = false) String importantTags,
                          @RequestParam(name = "fontFamily", required = false) String fontFamily,
                          @RequestParam(name = "fontSize", required = false) String fontSize,
                          @RequestParam(name = "emailSender", required = false) String emailSender,
                          @RequestParam(name = "blackListedDomain", required = false) String blackListedDomain,
                          @RequestParam(name = "blackListedSubject", required = false) String blackListedSubject,
                          @RequestParam(name = "timeZone", required = false) String timeZone,
                          @RequestParam(name = "createdTime", required = false) Date createdTime,
                          @RequestParam(name = "modifiedTime", required = false) Date modifiedTime,
                          @RequestParam(name = "conversationId", required = false) String conversationId,
                          @RequestParam(name = "checkin", required = false) LocalTime checkin,
                          @RequestParam(name = "checkout", required = false) LocalTime checkout,
                          @RequestParam(name = "fontColor", required = false) String fontColor,
                          @RequestParam(name = "displayName", required = false) String displayName) {
return service.count(id, userId, emailSubject, senderCompany, importantTags, fontFamily, fontSize,
                               emailSender, blackListedDomain, blackListedSubject, timeZone, createdTime,
                               modifiedTime, conversationId, checkin, checkout, fontColor, displayName);
}

@PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "preferenceByFilter")
@Operation(summary = "Get email preferences by filter", tags = "Email Preferences", description = "Fetch email preferences by applying the given filter criteria and pagination options.",
        security = {
                @SecurityRequirement(
                        name = APIConstants.DEFAULT,
                        scopes = {APIConstants.ROLE_API_PREFERENCE_BY_FILTER})})
@ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved email preferences matching the filter."),
        @ApiResponse(responseCode = "500", description = "Internal server error occurred during the filter process.")
})
List<EmailPreferences> preferenceByFilter(
        @Parameter(name = "Minimum number of records required") @RequestParam(required = false, name = "llimit") Integer llimit,
        @Parameter(name = "Maximum number of records required") @RequestParam(required = false, name = "ulimit") Integer ulimit,
        @Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap){
        return service.preferenceByFilter(llimit, ulimit, filterMap);
    }



    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "preferenceCountByFilter")
    @Operation(summary = "Get email preference count by filter", tags = "Email Preferences", description = "Fetch the count of email preferences based on the provided filter criteria.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_PREFERENCE_COUNT_BY_FILTER})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the count of email preferences matching the filter."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred during the count process.")
    })
    long preferenceCountByFilter(@Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap){
        return service.preferenceCountByFilter( filterMap);
    }

    @PostMapping("/updateNotificationInfo")
    @Operation(summary = "Update notification information", tags = "Email Preferences", description = "Update notification information (deviceId or gcmId) for the user.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_UPDATE_NOTIFICATION_INFO})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification information updated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred during the update process.")
    })
    public Map<String, String> updateNotificationInfo(@RequestBody Map<String, Object> requestBody) {
        Boolean allowNotification = (Boolean) requestBody.get("allowNotification");
        String deviceId = (String) requestBody.get("deviceId");

        if (allowNotification == null && deviceId == null) {
            throw new IllegalArgumentException("At least one of key allowNotification or deviceId is required");
        }

        String result = service.updateNotificationInfo(allowNotification, deviceId);
        return Map.of("result", result);
    }


}
