package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.ai.dto.meeting.AvailableTimeSlots;
import com.enttribe.emailagent.ai.dto.objective.ObjectiveAIResponse;
import com.enttribe.emailagent.ai.polling.OutlookPollingAI;
import com.enttribe.emailagent.ai.service.AIService;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.dto.*;
import com.enttribe.emailagent.entity.*;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.jenkins.BuildEmailPoller;
import com.enttribe.emailagent.repository.*;
import com.enttribe.emailagent.service.ChatService;
import com.enttribe.emailagent.service.DeploymentService;
import com.enttribe.emailagent.service.EwsService;
import com.enttribe.emailagent.service.GraphIntegrationService;
import com.enttribe.emailagent.service.ImailSummaryService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.enttribe.emailagent.utils.APIConstants;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.DateUtils;
import com.enttribe.emailagent.utils.EmailConstants;
import com.enttribe.emailagent.wrapper.AvailableSlots;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.enttribe.emailagent.utils.EmailConstants.*;
import static com.enttribe.emailagent.utils.ExceptionUtils.getStackTraceAsString;

/**
 * The type Graph integration rest.
 *  <AUTHOR> Pathak
 */
@RestController("graphIntegrationRest")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/emailservice")
@Slf4j
public class GraphIntegrationRest {

    @Autowired
    private AIService aiService;

    @Autowired
    private UserActionsDao userActionsDao;

    @Autowired
    private IEmailThreadDao threadDao;

    @Autowired
    private GraphIntegrationService graphIntegrationService;

    @Autowired
    private DeploymentService deploymentService;

    @Autowired
    private BuildEmailPoller buildEmailPoller;

    @Autowired
    private FailureLogsServiceDao failureDao;

    @Autowired
    private UserContextHolder userContextHolder;

    @Autowired
    private EmailUserDao emailUser;

    @Autowired
    private IMailSummaryDao mailSummaryDao;

    @Autowired
    private ImailSummaryService imailSummaryService;

    @Autowired
    private EwsService ewsService;

    @Autowired
    private GmailIntegration gmailIntegration;

    @Autowired
    private IEmailPreferencesDao preferencesDao;

    @Autowired
    private UserFoldersRepository userFoldersRepository;


    @Autowired
    OutlookPollingAI aiPolling;

    @Value("${pollingMode}")
    private String pollingMode;

    @Autowired
    private ChatService chatService;

    @Value("${threadConcurency}")
    private Integer threadConcurency;

    @Autowired
    IOrganisationRepository orgRepo;

    private static final Logger auditLog = EmailAgentLogger.getLogger(GraphIntegrationRest.class);

    private static final List<String> allowedEmails = List.of("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>");


    @GetMapping(path = "/emails")
    @Operation(summary = "Retrieve Emails of User", tags = "Emails", description = "Fetches a list of emails from the user's inbox based on the provided search criteria, such as userId, email, receivedDateTime, categories, isRead, etc.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_EMAILS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved emails of the user."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving emails.")
    })
    public UserEmailResponseDto getEmailsOfUser(@RequestParam(required = false) String userId,
                                                @RequestParam(required = false) String email,
                                                @RequestParam(required = false) String receivedDateTime,
                                                @RequestParam(required = false) String categories,
                                                @RequestParam(required = false) Boolean isRead,
                                                @RequestParam(required = false) Integer limit,
                                                @RequestParam(required = false) Integer offset
    ) throws IOException, InterruptedException {


        return graphIntegrationService.getEmailsOfUser(userId, email, receivedDateTime, categories, isRead, "inbox", limit, offset);
    }

    @GetMapping(path = "/getEmails")
    @Operation(summary = "Retrieve Emails of User from server.", tags = "Emails", description = "Retrieve a list of emails from the user's specific folder using pagination.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_EMAILS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved emails of the user."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving emails.")
    })
    public List<UserEmailDto> getEmailsOfUser(@RequestParam String mailFolder,
                                              @RequestParam Integer lowerLimit,
                                              @RequestParam Integer upperLimit
    ) {
        String email = userContextHolder.getCurrentUser().getEmail();
        log.debug("Inside @method getEmailsOfUser for user: {}", email);
        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.getEmailsOfUser(email, mailFolder, lowerLimit, upperLimit);
        }
        else {
            return graphIntegrationService.getEmailsOfUser(email, mailFolder, lowerLimit, upperLimit);
        }
    }

    @PostMapping(path = "/getEmailsWithFilter")
    @Operation(summary = "Retrieve Emails of User from server.", tags = "Emails", description = "Retrieve a list of emails from the user's specific folder using pagination.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_EMAILS_WITH_FILTER}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved emails of the user."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving emails.")
    })
    public List<UserEmailDto> getEmailsWithFilter(@RequestBody Map<String, Object> request) {
        log.debug("Inside @method getEmailsWithFilter. request: {}", request);
        String email = (String) request.get("email");
        if (!allowedEmails.contains(email.toLowerCase())) {
            throw new BusinessException("Email Id " + email + " is not present in our records");
        }
        if (pollingMode.equalsIgnoreCase("EWS")) {
            return new ArrayList<>();
        } else {
            Integer minutesBefore = (Integer) request.get("minutesBefore");
            String hourBehindCurrentTime = DateUtils.getMinutesBehindCurrentTime(minutesBefore);
            String subject = (String) request.get("subject");
            String filter;
            if (subject == null) {
                filter = String.format("receivedDateTime ge %s", hourBehindCurrentTime);
            } else {
                filter = String.format("contains(subject, '%s') and receivedDateTime ge %s", subject, hourBehindCurrentTime);
            }
            return graphIntegrationService.getEmailsWithFilter(email, filter, "Inbox");
        }
    }

    @PostMapping(path = "/getEmailByMessageId")
    @Operation(summary = "Retrieve Emails of User from server.", tags = "Emails", description = "Retrieve a list of emails from the user's specific folder using pagination.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_EMAIL_BY_MESSAGE_ID}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved emails of the user."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving emails.")
    })
    public Object getEmailByMessageId(@RequestBody Map<String, String> request) {
        log.debug("Inside @method getEmailByMessageId. request: {}", request);
        String email = userContextHolder.getCurrentUser().getEmail();
        String messageId = request.get("messageId");
        return graphIntegrationService.getEmailByMessageId(email, messageId);
    }

    @PostMapping("/cancelAutoReply")
    @Operation(summary = "Cancel auto-reply", tags = "Email", description = "Cancel the auto-reply setting for a specific email or user based on the provided details.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_CANCEL_AUTO_REPLY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Auto-reply canceled successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public ResponseEntity<String> cancelAutoReply(@RequestBody Map<String, String> body) {

        String userEmail = userContextHolder.getCurrentUser().getEmail();

        log.debug("Inside @method cancelAutoReply for user: {}", userEmail);
        try {
            if (pollingMode.equalsIgnoreCase("EWS")) {
                ewsService.cancelAutoReply(userEmail);
            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                gmailIntegration.cancelAutoReply(userEmail);
            } else {
                graphIntegrationService.cancelAutoReplyForUser(userEmail);
            }
            return ResponseEntity.ok("Auto-reply disabled successfully for user: " + userEmail);
        } catch (Exception e) {
            log.error("Error inside @method cancelAutoReply", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to disable auto-reply for user: " + userEmail);
        }
    }

    @GetMapping("/getAutoReplySettings")
    @Operation(summary = "Get auto-reply settings", tags = "Email", description = "Retrieve the auto-reply settings for a specific user based on their email address.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_AUTO_REPLY_SETTINGS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Auto-reply settings retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public ResponseEntity<Map<String, Object>> getAutoReplySettings() {
        String userEmail = userContextHolder.getCurrentUser().getEmail();
        log.debug("Inside @method getAutoReplySettings for user: {}", userEmail);
        Map<String, Object> autoReplySettings = null;
        try {
            if (pollingMode.equalsIgnoreCase("EWS")) {
                autoReplySettings = ewsService.getAutoReplySettingsForUser(userEmail);
            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                autoReplySettings = gmailIntegration.getAutoReplySettingsForUser(userEmail);
            } else {
                autoReplySettings = graphIntegrationService.getAutoReplySettingsForUser(userEmail);
            }
            return ResponseEntity.ok(autoReplySettings);
        } catch (Exception e) {
            log.error("Error inside @method getAutoReplySettings", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.singletonMap("error", "Failed to retrieve auto-reply settings for user: " + userEmail));
        }
    }

    @PostMapping("/getOutOfOffice")
    @Operation(summary = "Get out of office details of users", tags = "Email", description = "Retrieve the out of office details of provided users",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_OUT_OF_OFFICE}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Auto-reply settings retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Map<String, String>> getOutOfOffice(@RequestBody List<String> emails) {
        log.debug("Inside @method getOutOfOffice for users: {}", emails);
        Map<String, Map<String, String>> autoReplySettings = null;
        try {
            if (pollingMode.equalsIgnoreCase("EWS")) {
                autoReplySettings = ewsService.getOutOfOffice(emails);;

            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                autoReplySettings = null;
            } else {
                autoReplySettings = graphIntegrationService.getOutOfOffice(emails);
            }
            return autoReplySettings;
        } catch (Exception e) {
            log.error("Error inside @method getAutoReplySettings", e);
            throw new BusinessException("unable to get out of office settings");
        }
    }


    @PostMapping("/setAutoReply")
    @Operation(summary = "Set auto-reply", tags = "Email", description = "Configure the auto-reply settings for a specific user or email based on the provided details.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_SET_AUTO_REPLY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Auto-reply settings configured successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public ResponseEntity<String> setAutoReply(@RequestBody Map<String, String> body) {
        String userEmail = userContextHolder.getCurrentUser().getEmail();
        String internalReplyMessage = body.get("internalReplyMessage");
        String externalReplyMessage = body.get("externalReplyMessage");
        String scheduledStartDateTime = body.get("scheduledStartDateTime");
        String scheduledEndDateTime = body.get("scheduledEndDateTime");
        String timeZone = body.get(EmailConstants.TIME_ZONE); // Can be null or empty
        try {
            if (pollingMode.equalsIgnoreCase("EWS")) {
                ewsService.setAutoReplyForUser(userEmail, internalReplyMessage, externalReplyMessage,
                        scheduledStartDateTime, scheduledEndDateTime, timeZone);
            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                gmailIntegration.setAutoReplyForUser(userEmail, internalReplyMessage, externalReplyMessage,
                        scheduledStartDateTime, scheduledEndDateTime, timeZone);
            } else {
                graphIntegrationService.setAutoReplyForUser(userEmail, internalReplyMessage, externalReplyMessage,
                        scheduledStartDateTime, scheduledEndDateTime, timeZone);
            }
            return ResponseEntity.ok("Auto-reply settings updated successfully for user: " + userEmail);
        } catch (Exception e) {
            log.error("Error inside @method setAutoReply", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to update auto-reply settings for user: " + userEmail);
        }
    }

    @PostMapping(path = "/flagEmail")
    @Operation(summary = "Flag email", tags = "Email", description = "Flag an email based on the provided message ID and flag status.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_FLAG_EMAIL}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email flagged successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public String flagEmail(@RequestParam String messageId,
                            @RequestBody String flag) throws IOException, InterruptedException {

        if (pollingMode.equalsIgnoreCase("EWS")) {
            messageId = messageId.replace(" ", "+");
            messageId = messageId.replace("_", "+");
            messageId = messageId.replace("-", "/");
        } else {
            messageId = messageId.replace("/", "-");
        }
        String email = userContextHolder.getCurrentUser().getEmail();
        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.flagEmail(email, messageId, flag);
        } else {
            return graphIntegrationService.flagEmail(email, messageId, flag);
        }
    }


    @GetMapping(path = "/getFlagStatus")
    @Operation(summary = "Get email flag status", tags = "Email", description = "Retrieve the flag status of an email based on the provided message ID.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_FLAG_STATUS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Flag status retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> getFlagStatus(@RequestParam(name = APIConstants.MESSAGE_ID, required = false) String messageId, @RequestParam(name = APIConstants.INTERNET_MESSAGE_ID, required = false) String internetMessageId) throws IOException, InterruptedException {
        log.info("MessageId to flag email is {}", messageId);
        String email = userContextHolder.getCurrentUser().getEmail();
        if (pollingMode.equalsIgnoreCase("EWS")) {
            messageId = messageId.replace(" ", "+");
            messageId = messageId.replace("_", "+");
            messageId = messageId.replace("-", "/");
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            messageId = Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, email)).map(MailSummary::getMessageId).orElse(messageId);
        } else {
            messageId = messageId.replace("/", "-");
        }
        String flagStatus = "";
        if (pollingMode.equalsIgnoreCase("EWS")) {
            flagStatus = ewsService.getFlagStatus(email, messageId);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            log.info("email is {}", email);
            log.info("messageId is {}", messageId);
            flagStatus = gmailIntegration.getFlagStatus(email, messageId);
        } else {
            flagStatus = graphIntegrationService.getFlagStatus(messageId);
        }
        return Map.of(EmailConstants.RESULT, flagStatus);
    }

    @PostMapping("/getAvailableSlotsAndConflict")
    @Operation(summary = "Get available slots and conflicts", tags = "Meeting", description = "Retrieve available meeting slots and any scheduling conflicts based on the provided request data.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_AVAILABLE_SLOTS_AND_CONFLICT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Available slots and conflicts retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public AvailableSlots getAvailableSlotsAndConflict(@RequestBody Map<String, Object> request) throws Exception {
        List<String> emails = (List<String>) request.get("requiredAttendees");
        String startDateTime = (String) request.get("startTime");
        String endDateTime = (String) request.get("endTime");
        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userContextHolder.getCurrentUser().getEmail());
        String timeZone = preferences.getTimeZone();
        Integer slotDuration = (Integer) request.get("slotDuration");
        if (slotDuration == null) slotDuration = 30;
        if (startDateTime != null && endDateTime != null && !startDateTime.endsWith("Z")) {
            startDateTime = DateUtils.convertToUTCString(startDateTime, timeZone);
            endDateTime = DateUtils.convertToUTCString(endDateTime, timeZone);
        }
        List<Meeting> availableSlots = chatService.get3AvailableSlots(emails, startDateTime, slotDuration);
        AvailableSlots availableSlotsAndConflict;
        if (pollingMode.equalsIgnoreCase("EWS")) {
            availableSlotsAndConflict = ewsService.getAvailableSlotsAndConflict(emails, startDateTime, endDateTime, slotDuration);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            availableSlotsAndConflict = gmailIntegration.getAvailableSlotsAndConflict(emails, startDateTime, endDateTime, slotDuration);
        } else {
            availableSlotsAndConflict = graphIntegrationService.getAvailableSlotsAndConflict(emails, startDateTime, endDateTime, slotDuration);
        }
        availableSlotsAndConflict.setAvailableSlots(availableSlots);
        return availableSlotsAndConflict;
    }

    @Deprecated
    @PostMapping("/v2/getAvailableMeetingSlots")
    @Operation(summary = "Get available meeting slots", tags = "Meeting", description = "Retrieve available meeting slots for the specified email addresses within the optional date range.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V2}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Available meeting slots retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public List<AvailableTimeSlots> getAvailableMeetingSlotsV2(@RequestBody Map<String, Object> request) {
        log.debug("Inside @method getAvailableMeetingSlots. @param : {}", request);
        List<String> emails = (List<String>) request.get("participants");
        String startDateTime = (String) request.get("startDateTime");
        String endDateTime = (String) request.get("endDateTime");
        Integer slotDuration = (Integer) request.get("slotDuration");
        if (slotDuration == null) slotDuration = 60;

        if (!startDateTime.endsWith("Z")) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userContextHolder.getCurrentUser().getEmail());
            startDateTime = DateUtils.convertToUTCString(startDateTime, preferences.getTimeZone());
            endDateTime = DateUtils.convertToUTCString(endDateTime, preferences.getTimeZone());
        }

        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.getAvailableMeetingSlotsV2(emails, startDateTime, endDateTime, slotDuration);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.getAvailableMeetingSlotsV2(emails, startDateTime, endDateTime, slotDuration);
        } else {

            return graphIntegrationService.getAvailableMeetingSlotsV2(emails, startDateTime, endDateTime, slotDuration);
        }
    }

    @Deprecated
    @PostMapping("/v3/getAvailableMeetingSlots")
    @Operation(summary = "Get available meeting slots", tags = "Meeting", description = "Retrieve available meeting slots for the specified email addresses within the optional date range.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V3}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Available meeting slots retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public List<Meeting> getAvailableMeetingSlotsV3(@RequestBody Map<String, Object> request) {
        log.debug("Inside @method getAvailableMeetingSlots. @param : {}", request);
        List<String> emails = (List<String>) request.get("participants");

        String startDateTime = (String) request.get("startDateTime");
//        String endDateTime = (String) request.get("endDateTime");
        Integer slotDuration = (Integer) request.get("slotDuration");

//        Integer slotDuration = DateUtils.calculateDurationInMinutes(startDateTime, endDateTime);

        String meetingDate = startDateTime.split("T")[0];

        List<LocalDateTime> checkoutTimesOfUsers = getCheckoutTimesOfUsers(emails, meetingDate);
        LocalDateTime earliestTime = DateUtils.getEarliestLocalDateTime(checkoutTimesOfUsers);
        String startTime, endTime;
        if (DateUtils.isToday(meetingDate)) {
            startTime = DateUtils.getUTCDateStringWithOffset();
        } else {
            List<LocalDateTime> checkinTimesOfUsers = getCheckInTimesOfUsers(emails, meetingDate);
            LocalDateTime latestTime = DateUtils.getLatestLocalDateTime(checkinTimesOfUsers);
            startTime = DateUtils.getUTCStringFromLocalDateTime(latestTime);
        }
        endTime = DateUtils.getUTCStringFromLocalDateTime(earliestTime);


        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.getAvailableMeetingSlotsOOF(emails, startTime, endTime, slotDuration);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.getAvailableMeetingSlotsOFF(emails, startTime, endTime, slotDuration);
        } else {
            return graphIntegrationService.getAvailableMeetingSlotsOOF(emails, startTime, endTime, slotDuration);
        }
    }

    private List<LocalDateTime> getCheckoutTimesOfUsers(List<String> emails, String meetingDate) {
        if (meetingDate == null || meetingDate.isBlank()) meetingDate = LocalDate.now().toString();
        List<LocalDateTime> localTimes = new ArrayList<>();
        for (String email : emails) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            if (preferences != null && preferences.getCheckout() != null) {
                try {
                    //Here we convert user's checkout time to utc local time
                    LocalDateTime toUTCZone = DateUtils.parseDateToUTC(meetingDate, preferences.getTimeZone(), preferences.getCheckout());
                    localTimes.add(toUTCZone);
                } catch (Exception e) {
                    log.error("Error inside @method getCheckoutTimesOfUsers. @param : emails -> {} meetingDate -> {}", emails, meetingDate);
                    log.error("Error while getting getCheckoutTimesOfUsers", e);
                }
            }

        }
        return localTimes;
    }

    private List<LocalDateTime> getCheckInTimesOfUsers(List<String> emails, String meetingDate) {
        if (meetingDate == null || meetingDate.isBlank()) meetingDate = LocalDate.now().toString();
        List<LocalDateTime> localTimes = new ArrayList<>();
        for (String email : emails) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            if (preferences != null && preferences.getCheckout() != null) {
                //Here we convert user's checkout time to utc local time
                LocalDateTime toUTCZone = DateUtils.parseDateToUTC(meetingDate, preferences.getTimeZone(), preferences.getCheckin());
                localTimes.add(toUTCZone);
            }

        }
        return localTimes;
    }

    @Deprecated
    @PostMapping("/v1/getAvailableMeetingSlots")
    @Operation(summary = "Get available meeting slots (v1)", tags = "Meeting", description = "Retrieve available meeting slots based on the provided request data using version 1 of the API.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V1}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Available meeting slots retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public List<Meeting> getAvailableMeetingSlots(@RequestBody Map<String, Object> request) throws Exception {

        List<String> emails = (List<String>) request.get("requiredAttendees");
        String startDateTime = (String) request.get("startTime");
        String endDateTime = (String) request.get("endTime");
        Integer slotDuration = (Integer) request.get("slotDuration");
        if (slotDuration == null) slotDuration = 60;

        if (startDateTime != null && !startDateTime.endsWith("Z")) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userContextHolder.getCurrentUser().getEmail());
            startDateTime = DateUtils.convertToUTCString(startDateTime, preferences.getTimeZone());
            endDateTime = DateUtils.convertToUTCString(endDateTime, preferences.getTimeZone());
        }

        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.getAvailableMeetingSlots(emails, startDateTime, endDateTime, slotDuration);
        } else {

            return graphIntegrationService.getAvailableMeetingSlots(emails, startDateTime, endDateTime, slotDuration);
        }
    }

    @Deprecated
    @GetMapping(path = "/getCalendarEvents")
    @Operation(summary = "Get calendar events", tags = "Calendar", description = "Retrieve calendar events based on the provided email address and optional date range.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_CALENDAR_EVENTS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Calendar events retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getCalendarEvents(@RequestParam(required = false) String startDateTime,
                                                 @RequestParam(required = false) String endDateTime) throws Exception {

        List<EventDto> calendarEvents;
        String email = userContextHolder.getCurrentUser().getEmail();
        if (pollingMode.equalsIgnoreCase("EWS")) {
            calendarEvents = ewsService.getCalendarEvents(email, startDateTime, endDateTime, false);
        } else if (pollingMode.equalsIgnoreCase("Gmail")) {
            log.info("inside getCalendarEvents for Gmail");
            calendarEvents = gmailIntegration.getCalendarEvents(email, startDateTime, endDateTime);
        } else {
            calendarEvents = graphIntegrationService.getCalendarEventsV1(email, startDateTime, endDateTime,null);
        }
        Map<String, Object> response = new HashMap<>();
        response.put("meetingCount", calendarEvents.size());
        response.put("meetings", calendarEvents);
        return response;
    }

    @Deprecated
    @PostMapping(path = "/getCalendarEvents")
    @Operation(summary = "Get calendar events", tags = "Calendar", description = "Retrieve calendar events based on the details provided in the request body.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_CALENDAR_EVENTS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Calendar events retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getCalendarEvents(@RequestBody Map<String, String> request) throws Exception {

        String startDateTime = request.get("startDateTime");
        String endDateTime = request.get("endDateTime");
        String subject = request.get("subject");

        List<EventDto> calendarEvents;
        String email;
        try {
            email = userContextHolder.getCurrentUser().getEmail();
        } catch (Exception e) {
            email = request.get("email");
            if (!allowedEmails.contains(email.toLowerCase())) {
                throw new BusinessException("Email Id " + email + " is not present in our records");
            }
        }
        if (pollingMode.equalsIgnoreCase("EWS")) {
            calendarEvents = ewsService.getCalendarEvents(email, startDateTime, endDateTime, false);
        } else {
            calendarEvents = graphIntegrationService.getCalendarEventsV1(email, startDateTime, endDateTime,subject);
        }
        Map<String, Object> response = new HashMap<>();
        response.put("meetingCount", calendarEvents.size());
        response.put("meetings", calendarEvents);
        return response;
    }


    @PostMapping("/v2/getDaySummary")
    @Operation(summary = "Get day summary (v2)", tags = "Summary", description = "Retrieve the summary for the current day, optionally filtered by the specified type and date, using version 1 of the API.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_DAY_SUMMARY_V2}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Day summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getDaySummaryV2(@RequestBody Map<String, String> request) {
        String type = request.get("type");
        String date = request.get("date");
        UserInfo userInfo = userContextHolder.getCurrentUser();
        String email = userInfo.getEmail();

        log.debug("Inside @method getDaySummaryV2. @param : email -> {} type -> {} date -> {}", email, type, date);

        List<DaySummary> message = imailSummaryService.getDaySummaryV2(email, email, type, date);
        if (type == null) type = EmailConstants.TODAY;
        if (type.equalsIgnoreCase(EmailConstants.TODAY)) {
            try {
                List<EventDto> calendarEvents;
                if (pollingMode.equalsIgnoreCase("EWS")) {
                    calendarEvents = ewsService.getCalendarEvents(email, null, null, false);
                } else if (pollingMode.equalsIgnoreCase("Gmail")) {
                    log.info("inside getCalendarEvents for Gmail");
                    calendarEvents = gmailIntegration.getCalendarEvents(email, null, null);
                } else {
                    calendarEvents = graphIntegrationService.getCalendarEventsV1(email, null, null,null);
                }
                DaySummary daySummary = getTodayEvents(calendarEvents);

                message.add(daySummary);
            } catch (Exception e) {
                log.error("Error while adding calendar events. Exception message  : {}", e.getMessage());
            }
        }
        message.removeIf(Objects::isNull);
        long emailCount = imailSummaryService.countAllMailOfUser(email, type, date);
        Map<String, Object> obj = new HashMap<>();
        obj.put(EmailConstants.RESULT, message);
        obj.put("totalEmailCount", emailCount);
        return obj;
    }

    @PostMapping("/v2/getDateRangeSummary")
    @Operation(summary = "Get date range summary (v2)", tags = "Summary", description = "Retrieve the summary for the start and end date range, using version 1 of the API.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_SUMMARY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Date range summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getDateRangeSummary(@RequestBody Map<String, String> request) {
        String startDateTime = request.get("startDate");
        String endDateTime = request.get("endDate");
        Map<String, Object> obj = new HashMap<>();
        log.info("Starting getDateRangeSummary method");
        try {
            UserInfo userInfo = userContextHolder.getCurrentUser();
            String email = userInfo.getEmail();
            log.debug("Inside @method getDateRangeSummary. @param : email -> {} startDate -> {} endDate -> {}", email, startDateTime, endDateTime);
            LocalDateTime startDate = DateUtils.convertToLocalDateTime(startDateTime);
            LocalDateTime endDate = DateUtils.convertToLocalDateTime(endDateTime);
            List<DaySummary> message = imailSummaryService.getDateRangeSummary(email, startDate, endDate); // you will need to implement or adjust this service

           /* List<EventDto> calendarEvents;
            if (pollingMode.equalsIgnoreCase("EWS")) {
                calendarEvents = ewsService.getCalendarEvents(email, startDate, endDate, false);
            } else if (pollingMode.equalsIgnoreCase("Gmail")) {
                log.info("inside method getCalendarEvents for Gmail");
                calendarEvents = gmailIntegration.getCalendarEvents(email, startDate, endDate);
                log.info("Successfully retrieved calendar events for Gmail.");
            } else {
                calendarEvents = graphIntegrationService.getCalendarEventsV1(email, startDate, endDate);
                log.info("Successfully retrieved calendar events for Graph.");
            }
            DaySummary daySummary = getTodayEvents(calendarEvents);

            message.add(daySummary);*/

            message.removeIf(Objects::isNull);
            long emailCount = mailSummaryDao.countAllMailOfUser(startDate, endDate, email, "Sent Items");
            obj.put(EmailConstants.RESULT, message);
            obj.put("totalEmailCount", emailCount);
            log.info("Successfully retrieved date range summary.");
        } catch (Exception e) {
            log.error("Error while getting date range summary. Exception message  : {}", e.getMessage());
        }
        return obj;
    }

    @PostMapping("/getDaySummaryStructured")
    @Operation(summary = "Get day summary (v2)", tags = "Summary", description = "Retrieve the summary for the current day, optionally filtered by the specified type and date, using version 1 of the API.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_DAY_SUMMARY_STRUCTURED}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Day summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public StructuredResponse getDaySummaryStructured(@RequestBody Map<String, String> request) {
        String type = request.get("type");
        String date = request.get("date");
        UserInfo userInfo = userContextHolder.getCurrentUser();
        String email = userInfo.getEmail();

        log.debug("Inside @method getDaySummaryV2. @param : email -> {} type -> {} date -> {}", email, type, date);

        List<DaySummary> messages = imailSummaryService.getDaySummaryV2(email, email, type, date);

        String message = aiService.generateChatMessage(messages);
        return new StructuredResponse("custom", "MeetingSummary", message);
    }

    private record StructuredResponse(String use_template, String template_router, String message) {
    }


    @Deprecated
    @GetMapping("/v1/getUpcomingEvents")
    @Operation(summary = "Get upcoming events (v1)", tags = "Events", description = "Retrieve a list of upcoming events using version 1 of the API.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_UPCOMING_EVENTS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Upcoming events retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getUpcomingEvents() {
        UserInfo userInfo = userContextHolder.getCurrentUser();
        String email = userInfo.getEmail();
        List<DaySummary> message = new ArrayList<DaySummary>();
        Integer totalMeetingCount = null;
        try {

            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            String startDateTime = DateUtils.getUTCString(true, 0, preferences.getTimeZone());
            String endDateTime = DateUtils.getUTCString(false, 6, preferences.getTimeZone());

            log.info("start time is {}", startDateTime);
            log.info("end time is {}", endDateTime);
            List<EventDto> calendarEvents;
            if (pollingMode.equalsIgnoreCase("EWS")) {
                calendarEvents = ewsService.getCalendarEvents(email, startDateTime, endDateTime, false);
            } else if (pollingMode.equalsIgnoreCase("Gmail")) {
                log.info("inside get upcoming events for Gmail");
                calendarEvents = gmailIntegration.getCalendarEvents(email, startDateTime, endDateTime);
            } else {
                calendarEvents = graphIntegrationService.getCalendarEventsV1(email, startDateTime, endDateTime,null);
            }
            totalMeetingCount = calendarEvents.size();
            DaySummary daySummary = getUpcomingEvents(calendarEvents);

            message.add(daySummary);
        } catch (Exception e) {
            log.error("Error while adding calendar events. Exception message  : {}", e.getMessage());
        }
        message.removeIf(Objects::isNull);
        Map<String, Object> obj = new HashMap<>();
        obj.put(EmailConstants.RESULT, message);
        obj.put("totalMeetingCount", totalMeetingCount);
        return obj;
    }

    @PostMapping("/v1/getSummaryByType")
    @Operation(summary = "Get summary by type", tags = "Summary", description = "Retrieve a summary based on the specified type and action.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_SUMMARY_BY_TYPE}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getSummaryByTypeV1(@RequestBody Map<String, String> request) {

        String type = request.get("type");
        String action = request.get("action");
        String date = request.get("date");

        log.debug("Inside @method getSummaryByTypeV1 in rest. @param : type -> {} action -> {} date -> {}", type, action, date);

        if (action != null && action.equals("Meeting")) {
            if (type.equalsIgnoreCase(EmailConstants.TODAY)) {
                String email = userContextHolder.getCurrentUser().getEmail();
                try {
                    List<EventDto> calendarEvents;
                    if (pollingMode.equalsIgnoreCase("EWS")) {
                        calendarEvents = ewsService.getCalendarEvents(email, null, null, false);
                    } else if (pollingMode.equalsIgnoreCase("Gmail")) {
                        calendarEvents = gmailIntegration.getCalendarEvents(email, null, null);
                    } else {
                        calendarEvents = graphIntegrationService.getCalendarEventsV1(email, null, null,null);
                    }
                    calendarEvents.sort(Comparator.comparing(EventDto::getMeetingStartTime));
                    return Map.of(EmailConstants.MAIL_OBJECT, calendarEvents);
                } catch (Exception e) {
                    log.error("Error while adding calendar events. Exception message -> {} ", e.getMessage());
                }
            } else if (type.equalsIgnoreCase("upcoming")) {
                String email = userContextHolder.getCurrentUser().getEmail();
                try {
                    EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
                    String startDateTime = DateUtils.getUTCString(true, 0, preferences.getTimeZone());
                    String endDateTime = DateUtils.getUTCString(false, 6, preferences.getTimeZone());

                    log.info("start time is {}", startDateTime);
                    log.info("end time is {}", endDateTime);
                    List<EventDto> calendarEvents;
                    if (pollingMode.equalsIgnoreCase("EWS")) {
                        calendarEvents = ewsService.getCalendarEvents(email, startDateTime, endDateTime, false);
                    } else if (pollingMode.equalsIgnoreCase("Gmail")) {
                        calendarEvents = gmailIntegration.getCalendarEvents(email, startDateTime, endDateTime);
                    } else {
                        calendarEvents = graphIntegrationService.getCalendarEventsV1(email, startDateTime, endDateTime,null);
                    }
                    return Map.of(EmailConstants.MAIL_OBJECT, calendarEvents);
                } catch (Exception e) {
                    log.error("Error while adding calendar events. Exception message -> {} ", e.getMessage());
                }
            }
        }
        List<MailSummary> mailSummaries = imailSummaryService.getSummaryByTypeV1(type, action, date);
        return Map.of(EmailConstants.MAIL_OBJECT, mailSummaries);
    }

    @PostMapping("/v1/getDateRangeSummaryByAction")
    @Operation(summary = "Get date range summary by type", tags = "Summary", description = "Retrieve a summary based on the specified type , action and date range.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_DATE_RANGE_SUMMARY_BY_ACTION}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getDateRangeSummaryByAction(@RequestBody Map<String, String> request) {
        log.info("Starting getDateRangeSummaryByAction method");
        List<MailSummary> mailSummaries = new ArrayList<>();
        try {
            String action = request.get("action");
            String startDate = request.get("startDate");
            String endDate = request.get("endDate");
            UserInfo userInfo = userContextHolder.getCurrentUser();
            String userId = userInfo.getEmail();
            log.debug("Inside @method getDateRangeSummaryByAction in rest. action -> {} startDate -> {} endDate -> {} ", action, startDate, endDate);
            mailSummaries = imailSummaryService.getDateRangeSummaryByAction(userId, startDate, endDate, action);
            log.info("Successfully retrieved {} mail summaries for action: {}", mailSummaries.size(), action);

        } catch (Exception e) {
            log.error("Error in getDateRangeSummaryByAction: {}", e.getMessage(), e);
            auditLog.error("Error in getDateRangeSummaryByAction", e.getMessage(), getStackTraceAsString(e),
                    null, null, "DATE_RANGE_SUMMARY", userContextHolder.getCurrentUser().getId(),
                    null, null, null, null, AIUtils.convertToJSON(request, true), null);
        }
        return Map.of(EmailConstants.MAIL_OBJECT, mailSummaries);
    }

    private DaySummary getTodayEvents(List<EventDto> calendarEvents) {
        DaySummary daySummary = new DaySummary();
        if (calendarEvents == null || calendarEvents.isEmpty()) {
            daySummary.setContent(EmailConstants.getMeetingMessage());
            daySummary.setMobileContent(EmailConstants.getMeetingMessage());
            daySummary.setValue(0);
            daySummary.setMeetings(null);
        } else if (calendarEvents.size() == 1) {
            daySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + calendarEvents.size() + " meeting</span> remaining today");
            daySummary.setMobileContent("Remaining meeting today");
            daySummary.setValue(calendarEvents.size());
            daySummary.setAction("Meeting");
            daySummary.setMeetings(calendarEvents);
        } else {
            daySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + calendarEvents.size() + " meetings</span> remaining today");
            daySummary.setMobileContent("Remaining meetings today");
            daySummary.setAction("Meeting");
            daySummary.setValue(calendarEvents.size());
            daySummary.setMeetings(calendarEvents);
        }
        return daySummary;
    }

    private DaySummary getUpcomingEvents(List<EventDto> calendarEvents) {
        DaySummary daySummary = new DaySummary();
        if (calendarEvents == null || calendarEvents.isEmpty()) {
            daySummary.setContent(EmailConstants.getMeetingMessage());
            daySummary.setMobileContent(EmailConstants.getMeetingMessage());
            daySummary.setValue(0);
            daySummary.setMeetings(null);
        } else if (calendarEvents.size() == 1) {
            daySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + calendarEvents.size() + " meeting</span> scheduled for next week.");
            daySummary.setMobileContent("Meeting scheduled for next week");
            daySummary.setAction("Meeting");
            daySummary.setValue(calendarEvents.size());
            daySummary.setMeetings(calendarEvents);
        } else {
            daySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + calendarEvents.size() + " meetings</span> scheduled for next week");
            daySummary.setMobileContent("Meetings scheduled for next week");
            daySummary.setValue(calendarEvents.size());
            daySummary.setAction("Meeting");
            daySummary.setMeetings(calendarEvents);
        }
        return daySummary;
    }

    @Deprecated
    @GetMapping(path = "/getCalendarEventById")
    @Operation(summary = "Get calendar event by ID", tags = "Calendar", description = "Retrieve calendar event details by the specified event ID.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_CALENDAR_EVENT_BY_ID}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public List<EventDto> getCalendarEventById(@RequestParam String id, @RequestParam(name = APIConstants.INTERNET_MESSAGE_ID, required = false) String internetMessageId) throws Exception {
        String email = userContextHolder.getCurrentUser().getEmail();

        if (pollingMode.equalsIgnoreCase("EWS")) {
            id = id.replace(" ", "+");
            id = id.replace("_", "+");
            id = id.replace("-", "/");
        } else {
            id = id.replace("/", "-");
        }

        log.debug("Inside @method getCalendarEventById. @param : email -> {} id -> {}", email, id);
        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.getCalendarEventById(email, id);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.getCalendarEventsByMessageId(email, Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, email)).map(MailSummary::getMessageId).orElse(id));
        } else {
            return graphIntegrationService.getCalendarEventById(email, id);
        }
    }

    @Deprecated
    @PostMapping(path = "/v1/getCalendarEventById")
    @Operation(summary = "Get calendar event by ID (v1)", tags = "Calendar", description = "Retrieve calendar event details by the specified event ID using version 1 of the API.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_CALENDAR_EVENT_BY_ID}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public List<EventDto> getCalendarEventByIdV1(@RequestBody Map<String, String> request) throws Exception {
        String email = userContextHolder.getCurrentUser().getEmail();
        String id = request.get("id");
        String internetMessageId = request.get("internetMessageId");
        log.debug("Inside @method getCalendarEventByIdV1. @param : email -> {} id -> {}", email, id);
        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.getCalendarEventById(email, id);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            internetMessageId = internetMessageId.replace(" ", "+");
            return gmailIntegration.getCalendarEventsByMessageId(email, Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, email)).map(MailSummary::getMessageId).orElse(id));
        } else {
            return graphIntegrationService.getCalendarEventById(email, id);
        }
    }

    @PostMapping("/getEmailByInternetMessageId")
    @Operation(summary = "Get email by Internet Message ID", tags = "Email", description = "Retrieve an email using its Internet Message ID.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_EMAIL_BY_INTERNET_MESSAGE_ID}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public UserEmailDto getEmailByInternetMessageId(@RequestBody Map<String, String> request) {
        String internetMessageId = request.get("internetMessageId");
        String folderName = request.get("folderName");
        log.debug("Inside @method getEmailByInternetMessageId. @param : internetMessageId -> {}", internetMessageId);
        try {
            String email = userContextHolder.getCurrentUser().getEmail();
            if (pollingMode.equalsIgnoreCase("EWS")) {
                return ewsService.getEmailByInternetMessageId(email, internetMessageId, folderName);
            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                return gmailIntegration.getMessageById(Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, email)).map(MailSummary::getMessageId).orElse(internetMessageId));
            } else {
                return graphIntegrationService.getEmailByInternetMessageId(email, internetMessageId, folderName);
            }
        } catch (Exception e) {
            log.error("Error inside @method getMailSummary", e);
            throw new BusinessException(e.getMessage());
        }
    }

    @GetMapping(path = "/declineMeeting")
    @Operation(summary = "Decline meeting", tags = "Meeting", description = "Decline a meeting invitation using the specified event ID.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_DECLINE_MEETING}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Meeting declined successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> declineMeeting(@RequestParam String eventId) throws Exception {
        if (pollingMode.equalsIgnoreCase("EWS")) {
            eventId = eventId.replace(" ", "+");
            eventId = eventId.replace("_", "+");
            eventId = eventId.replace("-", "/");
        } else {
            eventId = eventId.replace("/", "-");
        }

        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.declineMeeting(eventId);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.updateMeetingStatus(eventId, DECLINED);
        } else {
            return graphIntegrationService.declineMeeting(eventId);
        }
    }

    @GetMapping(path = "/acceptMeeting")
    @Operation(summary = "Accept meeting", tags = "Meeting", description = "Accept a meeting invitation using the specified event ID.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_ACCEPT_MEETING}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Meeting accepted successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> acceptMeeting(@RequestParam String eventId) throws Exception {
        if (pollingMode.equalsIgnoreCase("EWS")) {
            eventId = eventId.replace(" ", "+");
            eventId = eventId.replace("_", "+");
            eventId = eventId.replace("-", "/");
        } else {
            eventId = eventId.replace("/", "-");
        }

        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.acceptMeeting(eventId);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.updateMeetingStatus(eventId, ACCEPTED);
        } else {
            return graphIntegrationService.acceptMeeting(eventId);
        }
    }

    @GetMapping(path = "/tentativelyAccept")
    @Operation(summary = "Tentatively accept meeting", tags = "Meeting", description = "Tentatively accept a meeting invitation using the specified event ID.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_TENTATIVELY_ACCEPT_MEETING}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Meeting tentatively accepted successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> tentativelyAcceptMeeting(@RequestParam String eventId) throws Exception {
        if (pollingMode.equalsIgnoreCase("EWS")) {
            eventId = eventId.replace(" ", "+");
            eventId = eventId.replace("_", "+");
            eventId = eventId.replace("-", "/");
        } else {
            eventId = eventId.replace("/", "-");
        }

        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.tentativelyAcceptMeeting(eventId);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.updateMeetingStatus(eventId, TENTATIVE);
        } else {
            return graphIntegrationService.tentativelyAccept(eventId);
        }
    }

    @PostMapping(path = "/scheduleEvent")
    @Operation(summary = "Schedule event", tags = "Event", description = "Schedule an event based on the provided request body.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_SCHEDULE_EVENT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event scheduled successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> scheduleEvent(@RequestBody Map<String, Object> jsonBody) {
        String email;
        try {
            email = userContextHolder.getCurrentUser().getEmail();
        } catch (Exception e) {
            email = (String) jsonBody.get("organiser");
            if (!allowedEmails.contains(email.toLowerCase())) {
                throw new BusinessException("Email Id " + email + " is not present in our records");
            }
        }

        String result;
        String timeZone = (String) jsonBody.get(EmailConstants.TIME_ZONE);
        if (timeZone == null || timeZone.isBlank()) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            timeZone = preferences.getTimeZone();
            jsonBody.put(EmailConstants.TIME_ZONE, timeZone);
        }

        String meetingType = (String) jsonBody.get("meetingType");
        if (meetingType == null || meetingType.isBlank()) {
            jsonBody.put("meetingType", "Teams");
        }

        if (pollingMode.equalsIgnoreCase("EWS")) {
            result = ewsService.scheduleEvent(email, jsonBody);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            result = gmailIntegration.createMeetingEvent(email, jsonBody);
        } else {
            String meetingRequestJson = CommonUtils.convertToMeetingRequestJson(jsonBody, email);
            Map<String,Object> response = graphIntegrationService.scheduleEvent(email, meetingRequestJson);
            return response;
        }
        return Map.of(EmailConstants.RESULT, result);
    }

    private void updateTimeZoneUserWise(String userId, Map<String, Object> jsonBody) {
        String timeZone = (String) jsonBody.get(EmailConstants.TIME_ZONE);
        if (timeZone == null || timeZone.isBlank()) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
            timeZone = preferences.getTimeZone();
            jsonBody.put(EmailConstants.TIME_ZONE, timeZone);
        }
    }

    @PostMapping(path = "/scheduleEventRecurring")
    @Operation(summary = "Schedule recurring event", tags = "Event", description = "Schedule a recurring event based on the provided request body.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_SCHEDULE_EVENT_RECURRING}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event scheduled successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> scheduleEventRecurring(@RequestBody Map<String, Object> jsonBody) {
        String email = userContextHolder.getCurrentUser().getEmail();

        String timeZone = (String) jsonBody.get(EmailConstants.TIME_ZONE);
        if (timeZone == null || timeZone.isBlank()) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            timeZone = preferences.getTimeZone();
            jsonBody.put(EmailConstants.TIME_ZONE, timeZone);
        }

        log.debug("Inside @method scheduleEventRecurring. @param : email -> {}", email);
        String result;
        if (pollingMode.equalsIgnoreCase("EWS")) {
            result = ewsService.scheduleEventRecurring(email, jsonBody);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            updateTimeZoneUserWise(email, jsonBody);
            result = gmailIntegration.createMeetingEvent(email, jsonBody);
        } else {
            String meetingRequestJson = CommonUtils.convertToRecurringMeetingRequestJson(jsonBody, email);
            Map<String,Object> response =graphIntegrationService.scheduleEvent(email, meetingRequestJson);
            result = (String) response.get(EmailConstants.RESULT);
        }
        if (result.equals(EmailConstants.SUCCESS)) {
            String conversationId = (String) jsonBody.get("conversationId");
            String body = (String) jsonBody.get("body");
            if (conversationId != null && body != null && !body.isBlank()) {
                try {
                    takeAction(email, conversationId, body);
                } catch (Exception e) {
                    log.error("Error while taking action");
                }
            }
        }
        return Map.of(EmailConstants.RESULT, result);
    }

    @Async
    private void takeAction(String userId, String conversationId, String body) {
        log.debug("Inside @method takeAction. @param : userId -> {}", userId);

        List<ActionItemDto> actionItemDetails = imailSummaryService.getActionItemDetails(userId, conversationId, null);
        if (actionItemDetails.isEmpty()) {
            log.debug("No action item is found for the conversation for user id : {}", userId);
            return;
        }
//            EmailThread emailThread = emailThreadDao.findByConversationId(conversationId, userId);
        List<ActionTakenResponse> actionOwnerResponses = aiService.checkActionTaken(body, userId, actionItemDetails.toString(), Map.of("emailBody", body, "userId", userId, "actionItemDetails", actionItemDetails.toString(), "type", "AUTO_ACTION"));
        log.debug("actionOwnerResponses from AI : {}", actionOwnerResponses);
        if (actionOwnerResponses != null && !actionOwnerResponses.isEmpty()) {
            log.debug("Action owner response for userid {} and response is {}", userId, actionOwnerResponses);
            for (ActionTakenResponse actionTakenResponse : actionOwnerResponses) {

                UserActions userActions = userActionsDao.findById(actionTakenResponse.getMailSummaryId()).orElse(null);
                if (userActions != null) {
                    log.debug("Updating user action for ActionTakenResponse : {}", actionTakenResponse);
                    userActions.setActionTaken(actionTakenResponse.getActionTaken());
                    userActions.setActionTakenReason(actionTakenResponse.getReasoning());
                    userActionsDao.save(userActions);
                }
            }
        }
    }

    @PostMapping(path = "/rescheduleEvent")
    @Operation(summary = "rescheduleEvent", tags = "Email", description = "Api used to reschedule event",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_RESCHEDULE_EVENT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event rescheduled successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> rescheduleEvent(@RequestBody Map<String, String> request) {
        log.debug("Inside @method rescheduleEvent.");
        String email = userContextHolder.getCurrentUser().getEmail();
        if (pollingMode.equalsIgnoreCase("EWS")) {
            return ewsService.rescheduleEvent(request);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            return gmailIntegration.rescheduleEvent(email, request);
        } else {
            return graphIntegrationService.rescheduleEvent(request);
        }
    }

    @Deprecated
    @GetMapping(path = "/createCategoriesForAllUsers")
    @Operation(summary = "Create categories for all users", tags = "Categories", description = "Create categories for all users in the system.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_CREATE_CATEGORIES}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Categories created successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public String createCategoriesForAllUsers() throws Exception {
        return graphIntegrationService.createCategoriesForAllUsers();
    }

    @GetMapping("/deployBuild")
    public String deployBuild() {
        log.debug("inside @method deployBuild");
        buildEmailPoller.pollBuildEmail();
        return "done";
    }

    @GetMapping("/checkBuildStatus")
    public String checkBuildStatus() {
        log.debug("inside @method checkBuildStatus");
        buildEmailPoller.checkBuildStatus();
        return "done";
    }


    @PostMapping("/getAttachmentStatus")
    @Operation(summary = "Get attachment status", tags = "Attachment", description = "Retrieve the status of attachments based on the provided request body.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_ATTACHMENT_STATUS}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Attachment status retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, List<Attachments>> getAttachmentStatus(@RequestBody Map<String, String> request) {
        String internetMessageId = request.get("internetMessageId");
        String userId = userContextHolder.getCurrentUser().getId();
        String messageId = request.get("messageId");
        log.debug("InternetMessageId and messageId for attachment status is {}, {}", internetMessageId, messageId);
        if (internetMessageId == null || internetMessageId.isEmpty() && (messageId != null && !messageId.isEmpty())) {
            internetMessageId = mailSummaryDao.findByMessageId(messageId, userId).getInternetMessageId();
        }
        Map<String, List<Attachments>> obj = new HashMap<String, List<Attachments>>();
        try {
            List<Attachments> attachmentList = graphIntegrationService.findAttachmentStatus(internetMessageId);
            obj.put(EmailConstants.RESULT, attachmentList);
            return obj;
        } catch (Exception e) {
            log.error("Error while getting attachment status ", e);
            obj.put(EmailConstants.RESULT, null);
            return obj;
        }
    }

    @GetMapping("/reProcessEmail/{id}")
    @Operation(summary = "Process email by Failure Logs ID", tags = "Email", description = "Process an email using its Internet Message ID based on the provided request data.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_REPROCESS_EMAIL}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email processed successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> reProcessEmail(@PathVariable("id") Integer failureId) {
        Optional<FailureLogs> failureObject = failureDao.findById(failureId);
        //Map<String, String> request = new HashMap<>();
        JSONObject request = new JSONObject();
        if (failureObject.get() != null) {
            request = new JSONObject(failureObject.get().getIntent());
            log.debug("request object is {}", request.toString());
        } else {
            Map<String, String> resultMap = new HashMap<String, String>();
            resultMap.put("result", "Failed");
        }
        if (request.has("internetMessageId") && request.has("userId") && request.has("folderName")) {
            String internetMessageId = request.getString("internetMessageId");
            String userId = request.getString("userId");
            String folderName = request.getString("folderName");
            UserEmailDto mailObject;
            log.debug("Inside @method getEmailByInternetMessageId. @param : internetMessageId -> {}", internetMessageId);
            try {
                if (pollingMode.equalsIgnoreCase("EWS")) {
                    mailObject = ewsService.getEmailByInternetMessageId(userId, internetMessageId, folderName);
                } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                    mailObject = gmailIntegration.getMessageById(Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, userId)).map(MailSummary::getMessageId).orElse(internetMessageId));
                } else {
                    mailObject = graphIntegrationService.getEmailByInternetMessageId(userId, internetMessageId, folderName);
                }
                Map<String, Object> resultMap = aiPolling.manualProcessEmail(mailObject, userId, folderName);
                if (resultMap.get("result").equals("Success")) {
                    FailureLogs obj = failureObject.get();
                    obj.setStatus("COMPLETED");
                    failureDao.save(obj);
                }
                return resultMap;
            } catch (Exception e) {
                log.error("Error inside @method getMailSummary", e);
                Map<String, Object> resultMap = new HashMap<String, Object>();
                resultMap.put("result", "Failed");
                return resultMap;
            }
        } else {
            Map<String, Object> resultMap = new HashMap<String, Object>();
            resultMap.put("result", "Failed");
            return resultMap;
        }
    }

    @GetMapping("/getSupportedMeetingTypes")
    @Operation(summary = "Get Supported Meeting Types", tags = "Meetings", description = "Fetches the list of supported meeting types for the organization based on the current user's email.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_SUPPORTED_MEETING_TYPES}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved supported meeting types."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving meeting types.")
    })
    public List<String> getSupportedMeetingTypes() {
        log.debug("Going to get supported meeting types for {}", userContextHolder.getCurrentUser().getId());
        try {
            Organisation organisation = orgRepo.findByOrgName(CommonUtils.getOrganizationFromEmail(userContextHolder.getCurrentUser().getId()));
            if (organisation != null) {
                List<String> meetingType = Arrays.asList(organisation.getMeetingType().split(","));
                return meetingType;
            }

        } catch (Exception e) {
            log.error("Error while getting meeting type list", e);
        }
        return new ArrayList<>();
    }

    @PostMapping("/getAnswerFromSummary")
    @Operation(summary = "Get answer from attachment", tags = "Attachment", description = "Retrieve an answer based on the provided attachment details.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_ANSWER_FROM_SUMMARY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Answer retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> getAnswerFromSummary(@RequestBody Map<String, Object> requestBody) {
        try {
            log.debug("going to query vector to get answer for {}", requestBody.toString());
            String answerFromAttachment = graphIntegrationService.queryMailSummaryInVector(requestBody);
            return Map.of("answer", answerFromAttachment);
        } catch (Exception e) {
            log.error("Error inside @method getAnswerFromAttachment", e);
            auditLog.error("Error while getting answer from attachment", e.getMessage(), getStackTraceAsString(e), null, null, "VOICE_ATTACHMENT_ANSWER", userContextHolder.getCurrentUser().getEmail(), null, null, null, null, requestBody, null);
            return Map.of("answer", "Failed to get Answer");
        }
    }

    @PostMapping("/regenrateObjectives")
    @Operation(summary = "Get answer from attachment", tags = "Attachment", description = "Retrieve an answer based on the provided attachment details.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_REGENERATE_OBJECTIVES}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Answer retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> regenrateObjectives(@RequestBody Map<String, String> requestBody) {
        String internetMessageId = requestBody.get("internetMessageId");
        String messageId = requestBody.get("messageId");
        String userId = userContextHolder.getCurrentUser().getId();
        Map<String, Object> responseMap = new HashMap<>();
        Map<String, String> auditMap = new HashMap<>();
        auditMap.put("type", "REGENERATE_OBJECTIVE");
        auditMap.put("userId", userId);
        auditMap.put("internetMessageId", internetMessageId);
        try {
            log.debug("going to get email by internetMessageId {} userId {}", internetMessageId, userId);
            UserEmailDto mailObject;

            MailSummary summary = mailSummaryDao.findByInternetMessageId(internetMessageId, userId);
            if((internetMessageId == null || internetMessageId.isEmpty()) && (messageId != null && !messageId.isEmpty())){
                summary = mailSummaryDao.findByMessageId(messageId, userId);
            }
            if (summary == null) {
                responseMap.put("result", null);
                auditLog.error("Error inside @method regenerate objective", "Mail Summary not found", "Mail Summary not found", null, auditMap.get("internetMessageId"), auditMap.get("type"), auditMap.get("userId"), null, null, null, null,
                        AIUtils.convertToJSON(auditMap, true), null);
                return responseMap;
            } else {
                String folderName = summary.getFolderName();
                auditMap.put("folderName", folderName);

                EmailThread thread = threadDao.findByConversationId(summary.getConversationId(), userId);
                if (thread == null) {
                    responseMap.put("result", null);
                    auditLog.error("Error inside @method regenerate objective", "Thread Summary not found", "Thread Summary not found", null, auditMap.get("internetMessageId"), auditMap.get("type"), auditMap.get("userId"), null, null, null, null,
                            AIUtils.convertToJSON(auditMap, true), null);
                    return responseMap;
                } else {
                    if (pollingMode.equalsIgnoreCase("EWS")) {
                        mailObject = ewsService.getEmailByInternetMessageId(userId, internetMessageId, folderName);
                    } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                        mailObject = gmailIntegration.getMessageById(summary.getMessageId());
                    } else {
                        mailObject = graphIntegrationService.getEmailByInternetMessageId(userId, internetMessageId, folderName);
                    }
                    String content = mailObject.getBody();
                    if(content == null){
                        content = "";
                    }
                    String previousObjective = summary.getObjective();
                    String threadSummary = thread.getThreadSummary();
                    List<ObjectiveAIResponse> aiResponse = aiService.regenerateObjective(content, threadSummary, userId, auditMap, previousObjective, internetMessageId);
                    responseMap.put("result", aiResponse);
                    return responseMap;
                }
            }

        } catch (Exception e) {
            log.error("Error inside @method getMailSummary", e);
            responseMap.put("result", "Failed");
            return responseMap;
        }

    }


    @PostMapping("/onDemandProcessEmail")
    @Operation(summary = "Process email by Failure Logs ID", tags = "Email", description = "Process an email using its Internet Message ID based on the provided request data.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_ON_DEMAND_PROCESS_EMAIL}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email processed successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> onDemandProcessEmail(@RequestBody Map<String, String> requestBody) {
        log.debug("Inside method onDemandPolling {}", requestBody.get("internetMessageId"));
        String internetMessageId = requestBody.get("internetMessageId");
        String messageId = requestBody.get("messageId");

        String userId = userContextHolder.getCurrentUser().getId();
        List<UserFolders> activeMailFolders = userFoldersRepository.findByEmail(userId);
        UserEmailDto mailObject = null;
        String folderName = null;
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (pollingMode.equalsIgnoreCase("EWS")) {
//                mailObject = ewsService.getEmailByInternetMessageId(userId, internetMessageId);
                for (int i = 0; i < activeMailFolders.size(); i++) {
                    mailObject = ewsService.getEmailByInternetMessageId(userId, internetMessageId, activeMailFolders.get(i).getDisplayName());
                    if (mailObject != null) {
                        folderName=activeMailFolders.get(i).getDisplayName();
                        break;
                    }
                }
            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                for (int i = 0; i < activeMailFolders.size(); i++) {
                    mailObject = gmailIntegration.getMessageByInternetMessageId(internetMessageId, activeMailFolders.get(i).getDisplayName());
                    if(mailObject == null){
                        mailObject =  gmailIntegration.getMessageByIdAndFolderName(messageId,activeMailFolders.get(i).getDisplayName());
                    }
                    if (mailObject != null) {
                        folderName = activeMailFolders.get(i).getDisplayName();
                        break;
                    }
                }

            } else {
                mailObject = graphIntegrationService.getEmailByInternetMessageId(userId, internetMessageId, null);
                if (mailObject != null) {
                    String displayName = userFoldersRepository.findDisplayNameByFolderIdAndEmail(mailObject.getFolderName(), userId);
                    mailObject.setFolderName(displayName);
                }

            }
            if (mailObject != null) {
                resultMap = aiPolling.manualProcessEmail(mailObject, userId, folderName);
                if (resultMap.get("result").toString().equalsIgnoreCase("Success")) {
                    MailSummary summary = mailSummaryDao.findByInternetMessageId(internetMessageId, userId);
                    if(summary == null && pollingMode.equalsIgnoreCase("GMAIL")){
                        summary =  mailSummaryDao.findByMessageId(messageId,userId);
                    }
                    resultMap.put("result", summary);
                }
            } else {
                log.error("Mail Object not found with internet message id {} userId {} folderName {}", internetMessageId, userId, folderName);
                resultMap.put("result", "Failed");
            }
            return resultMap;
        } catch (Exception e) {
            log.error("Error inside @method onDemandPolling", e);
            resultMap.put("result", "Failed");
        }

        resultMap.put("result", "Failed");
        return resultMap;
    }

    @GetMapping(path = "/generateMeetingSummary")
    @Operation(summary = "Generate Meeting Summary", tags = "Meetings", description = "Generates a summary of meetings based on the user's data and other relevant information.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GENERATE_MEETING_SUMMARY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully generated the meeting summary."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while generating the meeting summary.")
    })
    public void generateMeetingSummary() throws IOException, InterruptedException {


        graphIntegrationService.generateMeetingSummary();
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "emailStatsByFilter")
    @Operation(summary = "Retrieve Email Stats by Filter", tags = "Email Stats", description = "Fetches email statistics based on the provided filters such as minimum and maximum records.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_EMAIL_STATS_BY_FILTER}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved email stats."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving email stats.")
    })
    List<EmailStats> failureLogByFilter(
            @Parameter(name = "Minimum number of records required") @RequestParam(required = false, name = "llimit") Integer llimit,
            @Parameter(name = "Maximum number of records required") @RequestParam(required = false, name = "ulimit") Integer ulimit,
            @Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap) {
        return graphIntegrationService.emailStatsByFilter(llimit, ulimit, filterMap);
    }


    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "emailStatsCountByFilter")
    @Operation(summary = "Retrieve Email Stats Count by Filter", tags = "Email Stats", description = "Fetches the count of email statistics based on the provided filter criteria.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_EMAIL_STATS_COUNT_BY_FILTER}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the email stats count."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the email stats count.")
    })
    long failureLogCountByFilter(
            @Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap) {
        return graphIntegrationService.emailStatsCountByFilter(filterMap);
    }


    @PostMapping(path = "/markMailRead")
    @Operation(summary = "Mark Mail as Read or Unread", tags = "Email Operations", description = "Marks a specified email as read or unread based on the provided criteria. Supports EWS, Gmail, and Graph integration.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_MARK_MAIL_READ}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully marked the email as read or unread."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while marking the email read/unread.")
    })
    Map<String, String> markMailRead(
            @Parameter(name = "requestMap") @RequestBody Map<String, String> requestMap) {
        String email = userContextHolder.getCurrentUser().getId();
        String internetMessageId = requestMap.get("internetMessageId");
        String folderName = requestMap.get("folderName");
        Boolean markAsRead = Optional.ofNullable(requestMap.get("markAsRead"))
                .filter(value -> !value.isEmpty())
                .map(Boolean::parseBoolean)
                .orElse(true);
        String response;
        if (pollingMode.equalsIgnoreCase("EWS")) {
            response = ewsService.markMessageReadUnread(email, internetMessageId, markAsRead);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            response = gmailIntegration.markMessageReadUnread(email, internetMessageId, markAsRead);
        } else {
            response = graphIntegrationService.markMessageReadUnread(email, internetMessageId, markAsRead, folderName);
        }
        Map<String, String> map = new HashMap<>();
        map.put("result", response);
        return map;

    }

    @PostMapping("/deleteEmail")
    @Operation(summary = "Delete Email by ID", tags = "Emails", description = "Deletes an email from the user's mailbox based on the provided email ID.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_DELETE_EMAIL}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully deleted the email."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while deleting the email.")
    })
    public Map<String, String> deleteEmail(@RequestBody Map<String, String> requestBody) {

        String messageId = requestBody.get("messageId");
        String userEmail = userContextHolder.getCurrentUser().getEmail();

        if (pollingMode.equalsIgnoreCase("EWS")) {
            messageId = messageId.replace(" ", "+");
            messageId = messageId.replace("_", "+");
            messageId = messageId.replace("-", "/");
        } else {
            messageId = messageId.replace("/", "-");
        }

        Map<String, String> response = new HashMap<>();
        try {
            boolean isDeleted;

            if (pollingMode.equalsIgnoreCase("EWS")) {
                isDeleted = ewsService.deleteEmailById(userEmail, messageId);
            } else {
                isDeleted = graphIntegrationService.deleteEmailById(userEmail, messageId);
            }
            if (isDeleted) {
                response.put("result", "success");
            } else {
                response.put("result", "failed");
            }
        } catch (Exception e) {
            log.error("Error deleting email with ID: {}", messageId, e);
            response.put("result", "failed");
        }
        return response;
    }

    @PostMapping("/v1/getAvailableSlotsAndConflict")
    @Operation(summary = "Get available slots and conflicts (v1)", tags = "Meeting", description = "Retrieve available meeting slots and any scheduling conflicts based on the provided request data using the getSchedule API.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_AVAILABLE_SLOTS_AND_CONFLICT_V1}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Available slots and conflicts retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public AvailableSlots getAvailableSlotsAndConflictV1(@RequestBody Map<String, Object> request) throws Exception {
        List<String> emails = (List<String>) request.get("requiredAttendees");
        String startDateTime = (String) request.get("startTime");
        String endDateTime = (String) request.get("endTime");
        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userContextHolder.getCurrentUser().getEmail());
        String timeZone = preferences.getTimeZone();
        Integer slotDuration = (Integer) request.get("slotDuration");
        if (slotDuration == null) slotDuration = 30;
        if (startDateTime != null && endDateTime != null && !startDateTime.endsWith("Z")) {
            startDateTime = DateUtils.convertToUTCString(startDateTime, timeZone);
            endDateTime = DateUtils.convertToUTCString(endDateTime, timeZone);
        }
        AvailableSlots availableSlotsAndConflict;
        if (pollingMode.equalsIgnoreCase("EWS")) {
            availableSlotsAndConflict = ewsService.getAvailableSlotsAndConflict(emails, startDateTime, endDateTime, slotDuration);
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            availableSlotsAndConflict = gmailIntegration.getAvailableSlotsAndConflict(emails, startDateTime, endDateTime, slotDuration);
        } else {
            availableSlotsAndConflict = graphIntegrationService.getAvailableSlotsAndConflictV1(emails, startDateTime, endDateTime, slotDuration);
        }
        return availableSlotsAndConflict;
    }

    @PostMapping("/v2/getAvailability")
    @Operation(summary = "Get availability using getSchedule API", tags = "Meeting", description = "Retrieve availability information for multiple users using the Microsoft Graph getSchedule API.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_AVAILABILITY}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Availability information retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public AvailabilityResponse getAvailability(@RequestBody Map<String, Object> request) {
        List<String> emails = (List<String>) request.get("schedules");
        String startDateTime = (String) request.get("startTime");
        String endDateTime = (String) request.get("endTime");
        Integer slotDuration = (Integer) request.get("availabilityViewInterval");
        if (slotDuration == null) slotDuration = 30;

        try {
            return graphIntegrationService.getAvailability(emails, startDateTime, endDateTime, slotDuration);
        } catch (Exception e) {
            log.error("error in /v2/getAvailability : {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/cancelEvent")
    @Operation(summary = "Cancel event", tags = "Event", description = "Cancel an event based on the provided event ID and comment.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_CANCEL_EVENT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event cancelled successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> cancelEvent(@RequestBody Map<String, String> request) throws Exception {
        String eventId = request.get("eventId");
        String comment = request.get("comment");
        try {
            return graphIntegrationService.cancelEvent(eventId, comment);
        } catch (Exception e) {
            log.error("error in /cancelEvent : {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/forwardEvent")
    @Operation(summary = "Forward event", tags = "Event", description = "Forward a calendar event to multiple recipients with a comment.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_FORWARD_EVENT}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event forwarded successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> forwardEvent(@RequestBody ForwardEventDto forwardEventDto) throws Exception {
        String eventId = forwardEventDto.getEventId();
        List<String> emailIds = forwardEventDto.getEmailIds();
        String comment = forwardEventDto.getComment();
        try {
            return graphIntegrationService.forwardEvent(eventId, emailIds, comment);
        } catch (Exception e) {
            log.error("error in /forwardEvent : {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/getCalendarEventByEventId")
    @Operation(summary = "Get calendar event by event ID", tags = "Calendar", description = "Retrieve calendar event details by the specified event ID using Microsoft Graph API.",
            security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_GET_CALENDAR_EVENT_BY_EVENT_ID}))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public EventDto getCalendarEventByEventId(@RequestBody Map<String, String> request) {
        try {
            String eventId = request.get("eventId");
            if (eventId == null || eventId.isEmpty()) {
                throw new IllegalArgumentException("eventId is required in request body");
            }
            return graphIntegrationService.getCalendarEventByEventId(eventId);
        } catch (Exception e) {
            log.error("Error in /getCalendarEventByEventId : {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
