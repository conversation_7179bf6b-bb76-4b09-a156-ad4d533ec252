package com.enttribe.emailagent.rest;
 
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.integration.impl.GmailIntegrationImpl;
import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.service.FailureLogsService;
import com.enttribe.emailagent.utils.APIConstants;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

/**
* REST controller for managing FailureLogs.
* This controller provides endpoints for creating, reading, updating, and deleting FailureLogs.
 *  <AUTHOR> Pathak
*/
@RestController("failureLogsRestController")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/failureLogsRestController")
public class FailureLogsRestController {
 
    private static final Logger log = EmailAgentLogger.getLogger(FailureLogsRestController.class);
 
    @Autowired
    private FailureLogsService failureLogsService;
 
    /**
     * Creates a new FailureLog.
     *
     * @param failureLog the FailureLog entity to create
     * @return the created FailureLog entity
     */
    @PostMapping("/failureLogs")
    @Operation(summary = "Create Failure Log", tags = "Failure Logs", description = "Creates a new FailureLog entity and saves it to the database.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_CREATE_FAILURE_LOG})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully created the FailureLog entity."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while creating the FailureLog.")
    })
    public FailureLogs createFailureLog(@RequestBody FailureLogs failureLog) {
        log.info("Creating a new failure log");
        return failureLogsService.save(failureLog);
    }
 
    /**
     * Retrieves all FailureLogs.
     *
     * @return a list of all FailureLogs
     */
    @GetMapping("/getAllFailureLogs")
    @Operation(summary = "Get All Failure Logs", tags = "Failure Logs", description = "Retrieves a list of all FailureLog entities from the database.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_GET_ALL_FAILURE_LOGS})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the list of all FailureLogs."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the FailureLogs.")
    })
    public List<FailureLogs> getAllFailureLogs() {
        log.info("Fetching all failure logs");
        return failureLogsService.findAll();
    }
 
    /**
     * Retrieves a specific FailureLog by its ID.
     *
     * @param id the ID of the FailureLog to retrieve
     * @return the FailureLog entity with the specified ID
     */
    @GetMapping("/failureLogsById/{id}")
    @Operation(summary = "Get Failure Log by ID", tags = "Failure Logs", description = "Retrieves a specific FailureLog entity by its ID.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_GET_FAILURE_LOG_BY_ID})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the FailureLog with the specified ID."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the FailureLog.")
    })
    public FailureLogs getFailureLogById(@PathVariable Integer id) {
        log.info("Fetching failure log with ID: " + id);
        return failureLogsService.findById(id);
    }
 
    /**
     * Retrieves a specific FailureLog by its ID.
     *
     * @param email the email of the FailureLog to retrieve
     * @return the FailureLog entity with the specified ID
     */
    @GetMapping("/failureLogsByEmail/{email}")
    @Operation(summary = "Get Failure Log by Email", tags = "Failure Logs", description = "Retrieves a list of FailureLog entities associated with the specified email.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_GET_FAILURE_LOG_BY_EMAIL})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the FailureLogs for the specified email."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the FailureLogs.")
    })
    public List<FailureLogs> getFailureLogByEmail(@PathVariable String email) {
        log.info("Fetching failure log with ID: " + email);
        return failureLogsService.getFailureLogByEmail(email);
    }
 
 
    /**
     * Updates an existing FailureLog by its ID.
     *
     * @param id the ID of the FailureLog to update
     * @param failureLogDetails the FailureLog entity containing updated details
     * @return a ResponseEntity containing the updated FailureLog entity
     */
    @PutMapping("/updateFailureLogs/{id}")
    @Operation(summary = "Update Failure Log", tags = "Failure Logs", description = "Updates an existing FailureLog entity by its ID with the provided updated details.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_UPDATE_FAILURE_LOG})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated the FailureLog with the specified ID."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the FailureLog.")
    })
    public FailureLogs updateFailureLog(@PathVariable Integer id, @RequestBody FailureLogs failureLogDetails) {
        log.info("Updating failure log with ID: " + id);
        return failureLogsService.update(id, failureLogDetails);
    }
 
    /**
     * Deletes a FailureLog by its ID.
     *
     * @param id the ID of the FailureLog to delete
     * @return true if the FailureLog was successfully deleted, false otherwise
     */
    @DeleteMapping("/deletefailureLogById/{id}")
    @Operation(summary = "Delete Failure Log by ID", tags = "Failure Logs", description = "Deletes the FailureLog entity with the specified ID.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_DELETE_FAILURE_LOG})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully deleted the FailureLog with the specified ID."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while deleting the FailureLog.")
    })
    public Boolean deleteFailureLog(@PathVariable Integer id) {
        log.info("Deleting failure log with ID: " + id);
        return failureLogsService.deleteById(id);
    }
 

    @GetMapping("/count")
    @Operation(summary = "Count Failure Logs", tags = "Failure Logs", description = "Counts the number of FailureLog entities based on the provided search criteria.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_COUNT_FAILURE_LOGS})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the count of FailureLogs."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while counting FailureLogs.")
    })
    public Long count(@RequestParam(name = "id", required = false) Integer id,
    @RequestParam(name = "internetMessageId", required = false) String internetMessageId,
    @RequestParam(name = "email", required = false) String email,
    @RequestParam(name = "emailSubject", required = false) String emailSubject,
    @RequestParam(name = "messageId", required = false) String messageId,
    @RequestParam(name = "conversationId", required = false) String conversationId,
    @RequestParam(name = "type", required = false) String type,
    @RequestParam(name = "exceptionMessage", required = false) String exceptionMessage,
    @RequestParam(name = "customExceptionMessage", required = false) String customExceptionMessage,
    @RequestParam(name = "exceptionTrace", required = false) String exceptionTrace,
    @RequestParam(name = "errorDate", required = false) String errorDate) {
        log.info("Fetching all count logs");
        return failureLogsService.count(id, internetMessageId, email, emailSubject, messageId, conversationId, type, exceptionMessage, customExceptionMessage, exceptionTrace, errorDate);
    }

    @GetMapping("/typesOfFailureLogs")
    @Operation(summary = "Get Types of Failure Logs", tags = "Failure Logs", description = "Retrieves a list of distinct failure log types.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_GET_FAILURE_LOG_TYPES})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the list of failure log types."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the failure log types.")
    })
    public List<String> typesOfFailureLogs(){
        return failureLogsService.typesOfFailureLogs();
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "failureLogsByFilter")
    @Operation(summary = "Fetch Failure Logs by Filter", tags = "Failure Logs", description = "Fetches a list of FailureLog entities based on the provided filter criteria.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_GET_FAILURE_LOGS_BY_FILTER})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the filtered list of FailureLogs."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching filtered FailureLogs.")
    })
    List<FailureLogs> failureLogByFilter(
            @Parameter(name = "Minimum number of records required") @RequestParam(required = false, name = "llimit") Integer llimit,
            @Parameter(name = "Maximum number of records required") @RequestParam(required = false, name = "ulimit") Integer ulimit,
            @Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap){
                return failureLogsService.failureLogByFilter(llimit, ulimit, filterMap);  
            }
         
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "failureLogsCountByFilter")
    @Operation(summary = "Fetch Failure Log Count by Filter", tags = "Failure Logs", description = "Retrieves the count of FailureLog entities based on the provided filter criteria.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_COUNT_FAILURE_LOGS_BY_FILTER})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the count of FailureLogs matching the filter."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the count of FailureLogs.")
    })
    long failureLogCountByFilter(
            @Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap){
                return failureLogsService.failureLogCountByFilter(filterMap);  
            }

    @PostMapping("updateStatus/{id}")
    @Operation(summary = "Update Failure Log Status", tags = "Failure Logs", description = "Updates the status of a FailureLog entity by its ID.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_UPDATE_FAILURE_LOG_STATUS})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated the status of the FailureLog with the specified ID."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the FailureLog status.")
    })
    public String updateStatus(@PathVariable Integer id, @RequestParam String status) {
        return failureLogsService.updateStatus(id, status);
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "mailSummaryByFilter")
    @Operation(summary = "Fetch Mail Summaries by Filter", tags = "Mail Summaries", description = "Fetches a list of MailSummary entities based on the provided filter criteria.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_GET_MAIL_SUMMARIES_BY_FILTER})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the filtered list of MailSummaries."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching filtered MailSummaries.")
    })
    List<MailSummary> mailSummaryByFilter(
            @Parameter(name = "Minimum number of records required") @RequestParam(required = false, name = "llimit") Integer llimit,
            @Parameter(name = "Maximum number of records required") @RequestParam(required = false, name = "ulimit") Integer ulimit,
            @Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap){
        return failureLogsService.mailSummaryByFilter(llimit, ulimit, filterMap);
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "mailSummaryCountByFilter")
    @Operation(summary = "Fetch Mail Summary Count by Filter", tags = "Mail Summaries", description = "Retrieves the count of MailSummary entities based on the provided filter criteria.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT,
                            scopes = {APIConstants.ROLE_API_COUNT_MAIL_SUMMARIES_BY_FILTER})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the count of MailSummaries matching the filter."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the count of MailSummaries.")
    })
    long mailSummaryCountByFilter(
            @Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap){
        return failureLogsService.mailSummaryCountByFilter(filterMap);
    }
}
