package com.enttribe.emailagent.dao;

import com.enttribe.emailagent.entity.BuildEmails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * The interface Build email dao.
 *  <AUTHOR>
 */
public interface IBuildEmailDao extends JpaRepository<BuildEmails, Integer> {

    @Query("select u from BuildEmails u where u.conversationId = ?1")
    BuildEmails getEmailByConversationId(String conversationId);

    //For polling
    @Query("select u from BuildEmails u where u.internetMessageId = :internetMessageId")
    BuildEmails getBuildEmailByInternetMessageId(String internetMessageId);

    //For action
    @Query("select u from BuildEmails u where u.conversationId = :conversationId and u.status = 'PENDING' and u.status IN ('PENDING', 'ERROR') and u.type = 'REQUESTEE'")
    List<BuildEmails> getForApprovalEmails(String conversationId);

    @Query("select u from BuildEmails u where u.status = 'WAITING'")
    List<BuildEmails> getWaitingBuildEmails();

}
