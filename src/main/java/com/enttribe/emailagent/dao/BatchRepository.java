package com.enttribe.emailagent.dao;

import com.enttribe.emailagent.entity.Batch;
import com.enttribe.emailagent.entity.UserMailAttachment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * The interface Batch repository.
 * <AUTHOR>
 */
public interface BatchRepository extends JpaRepository<Batch, Integer> {

    public Batch findByBatchId(String batchId);

    @Query("select b.batchId from Batch b")
    public List<String> getBatchList();
}
