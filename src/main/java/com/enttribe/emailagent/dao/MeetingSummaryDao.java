package com.enttribe.emailagent.dao;

import com.enttribe.emailagent.entity.MeetingSummary;
import com.enttribe.emailagent.entity.UserMailAttachment;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * The interface Meeting summary dao.
 * <AUTHOR> <PERSON>
 */
public interface MeetingSummaryDao extends JpaRepository<MeetingSummary, Integer> {

    @Query("select m from MeetingSummary m where m.userId=:userId and m.meetingId=:meetingId")
    MeetingSummary findMeetingSummaryByMeetingId(String userId,String meetingId);


    @Query("select m from MeetingSummary m where m.status=:status")
    List<MeetingSummary> findByStatus(String status);

    @Query("SELECT m FROM MeetingSummary m WHERE m.internetMessageId = :internetMessageId")
    MeetingSummary findMeetingSummaryByInternetMessageId(@Param("internetMessageId") String internetMessageId);

}
