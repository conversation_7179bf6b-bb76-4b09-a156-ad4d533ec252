package com.enttribe.emailagent.dao;


import com.enttribe.emailagent.entity.EmailStats;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * The interface Email stats dao.
 *  <AUTHOR>
 */
public interface IEmailStatsDao extends JpaRepository<EmailStats, Integer> {

    @Query(value = "SELECT * FROM EMAIL_STATS e WHERE e.USER_ID = ?1 AND DATE(e.STATS_DATE) = DATE(?2) AND e.TYPE = ?3", nativeQuery = true)
    EmailStats getStatsForUser(String userId, Date statsDate,String type);

}
