package com.enttribe.emailagent.dao;

import com.enttribe.emailagent.dto.AttachmentsDto;
import com.enttribe.emailagent.entity.UserMailAttachment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


/**
 * The interface User mail attachment repository.
 *  <AUTHOR> Sonsale
 */
public interface UserMailAttachmentRepository extends JpaRepository<UserMailAttachment, Integer> {

    @Query("select u.longSummary from UserMailAttachment u where u.uniqueName = ?1")
    String getLongSummaryForDocument(String docId);
    @Query("select u.processingStatus from UserMailAttachment u where u.uniqueName = ?1")
    String processingStatusForDocument(String docId);


    @Query("select new com.enttribe.emailagent.dto.AttachmentsDto(u.longSummary, u.shortSummary, u.errorDisplay) from UserMailAttachment u where u.attachmentId = ?1")
    AttachmentsDto getLongAndShortSummaryForDocument(String docId);

    @Query("select u.ragDocumentId from UserMailAttachment u where u.attachmentId = ?1")
    String getRagIdFromDocId(String docId);

    @Query("select u from UserMailAttachment u where u.internetMessageId = ?1 and u.processingStatus= ?2 and u.userId=?3")
    List<UserMailAttachment> getAttachmentsForMessage(String messageId,String status,String userId);

    @Query("select u from UserMailAttachment u where u.internetMessageId = ?1 and u.processingStatus in ('NEW', 'IN_PROGRESS', 'COMPLETED', 'ERROR') and u.userId=?2")
    List<UserMailAttachment> getAllAttachmentsForMessage(String messageId,String userId);
    
    @Query("select u from UserMailAttachment u where u.internetMessageId = ?1")
    List<UserMailAttachment> getAttachmentsByInternetMessageId(String interneMessageId);




}



