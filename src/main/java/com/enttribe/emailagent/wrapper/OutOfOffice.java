package com.enttribe.emailagent.wrapper;

import com.enttribe.emailagent.dto.Meeting;
import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * The type Out of office.
 *
 *  <AUTHOR>
 */
@Setter
@Getter
public class OutOfOffice {
    String userId;
    String startDateTime;
    String endDateTime;
    String internalMessage;
    String externalMessage;
    String status;
    String timeZone;
    private List<Meeting> times;
}
