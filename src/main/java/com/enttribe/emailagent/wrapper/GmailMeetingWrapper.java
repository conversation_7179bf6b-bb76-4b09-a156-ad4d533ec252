package com.enttribe.emailagent.wrapper;

import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * The type Gmail meeting wrapper.
 *  <AUTHOR>
 */
@Getter
@Setter
public class GmailMeetingWrapper {
    String userId;
    private String summary;
    private String meetingType;
    private String location;
    private String eventId;
    private String calendarId;
    private String timeZone;
    private String description;
    private String startDateTime;
    private String endDateTime;
    private List<String> requiredAttendees;
    private List<String> optionalAttendees;
    private String recurrenceRule;
}
