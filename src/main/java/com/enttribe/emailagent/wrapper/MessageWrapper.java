package com.enttribe.emailagent.wrapper;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * The type Message wrapper.
 *  <AUTHOR>
 */
@Getter
@Setter
public class MessageWrapper {
    private String userName ;
    private String userId ;
    private String email ;
    private String from ;
    private String subject;
    private String content ;
    private String replyBody;
    private Boolean isDraft;
    private Boolean isRead;
    private Boolean hashAttachment;
    private String messageId;
    private String convercationId;
    private String internetMessageId;
    private List<String> toRecipients;
    private List<String> ccRecipients;
    private List<String> bccRecipients;
    private List<String> attachmentPathList;
}
