package com.enttribe.emailagent.wrapper;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * The type Meeting event.
 *  <AUTHOR>
 */
@Getter
@Setter
public class MeetingEvent {
    private String organizer;
    private List<String> attendees;
    private String subject;
    private String accepted;
    private String bodyPreview;
    private String joinUrl; // onlineMeeting
    private Boolean hasAttachments;
    private Date meetingStartTime;
    private Date meetingEndTime;
    private Date createdDateTime;
    private Date lastModifiedDateTime;
    private String id;
}
