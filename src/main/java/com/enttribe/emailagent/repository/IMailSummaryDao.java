package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.dto.ActionItemDto;
import com.enttribe.emailagent.entity.MailSummary;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * The interface Mail summary dao.
 *  <AUTHOR> <PERSON>gi
 */
@Repository
public interface IMailSummaryDao extends JpaRepository<MailSummary, Integer> {


    @Query("SELECT DISTINCT ms FROM MailSummary ms " +
            "LEFT JOIN UserActions ua ON ms.id = ua.mailSummary.id " +
            "WHERE (ms.mailReceivedTime >= :startDate AND ms.mailReceivedTime <= :endDate AND ms.userId = :userId AND ms.deleted = false AND ms.folderName!='Sent Items' AND (ms.priority = 'High' OR ms.category = 'Attention' OR ms.starMarked = true)) " +
            "OR (ua.actionOwner = :userId AND ua.deleted = false AND ua.actionTaken = false)")
    List<MailSummary> findMailSummariesByUserIdAndDateRange(
            @Param("userId") String userId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    @Query("SELECT DISTINCT ms FROM MailSummary ms WHERE ms.userId = :userId AND ms.notificationStatus NOT IN ('SENT', 'NOT_ALLOWED') AND ms.deleted = false AND ms.mailReceivedTime >= :startDate")
    List<MailSummary> getMailsForNotification(
            @Param("userId") String userId,
            @Param("startDate") LocalDateTime startDate
    );

    @Query(value = "SELECT * FROM (SELECT es.*, ROW_NUMBER() OVER(PARTITION BY es.CONVERSATION_ID ORDER BY es.MAIL_RECEIVED_TIME DESC) AS row_num FROM MAIL_SUMMARY es WHERE es.USER_ID = :userId AND es.CONVERSATION_ID IN :conversationIds) AS ranked WHERE ranked.row_num <= :conversationSize ORDER BY ranked.MAIL_RECEIVED_TIME DESC", nativeQuery = true)
    List<MailSummary> findTopConversationsOfUser(@Param("userId") String userId, @Param("conversationIds") List<String> conversationIds, @Param("conversationSize") Integer conversationSize);

    @Query("SELECT m FROM MailSummary m WHERE m.userId = :userId AND DATE(m.mailReceivedTime) = CURRENT_DATE AND m.deleted = false")
    List<MailSummary> getTodayMailsByUserId(@Param("userId") String userId);

    @Query("SELECT COUNT(e) FROM MailSummary e WHERE e.userId = :userId AND DATE(e.mailReceivedTime) = CURRENT_DATE AND e.deleted = false")
    Long countTodayMailsByUserId(@Param("userId") String userId);

    @Query("SELECT eu FROM MailSummary eu WHERE eu.messageId = :messageId and eu.userId= :userId AND eu.deleted = false")
    MailSummary findByMessageId(String messageId, String userId);

    @Query("SELECT eu FROM MailSummary eu WHERE eu.internetMessageId = :internetMessageId and eu.userId= :userId AND eu.deleted = false")
    MailSummary findByInternetMessageId(String internetMessageId, String userId);

    @Query("SELECT eu.objective FROM MailSummary eu WHERE eu.messageId = :messageId and eu.userId= :userId AND eu.deleted = false")
    String getObjective(String messageId, String userId);

    @Query("SELECT m FROM MailSummary m WHERE m.starMarked = true AND m.userId = :userId AND m.deleted = false")
    List<MailSummary> getStarMarkedMailSummary(@Param("userId") String userId);

    @Query("SELECT m FROM MailSummary m WHERE m.category = :category AND m.userId = :userId AND m.deleted = false")
    List<MailSummary> findByCategory(@Param("category") String category, @Param("userId") String userId);

    @Query("SELECT m FROM MailSummary m WHERE m.priority = :priority AND m.userId = :userId AND m.deleted = false")
    List<MailSummary> findByPriority(@Param("priority") String priority, @Param("userId") String userId);

    @Query("SELECT m FROM MailSummary m WHERE m.actionOwner LIKE %:actionOwner% AND m.userId = :userId AND m.deleted = false")
    List<MailSummary> findByActionOwnerLike(@Param("actionOwner") String actionOwner, @Param("userId") String userId);

    @Query("SELECT m FROM MailSummary m WHERE m.messageId = :messageId AND m.userId = :userId AND m.deleted = false")
    MailSummary getSummaryByMessageId(@Param("messageId") String messageId, @Param("userId") String userId);



//    @Query("SELECT eu FROM MailSummary eu WHERE DATE(eu.mailReceivedTime) = :today and eu.userId= :userId and eu.type=:type AND eu.deleted = false order by eu.mailReceivedTime desc")
//    List<MailSummary> getMailByUserId(Date today, String userId, String type);


    @Query("SELECT DISTINCT ms FROM MailSummary ms " +
            "LEFT JOIN UserActions ua ON ms.id = ua.mailSummary.id " +
            "WHERE (DATE(ms.mailReceivedTime)  = :today AND ms.userId = :userId AND ms.type=:type AND ms.deleted = false AND ms.folderName!='Sent Items' AND (ms.priority = 'High' OR ms.category = 'Attention' OR ms.starMarked = true)) " +
            "OR (ua.actionOwner = :userId AND ua.deleted = false AND ua.actionTaken = false)")
    List<MailSummary> getMailByUserId(Date today, String userId, String type);

    @Query("SELECT eu FROM MailSummary eu WHERE DATE(eu.mailReceivedTime) >= :startDate AND DATE(eu.mailReceivedTime) <= :endDate AND eu.userId = :userId and eu.type=:type AND eu.deleted = false ORDER BY eu.mailReceivedTime DESC")
    List<MailSummary> getMailByUserIdBetweenDate(@Param("startDate") Date startDate, @Param("endDate") Date endDateDate, @Param("userId") String userId, String type);

    @Query("SELECT eu FROM MailSummary eu WHERE DATE(eu.mailReceivedTime) >= :date AND DATE(eu.mailReceivedTime) <= CURRENT_DATE AND eu.userId = :userId and eu.type=:type AND eu.deleted = false ORDER BY eu.mailReceivedTime DESC")
    List<MailSummary> getMailByUserIdFromDate(@Param("date") LocalDate date, @Param("userId") String userId, String type);

    @Query("SELECT eu FROM MailSummary eu WHERE eu.conversationId=:conversationId and eu.userId= :userId AND eu.deleted = false")
    List<MailSummary> findByConversationId(String conversationId, String userId);

    @Query("SELECT COUNT(e) FROM MailSummary e WHERE e.conversationId = :conversationId and e.userId= :userId AND e.deleted = false")
    Long countByConversationId(@Param("conversationId") String conversationId, @Param("userId") String userId);

    @Query("SELECT m FROM MailSummary m WHERE m.userId = :userId AND m.messageId IN :messageIds AND m.deleted = false")
    List<MailSummary> findByMessageIds(@Param("userId") String userId, @Param("messageIds") List<String> messageIds);

    MailSummary findFirstByInternetMessageId(@Param("internetMessageId") String internetMessageId);

    @Modifying
    @Transactional
    @Query("UPDATE MailSummary ms SET ms.starMarked = :starMarked WHERE ms.conversationId = :conversationId")
    int setStarMarkedByConversationId(@Param("starMarked") Boolean starMarked, @Param("conversationId") String conversationId);

    @Modifying
    @Transactional
    @Query("UPDATE MailSummary ms SET ms.notificationStatus = :notificationStatus WHERE ms.id = :id")
    int updateNotificationStatus(@Param("notificationStatus") MailSummary.NotificationStatus notificationStatus, @Param("id") Integer id);

    @Modifying
    @Transactional
    @Query("UPDATE MailSummary ms SET ms.actionTaken = true, ms.categoryReason = :actionClearReason, ms.actionClearReason=:actionClearReason, ms.category = 'FYI' WHERE ms.id = :id")
    int markActionTaken(@Param("actionClearReason") String actionClearReason, @Param("id") Integer id);

    @Modifying
    @Transactional
    @Query("UPDATE MailSummary ms SET ms.actionTaken = :action, ms.categoryReason = 'Action is taken on the email manually.', ms.category = 'FYI' WHERE ms.userId = :userId AND ms.internetMessageId = :internetMessageId")
    int markActionTaken(@Param("userId") String userId, @Param("internetMessageId") String internetMessageId, Boolean action);

    @Modifying
    @Transactional
    @Query("UPDATE MailSummary ms SET ms.actionTaken = true, ms.categoryReason = 'Action is taken on the email manually.', ms.category = 'FYI' WHERE ms.userId = :userId AND ms.actionTaken = false")
    int markAllActionTaken(@Param("userId") String userId);

    @Modifying
    @Transactional
    @Query("UPDATE MailSummary ms SET ms.deleted = true WHERE ms.userId = :userId AND ms.internetMessageId IN :internetMessageIds")
    int markDeleted(@Param("userId") String userId, @Param("internetMessageIds") List<String> internetMessageIds);

    @Query("SELECT COUNT(eu) FROM MailSummary eu WHERE eu.mailReceivedTime >= :startDate AND eu.mailReceivedTime <= :endDate AND eu.userId = :userId  AND eu.folderName!=:folderName AND eu.deleted = false")
    long countAllMailOfUser(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("userId") String userId, @Param("folderName") String folderName);

    @Query("SELECT COUNT(eu) FROM MailSummary eu WHERE eu.mailReceivedTime >= :startDate AND eu.mailReceivedTime <= :endDate AND eu.userId = :userId AND eu.type = :type AND eu.priority = 'High' AND eu.deleted = false AND eu.folderName!='Sent Items'")
    long countHighPriorityMailByUserId(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("userId") String userId, @Param("type") String type);

    @Query("SELECT COUNT(eu) FROM MailSummary eu WHERE eu.mailReceivedTime >= :startDate AND eu.mailReceivedTime <= :endDate AND eu.userId = :userId AND eu.type = :type AND eu.category = 'Attention' AND eu.deleted = false AND eu.folderName!='Sent Items'")
    long countAttentionMailByUserId(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("userId") String userId, @Param("type") String type);

    @Query("SELECT COUNT(eu) FROM MailSummary eu WHERE eu.mailReceivedTime >= :startDate AND eu.mailReceivedTime <= :endDate AND eu.userId = :userId AND eu.type = :type AND eu.starMarked = true AND eu.deleted = false AND eu.folderName!='Sent Items'")
    long countStarMarkedMailByUserId(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("userId") String userId, @Param("type") String type);

    @Query("SELECT COUNT(eu) FROM MailSummary eu WHERE eu.mailReceivedTime <= :startDate AND eu.userId = :userId AND eu.type = :type AND eu.actionOwner LIKE %:actionOwner% AND eu.actionTaken = false AND eu.deleted = false AND eu.folderName!='Sent Items'")
    long countActionableMailByUserId(@Param("startDate") LocalDateTime startDate, @Param("userId") String userId, @Param("type") String type, @Param("actionOwner") String actionOwner);

    @Query("SELECT COUNT(eu) FROM MailSummary eu WHERE eu.mailReceivedTime >= :startDate AND eu.mailReceivedTime <= :endDate AND eu.userId = :userId AND eu.type = :type AND (eu.category = 'Attention' OR eu.priority = 'High') AND eu.deleted = false")
    long countEventByUserId(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("userId") String userId, @Param("type") String type);

    @Query("SELECT eu FROM MailSummary eu WHERE eu.mailReceivedTime >= :startDate AND eu.mailReceivedTime <= :endDate AND eu.userId = :userId AND eu.type = :type AND eu.priority = 'High' AND eu.deleted = false AND eu.folderName!='Sent Items' ORDER BY eu.mailReceivedTime DESC")
    List<MailSummary> highPriorityMailByUserId(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("userId") String userId, @Param("type") String type);

    @Query("SELECT eu FROM MailSummary eu WHERE eu.mailReceivedTime >= :startDate AND eu.mailReceivedTime <= :endDate AND eu.userId = :userId AND eu.type = :type AND eu.category = 'Attention' AND eu.deleted = false AND eu.folderName!='Sent Items' ORDER BY eu.mailReceivedTime DESC")
    List<MailSummary> attentionMailByUserId(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("userId") String userId, @Param("type") String type);

    @Query("SELECT eu FROM MailSummary eu WHERE eu.mailReceivedTime >= :startDate AND eu.mailReceivedTime <= :endDate AND eu.userId = :userId AND eu.type = :type AND eu.starMarked = true AND eu.deleted = false AND eu.folderName!='Sent Items' ORDER BY eu.mailReceivedTime DESC")
    List<MailSummary> starMarkedMailByUserId(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("userId") String userId, @Param("type") String type);

    @Query("SELECT eu FROM MailSummary eu WHERE eu.mailReceivedTime <= :startDate AND eu.userId = :userId AND eu.type = :type AND eu.actionOwner LIKE %:actionOwner% AND eu.actionTaken = false AND eu.deleted = false AND eu.folderName!='Sent Items' ORDER BY eu.mailReceivedTime DESC")
    List<MailSummary> actionableMailByUserId(@Param("startDate") LocalDateTime startDate, @Param("userId") String userId, @Param("type") String type, @Param("actionOwner") String actionOwner);

    @Query("SELECT new com.enttribe.emailagent.dto.ActionItemDto(eu.id, eu.actionOwner) FROM MailSummary eu WHERE eu.userId = :userId AND eu.type = 'Email' AND eu.conversationId = :conversationId AND eu.subject LIKE %:subject% AND eu.actionTaken = false AND eu.deleted = false AND eu.folderName!='Sent Items' ORDER BY eu.mailReceivedTime DESC")
    List<ActionItemDto> getActionItemDetails(@Param("userId") String userId, @Param("conversationId") String conversationId, @Param("subject") String subject);

    @Query("SELECT eu FROM MailSummary eu WHERE eu.mailReceivedTime >= :startDate AND eu.mailReceivedTime <= :endDate AND eu.userId = :userId AND eu.type = :type AND (eu.category = 'Attention' OR eu.priority = 'High') AND eu.deleted = false ORDER BY eu.mailReceivedTime DESC")
    List<MailSummary> eventByUserId(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("userId") String userId, @Param("type") String type);

    MailSummary findTopByConversationIdAndUserIdOrderByMailReceivedTimeDesc(String conversationId, String userId);

    MailSummary findByUserIdAndInternetMessageId(String userId, String internetMessageId);

    @Query("SELECT eu FROM MailSummary eu WHERE eu.id >= :startId AND eu.id <= :endId")
    List<MailSummary> findByIdBetween(Integer startId, Integer endId);
}
