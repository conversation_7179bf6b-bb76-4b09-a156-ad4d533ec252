package com.enttribe.emailagent.repository.impl;

import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.UserActions;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;


import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import jakarta.persistence.criteria.Path;

import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.query.FluentQuery.FetchableFluentQuery;
import org.springframework.stereotype.Repository;

import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.repository.FailureLogsServiceDao;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import org.springframework.data.jpa.repository.JpaRepository;


/**
 * The type Failure logs service dao.
 *  <AUTHOR> Lokhande
 */
@Repository
@Slf4j
@Primary
public class FailureLogsServiceDaoImpl {

    @PersistenceContext
    private EntityManager entityManager;

    public FailureLogsServiceDaoImpl( EntityManager entityManager) {

    }


    /**
     * Gets filtered failure logs.
     *
     * @param filterMap the filter map
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @return the filtered failure logs
     */
    public List<FailureLogs> getFilteredFailureLogs(Map<String, Object> filterMap, int llimit, int ulimit) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<FailureLogs> criteriaQuery = criteriaBuilder.createQuery(FailureLogs.class);
        Root<FailureLogs> failureLogsRoot = criteriaQuery.from(FailureLogs.class);

        List<Predicate> predicates = new ArrayList<>();

        // Add filters
        processFilterMap(filterMap, predicates, criteriaBuilder, failureLogsRoot);

        // Build the query
        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(failureLogsRoot.get("errorDate")));

        // Execute the query with limits
        return getListFromCriteriaQuery(criteriaQuery, llimit, ulimit);
    }

    private void processFilterMap(Map<String, Object> filterMap, List<Predicate> predicates,
                                  CriteriaBuilder criteriaBuilder, Root<FailureLogs> failureLogsRoot) {

        if (filterMap.get("email") != null && !filterMap.get("email").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("email").toString(), predicates, criteriaBuilder, failureLogsRoot.get("email"));
        }

        if (filterMap.get("status") != null && !filterMap.get("status").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("status").toString(), predicates, criteriaBuilder, failureLogsRoot.get("status"));
        }

        if (filterMap.get("emailSubject") != null && !filterMap.get("emailSubject").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("emailSubject").toString(), predicates, criteriaBuilder, failureLogsRoot.get("emailSubject"));
        }

        if (filterMap.get("messageId") != null && !filterMap.get("messageId").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("messageId").toString(), predicates, criteriaBuilder, failureLogsRoot.get("messageId"));
        }

        if (filterMap.get("conversationId") != null && !filterMap.get("conversationId").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("conversationId").toString(), predicates, criteriaBuilder, failureLogsRoot.get("conversationId"));
        }

        if (filterMap.get("id") != null) {
            predicates.add(criteriaBuilder.equal(failureLogsRoot.get("id"), filterMap.get("id")));
        }

        if (filterMap.get("internetMessageId") != null && !filterMap.get("internetMessageId").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("internetMessageId").toString(), predicates, criteriaBuilder, failureLogsRoot.get("internetMessageId"));
        }

        if (filterMap.get("type") instanceof List) {
            List<String> types = (List<String>) filterMap.get("type");
            if (!types.isEmpty()) {
                predicates.add(failureLogsRoot.get("type").in(types));
            }
        } else if (filterMap.get("type") != null && !filterMap.get("type").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("type").toString(), predicates, criteriaBuilder, failureLogsRoot.get("type"));
        }

        if (filterMap.get("customExceptionMessage") != null && !filterMap.get("customExceptionMessage").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("customExceptionMessage").toString(), predicates, criteriaBuilder, failureLogsRoot.get("customExceptionMessage"));
        }
if (filterMap.get("errorDate") != null) {
    Map<String, String> errorDateMap = (Map<String, String>) filterMap.get("errorDate");
    String dateStartStr = errorDateMap.get("dateStart");
    String dateEndStr = errorDateMap.get("dateEnd");
    if (dateStartStr != null && dateEndStr != null) {
        // Parse the string dates to Date and then to milliseconds
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date errorDateFrom = dateFormat.parse(dateStartStr);
            Date errorDateTo = dateFormat.parse(dateEndStr);
            predicates.add(criteriaBuilder.between(
                failureLogsRoot.get("errorDate"), 
                errorDateFrom.getTime(), 
                errorDateTo.getTime()
            ));
        } catch (Exception e) {
            // Handle parsing error
            log.error("Error parsing dates for errorDate filter", e);
        }
    }
}
    }

    private void getNameFilter(String value, List<Predicate> predicates, CriteriaBuilder cb, Path<String> field) {
        predicates.add(cb.like(cb.lower(field), "%" + value.toLowerCase() + "%"));
    }

    private List<FailureLogs> getListFromCriteriaQuery(CriteriaQuery<FailureLogs> criteriaQuery, Integer llimit, Integer ulimit) {
        Query query = entityManager.createQuery(criteriaQuery);
        if (llimit != null && ulimit != null && llimit >= 0 && ulimit > 0) {
            query.setMaxResults(ulimit - llimit + 1);
            query.setFirstResult(llimit);
        }
        return query.getResultList();
    }

    /**
     * Gets filtered failure logs count.
     *
     * @param filterMap the filter map
     * @return the filtered failure logs count
     */
    public List<FailureLogs> getFilteredFailureLogsCount(Map<String, Object> filterMap) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<FailureLogs> criteriaQuery = criteriaBuilder.createQuery(FailureLogs.class);
        Root<FailureLogs> failureLogsRoot = criteriaQuery.from(FailureLogs.class);

        List<Predicate> predicates = new ArrayList<>();

        // Add filters
        processFilterMap(filterMap, predicates, criteriaBuilder, failureLogsRoot);

        // Build the query
        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(failureLogsRoot.get("errorDate")));

        // Execute the query with limits
        return getListFromCriteriaQueryCount(criteriaQuery);
    }

    private List<FailureLogs> getListFromCriteriaQueryCount(CriteriaQuery<FailureLogs> criteriaQuery) {
        Query query = entityManager.createQuery(criteriaQuery);

        return query.getResultList();
    }

    /**
     * Gets filtered user actions.
     *
     * @param filterMap the filter map
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @return the filtered user actions
     */
    public List<UserActions> getFilteredUserActions(Map<String, Object> filterMap, int llimit, int ulimit) {
       try {
           CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
           CriteriaQuery<UserActions> criteriaQuery = criteriaBuilder.createQuery(UserActions.class);
           Root<UserActions> userActionsRoot = criteriaQuery.from(UserActions.class);

           List<Predicate> predicates = new ArrayList<>();

           if (filterMap.containsKey("actionOwner")) {
               String actionOwner = (String) filterMap.get("actionOwner");
               predicates.add(criteriaBuilder.like(userActionsRoot.get("actionOwner"), "%" + actionOwner + "%"));
           }
           if (filterMap.containsKey("subject")) {
               String subject = (String) filterMap.get("subject");
               predicates.add(criteriaBuilder.like(userActionsRoot.get("subject"), "%" + subject + "%"));
           }
           if (filterMap.containsKey("actionTaken")) {
               predicates.add(criteriaBuilder.equal(userActionsRoot.get("actionTaken"), filterMap.get("actionTaken")));
           }
           if (filterMap.containsKey("deleted")) {
               predicates.add(criteriaBuilder.equal(userActionsRoot.get("deleted"), filterMap.get("deleted")));
           }
           if (filterMap.containsKey("mailReceivedTime")) {
               String mailReceivedTimeStr = (String) filterMap.get("mailReceivedTime");
               LocalDateTime mailReceivedTime = LocalDateTime.parse(mailReceivedTimeStr);
               predicates.add(criteriaBuilder.greaterThanOrEqualTo(userActionsRoot.get("mailReceivedTime"), mailReceivedTime));
           }

           if(filterMap.containsKey("actionTakenReason")) {
               String actionTakenReason = (String) filterMap.get("actionTakenReason");

               if (actionTakenReason.equals("pending")) {
                   predicates.add(criteriaBuilder.isNull(userActionsRoot.get("actionTakenReason")));
               } else if (actionTakenReason.equals("notClear")) {
                   predicates.add(criteriaBuilder.isNotNull(userActionsRoot.get("actionTakenReason")));
               }
           }

           criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
           criteriaQuery.orderBy(criteriaBuilder.desc(userActionsRoot.get("createdAt")));

           return getListFromCriteriaQuery_v1(criteriaQuery, llimit, ulimit);
       }catch(Exception e){
           log.error("Error while fetching filtered user actions: {}", e.getMessage(), e);
           return new ArrayList<>();
       }
    }

    /**
     * Gets user actions count by filter.
     *
     * @param filterMap the filter map
     * @return the user actions count by filter
     */
//for count
    public Long getUserActionsCountByFilter(Map<String, Object> filterMap) {
        try {
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Long> criteriaQuery = criteriaBuilder.createQuery(Long.class);
            Root<UserActions> userActionsRoot = criteriaQuery.from(UserActions.class);

            List<Predicate> predicates = new ArrayList<>();

            if (filterMap.containsKey("actionOwner") && filterMap.get("actionOwner") != null) {
                String actionOwner = (String) filterMap.get("actionOwner");
                predicates.add(criteriaBuilder.like(userActionsRoot.get("actionOwner"), "%" + actionOwner + "%"));
            }

            if (filterMap.containsKey("subject") && filterMap.get("subject") !=null) {
                String subject = (String) filterMap.get("subject");
                predicates.add(criteriaBuilder.like(userActionsRoot.get("subject"), "%" + subject + "%"));
            }

            if (filterMap.containsKey("actionTaken") && filterMap.get("actionTaken") != null) {
                predicates.add(criteriaBuilder.equal(userActionsRoot.get("actionTaken"), filterMap.get("actionTaken")));
            }

            if (filterMap.containsKey("deleted") && filterMap.get("deleted") != null) {
                predicates.add(criteriaBuilder.equal(userActionsRoot.get("deleted"), filterMap.get("deleted")));
            }

            if (filterMap.containsKey("mailReceivedTime") && filterMap.get("mailReceivedTime") != null) {
                String mailReceivedTimeStr = (String) filterMap.get("mailReceivedTime");
                LocalDateTime mailReceivedTime = LocalDateTime.parse(mailReceivedTimeStr);
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(userActionsRoot.get("mailReceivedTime"), mailReceivedTime));
            }

            if(filterMap.containsKey("actionTakenReason")) {
                String actionTakenReason = (String) filterMap.get("actionTakenReason");

                if (actionTakenReason.equals("pending")) {
                    predicates.add(criteriaBuilder.isNull(userActionsRoot.get("actionTakenReason")));
                } else if (actionTakenReason.equals("notClear")) {
                    predicates.add(criteriaBuilder.isNotNull(userActionsRoot.get("actionTakenReason")));
                }
            }
            criteriaQuery.select(criteriaBuilder.count(userActionsRoot));
            if (!predicates.isEmpty()) {
                criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
            }

            return entityManager.createQuery(criteriaQuery).getSingleResult();
        } catch (Exception e) {
            log.error("Error while counting filtered user actions: {}", e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * Gets list from criteria query v1.
     *
     * @param criteriaQuery the criteria query
     * @param llimit        the llimit
     * @param ulimit        the ulimit
     * @return the list from criteria query v 1
     */
    public List<UserActions> getListFromCriteriaQuery_v1(CriteriaQuery<UserActions> criteriaQuery, Integer llimit, Integer ulimit) {
        Query userActionsQuery = entityManager.createQuery(criteriaQuery);

        if (llimit != null && ulimit != null && llimit >= 0 && ulimit >= llimit) {
            userActionsQuery.setFirstResult(llimit);
            userActionsQuery.setMaxResults(ulimit - llimit + 1);
        }

        return userActionsQuery.getResultList();
    }


    /**
     * Gets filtered mail summary.
     *
     * @param filterMap the filter map
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @return the filtered mail summary
     */
    public List<MailSummary> getFilteredMailSummary(Map<String, Object> filterMap, int llimit, int ulimit) {
        try {
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<MailSummary> criteriaQuery = criteriaBuilder.createQuery(MailSummary.class);
            Root<MailSummary> userActionsRoot = criteriaQuery.from(MailSummary.class);

            List<Predicate> predicates = new ArrayList<>();

            if (filterMap.containsKey("priority")) {
                String priority = (String) filterMap.get("priority");
                predicates.add(criteriaBuilder.like(userActionsRoot.get("priority"), "%" + priority + "%"));
            }
            if (filterMap.containsKey("subject")) {
                String subject = (String) filterMap.get("subject");
                predicates.add(criteriaBuilder.like(userActionsRoot.get("subject"), "%" + subject + "%"));
            }
            if (filterMap.containsKey("type")) {
                predicates.add(criteriaBuilder.equal(userActionsRoot.get("type"), filterMap.get("type")));
            }
            if (filterMap.containsKey("category")) {
                predicates.add(criteriaBuilder.equal(userActionsRoot.get("category"), filterMap.get("category")));
            }
            if (filterMap.containsKey("starMarked")) {
                predicates.add(criteriaBuilder.equal(userActionsRoot.get("starMarked"), filterMap.get("starMarked")));
            }
            if (filterMap.containsKey("fromUser")) {
                predicates.add(criteriaBuilder.like(userActionsRoot.get("fromUser"), "%"+ filterMap.get("fromUser")+"%"));
            }
                predicates.add(criteriaBuilder.equal(userActionsRoot.get("deleted"), false));
            LocalDate date = LocalDate.now();
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = date.atTime(LocalTime.MAX);
                predicates.add(criteriaBuilder.between(userActionsRoot.get("mailReceivedTime"), startOfDay,endOfDay));


            predicates.add(criteriaBuilder.equal(userActionsRoot.get("userId"), filterMap.get("userId")));
            criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
            criteriaQuery.orderBy(criteriaBuilder.desc(userActionsRoot.get("mailReceivedTime")));

            Query userActionsQuery = entityManager.createQuery(criteriaQuery);

                userActionsQuery.setFirstResult(llimit);
                userActionsQuery.setMaxResults(ulimit - llimit + 1);

            return userActionsQuery.getResultList();

//            return getListFromCriteriaQuery_v1(criteriaQuery, llimit, ulimit);
        }catch(Exception e){
            log.error("Error while fetching filtered user actions: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }


    /**
     * Gets mail summary count by filter.
     *
     * @param filterMap the filter map
     * @return the mail summary count by filter
     */
    public Long getMailSummaryCountByFilter(Map<String, Object> filterMap) {
        try {
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Long> criteriaQuery = criteriaBuilder.createQuery(Long.class);
            Root<MailSummary> userActionsRoot = criteriaQuery.from(MailSummary.class);

            List<Predicate> predicates = new ArrayList<>();

            if (filterMap.containsKey("priority")) {
                String priority = (String) filterMap.get("priority");
                predicates.add(criteriaBuilder.like(userActionsRoot.get("priority"), "%" + priority + "%"));
            }
            if (filterMap.containsKey("subject")) {
                String subject = (String) filterMap.get("subject");
                predicates.add(criteriaBuilder.like(userActionsRoot.get("subject"), "%" + subject + "%"));
            }
            if (filterMap.containsKey("type")) {
                predicates.add(criteriaBuilder.equal(userActionsRoot.get("type"), filterMap.get("type")));
            }
            if (filterMap.containsKey("category")) {
                predicates.add(criteriaBuilder.equal(userActionsRoot.get("category"), filterMap.get("category")));
            }
            if (filterMap.containsKey("starMarked")) {
                predicates.add(criteriaBuilder.equal(userActionsRoot.get("starMarked"), filterMap.get("starMarked")));
            }
            if (filterMap.containsKey("fromUser")) {
                predicates.add(criteriaBuilder.like(userActionsRoot.get("fromUser"), "%"+ filterMap.get("fromUser")+"%"));
            }
            predicates.add(criteriaBuilder.equal(userActionsRoot.get("deleted"), false));
            LocalDate date = LocalDate.now();
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = date.atTime(LocalTime.MAX);
            predicates.add(criteriaBuilder.between(userActionsRoot.get("mailReceivedTime"), startOfDay,endOfDay));
            predicates.add(criteriaBuilder.equal(userActionsRoot.get("userId"), filterMap.get("userId")));

            criteriaQuery.select(criteriaBuilder.count(userActionsRoot));
            if (!predicates.isEmpty()) {
                criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
            }

            return entityManager.createQuery(criteriaQuery).getSingleResult();
        } catch (Exception e) {
            log.error("Error while counting filtered user actions: {}", e.getMessage(), e);
            return 0L;
        }
    }



}
