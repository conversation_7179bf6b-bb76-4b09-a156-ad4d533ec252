package com.enttribe.emailagent.repository.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.Organisation;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

/**
 * The type Organistation repository.
 *  <AUTHOR>
 */
@Repository
@Slf4j
@Primary
public class OrganistationRepositoryImpl {


        @PersistenceContext
    private EntityManager entityManager;

    public OrganistationRepositoryImpl( EntityManager entityManager) {

    }

    /**
     * Organisation by filter list.
     *
     * @param filterMap the filter map
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @return the list
     */
    public List<Organisation> organisationByFilter (Map<String, Object> filterMap, int llimit, int ulimit) {
        log.info("came inside repository ===========================");
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Organisation> criteriaQuery = criteriaBuilder.createQuery(Organisation.class);
        Root<Organisation> organisationRoot = criteriaQuery.from(Organisation.class);

        List<Predicate> predicates = new ArrayList<>();

        // Add filters
        processFilterMap(filterMap, predicates, criteriaBuilder, organisationRoot);

        // Build the query
        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(organisationRoot.get("modifiedTime")));

        // Execute the query with limits
        return getListFromCriteriaQuery(criteriaQuery, llimit, ulimit);
    }


    private void processFilterMap(Map<String, Object> filterMap, List<Predicate> predicates,
                              CriteriaBuilder criteriaBuilder, Root<Organisation> organisationRoot) {

    // Handle the 'email' field
    if (filterMap.get("organisationName") != null && !filterMap.get("organisationName").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("organisationName").toString(), predicates, criteriaBuilder, organisationRoot.get("organisationName"));
    }

    // Handle the 'userId' field (default is 'email')
    if (filterMap.get("timeZone") != null && !filterMap.get("timeZone").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("timeZone").toString(), predicates, criteriaBuilder, organisationRoot.get("timeZone"));
    }

    // Handle the 'phoneNumber' field
    if (filterMap.get("blacklisted") != null && !filterMap.get("blacklisted").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("blacklisted").toString(), predicates, criteriaBuilder, organisationRoot.get("blacklisted"));
    }

    if (filterMap.get("createdTime") != null) {
        Map<String, String> errorDateMap = (Map<String, String>) filterMap.get("createdTime");
        String dateStartStr = errorDateMap.get("dateStart");
        String dateEndStr = errorDateMap.get("dateEnd");

            if (dateStartStr != null && dateEndStr != null) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date errorDateFrom = dateFormat.parse(dateStartStr);
                    Date errorDateTo = dateFormat.parse(dateEndStr);
                    predicates.add(criteriaBuilder.between(organisationRoot.get("createdTime"), errorDateFrom, errorDateTo));
                } catch (Exception e) {
                    log.error("Invalid errorDate range format provided.", e);
                }
            }
    }
    if (filterMap.get("modifiedTime") != null) {
        Map<String, String> errorDateMap = (Map<String, String>) filterMap.get("modifiedTime");
        String dateStartStr = errorDateMap.get("dateStart");
        String dateEndStr = errorDateMap.get("dateEnd");

            if (dateStartStr != null && dateEndStr != null) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date errorDateFrom = dateFormat.parse(dateStartStr);
                    Date errorDateTo = dateFormat.parse(dateEndStr);
                    predicates.add(criteriaBuilder.between(organisationRoot.get("modifiedTime"), errorDateFrom, errorDateTo));
                } catch (Exception e) {
                    log.error("Invalid errorDate range format provided.", e);
                }
            }
    }
    if (filterMap.get("meetingType") != null && !filterMap.get("meetingType").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("meetingType").toString(), predicates, criteriaBuilder, organisationRoot.get("meetingType"));
    }
    if (filterMap.get("active") != null) {
        Boolean deletedValue = Boolean.parseBoolean(filterMap.get("active").toString());
        predicates.add(criteriaBuilder.equal(organisationRoot.get("active"), deletedValue));
    }
}

    private void getNameFilter(String value, List<Predicate> predicates, CriteriaBuilder cb, Path<String> field) {
        predicates.add(cb.like(cb.lower(field), "%" + value.toLowerCase() + "%"));
    }

    private List<Organisation> getListFromCriteriaQuery(CriteriaQuery<Organisation> criteriaQuery, Integer llimit, Integer ulimit) {
        Query query = entityManager.createQuery(criteriaQuery);
        if (llimit != null && ulimit != null && llimit >= 0 && ulimit > 0) {
            query.setMaxResults(ulimit - llimit + 1);
            query.setFirstResult(llimit);
        }
        return query.getResultList();
    }


    /**
     * Organisation count by filter list.
     *
     * @param filterMap the filter map
     * @return the list
     */
    public List<Organisation> organisationCountByFilter(Map<String, Object> filterMap) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Organisation> criteriaQuery = criteriaBuilder.createQuery(Organisation.class);
        Root<Organisation> emailPreferenceRoot = criteriaQuery.from(Organisation.class);

        List<Predicate> predicates = new ArrayList<>();

        // Add filters
        processFilterMap(filterMap, predicates, criteriaBuilder, emailPreferenceRoot);

        // Build the query
        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(emailPreferenceRoot.get("modifiedTime")));
        

        // Execute the query with limits
        return getListFromCriteriaQueryForCount(criteriaQuery);
    }

    private List<Organisation> getListFromCriteriaQueryForCount(CriteriaQuery<Organisation> criteriaQuery) {
        Query query = entityManager.createQuery(criteriaQuery);
        return query.getResultList();
    }


    
}
