package com.enttribe.emailagent.repository.impl;

import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.query.FluentQuery.FetchableFluentQuery;
import org.springframework.stereotype.Repository;

import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

/**
 * The type Email preference dao.
 * <AUTHOR> Pathak
 */
@Repository
@Slf4j
@Primary
public class EmailPreferenceDaoImpl {

      @PersistenceContext
    private EntityManager entityManager;

    public EmailPreferenceDaoImpl( EntityManager entityManager) {

    }


    /**
     * Filteredpreference list.
     *
     * @param filterMap the filter map
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @return the list
     */
    public List<EmailPreferences> Filteredpreference(Map<String, Object> filterMap, int llimit, int ulimit) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<EmailPreferences> criteriaQuery = criteriaBuilder.createQuery(EmailPreferences.class);
        Root<EmailPreferences> emailPreferenceRoot = criteriaQuery.from(EmailPreferences.class);

        List<Predicate> predicates = new ArrayList<>();

        // Add filters
        processFilterMap(filterMap, predicates, criteriaBuilder, emailPreferenceRoot);

        // Build the query
        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(emailPreferenceRoot.get("modifiedTime")));

        // Execute the query with limits
        return getListFromCriteriaQuery(criteriaQuery, llimit, ulimit);
    }

    private void processFilterMap(Map<String, Object> filterMap, List<Predicate> predicates,
    CriteriaBuilder criteriaBuilder, Root<EmailPreferences> emailPreferenceRoot) {

if (filterMap.get("emailSubject") != null && !filterMap.get("emailSubject").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("emailSubject").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("emailSubject"));
}

if (filterMap.get("senderCompany") != null && !filterMap.get("senderCompany").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("senderCompany").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("senderCompany"));
}

if (filterMap.get("importantTags") != null && !filterMap.get("importantTags").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("importantTags").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("importantTags"));
}

if (filterMap.get("emailSender") != null && !filterMap.get("emailSender").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("emailSender").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("emailSender"));
}

if (filterMap.get("blackListedDomain") != null && !filterMap.get("blackListedDomain").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("blackListedDomain").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("blackListedDomain"));
}

if (filterMap.get("blackListedSubject") != null && !filterMap.get("blackListedSubject").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("blackListedSubject").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("blackListedSubject"));
}

if (filterMap.get("conversationId") != null && !filterMap.get("conversationId").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("conversationId").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("conversationId"));
}

if (filterMap.get("contactNumber") != null && !filterMap.get("contactNumber").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("contactNumber").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("contactNumber"));
}

if (filterMap.get("id") != null) {
predicates.add(criteriaBuilder.equal(emailPreferenceRoot.get("id"), filterMap.get("id")));
}

if (filterMap.get("fontColor") != null && !filterMap.get("fontColor").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("fontColor").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("fontColor"));
}

if (filterMap.get("displayName") != null && !filterMap.get("displayName").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("displayName").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("displayName"));
}

if (filterMap.get("userId") != null && !filterMap.get("userId").toString().trim().isEmpty()) {
getNameFilter(filterMap.get("userId").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("userId"));
}
if (filterMap.get("debugMode") != null) {
    Boolean deletedValue = Boolean.parseBoolean(filterMap.get("debugMode").toString());
    predicates.add(criteriaBuilder.equal(emailPreferenceRoot.get("debugMode"), deletedValue));
}

if (filterMap.get("fontFamily") != null && !filterMap.get("fontFamily").toString().trim().isEmpty()) {
    getNameFilter(filterMap.get("fontFamily").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("fontFamily"));
    }
        if (filterMap.get("maskContent") != null) {
            Boolean deletedValue = Boolean.parseBoolean(filterMap.get("maskContent").toString());
            predicates.add(criteriaBuilder.equal(emailPreferenceRoot.get("maskContent"), deletedValue));
        }
    if (filterMap.get("fontSize") != null && !filterMap.get("fontSize").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("fontSize").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("fontSize"));
        }
    if (filterMap.get("timeZone") != null && !filterMap.get("timeZone").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("timeZone").toString(), predicates, criteriaBuilder, emailPreferenceRoot.get("timeZone"));
        }


    if (filterMap.get("createdTime") != null) {
        Map<String, String> errorDateMap = (Map<String, String>) filterMap.get("createdTime");
        String dateStartStr = errorDateMap.get("dateStart");
        String dateEndStr = errorDateMap.get("dateEnd");

            if (dateStartStr != null && dateEndStr != null) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date errorDateFrom = dateFormat.parse(dateStartStr);
                    Date errorDateTo = dateFormat.parse(dateEndStr);
                    predicates.add(criteriaBuilder.between(emailPreferenceRoot.get("createdTime"), errorDateFrom, errorDateTo));
                } catch (Exception e) {
                    log.error("Invalid errorDate range format provided.", e);
                }
            }
    }
    if (filterMap.get("modifiedTime") != null) {
        Map<String, String> errorDateMap = (Map<String, String>) filterMap.get("modifiedTime");
        String dateStartStr = errorDateMap.get("dateStart");
        String dateEndStr = errorDateMap.get("dateEnd");

            if (dateStartStr != null && dateEndStr != null) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date errorDateFrom = dateFormat.parse(dateStartStr);
                    Date errorDateTo = dateFormat.parse(dateEndStr);
                    predicates.add(criteriaBuilder.between(emailPreferenceRoot.get("modifiedTime"), errorDateFrom, errorDateTo));
                } catch (Exception e) {
                    log.error("Invalid errorDate range format provided.", e);
                }
            }
    }

}


    private void getNameFilter(String value, List<Predicate> predicates, CriteriaBuilder cb, Path<String> field) {
        predicates.add(cb.like(cb.lower(field), "%" + value.toLowerCase() + "%"));
    }

    private List<EmailPreferences> getListFromCriteriaQuery(CriteriaQuery<EmailPreferences> criteriaQuery, Integer llimit, Integer ulimit) {
        Query query = entityManager.createQuery(criteriaQuery);
        if (llimit != null && ulimit != null && llimit >= 0 && ulimit > 0) {
            query.setMaxResults(ulimit - llimit + 1);
            query.setFirstResult(llimit);
        }
        return query.getResultList();
    }


    /**
     * Filteredpreference count list.
     *
     * @param filterMap the filter map
     * @return the list
     */
    public List<EmailPreferences> FilteredpreferenceCount(Map<String, Object> filterMap) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<EmailPreferences> criteriaQuery = criteriaBuilder.createQuery(EmailPreferences.class);
        Root<EmailPreferences> emailPreferenceRoot = criteriaQuery.from(EmailPreferences.class);

        List<Predicate> predicates = new ArrayList<>();

        // Add filters
        processFilterMap(filterMap, predicates, criteriaBuilder, emailPreferenceRoot);

        // Build the query
        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(emailPreferenceRoot.get("modifiedTime")));
        

        // Execute the query with limits
        return getListFromCriteriaQueryForCount(criteriaQuery);
    }

    private List<EmailPreferences> getListFromCriteriaQueryForCount(CriteriaQuery<EmailPreferences> criteriaQuery) {
        Query query = entityManager.createQuery(criteriaQuery);
        return query.getResultList();
    }
    
}
