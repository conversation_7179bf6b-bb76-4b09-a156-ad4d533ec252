package com.enttribe.emailagent.repository.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.query.FluentQuery.FetchableFluentQuery;
import org.springframework.stereotype.Repository;

import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.repository.EmailUserDao;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;


/**
 * The type Email user dao.
 *  <AUTHOR> Lokhande
 */
@Repository
@Slf4j
@Primary
public class EmailUserDaoImpl{

        @PersistenceContext
    private EntityManager entityManager;

    public EmailUserDaoImpl( EntityManager entityManager) {

    }


    /**
     * Filtered email user list.
     *
     * @param filterMap the filter map
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @return the list
     */
    public List<EmailUser> filteredEmailUser(Map<String, Object> filterMap, int llimit, int ulimit) {
        log.info("came inside repository ===========================");
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<EmailUser> criteriaQuery = criteriaBuilder.createQuery(EmailUser.class);
        Root<EmailUser> emailPreferenceRoot = criteriaQuery.from(EmailUser.class);

        List<Predicate> predicates = new ArrayList<>();

        // Add filters
        processFilterMap(filterMap, predicates, criteriaBuilder, emailPreferenceRoot);

        // Build the query
        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(emailPreferenceRoot.get("modifiedTime")));

        // Execute the query with limits
        return getListFromCriteriaQuery(criteriaQuery, llimit, ulimit);
    }

 private void processFilterMap(Map<String, Object> filterMap, List<Predicate> predicates,
                              CriteriaBuilder criteriaBuilder, Root<EmailUser> emailPreferencesRoot) {

    // Handle the 'email' field
    if (filterMap.get("email") != null && !filterMap.get("email").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("email").toString(), predicates, criteriaBuilder, emailPreferencesRoot.get("email"));
    }

    // Handle the 'userId' field (default is 'email')
    if (filterMap.get("userId") != null && !filterMap.get("userId").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("userId").toString(), predicates, criteriaBuilder, emailPreferencesRoot.get("userId"));
    }

    // Handle the 'batchId' field
    if (filterMap.get("batchId") != null) {
        predicates.add(criteriaBuilder.equal(emailPreferencesRoot.get("batchId"), filterMap.get("batchId")));
    }

    // Handle the 'phoneNumber' field
    if (filterMap.get("phoneNumber") != null && !filterMap.get("phoneNumber").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("phoneNumber").toString(), predicates, criteriaBuilder, emailPreferencesRoot.get("phoneNumber"));
    }

    // Handle the 'type' field (default is "Office365")
    if (filterMap.get("type") != null && !filterMap.get("type").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("type").toString(), predicates, criteriaBuilder, emailPreferencesRoot.get("type"));
    }

    // Handle the 'deleted' field (boolean)
    if (filterMap.get("deleted") != null) {
        Boolean deletedValue = Boolean.parseBoolean(filterMap.get("deleted").toString());
        predicates.add(criteriaBuilder.equal(emailPreferencesRoot.get("deleted"), deletedValue));
    }

    // Handle the 'name' field
    if (filterMap.get("name") != null && !filterMap.get("name").toString().trim().isEmpty()) {
        getNameFilter(filterMap.get("name").toString(), predicates, criteriaBuilder, emailPreferencesRoot.get("name"));
    }
}

    private void getNameFilter(String value, List<Predicate> predicates, CriteriaBuilder cb, Path<String> field) {
        predicates.add(cb.like(cb.lower(field), "%" + value.toLowerCase() + "%"));
    }

    private List<EmailUser> getListFromCriteriaQuery(CriteriaQuery<EmailUser> criteriaQuery, Integer llimit, Integer ulimit) {
        Query query = entityManager.createQuery(criteriaQuery);
        if (llimit != null && ulimit != null && llimit >= 0 && ulimit > 0) {
            query.setMaxResults(ulimit - llimit + 1);
            query.setFirstResult(llimit);
        }
        return query.getResultList();
    }


    /**
     * Filtered email user count list.
     *
     * @param filterMap the filter map
     * @return the list
     */
    public List<EmailUser> filteredEmailUserCount(Map<String, Object> filterMap) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<EmailUser> criteriaQuery = criteriaBuilder.createQuery(EmailUser.class);
        Root<EmailUser> emailPreferenceRoot = criteriaQuery.from(EmailUser.class);

        List<Predicate> predicates = new ArrayList<>();

        // Add filters
        processFilterMap(filterMap, predicates, criteriaBuilder, emailPreferenceRoot);

        // Build the query
        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(emailPreferenceRoot.get("modifiedTime")));

        // Execute the query with limits
        return getListFromCriteriaQueryCount(criteriaQuery);
    }
    private List<EmailUser> getListFromCriteriaQueryCount(CriteriaQuery<EmailUser> criteriaQuery) {
        Query query = entityManager.createQuery(criteriaQuery);
        return query.getResultList();
    }



    
}
