package com.enttribe.emailagent.repository.impl;

import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * The type Batch repository.
 *  <AUTHOR> <PERSON>
 */
@Repository
@Slf4j
@Primary
public class BatchRepositoryImpl {
	
	@PersistenceContext
	private EntityManager entityManager;

	
	public BatchRepositoryImpl(EntityManager entityManager) {

	}
	
	public List<Object[]> avgMailTime(){
		
		String jpql = "SELECT      MU.USER_ID,      SEC_TO_TIME(MOD(AVG(TIMESTAMPDIFF(SECOND, '1970-01-01 00:00:00', MS.MAIL_RECEIVED_TIME)), 86400)) AS avg_mail_received_time FROM      EMAIL_USER MU LEFT JOIN      MAIL_SUMMARY MS ON MU.USER_ID = MS.USER_ID     AND DATE(MS.MAIL_RECEIVED_TIME) = CURDATE() WHERE      MU.deleted = FALSE  and MU.BATCH_ID!='Batch-1' GROUP BY      MU.USER_ID ORDER BY      avg_mail_received_time ASC";

	List<Object[]> results = entityManager.createNativeQuery(jpql).getResultList();
	return results;
		
	}

	/**
	 * Update batch.
	 *
	 * @param batchId the batch id
	 * @param userIds the user ids
	 */
	@Transactional
	public void updateBatch(String batchId,List<String> userIds) {
		// Create a parameterized SQL update query
		String sql = "UPDATE EMAIL_USER SET BATCH_ID = :newBatchId WHERE USER_ID IN :userIds";

		// Execute the update query
		entityManager.createNativeQuery(sql)
		    .setParameter("newBatchId", batchId)  // Set your new batch ID here
		    .setParameter("userIds", userIds)
		    .executeUpdate();
	}
	
}
