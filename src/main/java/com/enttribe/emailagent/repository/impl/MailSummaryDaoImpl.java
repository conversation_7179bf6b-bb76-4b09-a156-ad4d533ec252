package com.enttribe.emailagent.repository.impl;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * The type Mail summary dao.
 *  <AUTHOR>
 */
@Repository
@Slf4j
@Primary
public class MailSummaryDaoImpl {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private UserContextHolder userContextHolder;

    /**
     * Filtered mail summary list.
     *
     * @param filterMap the filter map
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @return the list
     */
    public List<MailSummary> filteredMailSummary(Map<String, Object> filterMap, int llimit, int ulimit) {
        log.info("came inside filteredMailSummary ===========================");
        log.info("Current user is {}",userContextHolder.getCurrentUser().getEmail());

        filterMap.put("userId",userContextHolder.getCurrentUser().getEmail());

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<MailSummary> criteriaQuery = criteriaBuilder.createQuery(MailSummary.class);
        Root<MailSummary> mailSummaryRoot = criteriaQuery.from(MailSummary.class);

        List<Predicate> predicates = new ArrayList<>();
        processFilterMap(filterMap, predicates, criteriaBuilder, mailSummaryRoot);

        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(mailSummaryRoot.get("modifiedTime")));
        return getListFromCriteriaQuery(criteriaQuery, llimit, ulimit);
    }

    private void processFilterMap(Map<String, Object> filterMap, List<Predicate> predicates,
                                  CriteriaBuilder criteriaBuilder, Root<MailSummary> emailPreferencesRoot) {
        // Filter for conversationId
        if (filterMap.get("conversationId") != null && !filterMap.get("conversationId").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("conversationId").toString(), predicates, criteriaBuilder, emailPreferencesRoot.get("conversationId"));
        }

        // Filter for userId
        if (filterMap.get("userId") != null && !filterMap.get("userId").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("userId").toString(), predicates, criteriaBuilder, emailPreferencesRoot.get("userId"));
        }

        // Filter for messageId
        if (filterMap.get("messageId") != null) {
            predicates.add(criteriaBuilder.equal(emailPreferencesRoot.get("messageId"), filterMap.get("messageId")));
        }

        // Filter for category
        if (filterMap.get("category") != null && !filterMap.get("category").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("category").toString(), predicates, criteriaBuilder, emailPreferencesRoot.get("category"));
        }

        // Filter for priority
        if (filterMap.get("priority") != null && !filterMap.get("priority").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("priority").toString(), predicates, criteriaBuilder, emailPreferencesRoot.get("priority"));
        }

        // Filter for deleted flag
        if (filterMap.get("deleted") != null) {
            Boolean deletedValue = Boolean.parseBoolean(filterMap.get("deleted").toString());
            predicates.add(criteriaBuilder.equal(emailPreferencesRoot.get("deleted"), deletedValue));
        }

        // New filter for folderName
        if (filterMap.get("folderName") != null && !filterMap.get("folderName").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("folderName").toString(), predicates, criteriaBuilder, emailPreferencesRoot.get("folderName"));
        }
    }


    private void getNameFilter(String value, List<Predicate> predicates, CriteriaBuilder cb, Path<String> field) {
        predicates.add(cb.like(cb.lower(field), "%" + value.toLowerCase() + "%"));
    }

    private List<MailSummary> getListFromCriteriaQuery(CriteriaQuery<MailSummary> criteriaQuery, Integer llimit, Integer ulimit) {
        Query query = entityManager.createQuery(criteriaQuery);
        if (llimit != null && ulimit != null && llimit >= 0 && ulimit > 0) {
            query.setMaxResults(ulimit - llimit + 1);
            query.setFirstResult(llimit);
        }
        return query.getResultList();
    }

}
