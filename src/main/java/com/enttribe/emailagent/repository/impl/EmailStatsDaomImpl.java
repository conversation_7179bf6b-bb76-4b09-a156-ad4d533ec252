package com.enttribe.emailagent.repository.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import com.enttribe.emailagent.entity.EmailStats;
import com.enttribe.emailagent.entity.FailureLogs;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

/**
 * The type Email stats daom.
 *  <AUTHOR> Sonsale
 */
@Repository
@Slf4j
@Primary
public class EmailStatsDaomImpl {

	@PersistenceContext
	private EntityManager entityManager;

	public EmailStatsDaomImpl(EntityManager entityManager) {

	}


	/**
	 * Gets filtered email stats.
	 *
	 * @param filterMap the filter map
	 * @param llimit    the llimit
	 * @param ulimit    the ulimit
	 * @return the filtered email stats
	 */
	public List<EmailStats> getFilteredEmailStats(Map<String, Object> filterMap, int llimit, int ulimit) {
		 CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
		 CriteriaQuery<EmailStats> criteriaQuery = criteriaBuilder.createQuery(EmailStats.class);
		 Root<EmailStats> failureLogsRoot = criteriaQuery.from(EmailStats.class);

	        List<Predicate> predicates = new ArrayList<>();

	        // Add filters
	        processFilterMap(filterMap, predicates, criteriaBuilder, failureLogsRoot);

	        // Build the query
	        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
	        criteriaQuery.orderBy(criteriaBuilder.desc(failureLogsRoot.get("statsDate")));

	        // Execute the query with limits
	        return getListFromCriteriaQuery(criteriaQuery, llimit, ulimit);
	    }
	 
	 
	

	 

	private void processFilterMap(Map<String, Object> filterMap, List<Predicate> predicates,
			CriteriaBuilder criteriaBuilder, Root<EmailStats> EmailStatsRoot) {

		if (filterMap.get("userId") != null && !filterMap.get("userId").toString().trim().isEmpty()) {
			getNameFilter(filterMap.get("userId").toString(), predicates, criteriaBuilder, EmailStatsRoot.get("userId"));
		}

		if (filterMap.get("type") != null && !filterMap.get("type").toString().trim().isEmpty()) {
			getNameFilter(filterMap.get("type").toString(), predicates, criteriaBuilder,
					EmailStatsRoot.get("type"));
		}


		if (filterMap.get("id") != null) {
			predicates.add(criteriaBuilder.equal(EmailStatsRoot.get("id"), filterMap.get("id")));
		}

		if (filterMap.get("statsDate") != null) {
			Map<String, String> errorDateMap = (Map<String, String>) filterMap.get("statsDate");
			String dateStartStr = errorDateMap.get("dateStart");
			String dateEndStr = errorDateMap.get("dateEnd");
			if (dateStartStr != null && dateEndStr != null) {
// Parse the string dates to Date and then to milliseconds
				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				try {
					Date errorDateFrom = dateFormat.parse(dateStartStr);
					Date errorDateTo = dateFormat.parse(dateEndStr);
					predicates.add(criteriaBuilder.between(EmailStatsRoot.get("statsDate"), errorDateFrom.getTime(),
							errorDateTo.getTime()));
				} catch (Exception e) {
// Handle parsing error
					log.error("Error parsing dates for Date filter", e);
				}
			}
		}
	}

	private void getNameFilter(String value, List<Predicate> predicates, CriteriaBuilder cb, Path<String> field) {
		predicates.add(cb.like(cb.lower(field), "%" + value.toLowerCase() + "%"));
	}

	private List<EmailStats> getListFromCriteriaQuery(CriteriaQuery<EmailStats> criteriaQuery, Integer llimit,
			Integer ulimit) {
		Query query = entityManager.createQuery(criteriaQuery);
		if (llimit != null && ulimit != null && llimit >= 0 && ulimit > 0) {
			query.setMaxResults(ulimit - llimit + 1);
			query.setFirstResult(llimit);
		}
		return query.getResultList();
	}

	/**
	 * Gets filtered email stats count.
	 *
	 * @param filterMap the filter map
	 * @return the filtered email stats count
	 */
	public List<EmailStats> getFilteredEmailStatsCount(Map<String, Object> filterMap) {
		CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
		CriteriaQuery<EmailStats> criteriaQuery = criteriaBuilder.createQuery(EmailStats.class);
		Root<EmailStats> EmailStatsRoot = criteriaQuery.from(EmailStats.class);

		List<Predicate> predicates = new ArrayList<>();

// Add filters
		processFilterMap(filterMap, predicates, criteriaBuilder, EmailStatsRoot);

// Build the query
		criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
		criteriaQuery.orderBy(criteriaBuilder.desc(EmailStatsRoot.get("statsDate")));

// Execute the query with limits
		return getListFromCriteriaQueryCount(criteriaQuery);
	}

	private List<EmailStats> getListFromCriteriaQueryCount(CriteriaQuery<EmailStats> criteriaQuery) {
		Query query = entityManager.createQuery(criteriaQuery);

		return query.getResultList();
	}

}
