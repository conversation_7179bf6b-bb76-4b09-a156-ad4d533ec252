package com.enttribe.emailagent.repository.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.query.FluentQuery.FetchableFluentQuery;
import org.springframework.stereotype.Repository;

import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.entity.UserMailAttachment;
import com.enttribe.emailagent.repository.UserMailAttachmentDao;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

/**
 * The type User mail attachment dao.
 *  <AUTHOR> Lokhande
 */
@Repository
@Slf4j
@Primary
public class UserMailAttachmentDaoImpl {
    
    @PersistenceContext
    private EntityManager entityManager;

    public UserMailAttachmentDaoImpl( EntityManager entityManager) {

    }


    /**
     * User mail attachment by filter list.
     *
     * @param filterMap the filter map
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @return the list
     */
    public List<UserMailAttachment> userMailAttachmentByFilter(Map<String, Object> filterMap, int llimit, int ulimit) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<UserMailAttachment> criteriaQuery = criteriaBuilder.createQuery(UserMailAttachment.class);
        Root<UserMailAttachment> failureLogsRoot = criteriaQuery.from(UserMailAttachment.class);

        List<Predicate> predicates = new ArrayList<>();

        // Add filters
        processFilterMap(filterMap, predicates, criteriaBuilder, failureLogsRoot);

        // Build the query
        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(failureLogsRoot.get("modifiedTime")));

        // Execute the query with limits
        return getListFromCriteriaQuery(criteriaQuery, llimit, ulimit);
    }

    private void processFilterMap(Map<String, Object> filterMap, List<Predicate> predicates,
                                  CriteriaBuilder criteriaBuilder, Root<UserMailAttachment> failureLogsRoot) {

        if (filterMap.get("email") != null && !filterMap.get("email").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("email").toString(), predicates, criteriaBuilder, failureLogsRoot.get("email"));
        }

        if (filterMap.get("name") != null && !filterMap.get("name").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("name").toString(), predicates, criteriaBuilder, failureLogsRoot.get("name"));
        }

        if (filterMap.get("subject") != null && !filterMap.get("subject").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("subject").toString(), predicates, criteriaBuilder, failureLogsRoot.get("subject"));
        }

        if (filterMap.get("messageId") != null && !filterMap.get("messageId").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("messageId").toString(), predicates, criteriaBuilder, failureLogsRoot.get("messageId"));
        }

        if (filterMap.get("conversationId") != null && !filterMap.get("conversationId").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("conversationId").toString(), predicates, criteriaBuilder, failureLogsRoot.get("conversationId"));
        }

        if (filterMap.get("userId") != null && !filterMap.get("userId").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("userId").toString(), predicates, criteriaBuilder, failureLogsRoot.get("userId"));
        }

        if (filterMap.get("id") != null) {
            predicates.add(criteriaBuilder.equal(failureLogsRoot.get("id"), filterMap.get("id")));
        }

        if (filterMap.get("internetMessageId") != null && !filterMap.get("internetMessageId").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("internetMessageId").toString(), predicates, criteriaBuilder, failureLogsRoot.get("internetMessageId"));
        }

        if (filterMap.get("type") != null && !filterMap.get("type").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("type").toString(), predicates, criteriaBuilder, failureLogsRoot.get("type"));
        }

        if (filterMap.get("processingStatus") != null && !filterMap.get("processingStatus").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("processingStatus").toString(), predicates, criteriaBuilder, failureLogsRoot.get("processingStatus"));
        }

    if (filterMap.get("creationTime") != null) {
        Map<String, String> errorDateMap = (Map<String, String>) filterMap.get("creationTime");
        String dateStartStr = errorDateMap.get("dateStart");
        String dateEndStr = errorDateMap.get("dateEnd");

            if (dateStartStr != null && dateEndStr != null) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date errorDateFrom = dateFormat.parse(dateStartStr);
                    Date errorDateTo = dateFormat.parse(dateEndStr);
                    predicates.add(criteriaBuilder.between(failureLogsRoot.get("creationTime"), errorDateFrom, errorDateTo));
                } catch (Exception e) {
                    log.error("Invalid errorDate range format provided.", e);
                }
            }
    }
    if (filterMap.get("modifiedTime") != null) {
        Map<String, String> errorDateMap = (Map<String, String>) filterMap.get("modifiedTime");
        String dateStartStr = errorDateMap.get("dateStart");
        String dateEndStr = errorDateMap.get("dateEnd");

            if (dateStartStr != null && dateEndStr != null) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date errorDateFrom = dateFormat.parse(dateStartStr);
                    Date errorDateTo = dateFormat.parse(dateEndStr);
                    predicates.add(criteriaBuilder.between(failureLogsRoot.get("modifiedTime"), errorDateFrom, errorDateTo));
                } catch (Exception e) {
                    log.error("Invalid errorDate range format provided.", e);
                }
            }
    }
        if (filterMap.get("retryCount") != null && !filterMap.get("retryCount").toString().trim().isEmpty()) {
            getNameFilter(filterMap.get("retryCount").toString(), predicates, criteriaBuilder, failureLogsRoot.get("retryCount"));
        }
        
    }

    private void getNameFilter(String value, List<Predicate> predicates, CriteriaBuilder cb, Path<String> field) {
        predicates.add(cb.like(cb.lower(field), "%" + value.toLowerCase() + "%"));
    }

    private List<UserMailAttachment> getListFromCriteriaQuery(CriteriaQuery<UserMailAttachment> criteriaQuery, Integer llimit, Integer ulimit) {
        Query query = entityManager.createQuery(criteriaQuery);
        if (llimit != null && ulimit != null && llimit >= 0 && ulimit > 0) {
            query.setMaxResults(ulimit - llimit + 1);
            query.setFirstResult(llimit);
        }
        return query.getResultList();
    }

    /**
     * User mail attachment by filter count list.
     *
     * @param filterMap the filter map
     * @return the list
     */
    public List<UserMailAttachment> userMailAttachmentByFilterCount(Map<String, Object> filterMap) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<UserMailAttachment> criteriaQuery = criteriaBuilder.createQuery(UserMailAttachment.class);
        Root<UserMailAttachment> failureLogsRoot = criteriaQuery.from(UserMailAttachment.class);

        List<Predicate> predicates = new ArrayList<>();

        // Add filters
        processFilterMap(filterMap, predicates, criteriaBuilder, failureLogsRoot);

        // Build the query
        criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
        criteriaQuery.orderBy(criteriaBuilder.desc(failureLogsRoot.get("modifiedTime")));

        // Execute the query with limits
        return getListFromCriteriaQueryCount(criteriaQuery);
    }

    private List<UserMailAttachment> getListFromCriteriaQueryCount(CriteriaQuery<UserMailAttachment> criteriaQuery) {
        Query query = entityManager.createQuery(criteriaQuery);

        return query.getResultList();
    }

    
}
