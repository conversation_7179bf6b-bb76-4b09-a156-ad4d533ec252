package com.enttribe.emailagent.repository.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import com.enttribe.emailagent.entity.UserFolders;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;


import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

/**
 * The type User folder dao.
 *  <AUTHOR>
 */
@Repository
@Slf4j
@Primary
public class UserFolderDaoImpl{


    @PersistenceContext
    private EntityManager entityManager;

    public UserFolderDaoImpl( EntityManager entityManager) {
    }

    /**
     * Gets folders group by email.
     *
     * @param filterMap the filter map
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @return the folders group by email
     */
    public List<Object[]> getFoldersGroupByEmail(Map<String, Object> filterMap, int llimit, int ulimit) {
        try {
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Object[]> criteriaQuery = criteriaBuilder.createQuery(Object[].class);
            Root<UserFolders> userFoldersRoot = criteriaQuery.from(UserFolders.class);
    
            // Create the SELECT clause with GROUP_CONCAT and CASE WHEN for active and inactive folders
            Expression<String> activeFolders = criteriaBuilder.function(
                "GROUP_CONCAT",
                String.class,
                criteriaBuilder.selectCase()
                    .when(criteriaBuilder.isTrue(userFoldersRoot.get("active")), userFoldersRoot.get("displayName"))
                    .otherwise(criteriaBuilder.literal(null))
            );
    
            Expression<String> inactiveFolders = criteriaBuilder.function(
                "GROUP_CONCAT",
                String.class,
                criteriaBuilder.selectCase()
                    .when(criteriaBuilder.isFalse(userFoldersRoot.get("active")), userFoldersRoot.get("displayName"))
                    .otherwise(criteriaBuilder.literal(null))
            );
    
            // Select email, active folders, and inactive folders
            criteriaQuery.multiselect(
                userFoldersRoot.get("email"),
                activeFolders.alias("active_folders"),
                inactiveFolders.alias("inactive_folders")
            );
    
            // Create predicates for filtering
            List<Predicate> predicates = new ArrayList<>();
            log.info("Processing filter map in repository...");
            processFilterMap(filterMap, predicates, criteriaBuilder, userFoldersRoot);
            log.info("Filter processing complete.");
    
            // Apply WHERE clause with the predicates
            if (!predicates.isEmpty()) {
                criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
            }
    
            // Group by email
            criteriaQuery.groupBy(userFoldersRoot.get("email"));
    
            // Apply pagination
            TypedQuery<Object[]> query = entityManager.createQuery(criteriaQuery);
            query.setFirstResult(llimit);
            query.setMaxResults(ulimit - llimit);
    
            return query.getResultList();
        } catch (Exception e) {
            log.error("Error in getFoldersGroupByEmail", e);
        }
        return null;
    }
    
    private void processFilterMap(Map<String, Object> filterMap, List<Predicate> predicates, CriteriaBuilder criteriaBuilder, Root<UserFolders> userFoldersRoot) {
        try {
            if (filterMap.isEmpty()) {
                return; // No filters to process
            }
    
            filterMap.forEach((key, value) -> {
                log.info("Processing filter - Key: {}, Value: {}", key, value);
    
                if (value instanceof String) {
                    String likeValue = "%" + value + "%";
                    predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(userFoldersRoot.get(key)),
                        likeValue.toLowerCase())
                    );
                } else if (value instanceof List) {
                    List<String> values = (List<String>) value;
                    if (!values.isEmpty()) {
                        predicates.add(userFoldersRoot.get(key).in(values));
                    }
                } else if (value instanceof Integer) {
                    predicates.add(criteriaBuilder.equal(userFoldersRoot.get(key), value));
                }
                // Add more conditions as needed based on the type of the value
            });
        } catch (Exception e) {
            log.error("Error processing filter map", e);
        }
    }


    /**
     * Gets folders count group by email.
     *
     * @param filterMap the filter map
     * @return the folders count group by email
     */
    public List<Object[]> getFoldersCountGroupByEmail(Map<String, Object> filterMap) {
        try {
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Object[]> criteriaQuery = criteriaBuilder.createQuery(Object[].class);
            Root<UserFolders> userFoldersRoot = criteriaQuery.from(UserFolders.class);

            // Create the SELECT clause
            criteriaQuery.multiselect(
                    userFoldersRoot.get("email"),
                    criteriaBuilder.function("GROUP_CONCAT", String.class,
                            userFoldersRoot.get("displayName"))
            );

            // Create predicates for filtering
            List<Predicate> predicates = new ArrayList<>();
            log.info("Processing filter map in repository...");
            processFilterMap(filterMap, predicates, criteriaBuilder, userFoldersRoot);
            log.info("Filter processing complete.");

            criteriaQuery.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));
            criteriaQuery.groupBy(userFoldersRoot.get("email"));

            // Apply pagination
            TypedQuery<Object[]> query = entityManager.createQuery(criteriaQuery);

            return query.getResultList();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    

    
}



