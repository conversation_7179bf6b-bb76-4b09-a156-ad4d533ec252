package com.enttribe.emailagent.repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.entity.UserMailAttachment;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.data.repository.query.Param;

/**
 * The interface User mail attachment dao.
 *  <AUTHOR>
 */
@Repository

public interface UserMailAttachmentDao extends JpaRepository<UserMailAttachment, Integer> {


    @Query("SELECT f FROM UserMailAttachment f WHERE " +
    "(:id IS NULL OR f.id = :id) AND " +
    "(:userId IS NULL OR f.userId = :userId) AND " +
    "(:name IS NULL OR f.name = :name) AND " +
    "(:uniqueName IS NULL OR f.uniqueName = :uniqueName) AND " +
    "(:type IS NULL OR f.type = :type) AND " +
    "(:messageId IS NULL OR f.messageId = :messageId) AND " +
    "(:attachmentId IS NULL OR f.attachmentId = :attachmentId) AND " +
    "(:ragDocumentId IS NULL OR f.ragDocumentId = :ragDocumentId) AND " +
    "(:conversationId IS NULL OR f.conversationId = :conversationId) AND " +
    "(:internetMessageId IS NULL OR f.internetMessageId = :internetMessageId) AND " +
    "(:processingStatus IS NULL OR f.processingStatus = :processingStatus) AND " +
    "(:processingError IS NULL OR f.processingError = :processingError) AND " +
    "(:creationTime IS NULL OR f.creationTime = :creationTime) AND " +
    "(:modifiedTime IS NULL OR f.modifiedTime = :modifiedTime)"
)
List<UserMailAttachment> search(
    @Param("id") Integer id,
    @Param("userId") String userId,
    @Param("name") String name,
    @Param("uniqueName") String uniqueName,
    @Param("type") String type,
    @Param("messageId") String messageId,
    @Param("attachmentId") String attachmentId,
    @Param("ragDocumentId") String ragDocumentId,
    @Param("conversationId") String conversationId,
    @Param("internetMessageId") String internetMessageId,
    @Param("processingStatus") String processingStatus,
    @Param("processingError") String processingError,
    @Param("creationTime") Date creationTime,
    @Param("modifiedTime") Date modifiedTime);

    @Query("SELECT count(f) FROM UserMailAttachment f WHERE " +
    "(:id IS NULL OR f.id = :id) AND " +
    "(:userId IS NULL OR f.userId = :userId) AND " +
    "(:name IS NULL OR f.name = :name) AND " +
    "(:uniqueName IS NULL OR f.uniqueName = :uniqueName) AND " +
    "(:type IS NULL OR f.type = :type) AND " +
    "(:messageId IS NULL OR f.messageId = :messageId) AND " +
    "(:attachmentId IS NULL OR f.attachmentId = :attachmentId) AND " +
    "(:ragDocumentId IS NULL OR f.ragDocumentId = :ragDocumentId) AND " +
    "(:conversationId IS NULL OR f.conversationId = :conversationId) AND " +
    "(:internetMessageId IS NULL OR f.internetMessageId = :internetMessageId) AND " +
    "(:processingStatus IS NULL OR f.processingStatus = :processingStatus) AND " +
    "(:processingError IS NULL OR f.processingError = :processingError) AND " +
    "(:creationTime IS NULL OR f.creationTime = :creationTime) AND " +
    "(:modifiedTime IS NULL OR f.modifiedTime = :modifiedTime)"
)
long count(
    @Param("id") Integer id,
    @Param("userId") String userId,
    @Param("name") String name,
    @Param("uniqueName") String uniqueName,
    @Param("type") String type,
    @Param("messageId") String messageId,
    @Param("attachmentId") String attachmentId,
    @Param("ragDocumentId") String ragDocumentId,
    @Param("conversationId") String conversationId,
    @Param("internetMessageId") String internetMessageId,
    @Param("processingStatus") String processingStatus,
    @Param("processingError") String processingError,
    @Param("creationTime") Date creationTime,
    @Param("modifiedTime") Date modifiedTime);


     List<UserMailAttachment> userMailAttachmentByFilter(Map<String, Object> filterMap, int llimit, int ulimit);


    UserMailAttachment findByAttachmentId(String attachmentId);;

    
} 