package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.dto.UserFolderDto;
import com.enttribe.emailagent.entity.UserFolders;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * The interface User folders repository.
 *  <AUTHOR>
 */
@Repository
public interface UserFoldersRepository extends JpaRepository<UserFolders, Integer> {

    List<UserFolders> findByEmail(String email);

    @Query("SELECT uf FROM UserFolders uf WHERE uf.email = :email AND uf.active = true")
    List<UserFolders> getActiveMailFoldersByEmail(@Param("email") String email);

    @Query("SELECT uf FROM UserFolders uf WHERE uf.email = :email")
    List<UserFolders> getAllMailFoldersByEmail(@Param("email") String email);

    @Query("SELECT uf FROM UserFolders uf WHERE uf.displayName = :displayName AND uf.email = :email")
    UserFolders findByDisplayNameAndEmail(@Param("displayName") String displayName, @Param("email") String email);

    @Query("SELECT displayName FROM UserFolders uf WHERE uf.folderId = :folderId AND uf.email = :email")
    String findDisplayNameByFolderIdAndEmail(@Param("folderId") String folderId, @Param("email") String email);

    @Query("SELECT uf FROM UserFolders uf WHERE uf.id = :id AND uf.email = :email")
    Optional<UserFolders> findByIdAndEmail(@Param("id") Integer id, @Param("email") String email);

//     @Query("SELECT new com.example.dto.UserFoldersDTO(u.email, GROUP_CONCAT(u.displayName ORDER BY u.displayName ASC SEPARATOR ', ')) " +
//     "FROM UserFolders u GROUP BY u.email")
// List<UserFolderDto> getlistOfUserFolders();

    @Query(value = "SELECT u.email, GROUP_CONCAT(u.display_name ORDER BY u.display_name ASC SEPARATOR ', ') AS folderNames " +
            "FROM user_folders u GROUP BY u.email", nativeQuery = true)
    List<Object[]> getlistOfUserFolders();


}
