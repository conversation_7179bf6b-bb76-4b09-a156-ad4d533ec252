package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.entity.EmailUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

import org.springframework.data.repository.query.Param;

/**
 * The interface Email user dao.
 *   <AUTHOR>
 */
@Repository
public interface EmailUserDao extends JpaRepository<EmailUser, Integer> {

    @Query("SELECT CASE WHEN COUNT(e) > 0 THEN true ELSE false END FROM EmailUser e WHERE e.email = :email AND e.deleted = false")
    boolean existsByEmailAndNotDeleted(@Param("email") String email);

    @Query("SELECT e.userId FROM EmailUser e WHERE e.deleted = false")
    List<String> findAllActiveUserIds();

    @Query("SELECT e FROM EmailUser e WHERE e.deleted = false")
    List<EmailUser> findAllActiveUsers();

    @Query("SELECT COUNT(e) FROM EmailUser e WHERE e.deleted = false")
    long getActiveUsersCount();

    @Query("SELECT eu FROM EmailUser eu WHERE eu.email = :email")
    EmailUser findByEmail(String email);

    @Query("SELECT eu FROM EmailUser eu WHERE eu.email = :email or eu.userOID = :email")
    EmailUser findByEmailOrOid(String email);

    @Query("SELECT eu FROM EmailUser eu WHERE eu.userOID = :userOID")
    EmailUser findByOID(String userOID);

    @Query("SELECT eu FROM EmailUser eu WHERE eu.name = :name")
    EmailUser findByName(String name);
    
    @Query("SELECT eu FROM EmailUser eu WHERE eu.batchId = :batchId and eu.deleted=:deleted")
    List<EmailUser> findByBatchId(String batchId,Boolean deleted);

    @Query("SELECT eu FROM EmailUser eu WHERE eu.deleted = false and type= :type")
    List<EmailUser> findAll(String type);

    @Query("SELECT eu FROM EmailUser eu  WHERE eu.name LIKE :name")
    List<EmailUser> getUserList(String name);

    @Query("SELECT e FROM EmailUser e WHERE " +
            "(:id IS NULL OR e.id = :id) AND " +
            "(:userId IS NULL OR e.userId = :userId) AND " +
            "(:email IS NULL OR e.email = :email) AND " +
            "(:type IS NULL OR e.type = :type) AND " +
            "(:deleted IS NULL OR e.deleted = :deleted) AND " +
            "(:name IS NULL OR e.name = :name)")
    List<EmailUser> search(
            @Param("id") Integer id,
            @Param("userId") String userId,
            @Param("email") String email,
            @Param("type") String type,
            @Param("deleted") Boolean deleted,
            @Param("name") String name
    );

    @Query("SELECT count(e) FROM EmailUser e WHERE " +
            "(:id IS NULL OR e.id = :id) AND " +
            "(:userId IS NULL OR e.userId = :userId) AND " +
            "(:email IS NULL OR e.email = :email) AND " +
            "(:type IS NULL OR e.type = :type) AND " +
            "(:deleted IS NULL OR e.deleted = :deleted) AND " +
            "(:name IS NULL OR e.name = :name)")
    Long countLimited(
            @Param("id") Integer id,
            @Param("userId") String userId,
            @Param("email") String email,
            @Param("type") String type,
            @Param("deleted") Boolean deleted,
            @Param("name") String name
    );

     public List<EmailUser> filteredEmailUser(Map<String, Object> filterMap, int llimit, int ulimit);

     @Query("SELECT DISTINCT e.batchId FROM EmailUser e WHERE e.batchId LIKE %:batchId%")
     public List<String> getDistinctBatchId(@Param("batchId") String batchId);


     @Query("SELECT DISTINCT e.batchId FROM EmailUser e WHERE e.batchId IS NOT NULL")
     public List<String> getBatchIds();

    // Search for contacts by a single part of the name using SOUNDEX
    @Query(value = "SELECT USER_ID FROM EMAIL_USER " +
            "WHERE SOUNDEX(SUBSTRING_INDEX(name, ' ', 1)) = SOUNDEX(:name) " +
            "AND LOWER(SUBSTRING_INDEX(name, ' ', 1)) LIKE CONCAT(LEFT(LOWER(:name), 3), '%') " +
            "OR SOUNDEX(SUBSTRING_INDEX(name, ' ', -1)) = SOUNDEX(:name) " +
            "AND LOWER(SUBSTRING_INDEX(name, ' ', -1)) LIKE CONCAT(LEFT(LOWER(:name), 3), '%')",
            nativeQuery = true)
    List<String> searchBySingleName(@Param("name") String name);

    @Query(value = "SELECT USER_ID FROM EMAIL_USER " +
            "WHERE (" +
            "(SOUNDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(EMAIL, '@', 1), '.', 1)) = SOUNDEX(:firstName) " +
            "  AND LOWER(SUBSTRING_INDEX(SUBSTRING_INDEX(EMAIL, '@', 1), '.', 1)) LIKE CONCAT(LEFT(LOWER(:firstName), 3), '%') " +
            "  AND SOUNDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(EMAIL, '@', 1), '.', -1)) = SOUNDEX(:lastName) " +
            "  AND LOWER(SUBSTRING_INDEX(SUBSTRING_INDEX(EMAIL, '@', 1), '.', -1)) LIKE CONCAT(LEFT(LOWER(:lastName), 3), '%')) " +
            "OR " +
            "(EMAIL NOT LIKE '%.%@%' " +
            "  AND SOUNDEX(SUBSTRING_INDEX(EMAIL, '@', 1)) = SOUNDEX(:firstName) " +
            "  AND LOWER(SUBSTRING_INDEX(EMAIL, '@', 1)) LIKE CONCAT(LEFT(LOWER(:firstName), 3), '%'))" +
            ") " +
            "AND SOUNDEX(SUBSTRING_INDEX(EMAIL, '@', -1)) = SOUNDEX(:domain) " +
            "AND LOWER(SUBSTRING_INDEX(EMAIL, '@', -1)) LIKE CONCAT(LEFT(LOWER(:domain), 3), '%')",
            nativeQuery = true)
    List<String> searchByEmailParts(
            @Param("firstName") String firstName,
            @Param("lastName") String lastName,
            @Param("domain") String domain);


    // Search for contacts by both first name and last name using SOUNDEX
    @Query(value = "SELECT USER_ID FROM EMAIL_USER WHERE (SOUNDEX(SUBSTRING_INDEX(name, ' ', 1)) = SOUNDEX(:firstName) " +
            "AND SOUNDEX(SUBSTRING_INDEX(name, ' ', -1)) = SOUNDEX(:lastName))", nativeQuery = true)
    List<String> searchByFirstAndLastName(@Param("firstName") String firstName, @Param("lastName") String lastName);
     

}
