package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.entity.Organisation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * The interface Organisation repository.
 *  <AUTHOR>
 */
@Repository
public interface IOrganisationRepository extends JpaRepository<Organisation, Integer> {

    @Query("select org from Organisation org where org.organisationName=:orgName")
    public Organisation findByOrgName(String orgName);



}
