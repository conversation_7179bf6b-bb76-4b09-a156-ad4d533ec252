package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.entity.UserTemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * The interface User template repository.
 *  <AUTHOR> Sonsale
 */
@Repository
public interface UserTemplateRepository extends JpaRepository<UserTemplate, Integer> {

    UserTemplate findByEmail(String email);

}
