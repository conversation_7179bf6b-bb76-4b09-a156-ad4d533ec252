package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.dto.ActionItemDto;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.UserActions;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * The interface User actions dao.
 *  <AUTHOR> Sonsale
 */
@Repository
public interface UserActionsDao extends JpaRepository<UserActions, Integer> {

    @Query("SELECT new com.enttribe.emailagent.dto.ActionItemDto(ua.id, ua.actionOwnerReason) FROM UserActions ua WHERE ua.actionOwner = :actionOwner AND ua.conversationId = :conversationId AND ua.actionTaken = false")
    List<ActionItemDto> findByActionOwnerAndConversationId(@Param("actionOwner") String actionOwner, @Param("conversationId") String conversationId);

    @Query("SELECT COUNT(ua) FROM UserActions ua WHERE ua.mailReceivedTime <= :startDate AND ua.actionOwner = :userId AND ua.actionTaken = false AND ua.deleted = false")
    long countActionableMailByUserId(@Param("startDate") LocalDateTime startDate, @Param("userId") String userId);

    @Query("SELECT COUNT(ua) FROM UserActions ua WHERE ua.actionOwner = :userId AND ua.internetMessageId = :internetMessageId")
    long getUserActionByInternetMessageId(@Param("userId") String userId, @Param("internetMessageId") String internetMessageId);

    @Query("SELECT ua.mailSummary FROM UserActions ua WHERE ua.mailReceivedTime <= :startDate AND ua.actionOwner = :userId AND ua.actionTaken = false AND ua.deleted = false ORDER BY ua.mailReceivedTime DESC")
    List<MailSummary> actionableMailByUserId(@Param("startDate") LocalDateTime startDate, @Param("userId") String userId);

    @Modifying
    @Transactional
    @Query("UPDATE UserActions ua SET ua.actionTaken = :action, ua.actionTakenReason = 'Action marked manually.' WHERE ua.internetMessageId = :internetMessageId AND ua.actionOwner = :userId")
    int markActionTaken(@Param("userId") String userId, @Param("internetMessageId") String internetMessageId, @Param("action") Boolean action);

    @Modifying
    @Transactional
    @Query("UPDATE UserActions ua SET ua.actionTaken = true, ua.actionTakenReason = 'Action is taken on the email manually.' WHERE ua.actionOwner = :userId AND ua.actionTaken = false")
    int markAllActionTaken(@Param("userId") String userId);

    @Modifying
    @Transactional
    @Query("UPDATE UserActions ua SET ua.deleted = true WHERE ua.actionOwner = :userId AND ua.internetMessageId IN :internetMessageIds")
    int markDeleted(@Param("userId") String userId, @Param("internetMessageIds") List<String> internetMessageIds);

}
