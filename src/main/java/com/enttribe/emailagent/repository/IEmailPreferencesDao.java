package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.FailureLogs;

import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * The interface Email preferences dao.
 *  <AUTHOR> Dangi
 */
public interface IEmailPreferencesDao extends JpaRepository<EmailPreferences, Integer> {

    @Query("select u from EmailPreferences u where u.userId = ?1 ")
    EmailPreferences getEmailPreferencesByUserId(String userId);


    @Query("select u.displayName from EmailPreferences u where u.userId = ?1 ")
    String getDisplayNameByUserId(String userId);

    @Query("SELECT u.displayName FROM EmailPreferences u WHERE u.userId = ?1")
    String findDisplayNameByEmail(String emailSender);



    @Query("SELECT e FROM EmailPreferences e WHERE " +
        "(:id IS NULL OR e.id = :id) AND " +
        "(:userId IS NULL OR e.userId = :userId) AND " +
        "(:emailSubject IS NULL OR e.emailSubject = :emailSubject) AND " +
        "(:senderCompany IS NULL OR e.senderCompany = :senderCompany) AND " +
        "(:importantTags IS NULL OR e.importantTags = :importantTags) AND " +
        "(:fontFamily IS NULL OR e.fontFamily = :fontFamily) AND " +
        "(:fontSize IS NULL OR e.fontSize = :fontSize) AND " +
        "(:emailSender IS NULL OR e.emailSender = :emailSender) AND " +
        "(:blackListedDomain IS NULL OR e.blackListedDomain = :blackListedDomain) AND " +
        "(:blackListedSubject IS NULL OR e.blackListedSubject = :blackListedSubject) AND " +
        "(:timeZone IS NULL OR e.timeZone = :timeZone) AND " +
        "(:createdTime IS NULL OR e.createdTime = :createdTime) AND " +
        "(:modifiedTime IS NULL OR e.modifiedTime = :modifiedTime) AND " +
        "(:conversationId IS NULL OR e.conversationId = :conversationId) AND " +
        "(:checkin IS NULL OR e.checkin = :checkin) AND " +
        "(:checkout IS NULL OR e.checkout = :checkout) AND " +
        "(:fontColor IS NULL OR e.fontColor = :fontColor) AND " +
        "(:displayName IS NULL OR e.displayName = :displayName)")
List<EmailPreferences> search(
        @Param("id") Integer id,
        @Param("userId") String userId,
        @Param("emailSubject") String emailSubject,
        @Param("senderCompany") String senderCompany,
        @Param("importantTags") String importantTags,
        @Param("fontFamily") String fontFamily,
        @Param("fontSize") String fontSize,
        @Param("emailSender") String emailSender,
        @Param("blackListedDomain") String blackListedDomain,
        @Param("blackListedSubject") String blackListedSubject,
        @Param("timeZone") String timeZone,
        @Param("createdTime") Date createdTime,
        @Param("modifiedTime") Date modifiedTime,
        @Param("conversationId") String conversationId,
        @Param("checkin") LocalTime checkin,
        @Param("checkout") LocalTime checkout,
        @Param("fontColor") String fontColor,
        @Param("displayName") String displayName
);


@Query("SELECT count(e) FROM EmailPreferences e WHERE " +
        "(:id IS NULL OR e.id = :id) AND " +
        "(:userId IS NULL OR e.userId = :userId) AND " +
        "(:emailSubject IS NULL OR e.emailSubject = :emailSubject) AND " +
        "(:senderCompany IS NULL OR e.senderCompany = :senderCompany) AND " +
        "(:importantTags IS NULL OR e.importantTags = :importantTags) AND " +
        "(:fontFamily IS NULL OR e.fontFamily = :fontFamily) AND " +
        "(:fontSize IS NULL OR e.fontSize = :fontSize) AND " +
        "(:emailSender IS NULL OR e.emailSender = :emailSender) AND " +
        "(:blackListedDomain IS NULL OR e.blackListedDomain = :blackListedDomain) AND " +
        "(:blackListedSubject IS NULL OR e.blackListedSubject = :blackListedSubject) AND " +
        "(:timeZone IS NULL OR e.timeZone = :timeZone) AND " +
        "(:createdTime IS NULL OR e.createdTime = :createdTime) AND " +
        "(:modifiedTime IS NULL OR e.modifiedTime = :modifiedTime) AND " +
        "(:conversationId IS NULL OR e.conversationId = :conversationId) AND " +
        "(:checkin IS NULL OR e.checkin = :checkin) AND " +
        "(:checkout IS NULL OR e.checkout = :checkout) AND " +
        "(:fontColor IS NULL OR e.fontColor = :fontColor) AND " +
        "(:displayName IS NULL OR e.displayName = :displayName)")
long count(
        @Param("id") Integer id,
        @Param("userId") String userId,
        @Param("emailSubject") String emailSubject,
        @Param("senderCompany") String senderCompany,
        @Param("importantTags") String importantTags,
        @Param("fontFamily") String fontFamily,
        @Param("fontSize") String fontSize,
        @Param("emailSender") String emailSender,
        @Param("blackListedDomain") String blackListedDomain,
        @Param("blackListedSubject") String blackListedSubject,
        @Param("timeZone") String timeZone,
        @Param("createdTime") Date createdTime,
        @Param("modifiedTime") Date modifiedTime,
        @Param("conversationId") String conversationId,
        @Param("checkin") LocalTime checkin,
        @Param("checkout") LocalTime checkout,
        @Param("fontColor") String fontColor,
        @Param("displayName") String displayName
);

@Query("SELECT e FROM EmailPreferences e WHERE 1 = 1")
 public List<EmailPreferences> Filteredpreference(Map<String, Object> filterMap, int llimit, int ulimit);
    


}
