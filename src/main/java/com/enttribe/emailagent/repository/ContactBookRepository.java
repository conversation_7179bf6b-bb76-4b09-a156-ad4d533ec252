package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.entity.ContactBook;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * The interface Contact book repository.
 *  <AUTHOR> <PERSON>
 */
@Repository
public interface ContactBookRepository extends JpaRepository<ContactBook, Integer> {


    @Query("SELECT c FROM ContactBook c WHERE LOWER(c.contactName) LIKE LOWER(CONCAT('', :emailQuery, '%')) and c.userId=:userId")
    List<ContactBook> searchByContacts(@Param("emailQuery") String emailQuery,@Param("userId") String userId);

    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN TRUE ELSE FALSE END FROM ContactBook c WHERE c.userId = :userId AND c.contacts = :contact")
    boolean existsByUserIdAndContacts(@Param("userId") String userId, @Param("contact") String contact);



}

