package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.entity.BuildApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BuildApplicationDao extends JpaRepository<BuildApplication, Integer> {

    @Query("SELECT ba FROM BuildApplication ba WHERE ba.applicationName = :applicationName AND ba.environment = :environment")
    BuildApplication findByApplicationName(String applicationName, String environment);

}
