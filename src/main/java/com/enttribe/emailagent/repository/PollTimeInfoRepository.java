package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.entity.PollTimeInfo;

import jakarta.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * The interface Poll time info repository.
 *  <AUTHOR>
 */
@Repository
public interface PollTimeInfoRepository extends JpaRepository<PollTimeInfo, Integer> {

    @Query("SELECT p FROM PollTimeInfo p WHERE p.email = :email ORDER BY p.startTime")
    Optional<PollTimeInfo> findLastPollStartTimeByEmail(@Param("email") String email);

    Optional<PollTimeInfo> findFirstByEmailOrderByStartTimeDesc(String email);

    Optional<PollTimeInfo> findFirstByEmailAndStatusOrderByEndTimeDesc(String email,PollTimeInfo.Status status);
    

    @Modifying
    @Transactional
    @Query("UPDATE PollTimeInfo SET startTime=:startTime WHERE email=:email and status = 'PROCESSING'")
    int updatePollTimeInfo(@Param("email") String email, @Param("startTime")  LocalDateTime startTime);
    



}
