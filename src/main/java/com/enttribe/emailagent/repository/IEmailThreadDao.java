package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.entity.EmailThread;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * The interface Email thread dao.
 * <AUTHOR> Sonsale
 */
@Repository
public interface IEmailThreadDao extends JpaRepository<EmailThread, Integer> {

    @Query("SELECT eu FROM EmailThread eu WHERE eu.conversationId = :conversationId and eu.userId= :userId")
    EmailThread findByConversationId(String conversationId, String userId);

    @Query("SELECT m FROM EmailThread m WHERE m.starMarked = true AND m.userId = :userId")
    List<EmailThread> getStarMarkedEmailThread(@Param("userId") String userId);

    @Query("SELECT m FROM EmailThread m WHERE m.conversationId in :conversationId")
    List<EmailThread> getConversationSubject(@Param("conversationId") List<String> conversationId);

    @Query("SELECT eu FROM EmailThread eu WHERE eu.id >= :startId AND eu.id <= :endId")
    List<EmailThread> findByIdBetween(Integer startId, Integer endId);
}
