package com.enttribe.emailagent.repository;

import com.enttribe.emailagent.entity.FailureLogs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * The interface Failure logs service dao.
 *  <AUTHOR>
 */
public interface FailureLogsServiceDao extends JpaRepository<FailureLogs, Integer> {

    List<FailureLogs> findByEmail(String email);

    @Query("SELECT f FROM FailureLogs f WHERE " +
            "(:id IS NULL OR f.id = :id) AND " +
            "(:internetMessageId IS NULL OR f.internetMessageId = :internetMessageId) AND " +
            "(:email IS NULL OR f.email = :email) AND " +
            "(:emailSubject IS NULL OR f.emailSubject = :emailSubject) AND " +
            "(:messageId IS NULL OR f.messageId = :messageId) AND " +
            "(:conversationId IS NULL OR f.conversationId = :conversationId) AND " +
            "(:type IS NULL OR f.type = :type) AND " +
            "(:exceptionMessage IS NULL OR f.exceptionMessage = :exceptionMessage) AND " +
            "(:customExceptionMessage IS NULL OR f.customExceptionMessage = :customExceptionMessage) AND " +
            "(:exceptionTrace IS NULL OR f.exceptionTrace = :exceptionTrace) AND " +
            "(:errorDate IS NULL OR f.errorDate = :errorDate)")
    List<FailureLogs> search(
            @Param("id") Integer id,
            @Param("internetMessageId") String internetMessageId,
            @Param("email") String email,
            @Param("emailSubject") String emailSubject,
            @Param("messageId") String messageId,
            @Param("conversationId") String conversationId,
            @Param("type") String type,
            @Param("exceptionMessage") String exceptionMessage,
            @Param("customExceptionMessage") String customExceptionMessage,
            @Param("exceptionTrace") String exceptionTrace,
            @Param("errorDate") String errorDate
    );

     
    @Query("SELECT count(f) FROM FailureLogs f WHERE " +
    "(:id IS NULL OR f.id = :id) AND " +
    "(:internetMessageId IS NULL OR f.internetMessageId = :internetMessageId) AND " +
    "(:email IS NULL OR f.email = :email) AND " +
    "(:emailSubject IS NULL OR f.emailSubject = :emailSubject) AND " +
    "(:messageId IS NULL OR f.messageId = :messageId) AND " +
    "(:conversationId IS NULL OR f.conversationId = :conversationId) AND " +
    "(:type IS NULL OR f.type = :type) AND " +
    "(:exceptionMessage IS NULL OR f.exceptionMessage = :exceptionMessage) AND " +
    "(:customExceptionMessage IS NULL OR f.customExceptionMessage = :customExceptionMessage) AND " +
    "(:exceptionTrace IS NULL OR f.exceptionTrace = :exceptionTrace) AND " +
    "(:errorDate IS NULL OR f.errorDate = :errorDate)")
    Long countLimited(
    @Param("id") Integer id,
    @Param("internetMessageId") String internetMessageId,
    @Param("email") String email,
    @Param("emailSubject") String emailSubject,
    @Param("messageId") String messageId,
    @Param("conversationId") String conversationId,
    @Param("type") String type,
    @Param("exceptionMessage") String exceptionMessage,
    @Param("customExceptionMessage") String customExceptionMessage,
    @Param("exceptionTrace") String exceptionTrace,
    @Param("errorDate") String errorDate
    );


    List<FailureLogs> getFilteredFailureLogs(Map<String, Object> filterMap, int llimit, int ulimit);


        @Query("SELECT f.type FROM FailureLogs f group by f.type")
    List<String> typesOfFailureLogs();
    
  
}
