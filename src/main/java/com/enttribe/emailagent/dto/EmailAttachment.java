package com.enttribe.emailagent.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Email attachment.
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmailAttachment {

    private String name;
    private String attachmentId;
    private String ragDocumentId;
    private String contentType;
    private byte[] content;

}
