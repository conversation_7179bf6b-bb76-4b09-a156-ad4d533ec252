package com.enttribe.emailagent.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * The type Email profile response.
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class EmailProfileResponse {

    private Tone tone;
    private Style style;
    private Personalization personalization;
    private ContentPreferences contentPreferences;
    private OftenUsedWords oftenUsedWords;
    private List<RecipientBehavior> replyBehavior;

    @Getter
    @Setter
    @ToString
    public static class Tone {

        private String formality; // "formal", "semi-formal", "informal"
        private String politenessLevel; // "very polite", "polite", "neutral", "direct"
        private String useOfHumor; // "frequent", "occasional", "rare", "never"
        private String emotionalTone; // "positive", "neutral", "negative", "empathetic", "enthusiastic"

    }

    @Getter
    @Setter
    @ToString
    public static class Style {
        private String sentenceStructure; // "simple", "compound", "complex"
        private String vocabulary; // "professional", "casual", "technical", "creative"
        private String punctuation; // "minimal", "moderate", "extensive"
        private String abbreviationsUsage; // "frequent", "occasional", "rare", "never"
    }

    @Getter
    @Setter
    @ToString
    public static class Personalization {
        private List<String> salutations; // e.g., "Hi", "Hello", "Dear"
        private List<String> greetingContext; // e.g., "formal greetings", "casual greetings"
        private List<String> signOffs; // e.g., "Best regards", "Thanks"
        private List<String> signatureComponents; // e.g., "name", "position"
        private String signatureFormat; // "full signature", "minimal signature"
        private String personalReferences; // e.g., "I", "We", "You"
        private String frequencyOfPersonalTouches; // "high", "moderate", "low", "never"
    }

    @Getter
    @Setter
    @ToString
    public static class ContentPreferences {
        private String detailLevel; // "highly detailed", "moderately detailed", "concise", "very concise"
        private List<String> emphasisAreas; // e.g., "facts", "emotions", "actions"
        private String averageEmailLength; // "short", "medium", "long"
    }

    @Getter
    @Setter
    @ToString
    public static class OftenUsedWords {
        private List<String> commonPhrases; // List of frequently used words or phrases
    }

    @Getter
    @Setter
    @ToString
    public static class RecipientBehavior {
        private List<String> emailIds; // List of email addresses
        private String relationship; // "boss", "customer", "colleague", "other"
        private String formality; // e.g., "formal", "semi-formal"
        private String politenessLevel; // e.g., "very polite", "neutral"
        private String useOfHumor; // e.g., "occasional", "never"
        private String emotionalTone; // e.g., "positive", "neutral"
        private Style style; // Nested Style object
        private ContentPreferences contentPreferences; // Nested ContentPreferences object
        private OftenUsedWords oftenUsedWords; // Nested OftenUsedWords object
    }
}
