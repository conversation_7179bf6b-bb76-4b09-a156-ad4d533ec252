package com.enttribe.emailagent.dto;

import com.enttribe.emailagent.utils.Description;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * The type Action owner response.
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@XmlRootElement(name = "ActionOwnerResponse")
@Description("Action owner response object")
public class ActionOwnerResponse {
    @Description("action owner email for given unique action")
    private String actionOwnerEmail;
    @Description("reason for assigning action to email id")
    private String actionOwnerReason;
}
