package com.enttribe.emailagent.dto;

import com.enttribe.emailagent.entity.UserActions;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@Builder
public class MailSummaryDto {

    private Integer id;
    private String messageId;
    private List<Attachments> attachmentsList;
    private String priority;
    private String category;
    private String subject;
    private String fromUser;
    private String toUser;
    private String ccUser;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime mailReceivedTime;
    private String messageSummary;
    private Set<UserActions> userActions;
    private String userId;
    private String actionOwner;
    private String type;
    private Boolean starMarked;

}
