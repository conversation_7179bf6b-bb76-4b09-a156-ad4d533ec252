package com.enttribe.emailagent.dto;

import java.time.LocalTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * The type Email user dto.
 * <AUTHOR>
 */
@Getter
@Setter
public class EmailUserDto {

    private Integer id;
    private String userId;
    private String batchId;
    private String email;
    private String phoneNumber;
    private String type = "Office365";
    private Boolean deleted = false;
    private String name;
    private Long lastPollTime;
    private Long nextPollTime;
    private Date createdTime;
    private Date modifiedTime;
    private String timeZone;
}
