package com.enttribe.emailagent.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Setter
@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public class WorkingHoursSummary {
    private Set<String> commonWorkDays;
    private Map<String, List<TimeRange>> overlappingTimeRangesUTC;

    @AllArgsConstructor
    @Getter
    public static class TimeRange {
        private final int startMinutesUTC; // e.g., 1380 = 23:00
        private final int endMinutesUTC;   // e.g., 60 = 01:00 (next day)
    }
}
