package com.enttribe.emailagent.dto;

import com.enttribe.emailagent.utils.Description;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * The type User email dto.
 * <AUTHOR>
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@XmlRootElement(name = "UserEmailDto")
public class UserEmailDto {

    private String from;
    private String type;
    private List<String> toRecipients;
    private List<String> ccRecipients;
    private List<String> bccRecipients;
    private String subject;
    private String body;
    @JsonIgnore
    private String bodyPreview;
    private String conversationId;
    private Date createdTime;
    private Date meetingEndTime;
    private String id;
    private Boolean hasAttachments;
    private String internetMessageId;
    private String importance;
    private String folderName;

//    @Override
//    public String toString() {
//        return "{" +
//                "from='" + from + '\'' +
//                ", toRecipients=" + toRecipients +
//                ", ccRecipients=" + ccRecipients +
//                ", bccRecipients=" + bccRecipients +
//                ", subject='" + subject + '\'' +
//                ", body='" + body + '\'' +
//                ", conversationId='" + conversationId + '\'' +
//                ", createdTime=" + createdTime +
//                ", id='" + id + '\'' +
//                '}';
//    }

    @Override
    public String toString() {
        return "{" +
                "from='" + from + '\'' +
                ", toRecipients=" + toRecipients +
                ", ccRecipients=" + ccRecipients +
                ", subject='" + subject + '\'' +
                ", body='" + body + '\'' +
                '}';
    }
}
