package com.enttribe.emailagent.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * The type Notification dto.
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class NotificationDto {

    private String conversationId;
    private String messageId;
    private String category;
    private String priority;
    private String subject;
    private String type;
    private String graphDocIds;
    @JsonIgnore
    private String content;
    private String fromUser;
    private String internetMessageId;
    private String folderName;

}
