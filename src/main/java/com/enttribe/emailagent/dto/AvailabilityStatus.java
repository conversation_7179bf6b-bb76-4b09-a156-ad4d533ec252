package com.enttribe.emailagent.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AvailabilityStatus {
    private String email;
    private String status; // "free", "tentative", "busy", "outOfOffice", "workingElsewhere"
    private Object organizerMeeting; // "free", "tentative", "busy", "outOfOffice", "workingElsewhere"
}