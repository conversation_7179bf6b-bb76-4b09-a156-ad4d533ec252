package com.enttribe.emailagent.dto;

import com.enttribe.emailagent.entity.MailSummary;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * The type Day summary.
 * <AUTHOR>
 */
@Getter
@Setter
public class DaySummary {

    private String content;
    private String contentKey;
    private List<MailSummary> mailObject;
    private List<EventDto> meetings;
    private String mobileContent;
    private Integer value;
    private String action;
}
