package com.enttribe.emailagent.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * The type Event dto.
 * <AUTHOR>
 */
@Getter
@Setter
public class EventDto {

    private String organizer;
    private List<String> attendees;
    private List<AttendeeAndStatus> requiredAttendees;
    private List<AttendeeAndStatus> optionalAttendees;
    private String subject;
    private String accepted;
    private String bodyPreview;
    private String joinUrl; //onlineMeeting
    private String location;
    private Boolean hasAttachments;
    private Date meetingStartTime;
    private Date meetingEndTime;
    private Date createdDateTime;
    private Date lastModifiedDateTime;
    private String id;

}
