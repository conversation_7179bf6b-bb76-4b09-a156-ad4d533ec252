package com.enttribe.emailagent;

import com.enttribe.emailagent.ai.dto.draft.DraftResponse;
import com.enttribe.emailagent.ai.dto.draft.FreshDraftAIResponse;
import com.enttribe.emailagent.ai.dto.mail_summary.MailSummaryAIResponse;
import com.enttribe.emailagent.ai.polling.OutlookPollingAI;
import com.enttribe.emailagent.ai.service.AIService;
import com.enttribe.emailagent.ai.service.MeetingService;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.dto.ActionOwnerResponse;
import com.enttribe.emailagent.dto.ConversationEmailsDTO;
import com.enttribe.emailagent.dto.IntentWrapper;
import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.entity.*;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.masking.DataMasking;
import com.enttribe.emailagent.repository.ContactBookRepository;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IEmailThreadDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.service.*;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.enttribe.emailagent.utils.APIConstants;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.EmailConstants;
import com.enttribe.emailagent.utils.FreshDraftRequest;
import com.enttribe.emailagent.wrapper.MessageSummary;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.*;
import static com.google.common.base.Throwables.getStackTraceAsString;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * The type Email rest.
 *
 *  <AUTHOR> Sonsale
 */
@RestController("emailRest")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/emailservice")
@Slf4j
@Component
public class EmailRestImpl {

//    @Autowired
//    private OutlookPolling polling;

    @Autowired
    private AIService aiService;

    @Autowired
    private ChatService chatService;

    @Autowired
    private MeetingService meetingService;

    @Autowired
    private com.enttribe.emailagent.ai.service.DraftService draftServiceAI;

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailUserDao emailUser;
    
    @Autowired
    private ContactBookRepository contactRepo;

    @Autowired
    private OutlookPollingAI outlookPollingAI;

    @Autowired
    private UserContextHolder userContextHolder;

    @Autowired
    private IMailSummaryDao mailSummaryDao;

    @Autowired
    private GmailIntegration gmailIntegration;

    @Autowired
    private IEmailThreadDao threadDao;

    @Autowired
    private ImailSummaryService imailSummaryService;

    @Autowired
    private IEmailPreferencesDao preferencesDao;

    @Autowired
    private EwsService ewsService;

    @Autowired
    private PollTimeInfoService pollTimeInfoService;

    @Value("${pollingMode}")
    private String pollingMode;

    @Value("${threadConcurency}")
    private Integer threadConcurency;


    @GetMapping("/ping")
    public String sayHello() {
        return "I am alive";
    }

    private static final Logger auditLog = EmailAgentLogger.getLogger(EmailRestImpl.class);

    @GetMapping("/pollEmailByUser")
    @Operation(summary = "Poll emails by user", tags = "Email", description = "Poll emails for a specific user by their email ID. Optionally, apply a filter.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Emails retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public String pollEmailByUser(@RequestParam String emailId, @RequestParam(required = false) String filter) {
        log.error("Inside @method pollEmailByUser. @param : emailId -> {}", emailId);
        try {
            EmailUser emailUsers;
            emailUsers = emailUser.findByEmail(emailId);
            log.error("User for polling : {}", emailUsers);
            String timeFilter = emailService.getReceivedDateTimeFilter(emailUsers.getEmail());
            pollTimeInfoService.setStartTime(emailUsers.getEmail(), LocalDateTime.now(), PollTimeInfo.Status.PROCESSING);
            if (filter != null) {
                timeFilter = filter;
            }
            outlookPollingAI.pollOutlookInbox(emailUsers.getEmail(), emailUsers.getUserId(), timeFilter, pollingMode);
            pollTimeInfoService.setEndTime(emailId, LocalDateTime.now(), PollTimeInfo.Status.SUCCESS);
        } catch (Exception e) {
            pollTimeInfoService.setEndTime(emailId, LocalDateTime.now(), PollTimeInfo.Status.FAILED);
            log.error("Error while polling", e);
            return "failed";
        }
        return "success";
    }

    @GetMapping("/mail-count")
    @Operation(summary = "Get today's mail count by user ID", tags = "Email", description = "Retrieve the count of emails received today by the current user.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Mail count retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Long> getTodayMailCountByUserId() {
        String userId = userContextHolder.getCurrentUser().getId();
        log.debug("Inside @method getTodayMailCountByUserId. @param : userId -> {}", userId);
        Long count = mailSummaryDao.countTodayMailsByUserId(userId);
        return Map.of("result", count);
    }

    @GetMapping("/getThreadSummary")
    @Operation(summary = "Get conversation thread summary", tags = "Email", description = "Retrieve the summary of a conversation thread based on the provided conversation ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> getSummary(@RequestParam(name = APIConstants.CONVERSATION_ID, required = false) String conversationId, @RequestParam(name = APIConstants.INTERNET_MESSAGE_ID, required = false) String internetMessageId) {
        UserInfo userInfo = userContextHolder.getCurrentUser();
        String userId = userInfo.getId();
        if (pollingMode.equalsIgnoreCase("EWS")) {
            conversationId = conversationId.replace(" ", "+");
            conversationId = conversationId.replace("_", "+");
            conversationId = conversationId.replace("-", "/");
        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
            internetMessageId = internetMessageId.replace(" ", "+");
            conversationId = Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, userId)).map(MailSummary::getConversationId).orElse(conversationId);
            log.info("conversationId is {}",conversationId);
            log.info("internetMessageId is {}",internetMessageId);
        } else {
            conversationId = conversationId.replace("/", "-");
        }
        EmailThread message = threadDao.findByConversationId(conversationId, userId);
        if (message == null) {
            Map<String, Object> obj = new HashMap<>();
            obj.put(EmailConstants.RESULT, null);
            return obj;
        }
        if (message.getShortDecryptObject() != null) {
            JSONObject shortSummary = new JSONObject(message.getShortSummary());
            String decryptedString = DataMasking.decryptSensitiveData(shortSummary.getString("summary"), message.getShortDecryptObject());
            message.setShortSummary(shortSummary.put("summary", decryptedString).toString());
        }
        JSONObject longSummary = new JSONObject(message.getThreadSummary());
        if (longSummary.getJSONArray("actionItems").length() > 0) {
            JSONArray actionItems = longSummary.getJSONArray("actionItems");
            JSONArray tempActionItems = new JSONArray();
            String actionItemString = "";
            for (int i = 0; i < actionItems.length(); i++) {
                actionItemString = actionItems.get(i) + " @# ";
            }
            if ((!actionItemString.isBlank() || !actionItemString.isEmpty()) && message.getActionDecryptObject() != null) {
                String decryptObject = DataMasking.decryptSensitiveData(actionItemString, message.getActionDecryptObject());
                Arrays.stream(decryptObject.split(" @# ")).forEach(s -> tempActionItems.put(s));
                longSummary.put("actionItems", tempActionItems);
            }
        }

        if (longSummary.getJSONArray("summaryPoints").length() > 0) {
            JSONArray summaryPoints = longSummary.getJSONArray("summaryPoints");
            JSONArray tempSummaryItems = new JSONArray();
            String summaryItemString = "";
            for (int i = 0; i < summaryPoints.length(); i++) {
                summaryItemString = summaryPoints.get(i) + " @# ";
            }
            if ((!summaryItemString.isBlank() || !summaryItemString.isEmpty()) && message.getLongDecryptObject() != null) {
                String decryptObject = DataMasking.decryptSensitiveData(summaryItemString, message.getLongDecryptObject());
                Arrays.stream(decryptObject.split(" @# ")).forEach(s -> tempSummaryItems.put(s));
                longSummary.put("summaryPoints", tempSummaryItems);
            }
        }
        message.setThreadSummary(longSummary.toString());
        Long messageCount = mailSummaryDao.countByConversationId(conversationId, userId);
        Map<String, Object> obj = new HashMap<>();
        obj.put(EmailConstants.RESULT, message);
        obj.put("messageCount", messageCount);
        return obj;
    }

    @GetMapping("/getMailSummary")
    @Operation(summary = "Get email summary", tags = "Email", description = "Retrieve the summary of an email based on the provided message ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, MailSummary> getMailSummary(@RequestParam String messageId) throws JsonProcessingException {
        if (pollingMode.equalsIgnoreCase("EWS")) {
            messageId = messageId.replace(" ", "+");
            messageId = messageId.replace("_", "+");
            messageId = messageId.replace("-", "/");
        } else {
            messageId = messageId.replace("/", "-");
        }
        Map<String, MailSummary> obj = new HashMap<>();
        try {
            UserInfo userInfo = userContextHolder.getCurrentUser();
            log.debug("@param to get mail summary : messageId -> {} userId -> {}", messageId, userInfo.getId());
            MailSummary message = mailSummaryDao.findByMessageId(messageId, userInfo.getId());
            imailSummaryService.addAttachmentDetails(List.of(message));
            log.debug("Mail summary subject : {}", message.getSubject());
            modifyActionOwner(message);
            obj.put(EmailConstants.RESULT, message);
        } catch (Exception e) {
            log.error("Error inside @method getMailSummary", e);
            obj.put(EmailConstants.RESULT, null);
        }
        return obj;
    }

    public String getUserId() {
        return userContextHolder.getCurrentUser().getId();
    }

    @GetMapping("/getConversations")
    @Operation(summary = "Get list of mails grouped by conversationId", tags = "Email", description = "Retrieves paginated mail summary of a user grouped by conversation ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public List<ConversationEmailsDTO> getConversations(@RequestParam Integer lowerLimit,
                                                  @RequestParam Integer upperLimit) {

        String email = userContextHolder.getCurrentUser().getEmail();
        return imailSummaryService.getConversations(email, lowerLimit, upperLimit);
    }

    @PostMapping("/getMailsInConversation")
    @Operation(summary = "Get mails of a conversation", tags = "Email", description = "Retrieves paginated mail summary of a conversation")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public List<MailSummary> getMailsInConversation(@RequestParam Integer lowerLimit,
                                                              @RequestParam Integer upperLimit,
                                                              @RequestBody Map<String, String> request) {

        String email = userContextHolder.getCurrentUser().getEmail();
        String conversationId = request.get("conversationId");
        return imailSummaryService.getMailsInConversation(email, conversationId, lowerLimit, upperLimit);
    }

    @PostMapping("/v1/getMailSummary")
    @Operation(summary = "Get email summary (v1)", tags = "Email", description = "Retrieve the summary of an email based on the provided request data.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
   // @Cacheable(value = "mailSummaryCache", key = "#request['internetMessageId'] + '_' + #root.target.getUserId()")
    public Map<String, MailSummary> getMailSummaryV1(@RequestBody Map<String, String> request) {
        log.info("inside getMailSummaryV1");
        String messageId = request.get("messageId");
        String internetMessageId = request.get("internetMessageId");
        log.debug("Inside @method getMailSummaryV1. @param : internetMessageId -> {} messageId -> {}", internetMessageId,messageId);

        Map<String, MailSummary> obj = new HashMap<>();
        try {
            UserInfo userInfo = userContextHolder.getCurrentUser();
            log.debug("@param to get mail summary : internetMessageId -> {} userId -> {}", internetMessageId, userInfo.getId());
            MailSummary message = mailSummaryDao.findByInternetMessageId(internetMessageId, userInfo.getId());
            if(pollingMode.equalsIgnoreCase("GMAIL") && message == null){
                log.info("going for findByMessageId -----> {}",messageId);

                message = mailSummaryDao.findByMessageId(messageId, userInfo.getId());
            }
            if (message == null) {

                obj.put(EmailConstants.RESULT, null);
                return obj;
            }
            if (message.getDecryptSummary() != null) {
                JSONObject object = new JSONObject(message.getMessageSummary());
                String decryptedSummString = DataMasking.decryptSensitiveData(object.getJSONObject("summaryObject").getString("content"), message.getDecryptSummary());
                object.getJSONObject("summaryObject").put("content", decryptedSummString);
                message.setMessageSummary(object.toString());
            }
            imailSummaryService.addAttachmentDetails(List.of(message));
            log.debug("Mail summary subject: {}", message.getSubject());
            modifyActionOwner(message);
            obj.put(EmailConstants.RESULT, message);
        } catch (Exception e) {
            log.error("Error inside @method getMailSummary", e);
            obj.put(EmailConstants.RESULT, null);
        }
        return obj;
    }

    @PostMapping("/mailSummary")
    public MessageSummary mailSummary(@RequestBody Map<String, String> request){
        String messageId = request.get("messageId");
        String internetMessageId = request.get("internetMessageId");
        UserInfo userInfo = userContextHolder.getCurrentUser();
        MessageSummary messageSummary=new MessageSummary();
        if(gmailIntegration.checkMessageIsEvent(userInfo.getEmail(),messageId)){
            messageSummary.setType(EVENT);
            messageSummary.setEvents(gmailIntegration.getCalendarEventsByMessageId(userInfo.getEmail(), Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, userInfo.getEmail())).map(MailSummary::getMessageId).orElse(messageId)));
        }else {
            messageSummary.setType(MESSAGE);
            messageSummary.setMessageSummery(getMailSummaryV1(request));
        }
        return messageSummary;
    }

    private void modifyActionOwner(MailSummary message) {
        try {
            String emailString = message.getActionOwner();
            log.debug("Action Owner is {}", emailString);
            ObjectMapper objectMapper = new ObjectMapper();

            if (emailString != null) {
                // Deserialize the JSON string into a List of ActionOwnerResponse objects
                List<ActionOwnerResponse> owners = objectMapper.readValue(emailString, new TypeReference<List<ActionOwnerResponse>>() {});

                JSONArray jsonArray = new JSONArray();
                for (ActionOwnerResponse owner : owners) {
                    String email = owner.getActionOwnerEmail();
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("email", email);

                    // Fetch user details by email
                    EmailUser user = emailUser.findByEmail(email);
                    if (user != null && user.getName() != null) {
                        jsonObject.put("userName", user.getName());
                    } else {
                        jsonObject.put("userName", JSONObject.NULL);
                    }
                    jsonArray.put(jsonObject);
                }
                String result = jsonArray.toString();
                message.setActionOwner(result);
            }
        } catch (Exception e) {
            log.error("Error inside @method modifyActionOwner. Exception message: {}", e.getMessage(), e);
        }
    }


    @GetMapping("/getMailByConversationId")
    @Operation(summary = "Get emails by conversation ID", tags = "Email", description = "Retrieve a list of email summaries based on the provided conversation ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Emails retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public List<MailSummary> getMailByConversationId(@RequestParam String conversationId,@RequestParam(name = APIConstants.INTERNET_MESSAGE_ID, required = false) String internetMessageId) {
        UserInfo userInfo = userContextHolder.getCurrentUser();
        if (pollingMode.equalsIgnoreCase("GMAIL")) {
            conversationId = Optional.ofNullable(mailSummaryDao.findByInternetMessageId(internetMessageId, userInfo.getId())).map(MailSummary::getConversationId).orElse(conversationId);
        }
        return mailSummaryDao.findByConversationId(conversationId, userInfo.getId());
    }

    @Deprecated
    @PostMapping("/generateDraft")
    @Operation(summary = "Generate email draft", tags = "Email", description = "Generate a draft email based on the provided email object.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft generated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> generateDraft(@RequestBody DraftWrapper emailObject) {
        UserInfo userInfo = userContextHolder.getCurrentUser();
        changeDraftWrapper(emailObject);
        Map<String, String> obj = new HashMap<>();
        try {
            String objective = emailObject.getObjective();
            String messageId = emailObject.getMessageId();
            if (pollingMode.equalsIgnoreCase("EWS")) {
                messageId = messageId.replace(" ", "+");
                messageId = messageId.replace("_", "+");
                messageId = messageId.replace("-", "/");
            }
            if (pollingMode.equalsIgnoreCase("GMAIL")) {
                messageId = Optional.ofNullable(mailSummaryDao.findByInternetMessageId(emailObject.getInternetMessageId(), userInfo.getId())).map(MailSummary::getMessageId).orElse(messageId);
            }
            else {
                messageId = messageId.replace("/", "-");
            }
            MailSummary message = mailSummaryDao.findByMessageId(messageId, userInfo.getId());
            Map<String, String> auditMap = CommonUtils.getAuditMap(userInfo.getId(), message.getSubject(), message.getInternetMessageId(), GENERATE_DRAFT, objective, null, message.getMessageId(), message.getConversationId());

            String summary = message.getMessageSummary();
            MailSummaryAIResponse mailSummaryAIResponse = CommonUtils.convertJsonToObject(summary, MailSummaryAIResponse.class);
            summary = mailSummaryAIResponse.getSummaryObject().getContent();
            try {
                DraftResponse draftMessage = draftServiceAI.generateDraft(objective, summary, message.getFromUser(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), null, auditMap);
                obj.put(EmailConstants.RESULT, convertToJSON(draftMessage));

            } catch (Exception e) {
                auditLog.error("Error inside @method generate  draft", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), GENERATE_DRAFT, userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, Map.of("userPrompt", objective, "previousPrompt", null, "tone", emailObject.getTone(), "length", emailObject.getLength()), null);
            }

            return obj;
        } catch (Exception e) {
            log.error("Error inside @method generateDraft", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.EMAIL_IS_NULL);
        }
    }

    @Deprecated
    @PostMapping("/v1/generateDraft")
    @Operation(summary = "Generate email draft (v1)", tags = "Email", description = "Generate a draft email based on the provided email object using version 1 of the API.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft generated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> generateDraftV1(@RequestBody DraftWrapper emailObject) {
        UserInfo userInfo = userContextHolder.getCurrentUser();
        changeDraftWrapper(emailObject);
        Map<String, String> obj = new HashMap<>();
        try {
            String objective = emailObject.getObjective();
            String internetMessageId = emailObject.getInternetMessageId();
            String messageId = emailObject.getMessageId();
            MailSummary message = mailSummaryDao.findByInternetMessageId(internetMessageId, userInfo.getId());
            if(messageId!=null && !messageId.isEmpty() && message==null){
                message = mailSummaryDao.findByMessageId(messageId,userInfo.getId());
            }
            if (message == null) {

                obj.put("error", "Something went wrong!");
                obj.put("message", "No mail summary is found for given messageId");
                return obj;
            }
            Map<String, String> auditMap = CommonUtils.getAuditMap(userInfo.getId(), message.getSubject(), message.getInternetMessageId(), GENERATE_DRAFT, objective, null, message.getMessageId(), message.getConversationId());
            String summary = message.getMessageSummary();
            MailSummaryAIResponse mailSummaryAIResponse = CommonUtils.convertJsonToObject(summary, MailSummaryAIResponse.class);
            summary = mailSummaryAIResponse.getSummaryObject().getContent();
            try {

                String previousDraft = emailObject.getPreviousDraft();
                if (emailObject.getForward() != null && emailObject.getForward()) {
                    DraftResponse draftMessage = draftServiceAI.generateForwardDraft(objective, summary, message.getFromUser(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), previousDraft, auditMap
                    );

                    obj.put(EmailConstants.RESULT, convertToJSON(draftMessage));
                } else {
                    DraftResponse draftMessage;
                    if (previousDraft == null || previousDraft.isBlank()) {
                        draftMessage = draftServiceAI.generateDraft(objective, summary, message.getFromUser(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), null, auditMap);
                    } else {
                        draftMessage = draftServiceAI.reGenerateDraft(objective, summary, message.getFromUser(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), previousDraft, auditMap);
                    }
                    obj.put(EmailConstants.RESULT, convertToJSON(draftMessage));
                }


            } catch (Exception e) {
                auditLog.error("Error inside @method generate  draft", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), GENERATE_DRAFT, userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
            }

            return obj;
        } catch (Exception e) {
            log.error("Error inside @method generateDraftV1", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.EMAIL_IS_NULL);
        }
    }

    @Deprecated
    @PostMapping("/generateDraftFromContent")
    @Operation(summary = "Generate email draft using content(v1)", tags = "Email", description = "Generate a draft email based on the provided email object using version 1 of the API.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft generated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> generateDraftFromContent(@RequestBody DraftWrapper emailObject) {
        UserInfo userInfo = userContextHolder.getCurrentUser();
        changeDraftWrapper(emailObject);
        Map<String, String> obj = new HashMap<>();
        try {
            String objective = emailObject.getObjective();
            String content = emailObject.getContent();

            Map<String, String> auditMap = CommonUtils.getAuditMap(userInfo.getId(), "", "", GENERATE_DRAFT, objective, null, "", "");
            try {
                DraftResponse draftMessage;
                String previousDraft = emailObject.getPreviousDraft();
                if (previousDraft == null || previousDraft.isBlank()) {
                    draftMessage = draftServiceAI.generateDraftFromContent(objective, content, emailObject.getRecipients(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), null, auditMap);
                } else {
                    draftMessage = draftServiceAI.reGenerateDraft(objective, content, emailObject.getRecipients(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), previousDraft, auditMap);
                }
                obj.put(EmailConstants.RESULT, convertToJSON(draftMessage));

            } catch (Exception e) {
                auditLog.error("Error inside @method generateDraftFromContent", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), "GENERATE_DRAFT_FROM_CONTENT", userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
            }

            return obj;
        } catch (Exception e) {
            log.error("Error inside @method generateDraftV1", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.EMAIL_IS_NULL);
        }
    }

    private void changeDraftWrapper(DraftWrapper draftWrapper) {
        String messageId = draftWrapper.getMessageId();
        if (messageId == null) return;

        if (pollingMode.equalsIgnoreCase("EWS")) {
            messageId = messageId.replace(" ", "+");
        } else {
            messageId = messageId.replace("/", "-");
        }
        draftWrapper.setMessageId(messageId);
    }

    private String convertToJSON(Object object) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Error inside @method convertToJSON", e);
            return object.toString();
        }
    }

    @Deprecated
    @PostMapping("/improveDraft")
    @Operation(summary = "Improve email draft", tags = "Email", description = "Improve the content of an existing draft email based on the provided email object.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft improved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> improveDraft(@RequestBody DraftWrapper emailObject) {
        changeDraftWrapper(emailObject);
        try {
            UserInfo userInfo = userContextHolder.getCurrentUser();
            log.debug("UserInfo : {}", userInfo);
            Map<String, String> obj = new HashMap<>();
            String userPrompt = emailObject.getUserPrompt();
            String messageId = emailObject.getMessageId();
            if (pollingMode.equalsIgnoreCase("EWS")) {
                messageId = messageId.replace(" ", "+");
                messageId = messageId.replace("_", "+");
                messageId = messageId.replace("-", "/");
            } else {
                messageId = messageId.replace("/", "-");
            }
            String draft = emailObject.getDraft();
            MailSummary message = mailSummaryDao.findByMessageId(messageId, userInfo.getId());
            Map<String, String> auditMap = CommonUtils.getAuditMap(userInfo.getId(), message.getSubject(), message.getInternetMessageId(), IMPROVE_DRAFT, userPrompt, null, message.getMessageId(), message.getConversationId());
            try {
                DraftResponse draftMessage = draftServiceAI.improveDraft(draft, userPrompt, message.getFromUser(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), null, auditMap);

                log.debug("Draft message is : {}", draftMessage);
                obj.put(EmailConstants.RESULT, convertToJSON(draftMessage));
            } catch (Exception e) {
                auditLog.error("Error inside @method improve  draft", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), IMPROVE_DRAFT, userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
            }
            return obj;
        } catch (Exception e) {
            log.error("Error inside @method improveDraft", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.EMAIL_IS_NULL);
        }
    }

    @Deprecated
    @PostMapping("/v1/improveDraft")
    @Operation(summary = "Improve email draft (v1)", tags = "Email", description = "Improve the content of an existing draft email based on the provided email object using version 1 of the API.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft improved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> improveDraftV1(@RequestBody DraftWrapper emailObject) {
        try {
            UserInfo userInfo = userContextHolder.getCurrentUser();
            log.debug("UserInfo : {}", userInfo);
            Map<String, String> obj = new HashMap<>();
            String userPrompt = emailObject.getUserPrompt();
            String internetMessageId = emailObject.getInternetMessageId();
            String messageId = emailObject.getMessageId();
            String draft = emailObject.getDraft();
            MailSummary message = mailSummaryDao.findByInternetMessageId(internetMessageId, userInfo.getId());
            if(message == null && pollingMode.equalsIgnoreCase("GMAIL")){
                message = mailSummaryDao.findByMessageId(messageId, userInfo.getId());
            }
            Map<String, String> auditMap = CommonUtils.getAuditMap(userInfo.getId(), message.getSubject(), message.getInternetMessageId(), IMPROVE_DRAFT, userPrompt, null, message.getMessageId(), message.getConversationId());

            try {
                DraftResponse draftMessage = draftServiceAI.improveDraft(draft, userPrompt, message.getFromUser(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), null, auditMap);
                log.debug("Draft message is : {}", draftMessage);
                obj.put(EmailConstants.RESULT, convertToJSON(draftMessage));
            } catch (Exception e) {
                auditLog.error("Error inside @method improve  draft", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), IMPROVE_DRAFT, userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
            }
            return obj;
        } catch (Exception e) {
            log.error("Error inside @method improveDraft", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.EMAIL_IS_NULL);
        }
    }

    @Deprecated
    @PostMapping("/improveSelection")
    @Operation(summary = "Improve selected email content", tags = "Email", description = "Improve the content of a selected portion of an existing draft email based on the provided email object.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Selected content improved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> improveSelection(@RequestBody DraftWrapper emailObject) {
        changeDraftWrapper(emailObject);
        String userPrompt = emailObject.getUserPrompt();
        if (userPrompt == null) userPrompt = "";
        String messageId = emailObject.getMessageId();
        if (pollingMode.equalsIgnoreCase("EWS") && messageId != null) {
            messageId = messageId.replace(" ", "+");
            messageId = messageId.replace("_", "+");
            messageId = messageId.replace("-", "/");
        } else if (pollingMode.equalsIgnoreCase("Graph") && messageId != null) {
            messageId = messageId.replace("/", "-");
        }
        String selection = emailObject.getSelection();
        String draft = emailObject.getDraft();
        Map<String, String> auditMap = CommonUtils.getAuditMap("<EMAIL>", null, null, IMPROVE_SELECTION, selection, userPrompt, null, null);
        Map<String, String> obj = new HashMap<>();

        try {
            DraftResponse draftMessage = draftServiceAI.improveSelection(draft, selection, userPrompt, emailObject.getLength(), emailObject.getTone(), auditMap);
            log.debug("Draft message is: {}", draftMessage);
            obj.put(EmailConstants.RESULT, convertToJSON(draftMessage));
        } catch (Exception e) {
            auditLog.error("Error inside @method improve  selection", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), IMPROVE_SELECTION, userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
        }
        return obj;
    }

    @Deprecated
    @GetMapping("/getFreshDraft")
    @Operation(summary = "Get fresh draft email", tags = "Email", description = "Generate a fresh draft email based on the provided intent, user preferences, length, and tone.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Fresh draft generated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public FreshDraftAIResponse getFreshDraft(@RequestParam String intent, @RequestParam String user,
                                              @RequestParam String length, @RequestParam String tone) {

        return draftServiceAI.getFreshDraft(intent, user, length, tone);
    }

    @Deprecated
    @PostMapping("/v1/getFreshDraft")
    @Operation(summary = "Get fresh draft email (v1)", tags = "Email", description = "Generate a fresh draft email based on the provided request data using version 1 of the API.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Fresh draft generated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public FreshDraftAIResponse getFreshDraft(@RequestBody FreshDraftRequest request) {
        log.error("Inside method get Fresh Draft {}", request.getIntent());
        try {
            return draftServiceAI.getFreshDraft(request.getIntent(), request.getUser(), request.getLength(), request.getTone());
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("userPrompt", request.getIntent(), "previousPrompt", null, "tone", request.getTone(), "length", request.getLength());
            auditLog.error("Error inside @method fresh draft", e.getMessage(), getStackTraceAsString(e), null, null, FRESH_DRAFT, userContextHolder.getCurrentUser().getId(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            log.error("Error getting fresh draft", e);
        }
        return null;
    }

    @Deprecated
    @PostMapping("/improveFreshDraft")
    @Operation(summary = "Improve fresh draft email", tags = "Email", description = "Improve a fresh draft email based on the provided user preferences and draft details.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft improved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public FreshDraftAIResponse improveDraft(@RequestParam String user, @RequestBody Map<String, String> request) {
        String draft = request.get("draft");
        String intent = request.get("intent");
        String length = request.get("length");
        String tone = request.get("tone");

        try {
            return draftServiceAI.improveFreshDraft(draft, intent, user, length, tone);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("userPrompt", intent, "previousPrompt", draft, "tone", tone, "length", length);
            auditLog.error("Error inside @method improve fresh draft", e.getMessage(), getStackTraceAsString(e), null, null, IMPROVE_FRESH_DRAFT, userContextHolder.getCurrentUser().getId(), null, null, null, null,
                    AIUtils.convertToJSON(auditMap, true), null);
        }
        return null;
    }

    @PostMapping("/v2/intentBasedSearch")
    @Operation(summary = "Intent-based search", tags = "Search", description = "Perform a search based on the provided intent and related data.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search performed successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, Object> intentBasedSearchV2(@RequestBody IntentWrapper wrapper,
                                                   @RequestHeader(value = "X-TimeZone", required = false) String timeZone,
                                                   HttpServletRequest request) {
        if (timeZone != null) request.setAttribute("timeZone", timeZone);
        UserInfo userInfo = userContextHolder.getCurrentUser();
        return chatService.intentBasedSearchV2(userInfo.getEmail(), wrapper.getUserPrompt(), wrapper.getPreviousPrompt(), wrapper.getInternetMessageId(), userInfo.getId(),wrapper.getMessageId());
    }

    @GetMapping("/getUserList")
    @Operation(summary = "Get user contact list", tags = "Contacts", description = "Retrieves a list of contacts for the specified user.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contacts retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving contacts.")
    })
    public List<ContactBook> getUserList(@RequestParam String user) {
        log.debug("Inside @method getUserList. @param : user -> {}", user);
        return contactRepo.searchByContacts(user + "%",getUserId());
    }

    @GetMapping("/getConversationSubject")
    @Operation(summary = "Get conversation subjects", tags = "Email", description = "Retrieves a list of conversation subjects based on user preferences.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Conversation subjects retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving conversation subjects.")
    })
    public List<EmailThread> getConversationSubject() {
        UserInfo userInfo = userContextHolder.getCurrentUser();
        EmailPreferences preference = preferencesDao.getEmailPreferencesByUserId(userInfo.getId());
        List<String> conversationPreference = new ArrayList<>();
        if (preference.getConversationId() != null) {
            conversationPreference = Arrays.asList(preference.getConversationId().split(","));
        }
        return threadDao.getConversationSubject(conversationPreference);
    }

    @GetMapping("/updatePriority")
    @Operation(summary = "Update email priority", tags = "Email", description = "Update the priority of an email based on the provided message ID and priority value.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Priority updated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> updatePriority(@RequestParam String messageId, @RequestParam String priority) throws Exception {
        String result;
        
        if (pollingMode.equalsIgnoreCase("EWS")) {
            log.debug("@param to update priority : messageId -> {} priority -> {}", messageId, priority);
            result = ewsService.updatePriority(messageId, priority);
        } else if (pollingMode.equalsIgnoreCase("Gmail")) {
            result = emailService.updatePriority(messageId, priority);
        } else {
            result = emailService.updatePriority(messageId, priority);
        }
        Map<String, String> responseMap = new HashMap<>();
        responseMap.put(EmailConstants.RESULT, result);
        return responseMap;
    }


    @GetMapping("/pollEmailForAll")
    @Operation(summary = "Poll emails for all users", tags = "Email Polling", description = "Polls emails for all configured users using a concurrent processing approach.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email polling executed successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while polling emails.")
    })
    public String pollEmailForAll() {
        log.error("Inside @method pollEmailForAll with polling mode: {}", pollingMode);

        List<EmailUser> emailUsers = emailUser.findAll("Office365");

        Semaphore semaphore = new Semaphore(5);  // Limit concurrency to 10
        AtomicInteger threadCounter = new AtomicInteger(1);  // To track thread numbers

        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            emailUsers.forEach(user ->
                    executor.submit(() -> {
                        int threadNumber = threadCounter.getAndIncrement(); // Assign a thread number
                        try {
                            semaphore.acquire();  // Acquire a permit, limiting to 10 threads
                            log.info("Starting task for user: {} on thread: {}", user.getEmail(), threadNumber);
                            pollIndividualUser(user.getEmail(), user.getUserId(), pollingMode);
                            log.info("Finished task for user: {} on thread: {}", user.getEmail(), threadNumber);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt(); // Handle interrupt properly
                            log.error("Thread was interrupted for user: {} on thread: {}", user.getEmail(), threadNumber, e);
                        } finally {
                            semaphore.release();  // Release permit after task completion
                        }
                    })
            );
        } catch (Exception e) {
            log.error("Error occurred while polling emails: ", e);
        }

        return "success";
    }


    private void pollIndividualUser(String email, String userId, String source) {
        log.debug("Polling for email : {}", email);
        try {
            String timeFilter = emailService.getReceivedDateTimeFilter(email);
            pollTimeInfoService.setStartTime(email, LocalDateTime.now(), PollTimeInfo.Status.PROCESSING);
            outlookPollingAI.pollOutlookInbox(email, userId, timeFilter, source);
            pollTimeInfoService.setEndTime(email, LocalDateTime.now(), PollTimeInfo.Status.SUCCESS);
        } catch (Exception e) {
            pollTimeInfoService.setEndTime(email, LocalDateTime.now(), PollTimeInfo.Status.FAILED);
            log.error("Error while polling", e);
        }
    }

    @PostMapping("/generateMeetingDraft")
    @Operation(summary = "Generate meeting draft", tags = "Email", description = "Generate a draft email specifically for meeting invitations or responses based on the provided email object.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Meeting draft generated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public Map<String, String> generateMeetingDraft(@RequestBody DraftWrapper emailObject) {
        changeDraftWrapper(emailObject);
        Map<String, String> obj = new HashMap<>();
        try {
            String objective = emailObject.getObjective();
            String messageId = emailObject.getMessageId();
            if (pollingMode.equalsIgnoreCase("EWS") && messageId != null) {
                messageId = messageId.replace(" ", "+");
                messageId = messageId.replace("_", "+");
                messageId = messageId.replace("-", "/");
            }
            else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                messageId = Optional.ofNullable(mailSummaryDao.findByInternetMessageId(emailObject.getInternetMessageId(), userContextHolder.getCurrentUser().getId())).map(MailSummary::getMessageId).orElse(messageId);
            }
            else if (pollingMode.equalsIgnoreCase("Graph") && messageId != null) {
                messageId = messageId.replace("/", "-");
            }
            MailSummary message = mailSummaryDao.findByMessageId(messageId, null/*userInfo.getId()*/);
            Map<String, String> auditMap;
            if (message != null) {
                auditMap = CommonUtils.getAuditMap(message.getUserId(), message.getSubject(), message.getInternetMessageId(), MEETING_DRAFT, objective, message.getMessageSummary(), message.getMessageId(), message.getConversationId());
            } else {
                auditMap = CommonUtils.getAuditMap(userContextHolder.getCurrentUser().getId(), null, null, MEETING_DRAFT, objective, null, null, null);
            }
            DraftResponse draftMessage;
            try {
                if (message != null) {
                    draftMessage = draftServiceAI.generateMeetingDraft(objective, message.getMessageSummary(), auditMap);
                    obj.put(EmailConstants.RESULT, convertToJSON(draftMessage));
                } else {
                    draftMessage = draftServiceAI.generateMeetingDraft(objective, "", auditMap);
                    obj.put(EmailConstants.RESULT, convertToJSON(draftMessage));
                }
            } catch (Exception e) {
                auditLog.error("Error inside @method generate meeting draft", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), MEETING_DRAFT, userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
            }

            return obj;
        } catch (Exception e) {
            log.error("Error inside @method generateMeetingDraft", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.EMAIL_IS_NULL);
        }
    }

    @GetMapping("/pollEmailByBatchId")
    @Operation(summary = "Poll emails by batch ID", tags = "Email", description = "Retrieve emails associated with a specific batch ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Emails retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })
    public String pollEmailByBatchId(@RequestParam String batchId) {
        log.error("Inside @method pollEmailByBatchId. @param : batchId -> {} threadConcurrency {}", batchId, threadConcurency);
        try {
            List<EmailUser> emailUsers;
            emailUsers = emailUser.findByBatchId(batchId, false);
            Semaphore semaphore;
            if (threadConcurency != null) {
                semaphore = new Semaphore(threadConcurency);  // Limit concurrency to 10
            } else {
                semaphore = new Semaphore(5);  // Limit concurrency to 10
            }
            AtomicInteger threadCounter = new AtomicInteger(1);  // To track thread numbers
            try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
                emailUsers.forEach(user ->
                        executor.submit(() -> {
                            int threadNumber = threadCounter.getAndIncrement(); // Assign a thread number
                            try {
                                semaphore.acquire();  // Acquire a permit, limiting to 10 threads
                                log.info("Starting task for user: {} on thread: {}", user.getEmail(), threadNumber);
                                pollIndividualUserKafka(user.getEmail(), user.getUserId(), pollingMode, batchId,user.getQueueName());
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt(); // Handle interrupt properly
                                log.error("Thread was interrupted for user: {} on thread: {}", user.getEmail(), threadNumber, e);
                            } finally {
                                semaphore.release();  // Release permit after task completion
                            }
                        })
                );
            } catch (Exception e) {
                log.error("Error occurred while polling emails: ", e);
            }

        } catch (Exception e) {
            log.error("Error while polling", e);
            return "failed";
        }
        return "success";
    }

    private void pollIndividualUserKafka(String email, String userId, String source, String batchId,String queueName) {
        log.debug("Polling for email : {}, BatchId {}", email, batchId);
        try {
            String timeFilter = emailService.getReceivedDateTimeFilter(email);
            pollTimeInfoService.setStartTime(email, LocalDateTime.now(), PollTimeInfo.Status.PROCESSING);
            outlookPollingAI.pollOutlookInboxKafka(email, userId, timeFilter, source, batchId,queueName);
            
        } catch (Exception e) {
            pollTimeInfoService.setEndTime(email, LocalDateTime.now(), PollTimeInfo.Status.FAILED);
            log.error("Error while polling", e);
        }
    }

    @PostMapping("/ask")
    @Operation(summary = "Handle chat queries", tags = "Chat", description = "Processes a chat query and returns a response based on the provided input.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Query processed and response returned successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while processing the query.")
    })
    public Map<String, Object> ask(@RequestBody Map<String, String> queryMap) {
        log.debug("Inside @method ask. @param : queryMap -> {}", queryMap);
        Map<String, Object> response = chatService.getChatResponse(queryMap);
        return response;
    }

    @PostMapping("/getMeetingSummaryByInternetMessageId")
    @Operation(summary = "Retrieve meeting summary by internet message ID", tags = "Meetings", description = "Fetches the meeting summary based on the provided internet message ID from the request body.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Meeting summary retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the meeting summary.")
    })
    public Map<String, Object> getMeetingSummaryByInternetMessageId(@RequestBody Map<String, String> requestBody) {
        Map<String, Object> response = new HashMap<>();
        try {

            String internetMessageId = requestBody.get("internetMessageId");

            log.debug("Calling emailService.getMeetingSummaryByInternetMessageId with internetMessageId: {}", internetMessageId);

            MeetingSummary meetingSummary = emailService.getMeetingSummaryByInternetMessageId(internetMessageId);

            if (meetingSummary != null) {
                response.put("result", meetingSummary);
            } else {
                response.put("result", null);
            }
        } catch (Exception e) {
            log.error("Error occurred while fetching MeetingSummary for internetMessageId: {}. Error: {}", requestBody.get("internetMessageId"), e.getMessage(), e);
            response.put("error", e.getMessage());
        }
        return response;
    }

    @PostMapping("/get3AvailableSlots")
    @Operation(summary = "Get 3 available meeting slots", tags = "Meetings", description = "Retrieves 3 available meeting slots based on provided emails, start date/time, and meeting duration.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Available meeting slots retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching available slots.")
    })
    public List<Meeting> get3AvailableSlots(@RequestBody Map<String, Object> requestBody,
                                            @RequestHeader(value = "X-TimeZone", required = false) String timeZone,
                                            HttpServletRequest request) {

        if (timeZone != null) request.setAttribute("timeZone", timeZone);
        List<String> emails = (List<String>) requestBody.get("emails");
        String startDateTime = (String) requestBody.get("startDateTime");
        Integer meetingDuration = (Integer) requestBody.get("meetingDuration");
        return chatService.get3AvailableSlots(emails, startDateTime, meetingDuration);
    }


}
