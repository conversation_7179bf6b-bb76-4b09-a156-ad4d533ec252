package com.enttribe.emailagent.entity;

import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * The type User profile.
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Table(name = "UserProfile")
public class UserProfile {
    @Id
    @GeneratedValue(strategy = jakarta.persistence.GenerationType.IDENTITY)
    private Integer id;

    private String emailId;

    private String tone;

    private String style;

    private String personalization;

    private String contentPreferences;

    private String formatting;

    private String subjectLine;

    private String otherPreferences;

    private String replyBehaviour;

    private LocalDateTime modifiedTime;

    private LocalDateTime createdTime;


}
