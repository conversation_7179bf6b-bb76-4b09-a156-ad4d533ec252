package com.enttribe.emailagent.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * The type Poll time info.
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "POLL_TIME_INFO")
public class PollTimeInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "START_TIME")
    private LocalDateTime startTime;

    @Column(name = "END_TIME")
    private LocalDateTime endTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "STATUS")
    private Status status;

    @Column(name = "EMAIL")
    private String email;

    public enum Status {
        PROCESSING, SUCCESS, FAILED
    }
}
