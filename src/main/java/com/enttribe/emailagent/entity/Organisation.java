package com.enttribe.emailagent.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalTime;

/**
 * The type Organisation.
 * <AUTHOR> Sonsale
 */
@Entity
@Table(name = "ORGANISATION")
@Getter
@Setter
@ToString
public class Organisation extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "ORGANISATION_NAME")
    private String organisationName;

    @Column(name = "TIME_ZONE")
    private String timeZone;

    @Column(name = "BLACKLISTED")
    private String blacklisted;

    @Column(name = "CHECKIN")
    private LocalTime checkin;

    @Column(name = "CHECKOUT")
    private LocalTime checkout;

    @Column(name="MEETING_TYPE")
    private String meetingType;

    @Column(name = "ACTIVE")
    private boolean active;
}



