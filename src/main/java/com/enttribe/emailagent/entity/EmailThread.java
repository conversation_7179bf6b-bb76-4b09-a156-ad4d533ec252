package com.enttribe.emailagent.entity;

import com.enttribe.emailagent.utils.EmailSummaryEncryptor;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * The type Email thread.
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "EMAIL_THREAD")
public class EmailThread {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "CONVERSATION_ID")
    private String conversationId;

    @Convert(converter = EmailSummaryEncryptor.class)
    @Column(name = "THREAD_SUMMARY")
    private String threadSummary;

    @Column(name = "STAR_MARKED")
    private Boolean starMarked;

    @Column(name = "STAR_REASON")
    private String starReason;

    @Column(name = "USER_ID")
    private String userId;

    @Convert(converter = EmailSummaryEncryptor.class)
    @Column(name = "SUBJECT")
    private String subject;

    @Column(name = "CREATED_TIME")
    private Date createdTime;

    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;

    @Convert(converter = EmailSummaryEncryptor.class)
    @Column(name = "SHORT_SUMMARY")
    private String shortSummary;

    @Column(name= "SHORT_DECRYPT_OBJECT")
    private  String shortDecryptObject;

    @Column(name="LONG_DECRYPT_OBJECT")
    private  String longDecryptObject;

    @Column(name="ACTION_DECRYPT_OBJECT")
    private  String actionDecryptObject;

}
