package com.enttribe.emailagent.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * The type Email stats.
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@XmlRootElement(name = "EmailStats")
@Entity
@Table(name = "EMAIL_STATS")
@JsonIgnoreProperties(ignoreUnknown = true, value = {"hibernateLazyInitializer", "handler"})
public class EmailStats {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private int id;

    @Column(name = "USER_ID")
    private String userId;

    @Column(name = "TYPE")
    private String type;

    @Column(name = "STATS_DATE")
    private Date statsDate;

    @Column(name = "USED_COUNT")
    private Integer usedCount;

    @Column(name = "UNUSED_COUNT")
    private Integer UnusedCount;


}
