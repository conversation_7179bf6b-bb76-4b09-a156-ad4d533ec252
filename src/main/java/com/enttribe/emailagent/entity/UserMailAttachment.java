package com.enttribe.emailagent.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * The type User mail attachment.
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "USER_MAIL_ATTACHMENT")
@ToString
public class UserMailAttachment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "USER_ID", length = 1000)
    private String userId;

    @Column(name = "NAME", length = 200)
    private String name;

    @Column(name = "UNIQUE_NAME" , length = 2000)
    private String uniqueName;

    @Column(name = "ATTACHMENT_ID", length = 2000)
    private String attachmentId;

    @Column(name = "TYPE", length = 100)
    private String type; // contentType

    @Column(name = "MESSAGE_ID", length = 2000)
    private String messageId;

    @Column(name = "SHORT_SUMMARY", length = 255)
    private String shortSummary;

    @Column(name = "LONG_SUMMARY", length = 255)
    private String longSummary;

    @Column(name = "RAG_DOCUMENT_ID", length = 50)
    private String ragDocumentId;

    @Column(name = "CONVERSATION_ID", length = 2000)
    private String conversationId;

    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;

    @Column(name = "RETRY_COUNT")
    private Integer retryCount;

    @Column(name = "PROCESSING_STATUS")
    private String processingStatus = "NEW"; // NEW, IN_PROGRESS, COMPLETED, ERROR

    @Column(name = "PROCESSING_ERROR")
    private String processingError;

    @Column(name = "DOC_PATH", length = 2000)
    private String docPath;

    @Column(name = "INTERNET_MESSAGE_ID", length = 2000)
    private String internetMessageId;

    @Column(name = "SUBJECT", length = 1000)
    private String subject;
    
    @Column(name = "BATCH_ID", length = 50)
    private String batchId;

    @Column(name = "ERROR_DISPLAY", length = 1000)
    private String errorDisplay;


}
