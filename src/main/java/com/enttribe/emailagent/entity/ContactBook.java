package com.enttribe.emailagent.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * The type Contact book.
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "CONTACT_BOOK")
public class ContactBook {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "USER_ID")
    private String userId;

    @Column(name = "CONTACTS")
    private String contacts;

    @Column(name = "CONTACT_NAME")
    private String contactName;
}
