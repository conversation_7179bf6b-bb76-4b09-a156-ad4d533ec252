package com.enttribe.emailagent.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * The type User template.
 * <AUTHOR> <PERSON>gi
 */
@Getter
@Setter
@Entity
@Table(name = "USER_TEMPLATE")
public class UserTemplate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "EMAIL", length = 255)
    private String email;

    @JsonIgnore
    @Column(name = "DATA")
    private String data;

    // ObjectMapper should be reused to avoid performance issues
    @Transient
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // Set the data as a JSON string from a map
    public void setDataMap(Map<String, String> map) {
        try {
            this.data = objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize map to JSON", e);
        }
    }

    // Get the data as a map from the JSON string
    public Map<String, String> getDataMap() {
        if (this.data == null || this.data.isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(this.data, new TypeReference<Map<String, String>>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to deserialize JSON to map", e);
        }
    }
}
