package com.enttribe.emailagent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "BUILD_APPLICATION")
public class BuildApplication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "APPLICATION_NAME", length = 100)
    private String applicationName;

    @Column(name = "ENVIRONMENT", length = 50)
    private String environment;

    @Column(name = "ALLOWED_DEPLOYMENT")
    private Integer allowedDeployment;

}
