package com.enttribe.emailagent.entity;

import com.enttribe.emailagent.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalTime;

/**
 * The type Email user.
 * <AUTHOR> Sonsale
 */
@Entity
@Getter
@Setter
@Table(name = "EMAIL_USER")
@ToString
public class EmailUser extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "USER_ID")
    private String userId;  // Default as email

    @Column(name = "BATCH_ID")
    private String batchId;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "PHONE_NUMBER", length = 20)
    private String phoneNumber;

    @Column(name = "TYPE", length = 50)
    private String type = "Office365";

    @Column(name = "DELETED")
    private Boolean deleted = false;

    @Column(name = "NAME")
    private String name;

    @Column(name = "USER_OID")
    private String userOID;

   @Column(name = "QUEUE_NAME")
   private String queueName;


}
