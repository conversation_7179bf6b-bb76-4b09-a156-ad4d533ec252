package com.enttribe.emailagent.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * The type User actions.
 * <AUTHOR> <PERSON>gi
 */
@Entity
@Getter
@Setter
@Table(name = "USER_ACTIONS")
@ToString(exclude = {"mailSummary"})
public class UserActions {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "ACTION_OWNER")
    private String actionOwner;         // The user assigned as the action owner

    @Column(name = "ACTION_OWNER_REASON")
    private String actionOwnerReason;   // Reason for assigning the action owner

    @Column(name = "CONVERSATION_ID")
    private String conversationId;

    @Column(name = "INTERNET_MESSAGE_ID", length = 2000)
    private String internetMessageId;

    @Column(name = "ACTION_TAKEN", nullable = false)
    private Boolean actionTaken = false;  // Status of whether the action is taken

    @Column(name = "ACTION_TAKEN_REASON")
    private String actionTakenReason;   // Reason for action status

    @Column(name = "MAIL_RECEIVED_TIME")
    private LocalDateTime mailReceivedTime;

    @Column(name = "DELETED")
    private Boolean deleted = false;

    @Column(name = "CREATED_AT")
    private LocalDateTime createdAt;

    @Column(name = "SUBJECT")
    private String subject;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonBackReference
    @JoinColumn(name = "MAIL_SUMMARY_ID", nullable = false)
    private MailSummary mailSummary;

}
