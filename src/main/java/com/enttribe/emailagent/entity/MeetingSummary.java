package com.enttribe.emailagent.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;


/**
 * The type Meeting summary.
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "MEETING_SUMMARY")
@ToString

public class MeetingSummary {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="ID")
    private Integer id;

    @Column(name = "USER_ID", length = 50)
    private String userId;

    @Column(name = "MEETING_ID", length = 20)
    private String meetingId;

    @Column(name = "INTERNET_MESSAGE_ID", length = 2000)
    private String internetMessageId;

    @Column(name = "SUBJECT", length = 100)
    private String subject;

    @Column(name = "SHORT_SUMMARY", length = 1000)
    private String shortSummary;

    @Column(name = "LONG_SUMMARY", length = 1000)
    private String longSummary;

    @Column(name = "STATUS", length = 50)
    private String status;

    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;

    @Column(name = "PROCESSING_ERROR")
    private String processingError;

    @Column(name = "ACTION_ITEMS")
    private String actionItems;
}
