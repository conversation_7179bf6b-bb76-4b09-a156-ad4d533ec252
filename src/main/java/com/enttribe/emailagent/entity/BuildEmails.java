package com.enttribe.emailagent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * The type Build emails.
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "BUILD_EMAILS")
public class BuildEmails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "CONVERSATION_ID")
    private String conversationId;

    @Column(name = "MESSAGE_ID")
    private String messageId;

    @Column(name = "CONTENT", columnDefinition = "TEXT")
    private String content;

    @Column(name = "INTERNET_MESSAGE_ID")
    private String internetMessageId;

    @Column(name = "SUBJECT")
    private String subject;

    @Column(name = "ERROR", columnDefinition = "TEXT")
    private String error;

    @Column(name = "APPLICATION_NAME")
    private String applicationName;

    @Column(name = "TYPE")
    private String type;//APPROVER, REQUESTEE

    @Column(name = "QUEUE_URL")
    private String queueUrl;

    @Column(name = "BUILD_URL")
    private String buildUrl;

    @Column(name = "APPROVED_BY")
    private String approvedBy;

    @Column(name = "DEPLOYED_DATE")
    private Date deployedDate;

    @Column(name = "BUILD_PARAMETERS", columnDefinition = "TEXT")
    private String buildParameters;

    @Column(name = "EMAIL_FROM")
    private String emailFrom;

    @Column(name = "REPLY_TO")
    private String replyTo;

    @Column(name = "CREATED_TIME")
    private Date createdTime;

    @Column(name = "STATUS")
    private String status;//PENDING, APPROVED, ERROR, WAITING

    @Column(name = "BLUE_OCEAN_URL")
    private String blueOceanUrl;

    @Column(name = "IS_PRODUCTION_JOB")
    private Boolean isProductionJob = false;

}
