package com.enttribe.emailagent.entity;


import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * The type Failure logs.
 *  <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "FAILURE_LOGS")
@ToString
public class FailureLogs {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "INTERNET_MESSAGE_ID")
    private String internetMessageId;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "EMAIL_SUBJECT")
    private String emailSubject;

    @Column(name = "MESSAGE_ID")
    private String messageId;

    @Column(name = "CONVERSATION_ID")
    private String conversationId;

    @Column(name = "TYPE")
    private String type; // Email categorization and all other actions (flag, tag, etc)

    @Column(name = "EXCEPTION_MESSAGE")
    private String exceptionMessage;

    @Column(name = "CUSTOM_EXCEPTION_MESSAGE")
    private String customExceptionMessage;

    @Column(name = "EXCEPTION_TRACE")
    private String exceptionTrace;

    @Column(name = "ATTACHMENT")
    private Boolean attachment;

    @Column(name = "USER_MAIL_ATTACHMENT")
    private String userMailAttachment;

    @Column(name = "INTENT")
    private String intent; // Only in case of draft

    @Column(name = "ATTENDEES")
    private String attendees; // In case of meeting

    @Column(name = "ERROR_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date errorDate;


    @Column(name = "STATUS")
    private String status="NEW";

    public FailureLogs() {
    }

    public FailureLogs(String internetMessageId, String email, String emailSubject, String messageId, String conversationId, String type, String exceptionMessage, String customExceptionMessage, String exceptionTrace, Boolean attachment, String userMailAttachment, String intent, String attendees, Date errorDate) {
        this.internetMessageId = internetMessageId;
        this.email = email;
        this.emailSubject = emailSubject;
        this.messageId = messageId;
        this.conversationId = conversationId;
        this.type = type;
        this.exceptionMessage = exceptionMessage;
        this.customExceptionMessage = customExceptionMessage;
        this.exceptionTrace = exceptionTrace;
        this.attachment = attachment;
        this.userMailAttachment = userMailAttachment;
        this.intent = intent;
        this.attendees = attendees;
        this.errorDate = errorDate;
    }
}
