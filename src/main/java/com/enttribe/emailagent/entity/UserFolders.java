package com.enttribe.emailagent.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * The type User folders.
 * <AUTHOR> Sonsale
 */
@Entity
@Getter
@Setter
@Table(name = "USER_FOLDERS")
public class UserFolders {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "FOLDER_ID")
    private String folderId;

    @Column(name = "DISPLAY_NAME")
    private String displayName;

    @Column(name = "ACTIVE")
    private boolean active;

    @JsonIgnore
    @Column(name = "EMAIL")
    private String email;

}
