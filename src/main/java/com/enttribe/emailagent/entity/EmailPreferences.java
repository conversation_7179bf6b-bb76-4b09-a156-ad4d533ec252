package com.enttribe.emailagent.entity;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

import org.hibernate.envers.Audited;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.GenerationType;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * The type Email preferences.
 * <AUTHOR> Sonsale
 */
@Getter
@Setter
@ToString
@XmlRootElement(name = "EmailPreferences")
@Entity
@Table(name = "EMAIL_PREFERENCES")
@JsonIgnoreProperties(ignoreUnknown = true, value = {"hibernateLazyInitializer", "handler"})
@Audited
public class EmailPreferences {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private int id;

    @Column(name = "USER_ID")
    private String userId;

    @Column(name = "EMAIL_SUBJECT")
    private String emailSubject;

    @Column(name = "SENDER_COMPANY")
    private String senderCompany;

    @Column(name = "IMPORTANT_TAGS")
    private String importantTags;

    @Column(name = "FONT_FAMILY")
    private String fontFamily;

    @Column(name = "FONT_SIZE")
    private String fontSize;

    @Column(name = "EMAIL_SENDER")
    private String emailSender;

    @Column(name = "BLACK_LISTED_SENDER")
    private String blackListedSender;

    @Column(name = "BLACK_LISTED_DOMAIN")
    private String blackListedDomain;

    @Column(name = "BLACK_LISTED_SUBJECT")
    private String blackListedSubject;

    @Column(name = "TIME_ZONE")
    private String timeZone;

    @Column(name = "KEYBOARD_SHORTCUTS")
    private String keyboardShortcuts;


    @Column(name = "CREATED_TIME")
    private Date createdTime;

    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;

    @Column(name = "CONVERSATION_ID")
    private String conversationId;

    @Column(name = "CONTACT_NUMBER")
    private String contactNumber;

    @Column(name = "CHECKIN")
    private LocalTime checkin;

    @Column(name = "CHECKOUT")
    private LocalTime checkout;

    @Column(name = "FONT_COLOR")
    private String fontColor;

    @Column(name = "DISPLAY_NAME")
    private String displayName;

    @Transient
    private LocalDateTime lastPollTime;

    @Transient
    private LocalDateTime nextPollTime;

    @Column(name = "MASK_CONTENT")
    private Boolean maskContent=false;


    @Column(name = "MEETING_TYPE")
    private String meetingType="Teams";

    @Column(name  = "DEBUG_MODE")
    private Boolean debugMode;

    @Column(name  = "ALLOW_NOTIFICATION")
    private Boolean allowNotification = false;

    @Column(name = "DEVICE_ID")
    private String deviceId;

    @Column(name = "GCM_ID")
    private String gcmId;

    @Column(name  = "ON_DEMAND_ENABLED")
    private Boolean onDemandEnabled=false;

    @Column(name  = "PREFERRED_MEETING_DURATION")
    private Integer preferredMeetingDuration = 30;

    @Column(name  = "DATE_FORMAT")
    private String dateFormat = "DD-MMM-YY";

    @Column(name  = "TIME_FORMAT")
    private String timeFormat = "24Hrs";

    @Column(name = "IS_CATEGORY_ENABLED")
    private Boolean isCategoryEnabled = true;

    @Column(name = "IS_PRIORITY_ENABLED")
    private Boolean isPriorityEnabled = true;

    @Column(name = "IS_TONE_ENABLED")
    private Boolean isToneEnabled = true;



}
