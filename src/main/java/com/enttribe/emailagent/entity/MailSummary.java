package com.enttribe.emailagent.entity;

import com.enttribe.emailagent.dto.Attachments;
import com.enttribe.emailagent.utils.EmailSummaryEncryptor;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * The type Mail summary.
 * <AUTHOR> Sonsale
 */
@Entity
@Getter
@Setter
@Table(name = "MAIL_SUMMARY")
@ToString(exclude = {"userActions"})
public class MailSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "CONVERSATION_ID")
    private String conversationId;

    @Column(name = "MESSAGE_ID")
    private String messageId;

    @Convert(converter = EmailSummaryEncryptor.class)
    @Column(name = "MESSAGE_SUMMARY")
    private String messageSummary;

    @Column(name = "MEETING_PREVIEW")
    private String meetingPreview;

    @Column(name = "CATEGORY")
    private String category;

    @Column(name = "PRIORITY")
    private String priority;

    @Column(name = "TAG")
    private String tag;

    @Column(name = "STAR_MARKED")
    private Boolean starMarked = false;

    @Column(name = "DELETED")
    private Boolean deleted = false;

    @Column(name = "STAR_MARK_REASON")
    private String starMarkReason;

    @Column(name = "FLAG_STATUS")
    private String flagStatus;

    @Column(name = "TYPE")
    private String type;

    @Column(name = "MAIL_RECEIVED_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime mailReceivedTime;

    @Column(name = "USER_ID")
    private String userId;

    @Column(name = "FROM_USER")
    private String fromUser;

    @Column(name = "TO_USER")
    private String toUser;

    @Column(name = "CC_USER")
    private String ccUser;

    @Column(name = "CREATED_TIME")
    private Date createdTime;

    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;

    @Column(name = "OBJECTIVE")
    private String objective;

    @Convert(converter = EmailSummaryEncryptor.class)
    @Column(name = "SUBJECT")
    private String subject;

    @Column(name = "ACTION_OWNER")
    private String actionOwner;

    @Column(name = "ACTION_CLEAR_REASON")
    private String actionClearReason;

    @Column(name = "INTERNET_MESSAGE_ID")
    private String internetMessageId;

    @Column(name = "ACTION_TAKEN")
    private boolean actionTaken;

    @Transient
    private Boolean hasAttachment;

    @Transient
    private Boolean isUnread=false;


    @Transient
    private List<Attachments> attachmentsList;

    @Transient
    private Boolean isHighPriority;

    @Transient
    private Boolean isStarMarked;

    @Transient
    private Boolean isAttention;

    @Transient
    private Boolean isActionable;

    @Column(name = "CATEGORY_REASON")
    private String categoryReason;

    @Column(name = "PRIORITY_REASON")
    private String priorityReason;

    @Column(name = "EMAIL_TONE")
    private String emailTone;

    @Column(name = "EMAIL_TONE_REASON")
    private String emailToneReason;

    @Column(name = "DECRYPT_SUMMARY")
    private String decryptSummary;

    @Column(name = "FOLDER_NAME")
    private String folderName;

    @OneToMany(mappedBy = "mailSummary", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonManagedReference
    private Set<UserActions> userActions = new HashSet<>();

    @Enumerated(EnumType.STRING)
    @Column(name = "NOTIFICATION_STATUS")
    private NotificationStatus notificationStatus;

    public enum NotificationStatus {
        SENT, NOT_SENT, NOT_ALLOWED, FAILED
    }

}
