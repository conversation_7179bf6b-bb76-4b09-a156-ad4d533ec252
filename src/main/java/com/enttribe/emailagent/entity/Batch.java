package com.enttribe.emailagent.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * The type Batch.
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "BATCH")
@ToString
public class Batch {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "QUEUE_NAME")
    private String queueName;

    @Column(name = "BATCH_ID")
    private String batchId;
}
