package com.enttribe.emailagent;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

import com.enttribe.emailagent.exception.BusinessException;

import java.io.StringWriter;
import java.util.Properties;

/**
 * The type Velocity template util.
 *  <AUTHOR>
 *
 */
public class VelocityTemplateUtil {

    private VelocityTemplateUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    static {
        Properties props = new Properties();
        props.setProperty("resource.loader", "file");
        props.setProperty("file.resource.loader.class", "org.apache.velocity.runtime.resource.loader.FileResourceLoader");
     props.setProperty("file.resource.loader.path", "/Users/<USER>/Downloads/emailagent/src/main/resources/templates");
        Velocity.init(props);
    }

    public static String generateSystemPrompt(String type) throws BusinessException {
        VelocityContext context = new VelocityContext();
        Template template = null;
        if (type.equalsIgnoreCase("Objectives")) {
            template = Velocity.getTemplate("Systemprompt.vm");
        }  else if (type.equalsIgnoreCase("Profile")) {
            template = Velocity.getTemplate("SystempromptProfile.vm");
        }

        else if (type.equalsIgnoreCase("Category")) {
            template = Velocity.getTemplate("SystempromptCategory.vm");
        } else if (type.equalsIgnoreCase("MailSummary")) {
            template = Velocity.getTemplate("SystemPromptMailSummary.vm");
        } else if (type.equalsIgnoreCase("ThreadSummary")) {
            template = Velocity.getTemplate("SystemPromptThreadSummary.vm");
        } else if (type.equalsIgnoreCase("Draft")) {
            template = Velocity.getTemplate("SystemprompDraft.vm");
        } else if (type.equalsIgnoreCase("fresh")) {
            template = Velocity.getTemplate("SystempromptFreshEmail.vm");
        } else if (type.equalsIgnoreCase("freshImprove")) {
            template = Velocity.getTemplate("SystemPromptFreshImproveEmail.vm");
        } else if (type.equalsIgnoreCase("Intent")) {
            template = Velocity.getTemplate("SystemPrompIntentSearch.vm");
        } else if (type.equalsIgnoreCase("ConversationDetail")) {
            template = Velocity.getTemplate("SystemPromptConversationDetail.vm");
        } else if (type.equalsIgnoreCase("Build")) {
            template = Velocity.getTemplate("SystemPromptBuildParameters.vm");
        } else if (type.equalsIgnoreCase("BackendBuild")) {
            template = Velocity.getTemplate("SystemPromptBuildParameters.vm");
        } else if (type.equalsIgnoreCase("UiBuild")) {
            template = Velocity.getTemplate("SystemPromptBuildParametersMf.vm");
        } else if (type.equalsIgnoreCase("Approval")) {
            template = Velocity.getTemplate("SystemPromptBuildApproval.vm");
        } else if (type.equalsIgnoreCase("improveDraft")) {
            template = Velocity.getTemplate("SystemPromptImproveDraft.vm");
        } else if (type.equalsIgnoreCase("improveSelection")) {
            template = Velocity.getTemplate("SystemPromptImproveSelection.vm");
        }

        StringWriter writer = new StringWriter();
        template.merge(context, writer);

        return writer.toString();
    }

    public static String generateUserPrompt(String emailContent, String conversationHistory, String fromEmail, String user, String type, String extra1, String extra2) {
        VelocityContext context = new VelocityContext();
        Template template = null;
        if (emailContent != null)
            context.put("emailContent", emailContent);
        if (conversationHistory != null)
            context.put("conversationHistory", conversationHistory);
        if (fromEmail != null)
            context.put("fromEmail", fromEmail);
        if (user != null)
            context.put("emailUser", user);
        if (extra1 != null)
            context.put("extra1", extra1);
        if (extra2 != null)
            context.put("extra2", extra2);


        if (type.equalsIgnoreCase("Objectives")) {
            template = Velocity.getTemplate("UserpromptForObjective.vm");
        } else if (type.equalsIgnoreCase("Category")) {
            template = Velocity.getTemplate("UserpromptForCategory.vm");

        } else if (type.equalsIgnoreCase("MailSummary")) {
            template = Velocity.getTemplate("UserpromptForMailSummary.vm");

        } else if (type.equalsIgnoreCase("ThreadSummary")) {
            template = Velocity.getTemplate("UserpromptForThreadSummary.vm");

        } else if (type.equalsIgnoreCase("Draft")) {
            template = Velocity.getTemplate("UserpromptForDraft.vm");

        } else if (type.equalsIgnoreCase("fresh")) {
            template = Velocity.getTemplate("UserpromptFreshEmail.vm");
        } else if (type.equalsIgnoreCase("freshImprove")) {
            template = Velocity.getTemplate("UserPropmtFreshImprove.vm");
        } else if (type.equalsIgnoreCase("Intent")) {
            template = Velocity.getTemplate("UserpromptForIntentSearch.vm");
        } else if (type.equalsIgnoreCase("ConversationDetail")) {
            template = Velocity.getTemplate("UserPromptConversationDetail.vm");
        } else if (type.equalsIgnoreCase("BackendBuild")) {
            template = Velocity.getTemplate("UserPromptBuildParameters.vm");
        } else if (type.equalsIgnoreCase("UiBuild")) {
            template = Velocity.getTemplate("UserPromptBuildParameters.vm");
        } else if (type.equalsIgnoreCase("Approval")) {
            template = Velocity.getTemplate("UserPromptForBuildApproval.vm");
        } else if (type.equalsIgnoreCase("improveDraft")) {
            template = Velocity.getTemplate("UserPromptImproveDraft.vm");
        } else if (type.equalsIgnoreCase("improveSelection")) {
            template = Velocity.getTemplate("UserPromptImproveSelection.vm");
        }


        StringWriter writer = new StringWriter();
        template.merge(context, writer);

        return writer.toString();
    }
}
