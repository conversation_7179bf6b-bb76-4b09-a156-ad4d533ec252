package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.wrapper.MessageWrapper;
import microsoft.exchange.webservices.data.core.exception.service.local.ServiceLocalException;
import microsoft.exchange.webservices.data.core.service.item.EmailMessage;
import microsoft.exchange.webservices.data.property.complex.EmailAddress;
import org.apache.commons.lang3.StringEscapeUtils;
import java.util.Base64;
import java.util.List;

/**
 * Utility class for performing common operations related to email handling in the mobile context.
 *
 *  <AUTHOR>
 */
public class MobileUtils {

    /**
     * Encodes a given string into HTML format.
     *
     * @param body The string to be encoded.
     * @return The HTML-encoded string.
     */
    public static String encodeHTMLString(String body) {
        return StringEscapeUtils.escapeHtml4(body);
    }

    /**
     * Encodes a given string using Base64 encoding.
     *
     * @param body The string to be encoded.
     * @return The Base64-encoded string.
     */
    public static String encodeBase64(String body) {
        return Base64.getEncoder().encodeToString(body.getBytes());
    }


    /**
     * Converts a list of {@link EmailAddress} objects into a list of email address strings.
     *
     * @param emailAddresses The list of {@link EmailAddress} objects to be converted.
     * @return A list of strings containing the email addresses.
     */
    public static List<String> getEmailList(List<EmailAddress> emailAddresses) {
        return emailAddresses.stream().map(EmailAddress::getAddress).toList();
    }

}
