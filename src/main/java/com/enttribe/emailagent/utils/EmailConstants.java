package com.enttribe.emailagent.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * The type Email constants.
 *  <AUTHOR> Sonsale
 */
public class EmailConstants {

    public static final String RESULT = "result";
    public static final String EVENT_ID = "eventId";

    public static final String RESCHEDULE_REASON = "rescheduleReason";
    public static final String RECURRENCE_START_DATE = "recurrenceStartDate";
    public static final String DAY_OF_MONTH = "dayOfMonth";

    public static final String RANGE_TYPE = "rangeType";
    public static final String INTERVAL = "interval";
    public static final String RECURRENCE_END_DATE = "recurrenceEndDate";

    public static final String IS_RECURRING = "isRecurring";

    public static final String RECURRENCE_TYPE = "recurrenceType";

    public static final String DAYS_OF_WEEK = "daysOfWeek";



    public static final String EMAIL_KEY = "email";
    public static final String PROMPT_CONTENT = "content";
    public static final String HTTP_JSON_HEADER = "application/json";
    public static final String EMAIL_IS_NULL = "{\"email\":null}";
    public static final String MAIL_IMAP_PORT = "mail.imap.port";
    public static final String MAIL_IMAP_HOST = "mail.imap.host";
    public static final String MAIL_IMAP_STARTTLS_ENABLE = "mail.imap.starttls.enable";
    public static final String MAIL_IMAP_SSL_TRUST = "mail.imap.ssl.trust";
    public static final String IMAPS = "imaps";
    public static final String PREVIOUS_DRAFT = "previousDraft";
    public static final String INBOX = "INBOX";
    public static final String CONVERSATION_ID = "conversationId";
    public static final String ACTION_OWNER = "actionOwner";
    public static final String SUMMARY = "Summary";
    public static final String MEETING = "Meeting";
    public static final String CATEGORY = "Category";
    public static final String MATCHED_WITH_PREFERENCES = " matched with preferences";
    public static final String ATTENTION = "Attention";
    public static final String PREVIOUS_PROMPT = "previous prompt";
    public static final String THERE_IS_NO_SLOT_AVAILABLE_IN_WORKING_HOURS = "There is no slot available in working hours";
    public static final String DESCRIPTION = "description";
    public static final String INTENT = "intent";
    public static final String SEARCH_TERM = "searchTerm";
    public static final String EMAIL = "Email";
    public static final String TAGGED_MANUALLY = "Tagged Manually";
    public static final String SENDER_SPACE_AFTER = "Sender ";
    public static final String COMPANY_DOMAIN = "Company Domain ";
    public static final String EVENT = "Event";
    public static final String GMAIL = "Gmail";
    public static final String EMAIL_CONTENT = "emailContent";
    public static final String EMAIL_USER = "emailUser";
    public static final String USER_QUERY = "query";
    public static final String FORMAT = "format";
    public static final String SENDER = "sender";
    public static final String LENGTH = "length";
    public static final String OBJECTIVE = "objective";
    public static final String USER_NAME = "userName";
    public static final String PERVIOUS_PROMPT = "previous prompt";
    public static final String TIME_ZONE = "timeZone";
    public static final String ASIA_KOLKATA_TIME_ZONE = "Asia/Kolkata";
    public static final String TODAY = "today";
    public static final String YESTERDAY = "yesterday";
    public static final String MAIL_OBJECT = "mailObject";
    public static final String SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED = "<span class=\"title-text-highlighted\">";
    public static final String ADMIN = "admin";
    public static final String API_TOKEN = "115203607bad24c0594afb75b101b32930";
    public static final String AUTHORIZATION = "Authorization";
    public static final String BEARER = "Bearer ";
    public static final String BASIC = "Basic ";
    public static final String STATUS = "status";
    public static final String SUCCESS = "success";
    public static final String USER_ID = "5e036f3b-88df-429e-b862-ad9678518cc1";
    public static final String FAILURE = "FAILURE";
    public static final String MESSAGE = "message";
    public static final String UTF_8 = "UTF-8";
    public static final String ERROR_WAITING_FOR_BUILD = "Error waiting for build";
    public static final String THERE_IS_ON_PREFERENCE_RECORD_AVAILABLE_FOR_THE_USER = "There is no preference record available for the user.";
    public static final String DOMAIN = "domain";
    public static final String WRONG_TYPE_IS_PROVIDED = "Wrong type is provided";
    public static final String PRIORITY = "priority";
    public static final String FAILED = "failed";
    public static final String DECLINED ="declined";
    public static final String ACCEPTED ="accepted";
    public static final String TENTATIVE ="tentative";
    public static final String ZOOM = "Zoom";
    public static final String VALUE = "value";
    public static final String OUTLOOK_BODY_CONTENT_TYPE_TEXT = "outlook.body-content-type=\"text\"";
    public static final String PREFER = "Prefer";
    public static final String AND = " and ";
    public static final String EMAIL_DOES_NOT_EXIST_IN_OUR_STRING = "Email does not exist in our records.";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String APPLICATION_JSON = "application/json";
    public static final String SUBJECT = "subject";
    public static final String MESSAGE_ID ="messageId";
    public static final String BODY = "body";
    public static final String CONTENTTYPE = "contentType";
    public static final String CONTENT = "content";
    public static final String BCC_EMAIL ="bccEmail";
    public static final String TO_EMAIL ="toEmail";
    public static final String CC_EMAIL ="ccEmail";
    public static final String ATTACHMENT_PATH="attachmentPath";
    public static final String ATTACHMENT_LIST="attachmentList";
    public static final String ADDRESS = "address";
    public static final String EMAIL_ADDRESS = "emailAddress";
    public static final String SCHEDULED_START_DATE_TIME = "scheduledStartDateTime";
    public static final String DATE_TIME = "dateTime";
    public static final String SCHEDULED_END_DATE_TIME = "scheduledEndDateTime";
    public static final String DISPLAY_NAME = "displayName";
    public static final String ID = "id";
    public static final String DRAFTS = "Drafts";
    public static final String OUTBOX = "Outbox";
    public static final String GET_MESSAGE = "GET_MESSAGE";
    public static final String ARCHIVE = "Archive";
    public static final String INVALID_ARGUMENT_PROVIDED_FOR_FAILURELOGS_CREATION = "Invalid argument provided for FailureLogs creation";
    public static final String INVALID_ARGUMENT_PROVIDED = "Invalid argument provided";
    public static final String DATABASE_ACCESS_ERROR_WHILE_SAVING_FAILURELOGS = "Database access error while saving FailureLogs";
    public static final String DATABASE_ACCESS_ERROR = "Database access error";
    public static final String UNEXPECTED_ERROR_OCCURRED_WHILE_PROCESSING_FAILURELOGS = "Unexpected error occurred while processing FailureLogs";
    public static final String AN_UNEXPECTED_ERROR_OCCURRED = "An unexpected error occurred";
    public static final String FAILURELOGS_NOT_FOUND_FOR_THIS_ID = "FailureLog not found for this id :: ";
    public static final String MEETING_START_TIME = "meetingStartTime";
    public static final String MEETING_END_TIME = "meetingEndTime";
    public static final String REQUIRED_ATTENDEES = "requiredAttendees";
    public static final String RECURRENCE_RULE = "recurrenceRule";
    public static final String OPTIONAL_ATTENDEES = "optionalAttendees";
    public static final String TEAM = "Teams";
    public static final String MEETING_TYPE = "meetingType";
    public static final String LOCATION = "location";
    public static final String LOCATION_URL = "locationUri";
    public static final String YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String YYYY_MM_DD_T_HH_MM_SS_Z = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    public static final String THE_REQUEST_BODY_CAN_NOT_BE_NULL = "The request body can not be null.";


    public static String getMeetingMessage() {
    	List<String> messageList=new ArrayList<String>();
    	messageList.add("Freedom! No meetings");
    	messageList.add("Alert: Zero meetings left. Celebrate!");
    	messageList.add("Clear skies ahead, no meetings in sight!");
    	messageList.add("No meetings. Your time, your rules!");
    	messageList.add("Rejoice! Meetings have left the building!");
    	messageList.add("Guess what? No meetings to trap you!");
    	messageList.add("Relax! The calendar is finally empty");
    	Random random = new Random();
        int index = random.nextInt(messageList.size());
        return messageList.get(index);
    }
    
    public static String getActionItemMessage() {
    	List<String> messageList=new ArrayList<String>();
    	messageList.add("No more actions! Time for fun!");
    	messageList.add("You did it! No tasks left!");
    	messageList.add("All actions done. You’ve earned a break!");
    	messageList.add("Free at last! No pending actions!");
    	messageList.add("You're free of actions! Enjoy the calm");
    	messageList.add("Treat yourself! No more actions left");
    	messageList.add("No more tasks! Just chill!");
    	messageList.add("No pending actions. Grab some coffee!");
    	Random random = new Random();
        int index = random.nextInt(messageList.size());
        return messageList.get(index);
    	
    }


    public static  List<String> getListOftimezones(){
        List<String> timeZoneList = List.of(
           "Africa/Abidjan", "Africa/Accra", "Africa/Addis_Ababa", "Africa/Algiers", "Africa/Asmera", 
            "Africa/Bamako", "Africa/Bangui", "Africa/Banjul", "Africa/Bissau", "Africa/Blantyre", 
            "Africa/Brazzaville", "Africa/Bujumbura", "Africa/Cairo", "Africa/Casablanca", "Africa/Ceuta", 
            "Africa/Conakry", "Africa/Dakar", "Africa/Dar_es_Salaam", "Africa/Djibouti", "Africa/Douala", 
            "Africa/El_Aaiun", "Africa/Freetown", "Africa/Gaborone", "Africa/Harare", "Africa/Johannesburg", 
            "Africa/Juba", "Africa/Kampala", "Africa/Khartoum", "Africa/Kigali", "Africa/Kinshasa", 
            "Africa/Lagos", "Africa/Libreville", "Africa/Lome", "Africa/Luanda", "Africa/Lubumbashi", 
            "Africa/Lusaka", "Africa/Malabo", "Africa/Maputo", "Africa/Maseru", "Africa/Mbabane", 
            "Africa/Mogadishu", "Africa/Monrovia", "Africa/Nairobi", "Africa/Ndjamena", "Africa/Niamey", 
            "Africa/Nouakchott", "Africa/Ouagadougou", "Africa/Porto-Novo", "Africa/Sao_Tome", "Africa/Tripoli", 
            "Africa/Tunis", "Africa/Windhoek", "America/Adak", "America/Anchorage", "America/Anguilla", 
            "America/Antigua", "America/Araguaina", "America/Argentina/La_Rioja", "America/Argentina/Rio_Gallegos", 
            "America/Argentina/Salta", "America/Argentina/San_Juan", "America/Argentina/San_Luis", 
            "America/Argentina/Tucuman", "America/Argentina/Ushuaia", "America/Aruba", "America/Asuncion", 
            "America/Bahia", "America/Bahia_Banderas", "America/Barbados", "America/Belem", "America/Belize", 
            "America/Blanc-Sablon", "America/Boa_Vista", "America/Bogota", "America/Boise", "America/Buenos_Aires", 
            "America/Cambridge_Bay", "America/Campo_Grande", "America/Cancun", "America/Caracas", "America/Catamarca", 
            "America/Cayenne", "America/Cayman", "America/Chicago", "America/Chihuahua", "America/Ciudad_Juarez", 
            "America/Coral_Harbour", "America/Cordoba", "America/Costa_Rica", "America/Creston", "America/Cuiaba", 
            "America/Curacao", "America/Danmarkshavn", "America/Dawson", "America/Dawson_Creek", "America/Denver", 
            "America/Detroit", "America/Dominica", "America/Edmonton", "America/Eirunepe", "America/El_Salvador", 
            "America/Fort_Nelson", "America/Fortaleza", "America/Glace_Bay", "America/Godthab", "America/Goose_Bay", 
            "America/Grand_Turk", "America/Grenada", "America/Guadeloupe", "America/Guatemala", "America/Guayaquil", 
            "America/Guyana", "America/Halifax", "America/Havana", "America/Hermosillo", "America/Indiana/Knox", 
            "America/Indiana/Marengo", "America/Indiana/Petersburg", "America/Indiana/Tell_City", "America/Indiana/Vevay", 
            "America/Indiana/Vincennes", "America/Indiana/Winamac", "America/Indianapolis", "America/Inuvik", 
            "America/Iqaluit", "America/Jamaica", "America/Jujuy", "America/Juneau", "America/Kentucky/Monticello", 
            "America/Kralendijk", "America/La_Paz", "America/Lima", "America/Los_Angeles", "America/Louisville", 
            "America/Lower_Princes", "America/Maceio", "America/Managua", "America/Manaus", "America/Marigot", 
            "America/Martinique", "America/Matamoros", "America/Mazatlan", "America/Mendoza", "America/Menominee", 
            "America/Merida", "America/Metlakatla", "America/Mexico_City", "America/Miquelon", "America/Moncton", 
            "America/Monterrey", "America/Montevideo", "America/Montserrat", "America/Nassau", "America/New_York", 
            "America/Nome", "America/Noronha", "America/North_Dakota/Beulah", "America/North_Dakota/Center", 
            "America/North_Dakota/New_Salem", "America/Ojinaga", "America/Panama", "America/Paramaribo", 
            "America/Phoenix", "America/Port-au-Prince", "America/Port_of_Spain", "America/Porto_Velho", 
            "America/Puerto_Rico", "America/Punta_Arenas", "America/Rankin_Inlet", "America/Recife", "America/Regina", 
            "America/Resolute", "America/Rio_Branco", "America/Santarem", "America/Santiago", "America/Santo_Domingo", 
            "America/Sao_Paulo", "America/Scoresbysund", "America/Sitka", "America/St_Barthelemy", "America/St_Johns", 
            "America/St_Kitts", "America/St_Lucia", "America/St_Thomas", "America/St_Vincent", "America/Swift_Current", 
            "America/Tegucigalpa", "America/Thule", "America/Tijuana", "America/Toronto", "America/Tortola", 
            "America/Vancouver", "America/Whitehorse", "America/Winnipeg", "America/Yakutat", "Antarctica/Casey", 
            "Antarctica/Davis", "Antarctica/DumontDUrville", "Antarctica/Macquarie", "Antarctica/Mawson", 
            "Antarctica/McMurdo", "Antarctica/Palmer", "Antarctica/Rothera", "Antarctica/Syowa", "Antarctica/Troll", 
            "Antarctica/Vostok", "Arctic/Longyearbyen", "Asia/Aden", "Asia/Almaty", "Asia/Amman", "Asia/Anadyr", 
            "Asia/Aqtau", "Asia/Aqtobe", "Asia/Ashgabat", "Asia/Atyrau", "Asia/Baghdad", "Asia/Bahrain", "Asia/Baku", 
            "Asia/Bangkok", "Asia/Barnaul", "Asia/Beirut", "Asia/Bishkek", "Asia/Brunei", "Asia/Chita", "Asia/Choibalsan",
      "Asia/Colombo",
      "Asia/Damascus",
      "Asia/Dhaka",
      "Asia/Dili",
      "Asia/Dubai",
      "Asia/Dushanbe",
      "Asia/Famagusta",
      "Asia/Gaza",
      "Asia/Hebron",
      "Asia/Hong_Kong",
      "Asia/Hovd",
      "Asia/Irkutsk",
      "Asia/Jakarta",
      "Asia/Jayapura",
      "Asia/Jerusalem",
      "Asia/Kabul",
      "Asia/Kamchatka",
      "Asia/Karachi",
      "Asia/Katmandu",
      "Asia/Khandyga",
      "Asia/Kolkata",
      "Asia/Krasnoyarsk",
      "Asia/Kuala_Lumpur",
      "Asia/Kuching",
      "Asia/Kuwait",
      "Asia/Macau",
      "Asia/Magadan",
      "Asia/Makassar",
      "Asia/Manila",
      "Asia/Muscat",
      "Asia/Nicosia",
      "Asia/Novokuznetsk",
      "Asia/Novosibirsk",
      "Asia/Omsk",
      "Asia/Oral",
      "Asia/Phnom_Penh",
      "Asia/Pontianak",
      "Asia/Pyongyang",
      "Asia/Qatar",
      "Asia/Qostanay",
      "Asia/Qyzylorda",
      "Asia/Rangoon",
      "Asia/Riyadh",
      "Asia/Saigon",
      "Asia/Sakhalin",
      "Asia/Samarkand",
      "Asia/Seoul",
      "Asia/Shanghai",
      "Asia/Singapore",
      "Asia/Srednekolymsk",
      "Asia/Taipei",
      "Asia/Tashkent",
      "Asia/Tbilisi",
      "Asia/Tehran",
      "Asia/Thimphu",
      "Asia/Tokyo",
      "Asia/Tomsk",
      "Asia/Ulaanbaatar",
      "Asia/Urumqi",
      "Asia/Ust-Nera",
      "Asia/Vientiane",
      "Asia/Vladivostok",
      "Asia/Yakutsk",
      "Asia/Yekaterinburg",
      "Asia/Yerevan",
      "Atlantic/Azores",
      "Atlantic/Bermuda",
      "Atlantic/Canary",
      "Atlantic/Cape_Verde",
      "Atlantic/Faeroe",
      "Atlantic/Madeira",
      "Atlantic/Reykjavik",
      "Atlantic/South_Georgia",
      "Atlantic/St_Helena",
      "Atlantic/Stanley",
      "Australia/Adelaide",
      "Australia/Brisbane",
      "Australia/Broken_Hill",
      "Australia/Darwin",
      "Australia/Eucla",
      "Australia/Hobart",
      "Australia/Lindeman",
      "Australia/Lord_Howe",
      "Australia/Melbourne",
      "Australia/Perth",
      "Australia/Sydney",
      "Europe/Amsterdam",
      "Europe/Andorra",
      "Europe/Astrakhan",
      "Europe/Athens",
      "Europe/Belgrade",
      "Europe/Berlin",
      "Europe/Bratislava",
      "Europe/Brussels",
      "Europe/Bucharest",
      "Europe/Budapest",
      "Europe/Busingen",
      "Europe/Chisinau",
      "Europe/Copenhagen",
      "Europe/Dublin",
      "Europe/Gibraltar",
      "Europe/Guernsey",
      "Europe/Helsinki",
      "Europe/Isle_of_Man",
      "Europe/Istanbul",
      "Europe/Jersey",
      "Europe/Kaliningrad",
      "Europe/Kiev",
      "Europe/Kirov",
      "Europe/Lisbon",
      "Europe/Ljubljana",
      "Europe/London",
      "Europe/Luxembourg",
      "Europe/Madrid",
      "Europe/Malta",
      "Europe/Mariehamn",
      "Europe/Minsk",
      "Europe/Monaco",
      "Europe/Moscow",
      "Europe/Oslo",
      "Europe/Paris",
      "Europe/Podgorica",
      "Europe/Prague",
      "Europe/Riga",
      "Europe/Rome",
      "Europe/Samara",
      "Europe/San_Marino",
      "Europe/Sarajevo",
      "Europe/Saratov",
      "Europe/Simferopol",
      "Europe/Skopje",
      "Europe/Sofia",
      "Europe/Stockholm",
      "Europe/Tallinn",
      "Europe/Tirane",
      "Europe/Ulyanovsk",
      "Europe/Vaduz",
      "Europe/Vatican",
      "Europe/Vienna",
      "Europe/Vilnius",
      "Europe/Volgograd",
      "Europe/Warsaw",
      "Europe/Zagreb",
      "Europe/Zurich",
      "Indian/Antananarivo",
      "Indian/Chagos",
      "Indian/Christmas",
      "Indian/Cocos",
      "Indian/Comoro",
      "Indian/Kerguelen",
      "Indian/Mahe",
      "Indian/Maldives",
      "Indian/Mauritius",
      "Indian/Mayotte",
      "Indian/Reunion",
      "Pacific/Apia",
      "Pacific/Auckland",
      "Pacific/Bougainville",
      "Pacific/Chatham",
      "Pacific/Easter",
      "Pacific/Efate",
      "Pacific/Enderbury",
      "Pacific/Fakaofo",
      "Pacific/Fiji",
      "Pacific/Funafuti",
      "Pacific/Galapagos",
      "Pacific/Gambier",
      "Pacific/Guadalcanal",
      "Pacific/Guam",
      "Pacific/Honolulu",
      "Pacific/Kiritimati",
      "Pacific/Kosrae",
      "Pacific/Kwajalein",
      "Pacific/Majuro",
      "Pacific/Marquesas",
      "Pacific/Midway",
      "Pacific/Nauru",
      "Pacific/Niue",
      "Pacific/Norfolk",
      "Pacific/Noumea",
      "Pacific/Pago_Pago",
      "Pacific/Palau",
      "Pacific/Pitcairn",
      "Pacific/Ponape",
      "Pacific/Port_Moresby",
      "Pacific/Rarotonga",
      "Pacific/Saipan",
      "Pacific/Tahiti",
      "Pacific/Tarawa",
      "Pacific/Tongatapu",
      "Pacific/Truk",
      "Pacific/Wake",
      "Pacific/Wallis"
        );

        return timeZoneList;
    }
 
}
