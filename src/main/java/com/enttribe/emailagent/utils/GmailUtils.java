package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.UploadResponse;
import com.enttribe.emailagent.dto.UserEmailDto;
import com.enttribe.emailagent.dto.UserEmailResponseDto;
import com.enttribe.emailagent.wrapper.GmailMeetingWrapper;
import com.enttribe.emailagent.wrapper.MeetingWrapper;
import com.enttribe.emailagent.wrapper.MessageWrapper;
import com.google.api.client.util.DateTime;
import com.google.api.services.calendar.model.*;
import com.google.api.services.gmail.model.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

import com.google.api.services.calendar.model.Event;
import com.google.api.services.calendar.model.Event.Reminders;
import com.google.api.services.calendar.model.EventReminder;

import static com.enttribe.emailagent.utils.EmailConstants.*;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.strip;

//@Slf4j
public   class GmailUtils {

    private static final Logger log = LoggerFactory.getLogger(GmailUtils.class);

    public final static String APPLICATION_NAME="EMAIL_AGENT";
    public final static String DEFAULT_TIME_ZONE="Asia/Kolkata";
    public final static String LABEL_SHOW="labelShow";
    public final static String PRIMARY="primary";
    public final static String SHOW="show";
    public final static String RESULT="result";

    public final static String STARRED="STARRED";
    public final static String FLAGGED="flagged";
    public final static String NOT_FLAGGED="notFlagged";
    public final static String METEDATA="metadata";
    public final static String MY_CUSTOMER="my_customer";
    public final static String ACCEPTED = "accepted";
    public final static String CALENDAR="text/calendar";

    public final static String ATTACHMENT="attachment_";

    public final static String DASH="_";
    public final static String IMPORTANT="IMPORTANT";
    public final static String SLACE="/";
    public final static String HIGH="high";
    public final static String LOW="low";
    public final static String EMAIL="Email";
    public final static String FROM="From";
    public final static String TO="To";
    public final static String CC="Cc";
    public final static String BCC="Bcc";
    public final static String SUBJECT="Subject";
    public final static String MESSAGE_ID="Message-ID";
    public final static String TEXT_PLAN="text/plain";

    public final static String HTML_TEXT="text/html";
    public final static String MULTIPART_ALTERNATIVE="multipart/alternative";
    public final static String FILTER_AFTER="after:";
    public final static String SPACE=" ";
    public final static String FILTER_LABEL="label:";
    public final static String FILE="file";
    public final static String ICS_FILE_TYPE=".ics";
    public final static String ENABLED="enabled";
    public final static String DISABLED="disabled";
    public final static String ALL="all";
    public final static String INTERNAL_REPLY_MSG="internalReplyMessage";
    public final static String EXTERNAL_REPLY_MSG="externalReplyMessage";
    public final static String EXTERNAL_AUDIENCE="externalAudience";
    public final static String SEND_DRAFT="sendDraft";
    public final static String UNREAD="UNREAD";

    @Value("${documentUploadUrl}")
    private static String uploadUrl ="http://x101-ai-service.ansible.svc.cluster.local/api/knowledgeGraph/generate_document_embedding"; //="http://localhost:9441/api/knowledgeGraph/generate_document_embedding";
    public final static List<String> excludedLabels = Arrays.asList("IMPORTANT", "CHAT", "TRASH", "SPAM", "DRAFT", "CATEGORY_FORUMS", "CATEGORY_UPDATES","CATEGORY_PROMOTIONS","CATEGORY_SOCIAL","CATEGORY_PERSONAL","STARRED","UNREAD");
    public final static String CALENDAR_SCOPE="https://www.googleapis.com/auth/calendar";

    public static UserEmailDto parseGmailMessageToUserEmailDto(Message message) {
        UserEmailDto emailDto = new UserEmailDto();
        log.info("message inside parseGmailMessageToUserEmailDto is  {}",message);
        emailDto.setId(message.getId());
        setImportance(emailDto, message);
        emailDto.setConversationId(message.getThreadId());
        emailDto.setCreatedTime(new Date(message.getInternalDate()));
        emailDto.setHasAttachments(checkForAttachments(message));
        parseHeaders(emailDto, message.getPayload().getHeaders());
        emailDto.setBody(parseBody(message));
        emailDto.setType("Email");
        log.info("emailDto is {}", emailDto.toString());
        return emailDto;
    }


    public static Long convertDateStringToLong(String dateString) {
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateString, DateTimeFormatter.ISO_ZONED_DATE_TIME);
            return zonedDateTime.toInstant().toEpochMilli();
      }


    public static Long convertDateStringToLong(String dateString, String dateFormat, String timeZone) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
        LocalDateTime localDateTime = LocalDateTime.parse(dateString, formatter);
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of(timeZone));
        return zonedDateTime.toInstant().toEpochMilli();
    }

    public static String convertEpochMillisToUTC(long epochMillis) {
       try {
        log.info("epochMillis is {}",epochMillis);
        if (Objects.isNull(epochMillis) || epochMillis < 0) return null;
        Instant instant = Instant.ofEpochMilli(epochMillis);
        ZonedDateTime utcDateTime = instant.atZone(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        return utcDateTime.format(formatter);
       }catch (Exception e){
           log.error("getting error inside convertEpochMillisToUTC {}",epochMillis);
           return null;
       }
    }

    public static  Map<String, Object> convertDateStringToLong(VacationSettings vacationSettings) {
        Map<String, Object> map=new HashMap<>();
        log.info("vacationSettings is {}",vacationSettings);
        map.put(EXTERNAL_AUDIENCE,ALL);
        map.put(INTERNAL_REPLY_MSG,vacationSettings.getResponseSubject());
        map.put(EXTERNAL_REPLY_MSG,vacationSettings.getResponseBodyPlainText());
        map.put(EmailConstants.STATUS, vacationSettings.getEnableAutoReply()?ENABLED:DISABLED);
        setStartDateWithHandle(map,vacationSettings);
        setEndDateWithHandle(map,vacationSettings);
        return map;
    }

    public static Date parseDateFromString(String date, String timeZone) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS");
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(date, formatter.withZone(ZoneId.of(timeZone)));
        Instant instant = zonedDateTime.toInstant();
        return Date.from(instant);
    }

    private static void setStartDateWithHandle(Map<String, Object> map,VacationSettings vacationSettings){
        try {
            map.put(EmailConstants.SCHEDULED_END_DATE_TIME,convertEpochMillisToUTC(vacationSettings.getEndTime()));
        }catch (Exception e){
            log.error("error inside setStartDateWithHandle");
            map.put(EmailConstants.SCHEDULED_END_DATE_TIME,null);
        }
    }

    private static void setEndDateWithHandle(Map<String, Object> map,VacationSettings vacationSettings){
        try {
            map.put(EmailConstants.SCHEDULED_START_DATE_TIME,convertEpochMillisToUTC(vacationSettings.getStartTime()));
        }catch (Exception e){
            log.error("error inside setStartDateWithHandle");
            map.put(EmailConstants.SCHEDULED_START_DATE_TIME,null);
        }
    }

    public static List<String> splitStringIfContainsSlash(String input) {
        return input.contains(SLACE) ? List.of(input.split(SLACE)) : List.of(input);
    }

    public static boolean containsSlash(String input) {
        return input != null && input.contains(SLACE);
    }

    public static boolean checkFileIsICS(String fileName) {
        return fileName != null && fileName.endsWith(ICS_FILE_TYPE);
    }


    public static String getHtmlBodyFromMessage(List<MessagePart> messageParts,Boolean isEncoded) {
        if (messageParts == null || messageParts.isEmpty()) {
            return null;
        }

        for (MessagePart part : messageParts) {
            String mimeType = part.getMimeType();
            log.info("mimeType is {}", mimeType);

            try {
                if ("text/html".equals(mimeType)) {
                    log.info("Found HTML content");
                    String base64Data = part.getBody().getData();
                    if(isEncoded) return base64Data;
                    if (base64Data != null) {
                        String standardBase64 = base64Data.replace('-', '+').replace('_', '/');
                        int padding = (4 - standardBase64.length() % 4) % 4;
                        standardBase64 += "=".repeat(padding);
                        byte[] decodedBytes = Base64.getDecoder().decode(standardBase64);
                        return new String(decodedBytes, StandardCharsets.UTF_8);
                    }
                } else if ("multipart/alternative".equals(mimeType)) {
                    log.info("Found multipart/alternative, checking sub-parts");
                    String htmlContent = getHtmlBodyFromMessage(part.getParts(),isEncoded);
                    if (htmlContent != null) {
                        return htmlContent;
                    }
                }
            } catch (Exception e) {
                log.error("Error processing part: {}", e.getMessage());
            }
        }
        return null;
    }

    private static void setImportance(UserEmailDto emailDto, Message message) {
        List<String> labelIds = message.getLabelIds();
        if (labelIds.contains(IMPORTANT)) emailDto.setImportance(HIGH);
        else emailDto.setImportance(LOW);
    }

    private static void parseHeaders(UserEmailDto emailDto, List<MessagePartHeader> headers) {
        for (MessagePartHeader header : headers) {
            switch (header.getName()) {
                case FROM:
                    emailDto.setFrom(parseRecipient(header.getValue()));
                    break;
                case TO:
                    emailDto.setToRecipients(parseRecipients(header.getValue()));
                    break;
                case CC:
                    emailDto.setCcRecipients(parseRecipients(header.getValue()));
                    break;
                case BCC:
                    emailDto.setBccRecipients(parseRecipients(header.getValue()));
                    break;
                case SUBJECT:
                    emailDto.setSubject(header.getValue());
                    break;
                case MESSAGE_ID:
                    emailDto.setInternetMessageId(header.getValue());
                    break;
                default:
                    break;
            }
        }
    }
    private static String parseBody(Message message) {
        String messageBody =null;
        try {
            if (message.getPayload().getParts() != null) {
                String textPart = null;
                String htmlPart = null;
                List<MessagePart> messageParts = message.getPayload().getParts();
                for (MessagePart messagePart : messageParts) {
                    if (TEXT_PLAN.equals(messagePart.getMimeType()) || MULTIPART_ALTERNATIVE.equals(messagePart.getMimeType())) {
                        textPart = messagePart.getBody().getData();
                    } else if (HTML_TEXT.equals(messagePart.getMimeType())) {
                        htmlPart = messagePart.getBody().getData();
                    }
                }
                if (textPart == null) {
                    log.info("textpart is null adding html part {}", htmlPart);
                    if (htmlPart != null) {
                        htmlPart = htmlPart.replace('-', '+').replace('_', '/');
                        messageBody = new String(Base64.getDecoder().decode(htmlPart));
                    }
                    return messageBody;
                }
                try {
                    messageBody = new String(Base64.getDecoder().decode(textPart));
                } catch (IllegalArgumentException e) {
                    log.info("IllegalArgumentException body is {}", messageBody);
                    messageBody = new String(Base64.getUrlDecoder().decode(textPart));
                }
                log.info("Message body is {}", messageBody);
                return messageBody;
            }
            return messageBody;
        }catch (Exception e){
            log.error("Getting error -> ",e);
            return messageBody;
        }
    }


    public static MessageWrapper parseGmailMessageToMessageWrapper(String email,Message message) throws IOException {
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setMessageId(message.getId());
        MessagePart payload = message.getPayload();
        List<MessagePartHeader> headers = payload.getHeaders();
        for (MessagePartHeader header : headers) {
            switch (header.getName()) {
                case FROM:
                    messageWrapper.setFrom(parseRecipient(header.getValue()));
                    break;
                case TO:
                    messageWrapper.setToRecipients(parseRecipients(header.getValue()));
                    break;
                case SUBJECT:
                    messageWrapper.setSubject(header.getValue());
                    break;
                case CC:
                    messageWrapper.setCcRecipients(parseRecipients(header.getValue()));
                    break;
                case BCC:
                    messageWrapper.setBccRecipients(parseRecipients(header.getValue()));
                    break;
                case MESSAGE_ID:
                    messageWrapper.setInternetMessageId(header.getValue());
                default:
                    break;
            }
        }
        messageWrapper.setHashAttachment(checkForAttachments(message));
        messageWrapper.setUserId(email);
        messageWrapper.setIsRead(checkMessageIsRead(message));
        messageWrapper.setContent(getHtmlBodyFromMessage(message.getPayload().getParts(),true));
//        replyBody = getReplyBodyFromPayload(payload);
//        messageWrapper.setReplyBody(replyBody);
        List<String> attachmentPaths = new ArrayList<>();
//        extractAttachments(payload, attachmentPaths,userId);
        messageWrapper.setAttachmentPathList(attachmentPaths);
        return messageWrapper;
    }

    private static Boolean checkMessageIsRead(Message message) {
        return Optional.ofNullable(message.getLabelIds()).map(labelIds -> !labelIds.contains(UNREAD)).orElse(true);
    }



//    private void extractAttachments(MessagePart payload, List<String> attachmentPaths, String userId) throws IOException {
//        if (payload.getParts() != null) {
//            for (MessagePart part : payload.getParts()) {
//                if (part.getFilename() != null && part.getFilename().length() > 0) {
//                    String attId = part.getBody().getAttachmentId();
//                    MessagePartBody attachPart = getGmailService().users().messages().attachments()
//                            .get(userId, payload.getPartId(), attId).execute();
//
//                    byte[] fileByteArray = Base64.getDecoder().decode(attachPart.getData());
//                    Path filePath = saveAttachmentToFileSystem(part.getFilename(), fileByteArray);
//                    attachmentPaths.add(filePath.toString());
//                }
//            }
//        }
//    }

//    // Mock method for saving attachment (replace with actual file-saving logic)
//    private Path saveAttachmentToFileSystem(String filename, byte[] fileData) throws IOException {
//        Path filePath = Paths.get("/your/attachment/directory/", filename);
//        Files.write(filePath, fileData);
//        return filePath;
//    }

    private static String  parseRecipient(String recipient){
        recipient = recipient.trim();
        if (recipient.contains("<") && recipient.contains(">")) {
            int startIndex = recipient.indexOf('<') + 1;
            int endIndex = recipient.indexOf('>');

            if (startIndex > 0 && endIndex > startIndex) {
                return recipient.substring(startIndex, endIndex).trim();
            }
        }
        return recipient;
    }
    private static List<String> parseRecipients(String recipients) {
        List<String> recipientsList = new ArrayList<>();
        String[] recipientArray = recipients.split(",");
        for (String recipient : recipientArray) {
            recipient = recipient.trim();
            if (recipient.contains("<") && recipient.contains(">")) {
                int startIndex = recipient.indexOf('<') + 1;
                int endIndex = recipient.indexOf('>');

                if (startIndex > 0 && endIndex > startIndex) {
                    String email = recipient.substring(startIndex, endIndex).trim();
                    recipientsList.add(email);
                }
            } else if (recipient.contains("@")) {
                recipientsList.add(recipient);
            }
        }
        return recipientsList;
    }



    public static Boolean checkForAttachments(Message fullMessage) {
        if (fullMessage.getPayload() != null) {
            MessagePart payload = fullMessage.getPayload();
            if (payload.getParts() != null) {
                for (MessagePart part : payload.getParts()) {
                    if (part.getFilename() != null && !part.getFilename().isEmpty()) {
                        log.info("Attachment found: {}",part.getFilename());
                        return  true;
                    }
                }
            }
        }
        return  false;
    }

    public static Boolean checkForCalendarEvents(Message fullMessage) throws IOException {
        if (fullMessage.getPayload() != null) {
            MessagePart payload = fullMessage.getPayload();
            if (payload.getParts() != null) {
                for (MessagePart part : payload.getParts()) {
                    if (CALENDAR.equals(part.getMimeType())) {
                        log.info("Calendar is found");
                        return true;
                    }
                }
            }
        }
        return false;
    }


    public static List<List<UserEmailDto>> groupByEmailConversation(List<UserEmailDto> userEmailDtos) {
        Map<String, List<UserEmailDto>> conversationMap = new HashMap<>();
        for (UserEmailDto email : userEmailDtos) {
            String conversationId = email.getConversationId();
            if (conversationMap.containsKey(conversationId)) {
                conversationMap.get(conversationId).add(email);
            } else {
                List<UserEmailDto> newList = new ArrayList<>();
                newList.add(email);
                conversationMap.put(conversationId, newList);
            }
        }
        List<List<UserEmailDto>> resultList = new ArrayList<>(conversationMap.values());
        for (List<UserEmailDto> emailList : resultList) {
            if (emailList.size() > 1) {
                emailList.sort(Comparator.comparing(UserEmailDto::getCreatedTime));
            }
        }
        return resultList;
    }


    public static void createMeetingOnTeam(){

    }
    public static GmailMeetingWrapper convertToGmailMeetingWrapper(Map<String, Object> inputMap){
        GmailMeetingWrapper gmailMeetingWrapper = new GmailMeetingWrapper();
        log.info("inside convertToGmailMeetingWrapper inputMap is {}",inputMap);
        gmailMeetingWrapper.setSummary(Optional.ofNullable((String) inputMap.get(EmailConstants.SUBJECT)).orElse(EMPTY));
        gmailMeetingWrapper.setDescription(Optional.ofNullable((String) inputMap.get(EmailConstants.BODY)).orElse(EMPTY));
        gmailMeetingWrapper.setStartDateTime(Optional.ofNullable((String) inputMap.get(EmailConstants.MEETING_START_TIME)).orElse(EMPTY));
        gmailMeetingWrapper.setEndDateTime(Optional.ofNullable((String) inputMap.get(EmailConstants.MEETING_END_TIME)).orElse(EMPTY));
        gmailMeetingWrapper.setTimeZone(String.valueOf(Optional.ofNullable(inputMap.get(EmailConstants.TIME_ZONE)).orElse(EMPTY)));
        if (inputMap != null && inputMap.containsKey(EmailConstants.REQUIRED_ATTENDEES)) gmailMeetingWrapper.setRequiredAttendees((List<String>) inputMap.get(EmailConstants.REQUIRED_ATTENDEES));
        if (inputMap != null && inputMap.containsKey(EmailConstants.OPTIONAL_ATTENDEES)) gmailMeetingWrapper.setOptionalAttendees((List<String>) inputMap.get(EmailConstants.OPTIONAL_ATTENDEES));
        gmailMeetingWrapper.setLocation(Optional.ofNullable((String) inputMap.get(EmailConstants.LOCATION_URL)).orElse(null));
        gmailMeetingWrapper.setMeetingType(Optional.ofNullable((String) inputMap.get(EmailConstants.MEETING_TYPE)).orElse(null));
        if (inputMap.containsKey(EmailConstants.IS_RECURRING) && (boolean) inputMap.get(EmailConstants.IS_RECURRING)) {
            String recurrenceRule = generateRecurrenceRule(inputMap);
            gmailMeetingWrapper.setRecurrenceRule(recurrenceRule);
        } else {
            log.info("No recurrence rule, event is not recurring.");
        }
//        if (inputMap != null && inputMap.containsKey(EmailConstants.RECURRENCE_RULE)) gmailMeetingWrapper.setRecurrenceRule(String.valueOf(inputMap.containsKey(EmailConstants.RECURRENCE_RULE)));
        log.info("inside getRequiredAttendees is {}",gmailMeetingWrapper.getRequiredAttendees());
        log.info("inside time zone is {}",gmailMeetingWrapper.getTimeZone());
        return gmailMeetingWrapper;
    }

    private static String generateRecurrenceRule(Map<String, Object> inputMap) {
        StringBuilder rrule = new StringBuilder();
        try {
            if (inputMap == null || inputMap.isEmpty()) {
                log.warn("Input map is null or empty. Unable to generate recurrence rule.");
                return null;
            }
            rrule.append("RRULE:");
            String recurrenceType = (String) inputMap.get(EmailConstants.RECURRENCE_TYPE);
            log.info("recurrenceType is {}",recurrenceType);
            if (recurrenceType != null && !recurrenceType.isEmpty()) {
                rrule.append("FREQ=").append(recurrenceType.equalsIgnoreCase("AbsoluteMonthly") ? "MONTHLY" : recurrenceType.toUpperCase()).append(";");
            } else {
                log.warn("Recurrence type is null or empty");
                return "";
            }
            if ("AbsoluteMonthly".equalsIgnoreCase(recurrenceType)) {
                Integer dayOfMonth = (Integer) inputMap.get(EmailConstants.DAY_OF_MONTH);
                if (dayOfMonth != null) {
                    rrule.append("BYMONTHDAY=").append(dayOfMonth).append(";");
                } else {
                    log.warn("Day of the month is missing for AbsoluteMonthly recurrence.");
                    return "";
                }
            }
            @SuppressWarnings("unchecked")
            List<String> daysOfWeek = (List<String>) inputMap.get(EmailConstants.DAYS_OF_WEEK);
            if (daysOfWeek != null && !daysOfWeek.isEmpty()) {
                rrule.append("BYDAY=");
                for (String day : daysOfWeek) {
                    rrule.append(getDayCode(day)).append(",");
                }
                rrule.setLength(rrule.length() - 1);
                rrule.append(";");
            } else {
                log.info("No days of the week provided for recurrence rule.");
            }
            if (inputMap.containsKey(EmailConstants.INTERVAL)) {
                Object intervalObj = inputMap.get(EmailConstants.INTERVAL);
                try {
                    Integer interval = intervalObj instanceof String ? Integer.parseInt((String) intervalObj) : (Integer) intervalObj;

                    if (interval != null && interval >= 1) {
                        rrule.append("INTERVAL=").append(interval).append(";");
                    } else {
                        log.info("Interval is either missing or set to 1, skipping the interval field.");
                    }
                } catch (NumberFormatException e) {
                    log.error("Invalid interval value: " + intervalObj, e);
                }
            }

            String rangeType = (String) inputMap.get(EmailConstants.RANGE_TYPE);
            String recurrenceEndDate = (String) inputMap.get(EmailConstants.RECURRENCE_END_DATE);

            if (rangeType.equalsIgnoreCase("EndDate") && recurrenceEndDate != null && !recurrenceEndDate.isEmpty()) {
                LocalDate endDate = LocalDate.parse(recurrenceEndDate);
                rrule.append("UNTIL=").append(endDate.format(DateTimeFormatter.BASIC_ISO_DATE)).append(";");
            } else {
                log.warn("Recurrence end date is missing.");
            }
        } catch (DateTimeParseException e) {
            log.error("Error parsing recurrence end date: {}", inputMap.get(EmailConstants.RECURRENCE_END_DATE), e);
            return null;
        } catch (IllegalArgumentException e) {
            log.error("Invalid input detected: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("Unexpected error occurred while generating recurrence rule: ", e);
            return null;
        }
        if (rrule.length() > 0 && rrule.charAt(rrule.length() - 1) == ';') {
            rrule.setLength(rrule.length() - 1);
        }
        log.info("Generated recurrence rule: {}", rrule.toString());
        return rrule.toString();
    }

    private static String getDayCode(String day) {
        switch (day.toLowerCase()) {
            case "monday":
                return "MO";
            case "tuesday":
                return "TU";
            case "wednesday":
                return "WE";
            case "thursday":
                return "TH";
            case "friday":
                return "FR";
            case "saturday":
                return "SA";
            case "sunday":
                return "SU";
            default:
                throw new IllegalArgumentException("Invalid day: " + day);
        }
    }

    public static MessageWrapper convertToGmailMessageWrapper(Map<String, String> inputMap){
        MessageWrapper messageWrapper = new MessageWrapper();
        log.info("inside convertToGmailMessageWrapper inputMap is {}",inputMap);
        messageWrapper.setMessageId(Optional.ofNullable(inputMap.get(EmailConstants.MESSAGE_ID)).orElse(null));
        messageWrapper.setContent(Optional.ofNullable(inputMap.get(EmailConstants.CONTENT)).orElse(null));
        messageWrapper.setSubject(Optional.ofNullable(inputMap.get(EmailConstants.SUBJECT)).orElse(EMPTY));
        messageWrapper.setReplyBody(Optional.ofNullable(inputMap.get(EmailConstants.CONTENT)).orElse(EMPTY));
        messageWrapper.setBccRecipients(CommonUtils.getListFromCommaSeparatedString(inputMap.get(BCC_EMAIL)));
        messageWrapper.setToRecipients(CommonUtils.getListFromCommaSeparatedString(inputMap.get(TO_EMAIL)));
        messageWrapper.setCcRecipients(CommonUtils.getListFromCommaSeparatedString(inputMap.get(CC_EMAIL)));
        messageWrapper.setAttachmentPathList(CommonUtils.getListFromCommaSeparatedString(inputMap.get(ATTACHMENT_LIST)));
        messageWrapper.setIsDraft(inputMap.get(SEND_DRAFT) != null ? Boolean.parseBoolean(inputMap.get(SEND_DRAFT)) : false);
        return messageWrapper;
    }

    public static GmailMeetingWrapper convertToGmailRescheduleMeetingWrapper(Map<String, String> inputMap){
        GmailMeetingWrapper gmailMeetingWrapper = new GmailMeetingWrapper();
        gmailMeetingWrapper.setSummary(Optional.ofNullable(inputMap.get(EmailConstants.SUBJECT)).orElse(EMPTY));
        gmailMeetingWrapper.setEventId(Optional.ofNullable(inputMap.get(EVENT_ID)).orElse(EMPTY));
        gmailMeetingWrapper.setDescription(
                Optional.ofNullable(inputMap.get(EmailConstants.BODY))
                        .orElseGet(() -> Optional.ofNullable(inputMap.get(EmailConstants.RESCHEDULE_REASON))
                                .orElse(EMPTY))
        );
        gmailMeetingWrapper.setStartDateTime(Optional.ofNullable(inputMap.get(EmailConstants.MEETING_START_TIME)).orElse(EMPTY));
        gmailMeetingWrapper.setEndDateTime(Optional.ofNullable(inputMap.get(EmailConstants.MEETING_END_TIME)).orElse(EMPTY));
        gmailMeetingWrapper.setLocation(Optional.ofNullable(inputMap.get(EmailConstants.LOCATION_URL)).orElse(null));
        gmailMeetingWrapper.setMeetingType(Optional.ofNullable(inputMap.get(EmailConstants.MEETING_TYPE)).orElse(null));
        gmailMeetingWrapper.setTimeZone(Optional.ofNullable(inputMap.get(EmailConstants.TIME_ZONE)).orElse(null));
        if (inputMap != null && inputMap.containsKey(EmailConstants.RECURRENCE_RULE)) gmailMeetingWrapper.setRecurrenceRule(String.valueOf(inputMap.containsKey(EmailConstants.RECURRENCE_RULE)));
        return gmailMeetingWrapper;
    }

    public static UserEmailResponseDto parseToUserEmailResponseDto(String userEmail,String userId, List<UserEmailDto> userEmailDtos){
        log.info("inside UserEmailResponseDto");
        UserEmailResponseDto userEmailResponseDto = new UserEmailResponseDto();
        userEmailResponseDto.setMailBoxUserEmail(userEmail);
        userEmailResponseDto.setMailBoxUserEmail(userId);
        List<List<UserEmailDto>> userEmailDtoList = groupByEmailConversation(userEmailDtos);
        userEmailResponseDto.setMessages(userEmailDtoList);
        return userEmailResponseDto;
    }

    public static List<String> convertAttendees(List<EventAttendee> eventAttendees){
        List<String> users=new ArrayList<>();
        if(eventAttendees!=null) eventAttendees.forEach(obj->{users.add( obj.getEmail());});
        return users;
    }
    public static String getMeetingUrl(ConferenceData conferenceData){
        String joinUrl = null;
        if (conferenceData != null) {
            List<EntryPoint> entryPoints = conferenceData.getEntryPoints();
            if (entryPoints != null) {
                for (EntryPoint entryPoint : entryPoints) {
                    log.info("entryPoint is {}",entryPoint.getEntryPointType());
                    log.info("getUri is {}",entryPoint.getUri());
                    if ("video".equals(entryPoint.getEntryPointType())) joinUrl= entryPoint.getUri();
                }
            }
        }
        return joinUrl;
    }
    public static List<EventDto> parseToEventDto(List<Event> meetings){
        log.info("inside parseToEventDto");
        List<EventDto> eventDtos=new ArrayList<>();
        for (Event meeting : meetings) {
            EventDto eventDto=new EventDto();
            eventDto.setAttendees(convertAttendees(meeting.getAttendees()));
            eventDto.setSubject(meeting.getSummary());
            eventDto.setJoinUrl(getMeetingUrl(meeting.getConferenceData()));
            eventDto.setHasAttachments(meeting.getAttachments() != null && !meeting.getAttachments().isEmpty());
            eventDto.setOrganizer(meeting.getOrganizer().getEmail());
            eventDto.setAccepted(meeting.getStatus());
            eventDto.setBodyPreview(meeting.getDescription());
            eventDto.setId(meeting.getId());
            eventDto.setCreatedDateTime(convertDateTimeToDate(meeting.getCreated()));
            eventDto.setMeetingStartTime(meeting.getStart() != null ? convertDateTimeToDate(meeting.getStart().getDateTime()) : null);
            eventDto.setMeetingEndTime(convertDateTimeToDate(meeting.getEnd().getDateTime()));
            eventDtos.add(eventDto);
        }
        log.info("complete parseToEventDto");
        return eventDtos;
    }

    public static EventDto parseToEventDto(Event meeting, String userId){
        log.info("inside parseToEventDto");

            log.info("meeting is -----------------------");
            printAllFields(meeting);
            EventDto eventDto=new EventDto();
            eventDto.setAttendees(convertAttendees(meeting.getAttendees()));
            eventDto.setSubject(meeting.getSummary());
            eventDto.setJoinUrl(getMeetingUrl(meeting.getConferenceData()));
            eventDto.setHasAttachments(meeting.getAttachments() != null && !meeting.getAttachments().isEmpty());
            eventDto.setOrganizer(meeting.getOrganizer() != null ? meeting.getOrganizer().getEmail() : null);
            eventDto.setAccepted(meeting.getStatus());
//            eventDto.setAccepted("none");
        for (EventAttendee attendee : meeting.getAttendees()) {
            if (attendee.getEmail().equals(userId)) {
                String status = attendee.getResponseStatus() != null ? attendee.getResponseStatus() : "unknown";
                eventDto.setAccepted(status.equalsIgnoreCase("needsAction") ? "none" : status);
                break;
            }
        }
            eventDto.setBodyPreview(meeting.getDescription());
            eventDto.setId(meeting.getId());
            eventDto.setCreatedDateTime(convertDateTimeToDate(meeting.getCreated()));
            eventDto.setMeetingStartTime(meeting.getStart() != null ? convertDateTimeToDate(meeting.getStart().getDateTime()) : null);
            eventDto.setMeetingEndTime(convertDateTimeToDate(meeting.getEnd().getDateTime()));

        return eventDto;
    }
    public static List<EventDto> extractEventFromMessageContent(String raw){
        System.out.println("meeting raw is {}"+raw);
        return null;
    }
    public static Date convertDateTimeToDate(DateTime dateTime) {
        return new Date(dateTime.getValue());
    }



    public static UploadResponse uploadFile(Path filePath, String fileName, String contentType)  {
        try {
            log.debug("Inside @method uploadFile. @param : filePath -> {}", filePath);
            RestTemplate restTemplate = new RestTemplate();
            FileSystemResource fileResource = new FileSystemResource(filePath.toFile());
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add(FILE, fileResource);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            org.springframework.http.HttpEntity<MultiValueMap<String, Object>> requestEntity = new org.springframework.http.HttpEntity<>(body, headers);
            ResponseEntity<UploadResponse> response = restTemplate.exchange(uploadUrl, HttpMethod.POST, requestEntity, UploadResponse.class);
            log.info("Response coming is for upload response {}", response.getBody());
            return response.getBody();
        }catch (Exception e){
            e.printStackTrace();
            log.info("Error in uploadFile {}",e.getMessage());
        }
        return null;
    }

    /**
     * Deletes a temporary file created to store attachment data.
     *
     * @param tempFile Path object representing the location of the temporary file.
     */
    public static void deleteTempFile(Path tempFile) {
        try {
            Files.deleteIfExists(tempFile);
            log.debug("Temporary file deleted successfully");
        } catch (IOException e) {
            log.error("Failed to delete temporary file", e);
        }
    }

    public static Date convertStringToDate(String dateTimeString) {
        Instant instant = Instant.parse(dateTimeString);
        return Date.from(instant);
    }

    public static void printAllFields(Object obj) {
        Class<?> objClass = obj.getClass();
        Field[] fields = objClass.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                System.out.println("Field name: " + field.getName() + ", Field value: " + field.get(obj));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
    }

    public static void  printEventReminders(Event event) {
        Reminders reminders = event.getReminders();
        log.info("going to print all reminder");
        printAllFields(reminders);
        if (reminders != null) {
            System.out.println("Use Default: " + reminders.getUseDefault());
            if (reminders.getOverrides() != null) {
                for (EventReminder reminder : reminders.getOverrides()) {
                    System.out.println("Reminder Method: " + reminder.getMethod());
                    System.out.println("Reminder Minutes: " + reminder.getMinutes());
                }
            }
        }
    }


    public static void setAttendees(List<MeetingWrapper> meetingWrappers, List<String> requiredAttendees, List<String> optionalAttendees){
        log.info("requiredAttendees  is {}",requiredAttendees);
        if(requiredAttendees!=null) requiredAttendees.forEach(requiredAttendee->{meetingWrappers.add(setMeetingWrapper(requiredAttendee,"required"));});
        if(optionalAttendees!=null) optionalAttendees.forEach(optionalAttendee->{meetingWrappers.add(setMeetingWrapper(optionalAttendee,"optional"));});
    }
    private static MeetingWrapper setMeetingWrapper(String email,String type){
        return new MeetingWrapper(email,type);
    }



}
