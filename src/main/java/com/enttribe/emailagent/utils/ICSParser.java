package com.enttribe.emailagent.utils;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.wrapper.MeetingEvent;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import net.fortuna.ical4j.data.CalendarBuilder;
import net.fortuna.ical4j.data.ParserException;
import net.fortuna.ical4j.model.Calendar;
import net.fortuna.ical4j.model.Property;
import net.fortuna.ical4j.model.component.CalendarComponent;
import net.fortuna.ical4j.model.component.VEvent;
import net.fortuna.ical4j.model.property.Attendee;
import net.fortuna.ical4j.model.property.Organizer;
import net.fortuna.ical4j.model.property.DateProperty;
import org.slf4j.Logger;

/**
 * The type Ics parser.
 *  <AUTHOR> Dangi
 */
public class ICSParser {

    private static final Logger log = EmailAgentLogger.getLogger(ICSParser.class);

    public static EventDto byObject(EventDto eventDto, String icsContent,String userId) {
        InputStream in = null;
        try {
            in = new ByteArrayInputStream(icsContent.getBytes(StandardCharsets.UTF_8));
            System.setProperty("net.fortuna.ical4j.timezone.cache.impl", "net.fortuna.ical4j.util.MapTimeZoneCache");
            CalendarBuilder builder = new CalendarBuilder();
            Calendar calendar = builder.build(in);
            for (CalendarComponent component : calendar.getComponents()) {
                if (component instanceof VEvent) {
                    VEvent event = (VEvent) component;

                    eventDto.setSubject(event.getSummary() != null ? event.getSummary().getValue() : null);
                    eventDto.setMeetingStartTime(event.getStartDate() != null ? event.getStartDate().getDate() : null);
                    eventDto.setMeetingEndTime(event.getEndDate() != null ? event.getEndDate().getDate() : null);
                    Organizer organizer = event.getOrganizer();
                    if (organizer != null) {
                        eventDto.setOrganizer(removeMailto(organizer.getCalAddress().toString()));
                    }
                    List<String> attendeeEmails = new ArrayList<>();
                    for (Property property : event.getProperties("ATTENDEE")) {
                        Attendee attendee = (Attendee) property;
                        String attendeeEmail = removeMailto(attendee.getCalAddress().toString());
                        attendeeEmails.add(attendeeEmail);
                        //TODO Need to check value for  partStat and set none for need action value
                        if (attendeeEmail.equals(userId)) {
                            String partStat = attendee.getParameter("PARTSTAT").getValue();
                            eventDto.setAccepted(partStat != null ? partStat : "unknown");
                        }

                    }
                    eventDto.setAttendees(attendeeEmails.isEmpty() ? null : attendeeEmails);
                    eventDto.setJoinUrl(event.getProperty("X-GOOGLE-CONFERENCE") != null ?
                            event.getProperty("X-GOOGLE-CONFERENCE").getValue() : null);
                    eventDto.setAccepted(event.getProperty("STATUS") != null ?
                            event.getProperty("STATUS").getValue() : null);
                    eventDto.setAccepted("none");
                    eventDto.setHasAttachments(event.getProperties("ATTACH").size() > 0);
                    if (event.getCreated() != null) {
                        eventDto.setCreatedDateTime(event.getCreated().getDate());
                    }
                    if (event.getLastModified() != null) {
                        eventDto.setLastModifiedDateTime(event.getLastModified().getDate());
                    }
                    if (event.getUid() != null) {
                        eventDto.setId(event.getUid().getValue());
                    }
                }
            }
            return eventDto;
        } catch (IOException e) {
            log.error("Error reading the ICS content: ", e);
        } catch (ParserException e) {
            log.error("Error parsing the ICS content: ", e);
        } catch (Exception e) {
            log.error("An unexpected error occurred: ", e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("Error closing InputStream: ", e);
                }
            }
        }

        return eventDto;
    }


    private static String removeMailto(String email) {
        return email.replace("mailto:", "");
    }


    // Helper method to extract ID from UID (assuming "@google.com" removal)
    private static String getEventIdFromUid(String uid) {
        if (uid.contains("google.com")) {
            return uid.split("@")[0];
        }else return null;
    }

    // Assuming the removeMailto method is defined in this class


    public static EventDto parseICS( EventDto event,String icsContent) throws ParseException {

        log.info("icsContent inside parseICS is {}",icsContent);
        String[] lines = icsContent.split("\n");
        for (String line : lines) {
            String[] parts = line.split(":", 2);
            if (parts.length < 2) {
                continue;
            }
            String key = parts[0].trim();
            String value = parts[1].trim();
            log.info("key for parseICS is {}",key);

            switch (key) {
                case "ORGANIZER":
                    event.setOrganizer(value.split(";")[1].replace("mailto:", "").trim());
                    break;
                case "ATTENDEE":
                    String attendee = value.split(";")[1].replace("mailto:", "").trim();
                    if (event.getAttendees() == null) {
                        event.setAttendees(new ArrayList<>());
                    }
                    log.info("attendee is {}",attendee);
                    event.getAttendees().add(attendee);
                    break;
                case "DTSTART":
                    break;
                case "BEGIN":
                    break;
                case "DTEND":
                    break;
                case "PRODID":
                    break;
                case "SUMMARY":
                    event.setSubject(value);
                    break;
                case "STATUS":
                    event.setAccepted(value);
                    break;
                case "CREATED":
                    event.setCreatedDateTime(parseDate(value));
                    break;
                case "LAST-MODIFIED":
                    event.setLastModifiedDateTime(parseDate(value));
                    break;
                case "UID":
                    event.setId(value);
                    break;
                case "X-GOOGLE-CONFERENCE":
                    event.setJoinUrl(value);
                    break;
                default:
                    handleOtherCase(event,line);
                    break;
            }
        }
        return event;
    }

    public static String getEventIdFromICS(String icsContent) {
        try {
            InputStream in = new ByteArrayInputStream(icsContent.getBytes(StandardCharsets.UTF_8));
            System.setProperty("net.fortuna.ical4j.timezone.cache.impl", "net.fortuna.ical4j.util.MapTimeZoneCache");
            CalendarBuilder builder = new CalendarBuilder();
            Calendar calendar = builder.build(in);
            for (CalendarComponent component : calendar.getComponents()) {
                if (component instanceof VEvent) {
                    VEvent event = (VEvent) component;
                    return getEventIdFromUid(event.getUid().getValue());
                }
            }
        } catch (Exception e) {
            log.error("Error parsing ICS content: {}", e.getMessage(), e);
        }
        return null;
    }

    private static void handleOtherCase(EventDto event,String line){
            System.out.println("line is {}"+line);
            if(line.startsWith("DTSTART;TZID")) event.setMeetingStartTime(handleStartAndEndDates(line));
            if(line.startsWith("DTEND;TZID")) event.setMeetingEndTime(handleStartAndEndDates(line));
            if(line.startsWith("ORGANIZER")) event.setOrganizer(handleOrganizer(line));
    }

    private static Date handleStartAndEndDates(String line){
        System.out.println("line is {}"+line);
        return  parseDate(line.split(":")[1], extractTimeZone(line));
    }

    private static String handleOrganizer(String value) {
        String[] organizerParts = value.split(":");
        if (organizerParts.length > 1) {
            String emailPart = organizerParts[1].trim(); // "mailto:<EMAIL>"
            if (emailPart.startsWith("mailto:")) {
                return emailPart.replace("mailto:", "").trim();
            } else {
                System.err.println("Invalid ORGANIZER format: " + value);
                return null;
            }
        } else {
            System.err.println("Unexpected ORGANIZER format: " + value);
            return null;
        }
    }


    private static void handleAttendee(MeetingEvent event, String value) {
        String[] attendeeParts = value.split(";");
        if (attendeeParts.length > 1) {
            String attendee = attendeeParts[attendeeParts.length - 1].replace("mailto:", "").trim();
            if (event.getAttendees() == null) {
                event.setAttendees(new ArrayList<>());
            }
            event.getAttendees().add(attendee);
        } else {
            System.err.println("Unexpected ATTENDEE format: " + value);
        }
    }

    private static Date parseDate(String dateString) throws ParseException {
        SimpleDateFormat format;
        if (dateString.endsWith("Z")) {
            format = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
            format.setTimeZone(TimeZone.getTimeZone("UTC"));
        } else {
            format = new SimpleDateFormat("yyyyMMdd'T'HHmmss");
        }
        return format.parse(dateString);
    }


    private static Date parseDate(String dateString, String timeZoneId)  {
       try {
        SimpleDateFormat format;
        if (dateString.endsWith("Z")) {
            format = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
            format.setTimeZone(TimeZone.getTimeZone("UTC"));
        } else {
            format = new SimpleDateFormat("yyyyMMdd'T'HHmmss");
            if (timeZoneId != null) {
                format.setTimeZone(TimeZone.getTimeZone(timeZoneId));
            }
        }
        return format.parse(dateString);
       }catch (Exception e){
           e.printStackTrace();
           return null;
       }
    }
    private static String extractTimeZone(String line) {
        if (line.contains("TZID=")) {
            return line.split("TZID=")[1].split(":")[0].trim();
        }
        return null;
    }






}

