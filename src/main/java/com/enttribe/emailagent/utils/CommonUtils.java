package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.ai.dto.intent.IntentResponseDto;
import com.enttribe.emailagent.config.SpringContext;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.dto.OutOfOfficeDto;
import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.wrapper.ConflictMeetingWrapper;
import com.enttribe.emailagent.wrapper.MeetingWrapper;
import com.enttribe.emailagent.wrapper.OutOfOffice;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.enttribe.emailagent.utils.GmailUtils.*;
import static com.enttribe.emailagent.utils.GmailUtils.DASH;

/**
 * The type Common utils.
 *  <AUTHOR> Sonsale
 */
@Slf4j
public class CommonUtils {

    private CommonUtils() {

    }

    private static final ObjectMapper OBJECT_MAPPER = createObjectMapper();

    public static final String GMAIL="GMAIL";
    public static final String EWS="EWS";

    @Value("${attachment.file.path}")
    private static String attachmentFilePath="/tmp/gmail-credentials/";

    private static final String USER_ID = "userId";
    private static final String SCHEDULED_START_DATETIME = "scheduledStartDateTime";
    private static final String SCHEDULED_START_TIMEZONE = "scheduledStartTimeZone";
    private static final String SCHEDULED_END_DATETIME = "scheduledEndDateTime";
    private static final String INTERNAL_REPLY_MESSAGE = "internalReplyMessage";
    private static final String EXTERNAL_REPLY_MESSAGE = "externalReplyMessage";
    private static final String STATUS = "status";

    private static final String EMAIL_REGEX = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

    // Compile the regex pattern
    private static final Pattern EMAIL_PATTERN = Pattern.compile(EMAIL_REGEX);

    public static boolean isValidEmail(String email) {
        if (email == null) {
            return false;
        }
        Matcher matcher = EMAIL_PATTERN.matcher(email);
        return matcher.matches();
    }

    private static ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }

    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }


    public static JSONObject convertToJSONObject(Object obj) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = mapper.writeValueAsString(obj);
        return new JSONObject(jsonString);
    }

    public static List<String> getListFromString(String jsonString) throws JsonProcessingException {
        if (jsonString == null || jsonString.isBlank()) return List.of();
        ObjectMapper mapper = new ObjectMapper();
        return (List<String>) mapper.readValue(jsonString, List.class);
    }

    public static String extractJsonString(String inputString) {
        int firstIndex = inputString.indexOf('{');
        int lastIndex = inputString.lastIndexOf('}');
        if (firstIndex != -1 && lastIndex != -1 && firstIndex < lastIndex) {
            inputString = inputString.substring(firstIndex, lastIndex + 1);
        }
        return inputString;
    }

    public static JSONArray convertToJSONArray(Object obj) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = mapper.writeValueAsString(obj);
        return new JSONArray(jsonString);
    }

    public static String commaSepratedString(JSONArray jsonArray) {
        String commaSeparatedString = "";
        try {
            // Parse the JSON array

            // Initialize a StringBuilder to concatenate the strings
            StringBuilder result = new StringBuilder();

            // Loop through the JSON array
            for (int i = 0; i < jsonArray.length(); i++) {
                // Append the current string to the result
                result.append(jsonArray.getString(i));
                // If it's not the last element, add a comma and space
                if (i < jsonArray.length() - 1) {
                    result.append(", ");
                }
            }

            // Convert StringBuilder to String
            commaSeparatedString = result.toString();
            // Output the result
            //System.out.println(commaSeparatedString);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return commaSeparatedString;

    }

    public static String getPureJSON(String aiResponse) {
        int arrayStartIndex = aiResponse.indexOf('[');
        int arrayEndIndex = aiResponse.lastIndexOf(']');

        int objStartIndex = aiResponse.indexOf('{');
        int objEndIndex = aiResponse.lastIndexOf('}');

        int startIndex = Math.min(arrayStartIndex, objStartIndex);
        int endIndex = Math.max(arrayEndIndex, objEndIndex);

        if (startIndex != -1 && endIndex != -1 && startIndex < endIndex) {
            aiResponse = aiResponse.substring(startIndex, endIndex + 1);
        }
        return aiResponse;
    }

    public static String makeContentJsonParsable(String jsonResponse) {
        try {
            // Parse the input JSON string to a JSONObject
            JSONObject jsonObject = new JSONObject(jsonResponse);

            System.out.println("Json object is " + jsonObject.toString());


//
//            // Escape special characters in the email content
//            String jsonParsableContent = emailContent
//                .replace("\\", "\\\\")
//                .replace("\"", "\\\"")
//                .replace("\n", "\\n")
//                .replace("\r", "\\r");
//
//            // Update the email content in the JSONObject
//            jsonObject.put("email", jsonParsableContent);

            // Return the modified JSON string
            return jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getDomainFromEmail(String email) {
        if (email == null || !email.contains("@")) {
            return null;
        }

        int atIndex = email.lastIndexOf('@');
        if (atIndex == -1 || atIndex == email.length() - 1) {
            return null;
        }

        return email.substring(atIndex + 1);
    }

    public static String getOrganizationFromEmail(String email) {
        if (email == null || !email.contains("@")) {
            throw new IllegalArgumentException("Invalid email address");
        }

        // Split the email to get the domain part
        String[] parts = email.split("@");
        if (parts.length < 2) {
            throw new IllegalArgumentException("Invalid email address");
        }

        String domain = parts[1];

        // Split the domain to get the organization part
        String[] domainParts = domain.split("\\.");
        if (domainParts.length < 2) {
            throw new IllegalArgumentException("Invalid email domain");
        }

        // The organization is the first part of the domain
        return domainParts[0];
    }

    public static String[] splitStringFromPart(String longString, String part) {
        // Find the index of the part in the long string
        if (part == null || part.isBlank()) return new String[]{longString, ""};
        int partIndex = longString.indexOf(part);

        // If the part is not found, return the original string in the 0 index and an empty string in the 1 index
        if (partIndex == -1) {
            return new String[]{longString, ""};
        }

        // Split the string into two parts
        String beforePart = longString.substring(0, partIndex);
        String afterPart = longString.substring(partIndex + part.length());

        // Return the result in an array
        return new String[]{beforePart, afterPart};
    }

    public static String combineStrings(String before, String middle, String after) {
        // Combine the three parts back into a single string
        return before + middle + after;
    }

    public static List<Meeting> addOutOfOfficeToMeeting(List<Meeting> meetings, OutOfOfficeDto outOfOffice) {
        if (outOfOffice != null) {
            Meeting meeting = new Meeting();
            meeting.setStartTime(outOfOffice.getOutOfOfficeStart());
            meeting.setEndTime(outOfOffice.getOutOfOfficeEnd());
            meetings.add(meeting);
        }
        return meetings;
    }

    public static List<String> getListFromCommaSeparatedString(String commaSeparatedString) {
        if (commaSeparatedString == null || commaSeparatedString.isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(commaSeparatedString.split("\\s*,\\s*"));
    }

    public static String removeTimestamp(String fileName) {
        int underscoreIndex = fileName.indexOf("_");
        if (underscoreIndex != -1) {
            fileName = fileName.substring(underscoreIndex + 1);
        }
        return fileName;
    }


    public static String convertToMeetingRequestJson(Map<String, Object> inputMap, String emailId) {
        String timeZone = (String) inputMap.get(EmailConstants.TIME_ZONE);

        JSONObject resultJson = new JSONObject();

        // Subject
        resultJson.put(EmailConstants.SUBJECT, inputMap.get(EmailConstants.SUBJECT));

        // Body
        JSONObject bodyJson = new JSONObject();
        bodyJson.put("contentType", "HTML");
        bodyJson.put("content", inputMap.get("body"));
        resultJson.put("body", bodyJson);

        // Start time
        JSONObject startJson = new JSONObject();
        startJson.put("dateTime", inputMap.get(EmailConstants.MEETING_START_TIME));
        startJson.put(EmailConstants.TIME_ZONE, timeZone);
        resultJson.put("start", startJson);

        // End time
        JSONObject endJson = new JSONObject();
        endJson.put("dateTime", inputMap.get(EmailConstants.MEETING_END_TIME));
        endJson.put(EmailConstants.TIME_ZONE, timeZone);
        resultJson.put("end", endJson);
        // Attendees
        JSONArray attendeesJsonArray = new JSONArray();
        List<String> requiredAttendees = (List<String>) inputMap.get(EmailConstants.REQUIRED_ATTENDEES);
        List<String> optionalAttendees = new ArrayList<>();
        if (inputMap != null && inputMap.containsKey(EmailConstants.OPTIONAL_ATTENDEES))
            optionalAttendees = (List<String>) inputMap.get(EmailConstants.OPTIONAL_ATTENDEES);

        List<MeetingWrapper> meetingWrappers = new ArrayList<>();
        setAttendees(meetingWrappers, requiredAttendees, optionalAttendees);

        meetingWrappers.forEach(meetingWrapper -> {
            JSONObject attendeeJson = new JSONObject();
            JSONObject emailAddressJson = new JSONObject();
            emailAddressJson.put("address", meetingWrapper.getEmail());
            attendeeJson.put("emailAddress", emailAddressJson);
            attendeeJson.put("type", meetingWrapper.getType());
            attendeesJsonArray.put(attendeeJson);
        });

        resultJson.put("attendees", attendeesJsonArray);
        String meetingType = (String) inputMap.get("meetingType");

        if (meetingType != null && meetingType.equals("Teams")) {
            resultJson.put("isOnlineMeeting", true);
            resultJson.put("onlineMeetingProvider", "teamsForBusiness");
        } else if (meetingType != null && meetingType.equalsIgnoreCase("Zoom")) {
            try {
                Map<String, String> zoomResponse = ZoomClient.scheduleMeeting(emailId, inputMap.get(EmailConstants.MEETING_START_TIME).toString(), inputMap.get(EmailConstants.MEETING_END_TIME).toString(), inputMap.get("subject").toString(), inputMap.get("body").toString(), timeZone);

                if (zoomResponse != null) {
                    String body = inputMap.get("body").toString();
                    body += "<div>Meeting URL : <a style=\"color: blue\" href=" + zoomResponse.get("join_url") + ">" + zoomResponse.get("join_url") + "</a></div><br>Meeting ID : " + zoomResponse.get("meeting_id") + "<br>Passcode : " + zoomResponse.get("passcode");
                    log.debug("body is {}", body);
                    bodyJson.put("contentType", "HTML");
                    bodyJson.put("content", body);
                    resultJson.put("body", bodyJson);

                }
            } catch (Exception e) {
                log.error("Error inside @method convertToMeetingRequestJson.", e);
            }
        }
        String location = Optional.ofNullable((String) inputMap.get(EmailConstants.LOCATION)).orElse(null);
        String locationUrl = Optional.ofNullable((String) inputMap.get(EmailConstants.LOCATION_URL)).orElse(null);
        if (location != null) {
            JSONObject locationObj = new JSONObject();
            locationObj.put("displayName", location);
            Optional.ofNullable(locationUrl).ifPresent(url -> locationObj.put(EmailConstants.LOCATION_URL, url));
            resultJson.put(EmailConstants.LOCATION, locationObj);
        }

        return resultJson.toString();
    }

    public static String convertToRecurringMeetingRequestJson(Map<String, Object> inputMap, String emailId) {
        String timeZone = (String) inputMap.get(EmailConstants.TIME_ZONE);

        JSONObject resultJson = new JSONObject();

        // Subject
        resultJson.put(EmailConstants.SUBJECT, inputMap.get(EmailConstants.SUBJECT));

        // Body
        JSONObject bodyJson = new JSONObject();
        bodyJson.put("contentType", "HTML");
        bodyJson.put("content", inputMap.get("body"));
        resultJson.put("body", bodyJson);

        // Start time
        JSONObject startJson = new JSONObject();
        startJson.put("dateTime", inputMap.get(EmailConstants.MEETING_START_TIME));
        startJson.put(EmailConstants.TIME_ZONE, timeZone);
        resultJson.put("start", startJson);

        // End time
        JSONObject endJson = new JSONObject();
        endJson.put("dateTime", inputMap.get(EmailConstants.MEETING_END_TIME));
        endJson.put(EmailConstants.TIME_ZONE, timeZone);
        resultJson.put("end", endJson);
        // Attendees
        JSONArray attendeesJsonArray = new JSONArray();
        List<String> requiredAttendees = (List<String>) inputMap.get(EmailConstants.REQUIRED_ATTENDEES);
        List<String> optionalAttendees = new ArrayList<>();
        if (inputMap.containsKey(EmailConstants.OPTIONAL_ATTENDEES))
            optionalAttendees = (List<String>) inputMap.get(EmailConstants.OPTIONAL_ATTENDEES);
        List<MeetingWrapper> meetingWrappers = new ArrayList<>();
        setAttendees(meetingWrappers, requiredAttendees, optionalAttendees);
        meetingWrappers.forEach(meetingWrapper -> {
            JSONObject attendeeJson = new JSONObject();
            JSONObject emailAddressJson = new JSONObject();
            emailAddressJson.put("address", meetingWrapper.getEmail());
            attendeeJson.put("emailAddress", emailAddressJson);
            attendeeJson.put("type", meetingWrapper.getType());
            attendeesJsonArray.put(attendeeJson);
        });
        resultJson.put("attendees", attendeesJsonArray);
        String meetingType = (String) inputMap.get("meetingType");
        if (meetingType != null && meetingType.equals("Teams")) {
            resultJson.put("isOnlineMeeting", true);
            resultJson.put("onlineMeetingProvider", "teamsForBusiness");
        } else if (meetingType != null && meetingType.equalsIgnoreCase("Zoom")) {
            try {
                Map<String, String> zoomResponse = ZoomClient.scheduleMeeting(emailId, inputMap.get(EmailConstants.MEETING_START_TIME).toString(), inputMap.get(EmailConstants.MEETING_END_TIME).toString(), inputMap.get("subject").toString(), inputMap.get("body").toString(), timeZone);

                if (zoomResponse != null) {
                    String body = inputMap.get("body").toString();
//                    body += "\n\n\nMeeting URL : " + zoomResponse.get("join_url") + "\nMeeting ID : " + zoomResponse.get("meeting_id") + "\n Passcode : " + zoomResponse.get("passcode");
                    body += "<div>Meeting URL : <a style=\"color: blue\" href=" + zoomResponse.get("join_url") + ">" + zoomResponse.get("join_url") + "</a></div><br>Meeting ID : " + zoomResponse.get("meeting_id") + "<br>Passcode : " + zoomResponse.get("passcode");
                    body += SpringContext.getProperty("generatedBy");;
                    log.debug("body is {}", body);
                    bodyJson.put("contentType", "HTML");
                    bodyJson.put("content", body);
                    resultJson.put("body", bodyJson);

                }
            } catch (Exception e) {
                log.error("Error inside @method convertToMeetingRequestJson.", e);
            }
        }

        String location = (String) inputMap.get(EmailConstants.LOCATION);
        if (location != null) {
            JSONObject locationObj = new JSONObject();
            locationObj.put("displayName", location);
            resultJson.put(EmailConstants.LOCATION, locationObj);
        }

        // Handle recurrence if isRecurring is true
        Boolean isRecurring = (Boolean) inputMap.get("isRecurring");
        if (isRecurring != null && isRecurring) {
            JSONObject recurrenceJson = new JSONObject();

            // Recurrence pattern
            JSONObject patternJson = new JSONObject();
            String recurrenceType = (String) inputMap.get("recurrenceType");
            Integer interval = (Integer) inputMap.get("interval");

            // Handle different recurrence types
            switch (recurrenceType.toLowerCase()) {
                case "daily":
                    patternJson.put("type", "daily");
                    patternJson.put("interval", interval);
                    break;
                case "weekly":
                    patternJson.put("type", "weekly");
                    patternJson.put("interval", interval);

                    // Days of the week (e.g., ["Monday", "Wednesday"])
                    List<String> daysOfWeek = (List<String>) inputMap.get("daysOfWeek");
                    JSONArray daysOfWeekJsonArray = new JSONArray();
                    for (String day : daysOfWeek) {
                        daysOfWeekJsonArray.put(day);
                    }
                    patternJson.put("daysOfWeek", daysOfWeekJsonArray);
                    break;
                case "absolutemonthly":
                    patternJson.put("type", "absoluteMonthly");
                    patternJson.put("interval", interval);

                    // Day of the month (e.g., 3 for the 3rd of each month)
                    Integer dayOfMonth = (Integer) inputMap.get("dayOfMonth");
                    patternJson.put("dayOfMonth", dayOfMonth);
                    break;
                case "absoluteyearly":
                    patternJson.put("type", "absoluteYearly");
                    patternJson.put("interval", interval);

                    // Day of the month and month (e.g., March 15th every year)
                    Integer yearlyDayOfMonth = (Integer) inputMap.get("dayOfMonth");
                    Integer monthOfYear = (Integer) inputMap.get("monthOfYear");
                    patternJson.put("dayOfMonth", yearlyDayOfMonth);
                    patternJson.put("month", monthOfYear);
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported recurrence type: " + recurrenceType);
            }

            recurrenceJson.put("pattern", patternJson);

            // Recurrence range
            JSONObject rangeJson = new JSONObject();
            rangeJson.put("startDate", inputMap.get("recurrenceStartDate"));

            String rangeType = (String) inputMap.get("rangeType");
            if ("EndDate".equalsIgnoreCase(rangeType)) {
                rangeJson.put("type", "endDate");
                rangeJson.put("endDate", inputMap.get("recurrenceEndDate"));
            } else if ("NoEnd".equalsIgnoreCase(rangeType)) {
                rangeJson.put("type", "noEnd");
            }

            recurrenceJson.put("range", rangeJson);

            // Add recurrence to the result
            resultJson.put("recurrence", recurrenceJson);
        }

        return resultJson.toString();
    }

    public static String convertToRescheduleMeetingPayload(Map<String, String> map) {
        JSONObject meetingPayload = new JSONObject();

        // Start time object
        JSONObject start = new JSONObject();
        start.put("dateTime", map.get("startTime"));
        start.put("timeZone", map.get("timeZone"));

        // End time object
        JSONObject end = new JSONObject();
        end.put("dateTime", map.get("endTime"));
        end.put("timeZone", map.get("timeZone"));

        // Add start and end time to the main payload
        meetingPayload.put("start", start);
        meetingPayload.put("end", end);

        String rescheduleReason = map.get("rescheduleReason");
        if (rescheduleReason != null) {
            // Add body content (description)
            JSONObject body = new JSONObject();
            body.put("contentType", "HTML");  // or "Text" for plain text
            body.put("content", rescheduleReason);  // Fetching description from the map

            // Add body to the main payload
            meetingPayload.put("body", body);
        }

        return meetingPayload.toString();
    }

    private static void setAttendees(List<MeetingWrapper> meetingWrappers, List<String> requiredAttendees, List<String> optionalAttendees) {
        requiredAttendees.forEach(requiredAttendee -> {
            meetingWrappers.add(setMeetingWrapper(requiredAttendee, "required"));
        });
        if (optionalAttendees != null) optionalAttendees.forEach(optionalAttendee -> {
            meetingWrappers.add(setMeetingWrapper(optionalAttendee, "optional"));
        });
    }

    private static MeetingWrapper setMeetingWrapper(String email, String type) {
        return new MeetingWrapper(email, type);
    }

    public static Map<String, Object> getMeetingRequestBody(IntentResponseDto dto) {
        Map<String, Object> meetingRequest = new HashMap<>();
        meetingRequest.put(EmailConstants.SUBJECT, dto.getSubjectForMeeting());
        meetingRequest.put("body", dto.getBody());
        meetingRequest.put(EmailConstants.MEETING_START_TIME, dto.getMeetingStartTime());
        meetingRequest.put(EmailConstants.MEETING_END_TIME, dto.getMeetingEndTime());
        meetingRequest.put(EmailConstants.REQUIRED_ATTENDEES, dto.getAttendees());
        return meetingRequest;
    }

    public static String convertToMeetingRequestForUpdateEvent(Map<String, Object> jsonBody, String email) {
        String timeZone = (String) jsonBody.get(EmailConstants.TIME_ZONE);

        JSONObject resultJson = new JSONObject();

        // Subject
        String subject = (String) jsonBody.get(EmailConstants.SUBJECT);
        if (subject != null && !subject.trim().isEmpty()) {
            resultJson.put(EmailConstants.SUBJECT, subject);
        }

        // Body
        String body = (String) jsonBody.get("body");
        if (body != null && !body.trim().isEmpty()) {
            JSONObject bodyJson = new JSONObject();
            bodyJson.put("contentType", "HTML");
            bodyJson.put("content", body);
            resultJson.put("body", bodyJson);
        }

        // Start time
        String startTime = (String) jsonBody.get(EmailConstants.MEETING_START_TIME);
        if (startTime != null && !startTime.trim().isEmpty()) {
            JSONObject startJson = new JSONObject();
            startJson.put("dateTime", startTime);
            startJson.put(EmailConstants.TIME_ZONE, timeZone);
            resultJson.put("start", startJson);
        }

        // End time
        String endTime = (String) jsonBody.get(EmailConstants.MEETING_END_TIME);
        if (endTime != null && !endTime.trim().isEmpty()) {
            JSONObject endJson = new JSONObject();
            endJson.put("dateTime", endTime);
            endJson.put(EmailConstants.TIME_ZONE, timeZone);
            resultJson.put("end", endJson);
        }

        // Attendees
        JSONArray attendeesJsonArray = new JSONArray();
        List<String> requiredAttendees = (List<String>) jsonBody.get(EmailConstants.REQUIRED_ATTENDEES);
        List<String> optionalAttendees = new ArrayList<>();
        if (jsonBody != null && jsonBody.containsKey(EmailConstants.OPTIONAL_ATTENDEES)) {
            optionalAttendees = (List<String>) jsonBody.get(EmailConstants.OPTIONAL_ATTENDEES);
        }

        if (requiredAttendees != null && !requiredAttendees.isEmpty()) {
            List<MeetingWrapper> meetingWrappers = new ArrayList<>();
            setAttendees(meetingWrappers, requiredAttendees, optionalAttendees);

            meetingWrappers.forEach(meetingWrapper -> {
                JSONObject attendeeJson = new JSONObject();
                JSONObject emailAddressJson = new JSONObject();
                emailAddressJson.put("address", meetingWrapper.getEmail());
                attendeeJson.put("emailAddress", emailAddressJson);
                attendeeJson.put("type", meetingWrapper.getType());
                attendeesJsonArray.put(attendeeJson);
            });

            resultJson.put("attendees", attendeesJsonArray);
        }

        String meetingType = (String) jsonBody.get("meetingType");

        if (meetingType != null && meetingType.equals("Teams")) {
            resultJson.put("isOnlineMeeting", true);
            resultJson.put("onlineMeetingProvider", "teamsForBusiness");
        } else if (meetingType != null && meetingType.equalsIgnoreCase("Zoom")) {
            try {
                if (startTime != null && !startTime.trim().isEmpty() && endTime != null && !endTime.trim().isEmpty()
                        && subject != null && !subject.trim().isEmpty() && body != null && !body.trim().isEmpty()) {

                    Map<String, String> zoomResponse = ZoomClient.scheduleMeeting(email, startTime, endTime, subject, body, timeZone);

                    if (zoomResponse != null) {
                        String updatedBody = body;
                        updatedBody += "<div>Meeting URL : <a style=\"color: blue\" href=" + zoomResponse.get("join_url") + ">" + zoomResponse.get("join_url") + "</a></div><br>Meeting ID : " + zoomResponse.get("meeting_id") + "<br>Passcode : " + zoomResponse.get("passcode");
                        log.debug("body is {}", updatedBody);
                        JSONObject bodyJson = new JSONObject();
                        bodyJson.put("contentType", "HTML");
                        bodyJson.put("content", updatedBody);
                        resultJson.put("body", bodyJson);
                    }
                }
            } catch (Exception e) {
                log.error("Error inside @method convertToMeetingRequestJson.", e);
            }
        }

        String location = Optional.ofNullable((String) jsonBody.get(EmailConstants.LOCATION)).orElse(null);
        String locationUrl = Optional.ofNullable((String) jsonBody.get(EmailConstants.LOCATION_URL)).orElse(null);
        if (location != null) {
            JSONObject locationObj = new JSONObject();
            locationObj.put("displayName", location);
            Optional.ofNullable(locationUrl).ifPresent(url -> locationObj.put(EmailConstants.LOCATION_URL, url));
            resultJson.put(EmailConstants.LOCATION, locationObj);
        }

        return resultJson.toString();
    }


    /**
     * Converts the stack trace of an exception to a string.
     *
     * @param throwable the exception to convert
     * @return the stack trace as a string
     */
    public String getStackTraceAsString(Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    /**
     * Checks if the provided object is an instance of {@link FailureLogs}.
     *
     * @param obj The object to check.
     * @return {@code true} if the object is an instance of {@link FailureLogs}, {@code false} otherwise.
     */
    public static boolean isFailureLog(Object obj) {
        return obj != null && obj instanceof FailureLogs;
    }

    public static FailureLogs getFailureLogsObject(String exceptionMessage, String exceptionTrace, String internetMessageId, String email, String emailSubject, String messageId, String conversationId, String type, Boolean attachment, String userMailAttachment, String intent, String attendees) {
        return new FailureLogs(internetMessageId, email, emailSubject, messageId, conversationId, type, exceptionMessage, null, exceptionTrace, attachment, userMailAttachment, intent, attendees, new Date());
    }

    public static Map<String, String> getAuditMap(String userId, String subject, String internetMessageId, String type, String userPrompt, String previousPrompt, String messageId, String conversationId) {
        Map<String, String> auditMap = new HashMap<String, String>();
        auditMap.put("userId", userId);
        auditMap.put("subject", subject);
        auditMap.put("internetMessageId", internetMessageId);
        auditMap.put("type", type);
        auditMap.put("previousPrompt", previousPrompt);
        auditMap.put("userPrompt", userPrompt);
        auditMap.put("messageId", messageId);
        auditMap.put("conversationId", conversationId);
        return auditMap;
    }

    public static boolean areEmailsMatching(List<String> emailList, String commaSeparatedEmails) {
        // Convert the email list to a HashSet for faster lookups
        Set<String> emailSet = new HashSet<>(emailList);

        if (commaSeparatedEmails.isBlank() && emailList.isEmpty()) return true;

        // Split the comma-separated string into individual emails and trim spaces if any
        String[] emails = commaSeparatedEmails.split(",");

        // Iterate through each email from the comma-separated string
        for (String email : emails) {
            // Trim any leading or trailing spaces from each email
            email = email.trim();

            // If any email is present in the set, return true
            if (emailSet.contains(email)) {
                return true;
            }
        }
        // If no emails match, return false
        return false;
    }

    // Method to check if a string contains an email address
    public static boolean containsEmail(String input) {
        // Regular expression for a valid email address
        String emailRegex = "[a-zA-Z0-9._%+-^]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,7}";

        // Compile the regex into a pattern
        Pattern pattern = Pattern.compile(emailRegex);

        // Create a matcher for the input string
        Matcher matcher = pattern.matcher(input);

        // Return true if an email is found, false otherwise
        return matcher.find();
    }

    public static List<String> filterEmailsByDomain(List<String> emails, List<String> domains) {
        List<String> filteredEmails = new ArrayList<>();

        for (String email : emails) {
            String[] parts = email.split("@");
            if (parts.length == 2 && !domains.contains(parts[1].toLowerCase())) {
                filteredEmails.add(email);
            }
        }

        return filteredEmails;
    }

    public static List<String> extractEmails(String input) {
        // Define the regex pattern to match the email addresses inside parentheses Eg: @Nitin Vyas(<EMAIL>)
        String regex = "@[A-Za-z\\s]+\\([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}\\)";

        // Compile the regex pattern
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // List to hold extracted email addresses
        List<String> emails = new ArrayList<>();

        // Find all matches and extract emails
        while (matcher.find()) {
            // Get the matched string
            String match = matcher.group();

            // Extract the email part by finding the content inside parentheses
            int start = match.indexOf('(');
            int end = match.indexOf(')');
            if (start != -1 && end != -1) {
                String email = match.substring(start + 1, end); // Extract email without parentheses
                emails.add(email);
            }
        }

        return emails;
    }

    /**
     * Converts a JSON string to a Java object of the specified class type.
     *
     * @param jsonString the JSON string to convert
     * @param clazz the class of the object to create
     * @param <T> the type of the object
     * @return an instance of the specified class type
     * @throws RuntimeException if the conversion fails
     */
    public static <T> T convertJsonToObject(String jsonString, Class<T> clazz) {
        try {
            ObjectMapper objectMapper = CommonUtils.getObjectMapper();
            return objectMapper.readValue(jsonString, clazz);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert JSON to Object: " + e.getMessage(), e);
        }
    }

    /**
     * Converts a JSON string to a List of Java objects of the specified class type.
     *
     * @param jsonString the JSON string to convert
     * @param clazz the class of the objects to create
     * @param <T> the type of the objects
     * @return a List of instances of the specified class type
     * @throws RuntimeException if the conversion fails
     */
    public static <T> List<T> convertJsonToList(String jsonString, Class<T> clazz) {
        if (jsonString == null) return new ArrayList<>();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (Exception e) {
            log.error("Error inside @method convertJsonToList", e);
            return new ArrayList<>();
        }
    }

    public static void main(String[] args) {
        // Test input string
        String input = "Schedule meeting with @Nitin Vyas(<EMAIL>) and @Shiv (<EMAIL>)";

        // Extract emails
        List<String> emails = extractEmails(input);

        // Print the extracted emails
        System.out.println(emails);
    }

    public static List<ConflictMeetingWrapper> findUnavailableTimeSlots(Map<String, List<Meeting>> meetings, Date from, Date till) {
        List<ConflictMeetingWrapper> conflictMeetingWrapperList = new ArrayList<>();
        // Iterate over each user's meetings
        for (Map.Entry<String, List<Meeting>> entry : meetings.entrySet()) {
            String userId = entry.getKey();
            List<Meeting> userMeetings = entry.getValue();

            List<Meeting> unavailableSlots = new ArrayList<>();

            // Sort user's meetings by start time
            userMeetings.sort(Comparator.comparing(Meeting::getStartTime));

            // Iterate over each meeting and find overlaps with the given range (from, till)
            for (Meeting meeting : userMeetings) {
                // Check if the meeting overlaps with the given time range
                if (meeting.getEndTime().after(from) && meeting.getStartTime().before(till)) {
                    // Calculate the overlap
                    Date overlapStart = meeting.getStartTime().before(from) ? from : meeting.getStartTime();
                    Date overlapEnd = meeting.getEndTime().after(till) ? till : meeting.getEndTime();
                    unavailableSlots.add(new Meeting(overlapStart, overlapEnd));
                }
            }

            // Create a ConflictMeetingWrapper for this user
            ConflictMeetingWrapper conflictMeetingWrapper = new ConflictMeetingWrapper();
            conflictMeetingWrapper.setUserId(userId);
            conflictMeetingWrapper.setMeetings(unavailableSlots);
            if (!unavailableSlots.isEmpty()) conflictMeetingWrapperList.add(conflictMeetingWrapper);
        }

        return conflictMeetingWrapperList;
    }

    public static Date[] parseStartAndEndDates(String startDateTime, String endDateTime) {
        Date start, end;
        if (startDateTime.endsWith("Z")) {
            start = convertStringToDate(startDateTime);
            end = convertStringToDate(endDateTime);
        } else {
            start = DateUtils.parseDateWithoutTZ(startDateTime);
            end = DateUtils.parseDateWithoutTZ(endDateTime);
        }
        return new Date[]{start, end};
    }

    public static List<Meeting> convertEventDtosToMeetings(List<EventDto> eventDtos) {
        List<Meeting> meetings = new ArrayList<>();
        for (EventDto eventDto : eventDtos) {
            Meeting meeting = new Meeting();
            meeting.setStartTime(eventDto.getMeetingStartTime());
            meeting.setEndTime(eventDto.getMeetingEndTime());
            meetings.add(meeting);
        }
        return meetings;
    }

    public static OutOfOffice convertToOutOfOffice(String userId, Map<String, Object> result) {
        OutOfOffice outOfOffice = new OutOfOffice();
        outOfOffice.setUserId(userId);
        if (result.containsKey(SCHEDULED_START_DATETIME))
            outOfOffice.setStartDateTime((String) result.get(SCHEDULED_START_DATETIME));
        if (result.containsKey(SCHEDULED_START_TIMEZONE))
            outOfOffice.setTimeZone((String) result.get(SCHEDULED_START_TIMEZONE));
        if (result.containsKey(SCHEDULED_END_DATETIME))
            outOfOffice.setEndDateTime((String) result.get(SCHEDULED_END_DATETIME));
        if (result.containsKey(INTERNAL_REPLY_MESSAGE))
            outOfOffice.setInternalMessage((String) result.get(INTERNAL_REPLY_MESSAGE));
        if (result.containsKey(EXTERNAL_REPLY_MESSAGE))
            outOfOffice.setExternalMessage((String) result.get(EXTERNAL_REPLY_MESSAGE));
        if (result.containsKey(STATUS)) outOfOffice.setStatus((String) result.get(STATUS));
        return outOfOffice;
    }

    public static Date convertLocalTimeToDate(LocalTime localTime) {
        // Use the current date with the provided time
        LocalDate currentDate = LocalDate.now();
        LocalDateTime localDateTime = LocalDateTime.of(currentDate, localTime);

        // Convert LocalDateTime to Date
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Map<String, String> getMapFromJsonString(String jsonString) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(jsonString, new TypeReference<Map<String, String>>() {
            });
        } catch (JsonProcessingException e) {
            return new HashMap<>();
        }
    }


    /*
    public static List<ConflictMeetingWrapper> findUnavailableTimeSlots(Map<String, List<Meeting>> meetings, Date from, Date till) {
        List<ConflictMeetingWrapper> ConflictMeetingWrapperList=new ArrayList<>();
        ConflictMeetingWrapper conflictMeetingWrapper=new ConflictMeetingWrapper();
        List<Meeting> unavailableSlots = new ArrayList<>();
        // Merge all meetings into one list and sort by start time
        List<Meeting> allMeetings = new ArrayList<>();
        for (List<Meeting> userMeetings : meetings.values()) {
            allMeetings.addAll(userMeetings);
        }
        allMeetings.sort(Comparator.comparing(Meeting::getStartTime));
        // Iterate over each meeting and find overlaps with the given range (from, till)
        for (Meeting meeting : allMeetings) {
            // Check if the meeting overlaps with the given time range
            if (meeting.getEndTime().after(from) && meeting.getStartTime().before(till)) {
                // Calculate the overlap
                Date overlapStart = meeting.getStartTime().before(from) ? from : meeting.getStartTime();
                Date overlapEnd = meeting.getEndTime().after(till) ? till : meeting.getEndTime();
                unavailableSlots.add(new Meeting(overlapStart, overlapEnd));
            }
        }
        conflictMeetingWrapper.setUserId("<EMAIL>");
        conflictMeetingWrapper.setMeetings(unavailableSlots);
        ConflictMeetingWrapperList.add(conflictMeetingWrapper);
        return ConflictMeetingWrapperList;
    }
     */

    public static Map<String, String> convertStringToMap(String mapString) {
        mapString = mapString.substring(1, mapString.length() - 1);

        // Create a new HashMap to store the key-value pairs
        Map<String, String> map = new HashMap<>();

        // Split the string by comma followed by space to separate key-value pairs
        String[] keyValuePairs = mapString.split(", ");

        for (String pair : keyValuePairs) {
            // Split each pair by the equals sign to separate keys and values
            String[] entry = pair.split("=", 2);
            if (entry.length == 2) {
                String key = entry[0];
                String value = entry[1];
                map.put(key, value);
            } else {
                // Handle cases where there is no '=' or other parsing issues
                System.err.println("Skipping invalid pair: " + pair);
            }
        }

        return map;
    }

    public static String getMailSummaryForVector(MailSummary summary,String userId){
        String summaryString="";
        if(summary!=null){
            JSONObject summaryJson = new JSONObject(summary.getMessageSummary());
            String content="";
            JSONArray actionOwnerArray=new JSONArray();
            if(summaryJson.has("summaryObject")) {
               content = summaryJson.getJSONObject("summaryObject").getString("content");
                actionOwnerArray= summaryJson.getJSONArray("actionOwner");

           }
            String actionOwner="";
            for(int i=0;i<actionOwnerArray.length();i++){
                if(actionOwnerArray.get(i).equals(userId)){
                    actionOwner=actionOwnerArray.getString(i);
                }
            }
            summaryString+="Subject: "+summary.getSubject()+"\n From: "+summary.getFromUser()+"\n MailReceivedTime: "+summary.getMailReceivedTime()+
                "\n Priority: "+summary.getPriority()+"\n Category: "+summary.getCategory()+"\n Type: "+summary.getType()
            +"\nisStarMarked: "+summary.getStarMarked()+"\n Content: "+content+"\n messageId: "+summary.getMessageId()+"\n FolderName: "+summary.getFolderName()+"\n ActionOwner: "+actionOwner;
        }
        return summaryString;

    }

    public static String getMeetingId(String body){
        log.debug("Meeting content is {}",body);
        Pattern pattern = Pattern.compile("Meeting ID : (\\d+)");
        Matcher matcher = pattern.matcher(body);

        // Find and print the meeting ID
        if (matcher.find()) {
            String meetingId = matcher.group(1);
            System.out.println("Meeting ID: " + meetingId);
            return meetingId;
        } else {
            System.out.println("Meeting ID not found");
            return null;
        }
    }

    /**
     * Creates a temporary file to store the attachment data and writes the data to the file.
     *
     * @param fileBytes Byte array containing the attachment data.
     * @param filename  Name of the file to create.
     * @return A Path object representing the location of the created temporary file.
     * @throws IOException if an I/O error occurs during file creation or writing.
     */
    public static Path createTempFile(byte[] fileBytes, String filename, String timestamp) throws IOException {
        Path tempFile = Files.createTempFile(Paths.get(attachmentFilePath), ATTACHMENT, DASH +timestamp+DASH+ filename);
        try (FileOutputStream fos = new FileOutputStream(tempFile.toFile())) {
            fos.write(fileBytes);
        }
        return tempFile;
    }

}
