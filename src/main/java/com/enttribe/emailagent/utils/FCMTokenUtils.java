package com.enttribe.emailagent.utils;

import com.google.auth.oauth2.GoogleCredentials;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.time.Instant;

/**
 * The type Fcm token utils.
 *  <AUTHOR> <PERSON>
 */
@Service
@Slf4j
@ConditionalOnExpression("#{environment.getProperty('kafka.enabled', 'false') == 'false' and environment.getProperty('notification.enable', 'false') == 'true'}")
public class FCMTokenUtils {

    @Value("${fcm.token.refresh.interval}")
    private long refreshInterval;

    @Value("${notification.json.path}")
    private String jsonPath;

    private String accessToken;
    private Instant expiryTime;

    int maxRetries = 5;  // Set maximum retry attempts
    int retryCount = 0;  // Keep track of retry attempts
    long retryDelay = 5000;  // Delay between retries in milliseconds

    // Method to retrieve the access token
    public synchronized String getAccessToken() throws InterruptedException {
        if (accessToken == null || Instant.now().isAfter(expiryTime)) {
            log.error("\n\n\nWARNING!!! Refreshing token from @method getAccessToken\n\n\n");
            refreshToken();
        }
        return accessToken;
    }

    // Scheduled task to refresh the access token
    @Scheduled(fixedRateString = "${fcm.token.refresh.interval}")
    public synchronized void refreshToken() throws InterruptedException {
        log.debug("Refreshing token...");

        //Resetting the variables...
        maxRetries = 5;  // Set maximum retry attempts
        retryCount = 0;  // Keep track of retry attempts
        retryDelay = 5000;  // Delay between retries in milliseconds

        accessToken = fetchNewAccessToken();
        //Keep expiryTime 300000ms more to match exact token expiry time
        expiryTime = Instant.now().plusMillis(refreshInterval + 300000);
    }

    public String fetchNewAccessToken() throws InterruptedException {
        log.debug("Inside @method fetchNewAccessToken in FCM utils");

        while (retryCount < maxRetries) {
            try {
                // Load the service account JSON key
                GoogleCredentials credentials = GoogleCredentials
                        .fromStream(new FileInputStream(jsonPath))
                        .createScoped("https://www.googleapis.com/auth/firebase.messaging");

                // Refresh the token if expired, to get a new token
                credentials.refreshIfExpired();

                // Return the Bearer token string
                return credentials.getAccessToken().getTokenValue();
            } catch (Exception e) {
                log.error("Exception occurred while refreshing token on attempt {}", retryCount + 1, e);
                retryCount++;

                if (retryCount < maxRetries) {
                    log.info("Retrying to fetch token in {} seconds... Attempt {} of {}", retryDelay / 1000, retryCount + 1, maxRetries);
                    Thread.sleep(retryDelay);  // Wait before retrying
                } else {
                    log.error("Maximum retry attempts reached. Unable to fetch new access token.", e);
                    throw new RuntimeException("Failed to fetch new access token after " + maxRetries + " attempts.", e);
                }
            }
        }

        return null;  // Fallback, should never reach here since we throw on final failure
    }

}
