package com.enttribe.emailagent.utils;


import java.util.Map;
import java.util.Properties;
import com.google.cloud.pubsub.v1.Publisher;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import com.google.pubsub.v1.TopicName;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.google.pubsub.v1.TopicName;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.enttribe.emailagent.exception.EmailAgentLogger;

import lombok.extern.slf4j.Slf4j;

/**
 * The type Kafka utils.
 *  <AUTHOR> Sonsale
 */
@Service
@Slf4j
public class KafkaUtils {
	
	
	private static final Logger auditLog = EmailAgentLogger.getLogger(KafkaUtils.class);
	private static String kafkaUrl;

    private static String mode;

    @Value("${consumer.mode}")
    public void setconsumerMode(String mode) {
        this.mode = mode;
    }



    private static String pubSubProjectId;

    @Value("${pubsub.projectId}")
    public void setPubSubProjectId(String pubSubProjectId) {
        this.pubSubProjectId = pubSubProjectId;
    }
    
    @Value("${kafka.kafkaUrl}")
    public void setkafkaUrl(String kafkaUrl) {
        this.kafkaUrl = kafkaUrl;
    }

    
    private KafkaProducer<String, String> getKafkaProducer(){
    	Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaUrl); // Replace with your Kafka server address
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1000); // Adjust the linger time to wait before sending records
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 32768); // Adjust the batch size
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 67108864); // Adjust the buffer memory (32 MB)
        KafkaProducer<String, String> producer = new KafkaProducer<>(props);
        return producer;
    }
    
    /*public void produceMessage(String message,String batchId,String queueName) {
    	log.debug("message is {}",message);
    	KafkaProducer<String, String> producer=getKafkaProducer();
    	ProducerRecord<String, String> record = new ProducerRecord<>(queueName,batchId, message);
    	producer.send(record, (metadata, exception) -> {
            if (exception != null) {
                Map<String, String> auditMap = Map.of("batchId", batchId);
                auditLog.error("Error polling for user", exception.getMessage(), exception.getMessage(), null, null, "KAFKA_PRODUCE", null, null, null, null, null,
                        AIUtils.convertToJSON(auditMap, true), null);
            } else {
                log.debug("Message sent successfully. Metadata: " + metadata.toString());
            }
        });
        producer.close();
    	
    }*/

    public void produceMessage(String message, String batchId, String queueName) {
//        log.debug("message is {}", message);
        log.info("mode is {}",mode);
        if ("KAFKA".equalsIgnoreCase(mode)) {
            // Kafka producer logic
            KafkaProducer<String, String> producer = getKafkaProducer();
            ProducerRecord<String, String> record = new ProducerRecord<>(queueName, batchId, message);
            producer.send(record, (metadata, exception) -> {
                if (exception != null) {
                    Map<String, String> auditMap = Map.of("batchId", batchId);
                    auditLog.error("Error polling for user", exception.getMessage(), exception.getMessage(), null, null, "KAFKA_PRODUCE", null, null, null, null, null,
                            AIUtils.convertToJSON(auditMap, true), null);
                } else {
                    log.debug("Kafka Message sent successfully. Metadata: {}", metadata.toString());
                }
            });
            producer.close();
        } else if ("PUBSUB".equalsIgnoreCase(mode)) {
            // Google Pub/Sub publisher logic
            log.info("PUBSUB producer executed {} {}", pubSubProjectId, queueName);
            Publisher publisher = null;
            try {
                String projectId = pubSubProjectId;  // Update with your project ID
                TopicName topicName = TopicName.of(projectId, queueName);

                publisher = Publisher.newBuilder(topicName).build();  // Initialize publisher

                ByteString data = ByteString.copyFromUtf8(message);
                PubsubMessage pubsubMessage = PubsubMessage.newBuilder()
                        .setData(data)
                        .putAttributes("batchId", batchId)
                        .build();

                // Publish message and wait for result
                publisher.publish(pubsubMessage).get();
                log.debug("Pub/Sub Message sent successfully to topic: {}", queueName);

            } catch (Exception e) {
                Map<String, String> auditMap = Map.of("batchId", batchId);
                auditLog.error("Error publishing to Pub/Sub", e.getMessage(), e.getMessage(), null, null,
                        "PUBSUB_PRODUCE", null, null, null, null, null,
                        AIUtils.convertToJSON(auditMap, true), null);
            } finally {
                // Ensure the publisher is always shut down
                if (publisher != null) {
                    try {
                        publisher.shutdown();
                    } catch (Exception shutdownException) {
                        log.error("Error shutting down Pub/Sub publisher", shutdownException);
                    }
                }
                }
        }
    }





    public KafkaConsumer<String, String>  getKafkaConsumerClient(String queueName){
    	Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaUrl); // Replace with your Kafka server address
        props.put(ConsumerConfig.GROUP_ID_CONFIG, queueName+"-group"); // Replace with your consumer group ID
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest"); // Start reading from the beginning if no offset is committed
       // props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "true");
        //props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "1000");
        // Create Kafka Consumer
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
        return consumer;
    }
    
    
    

}
