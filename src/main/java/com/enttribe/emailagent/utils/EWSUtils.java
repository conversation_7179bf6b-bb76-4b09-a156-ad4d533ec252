package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.wrapper.MessageWrapper;
import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.enumeration.misc.ConnectingIdType;
import microsoft.exchange.webservices.data.core.enumeration.misc.ExchangeVersion;
import microsoft.exchange.webservices.data.core.exception.service.local.ServiceLocalException;
import microsoft.exchange.webservices.data.core.service.item.EmailMessage;
import microsoft.exchange.webservices.data.credential.WebCredentials;
import microsoft.exchange.webservices.data.misc.ImpersonatedUserId;
import microsoft.exchange.webservices.data.property.complex.ItemId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Base64;

import static com.enttribe.emailagent.utils.MobileUtils.encodeBase64;
import static com.enttribe.emailagent.utils.MobileUtils.getEmailList;


/**
 * The type Ews utils.
 *  <AUTHOR> Dangi
 */
@Slf4j
@Component
public class EWSUtils {

    private static String serviceAccountUsername;
    private static String serviceAccountPassword;
    private static String ewsURL;

    @Value("${ews.serviceAccountUsername}")
    public void setServiceAccountUsername(String serviceAccountUsername) {
        EWSUtils.serviceAccountUsername = serviceAccountUsername;
    }

    @Value("${ews.serviceAccountPassword}")
    public void setServiceAccountPassword(String serviceAccountPassword) {
        EWSUtils.serviceAccountPassword = serviceAccountPassword;
    }

    @Value("${ews.ewsURL}")
    public void setEwsURL(String ewsURL) {
        EWSUtils.ewsURL = ewsURL;
    }

    public static ExchangeService getServiceObject() throws URISyntaxException {
        log.debug("EWS parameters are {} {} {}", serviceAccountUsername, serviceAccountPassword, ewsURL);
        ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2010_SP2);
        service.setCredentials(new WebCredentials(serviceAccountUsername, serviceAccountPassword));
        service.setUrl(new URI(ewsURL));
        return service;
    }

    public static ExchangeService getServiceObjectForUser(String email) throws URISyntaxException {
        log.debug("EWS parameters for @method getServiceObjectForUser are {} {} {}", email, serviceAccountPassword, ewsURL);
        ExchangeService service = getServiceObject();
        ImpersonatedUserId impersonatedUserId = new ImpersonatedUserId(ConnectingIdType.SmtpAddress, email);
        service.setImpersonatedUserId(impersonatedUserId);
        return service;
    }

    public static EmailMessage findEmailById(ExchangeService service, String messageId) throws Exception {
        // Bind to the email message by its ID
        ItemId itemId = new ItemId(messageId);
        EmailMessage emailMessage = EmailMessage.bind(service, itemId);
        // Load email properties
        emailMessage.load();
        return emailMessage;
    }

    public static ExchangeService getServiceObjectForLogin(String username,String password) throws URISyntaxException {
        ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2010_SP2);
        service.setCredentials(new WebCredentials(username, password));
        service.setUrl(new URI(ewsURL));
        return service;
    }

    public static String decodeBase64(String encodedString) {
        // Decode Base64-encoded string
        byte[] decodedBytes = Base64.getDecoder().decode(encodedString);
        return new String(decodedBytes);
    }



    /**
     * Converts the {@link EmailMessage} object into a {@link MessageWrapper} object.
     *
     * <p>This method extracts details from the given {@link EmailMessage}, such as content, recipients,
     * subject, message ID, and attachment information. The email body is Base64 encoded before being
     * set in the {@link MessageWrapper} object.
     *
     * @param emailMessage The {@link EmailMessage} to be converted.
     * @return A {@link MessageWrapper} containing the converted email message details.
     */
    public static MessageWrapper convertToMessageWrapper(EmailMessage emailMessage) {
        MessageWrapper messageWrapper = new MessageWrapper();
        try {
            messageWrapper.setIsRead(emailMessage.getIsRead());
            messageWrapper.setUserName(emailMessage.getFrom().getName());
            messageWrapper.setUserId(emailMessage.getFrom().getAddress());
            messageWrapper.setEmail(emailMessage.getFrom().getAddress());
            messageWrapper.setFrom(emailMessage.getFrom().getAddress());
            messageWrapper.setContent(encodeBase64(emailMessage.getBody().toString()));
            messageWrapper.setMessageId(emailMessage.getId().getUniqueId());
            messageWrapper.setCcRecipients(getEmailList(emailMessage.getCcRecipients().getItems()));
            messageWrapper.setToRecipients(getEmailList(emailMessage.getToRecipients().getItems()));
            messageWrapper.setBccRecipients(getEmailList(emailMessage.getBccRecipients().getItems()));
            messageWrapper.setSubject(emailMessage.getSubject());
            messageWrapper.setInternetMessageId(emailMessage.getInternetMessageId());
            messageWrapper.setConvercationId(emailMessage.getConversationId().getUniqueId());
            messageWrapper.setHashAttachment(emailMessage.getHasAttachments());
        } catch (ServiceLocalException e) {
            e.printStackTrace();
        }
        return messageWrapper;
    }

}
