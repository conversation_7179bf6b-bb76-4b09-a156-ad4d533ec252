package com.enttribe.emailagent.utils;
import com.enttribe.emailagent.wrapper.MessageWrapper;
import org.apache.http.client.methods.*;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.http.entity.ContentType;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.enttribe.emailagent.utils.MobileUtils.encodeBase64;

/**
 * A utility class for parsing email content from a JSON object into a MessageWrapper.
 *  <AUTHOR>
 */
public class GraphUtils {

    // Constants for the JSON field names
    public static final String FROM = "from";
    public static final String TO_RECIPIENTS = "toRecipients";
    public static final String CC_RECIPIENTS = "ccRecipients";
    public static final String BCC_RECIPIENTS = "bccRecipients";
    public static final String BODY = "body";
    public static final String IS_READ = "isRead";
    public static final String ID = "id";
    public static final String EMAIL_ADDRESS = "emailAddress";
    public static final String ADDRESS = "address";
    public static final String CONTENT = "content";
    public static final String ATTACHMENTS = "attachments";
    public static final String NAME = "name";
    private static final Logger log = LoggerFactory.getLogger(GraphUtils.class);

    /**
     * Parses the given email JSON object into a MessageWrapper object.
     *
     * @param emailJson The JSON object containing the email content.
     * @return A MessageWrapper object with parsed email data.
     */
    public static MessageWrapper parseEmailContent(JSONObject emailJson) {
        log.info("emailJson is {}",emailJson);
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setSubject(parseSubject(emailJson));
        parseFrom(messageWrapper,emailJson);
        messageWrapper.setIsRead(parseMailRead(emailJson));
        messageWrapper.setToRecipients(parseRecipients(emailJson, TO_RECIPIENTS));
        messageWrapper.setCcRecipients(parseRecipients(emailJson, CC_RECIPIENTS));
        messageWrapper.setBccRecipients(parseRecipients(emailJson, BCC_RECIPIENTS));
        messageWrapper.setContent(parseBody(emailJson));
        messageWrapper.setMessageId(parseMessageId(emailJson));
        // messageWrapper.setAttachmentPathList(parseAttachments(emailJson)); // Uncomment if needed
        return messageWrapper;
    }

    /**
     * Parses the subject from the given email JSON object.
     *
     * @param emailJson The JSON object containing the email content.
     * @return The subject of the email as a string.
     */
    private static String parseSubject(JSONObject emailJson) {
        return emailJson.has(EmailConstants.SUBJECT) ? emailJson.getString(EmailConstants.SUBJECT) : null;
    }

    /**
     * Parses the sender's email address from the given email JSON object.
     *
     * @param emailJson The JSON object containing the email content.
     * @return The sender's email address as a string.
     */
    private static MessageWrapper parseFrom( MessageWrapper messageWrapper,JSONObject emailJson) {
        if (emailJson.has(FROM)) {
            JSONObject fromObject = emailJson.getJSONObject(FROM).getJSONObject(EMAIL_ADDRESS);
            messageWrapper.setEmail(fromObject.getString(ADDRESS));
            messageWrapper.setUserName(fromObject.getString(NAME));
            return messageWrapper;
        }
        return messageWrapper;
    }

    /**
     * Parses the recipients (to, cc, or bcc) from the given email JSON object.
     *
     * @param emailJson The JSON object containing the email content.
     * @param recipientType The type of recipients to parse (e.g., "toRecipients", "ccRecipients", "bccRecipients").
     * @return A list of recipient email addresses.
     */
    private static List<String> parseRecipients(JSONObject emailJson, String recipientType) {
        List<String> recipients = new ArrayList<>();
        if (emailJson.has(recipientType)) {
            JSONArray recipientArray = emailJson.getJSONArray(recipientType);
            for (int i = 0; i < recipientArray.length(); i++) {
                JSONObject recipientObject = recipientArray.getJSONObject(i).getJSONObject(EMAIL_ADDRESS);
                recipients.add(recipientObject.getString(ADDRESS));
            }
        }
        return recipients;
    }

    /**
     * Parses the body content from the given email JSON object.
     *
     * @param emailJson The JSON object containing the email content.
     * @return The body content encoded in Base64 as a string.
     */
    private static String parseBody(JSONObject emailJson) {
        if (emailJson.has(BODY)) {
            JSONObject bodyObject = emailJson.getJSONObject(BODY);
            return encodeBase64(bodyObject.getString(CONTENT));
        }
        return null;
    }

    /**
     * Parses the message is read/unread from the given email JSON object.
     *
     * @param emailJson The JSON object containing the email content.
     * @return The read/unread boolean.
     */
    private static Boolean parseMailRead(JSONObject emailJson) {
        if (emailJson != null && emailJson.has(IS_READ)) {
            try {
                Object isRead = emailJson.get(IS_READ);
                if (isRead instanceof Boolean) return (Boolean) isRead;
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }


    /**
     * Parses the message ID from the given email JSON object.
     *
     * @param emailJson The JSON object containing the email content.
     * @return The message ID as a string.
     */
    private static String parseMessageId(JSONObject emailJson) {
        return emailJson.has(ID) ? emailJson.getString(ID) : null;
    }

    // Uncomment if attachment parsing is required in the future
    /*
    private static List<String> parseAttachments(JSONObject emailJson) {
        List<String> attachmentPaths = new ArrayList<>();
        if (emailJson.has(ATTACHMENTS)) {
            JSONArray attachmentsArray = emailJson.getJSONArray(ATTACHMENTS);
            for (int i = 0; i < attachmentsArray.length(); i++) {
                JSONObject attachment = attachmentsArray.getJSONObject(i);
                attachmentPaths.add(attachment.getString(NAME));
            }
        }
        return attachmentPaths;
    }
    */


    /**
     * Moves a specified email message to the Deleted Items folder.
     *
     * @param messageId    The unique identifier of the email message to be moved.
     * @param accessToken  The OAuth 2.0 access token for authenticating the request.
     * @param httpClient   An instance of {@link CloseableHttpClient} used to execute the HTTP request.
     * @param userMail     The email address of the user whose message is being moved.
     * @return {@code true} if the message was successfully moved; {@code false} otherwise.
     * @throws Exception If an error occurs during the HTTP request execution or processing the response.
     */
    public static boolean moveMessageToFolder(String messageId, String accessToken, CloseableHttpClient httpClient, String userMail) throws Exception {
        HttpPost postRequest = getHttpPost(messageId, accessToken, userMail);

        try (CloseableHttpResponse response = httpClient.execute(postRequest)) {
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            if (statusCode == 200 ||statusCode == 201 ) {
                log.info("Successfully moved email to Deleted Items folder.");
                return true;
            } else {
                log.error("Failed to move email. ErrorMessage: {} Status code: {} Response body: {}", responseBody, statusCode, responseBody);
                return false;
            }
        } catch (IOException e) {
            log.error("IOException occurred while moving email: {}", e.getMessage());
            throw e;
        }
    }


    /**
     * Constructs an HTTP POST request to move a specified email message to a designated folder.
     *
     * @param messageId    The unique identifier of the email message to be moved.
     * @param accessToken  The OAuth 2.0 access token for authenticating the request.
     * @param userMail     The email address of the user whose message is being moved.
     * @return A configured {@link HttpPost} request ready for execution.
     */
    @NotNull
    private static HttpPost getHttpPost(String messageId, String accessToken, String userMail) {
        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/move", userMail, messageId);

        HttpPost postRequest = new HttpPost(url);
        postRequest.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + accessToken);
        postRequest.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

        String jsonBody = "{\"destinationId\": \"deleteditems\"}";
        postRequest.setEntity(new StringEntity(jsonBody, ContentType.APPLICATION_JSON));
        return postRequest;
    }


}

