package com.enttribe.emailagent.utils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * The type Aes decryption.
 *  <AUTHOR>
 */
public class AESDecryption {

    static String keyString = "Thisis128bitKey!"; // Must be 16 characters (128 bits)
    static String initVector = "RandomInitVector"; // Must be 16 characters


    public static String decrypt(String encrypted) throws Exception {
        // Convert the key and IV to byte arrays
        IvParameterSpec iv = new IvParameterSpec(initVector.getBytes("UTF-8"));
        SecretKeySpec secretKey = new SecretKeySpec(keyString.getBytes("UTF-8"), "AES");

        // Create the AES cipher in CBC mode with PKCS5 padding
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
        cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

        // Decode the base64-encoded encrypted string
        byte[] encryptedBytes = Base64.getDecoder().decode(encrypted);

        // Perform the decryption
        byte[] original = cipher.doFinal(encryptedBytes);

        // Convert the decrypted bytes to a string
        return new String(original);
    }



    public static void main(String[] args) throws Exception {
        // Static objects, the key and IV must be 16 characters each

        // Plain text to be encrypted
        String plainText = "<EMAIL>";

        // Encrypt the message
        String encryptedMessage = encrypt(keyString, initVector, plainText);

        // Log the encrypted message
        System.out.println("Encrypted message (Base64): " + encryptedMessage);
    }

    public static String encrypt(String key, String initVector, String plainText) throws Exception {
        // Convert the key and IV to byte arrays
        IvParameterSpec iv = new IvParameterSpec(initVector.getBytes("UTF-8"));
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes("UTF-8"), "AES");

        // Create the AES cipher in CBC mode with PKCS5 padding
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

        // Encrypt the plain text
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());

        // Encode the encrypted bytes to base64 to make it readable
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

}