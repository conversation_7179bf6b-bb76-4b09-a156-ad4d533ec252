package com.enttribe.emailagent.utils;

import com.microsoft.aad.msal4j.ClientCredentialFactory;
import com.microsoft.aad.msal4j.ClientCredentialParameters;
import com.microsoft.aad.msal4j.ConfidentialClientApplication;
import com.microsoft.aad.msal4j.IAuthenticationResult;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

/**
 * The type Token utils.
 *  <AUTHOR>
 */
@Service
@Slf4j
public class TokenUtils {

    @Value("${token.refresh.interval}")
    private long refreshInterval;

    @Value("${email.client.id}")
    private String clientId;
    @Value("${email.client.secret}")
    private String clientSecret;
    @Value("${email.tenant.id}")
    private String tenantId;

    private String accessToken;
    private Instant expiryTime;

    @PostConstruct
    public void init() {
        // Decrypt to verify and use internally
        try {
            log.info("decrypting the client credentials");
            this.clientId = AESUtils.decrypt(clientId);
            this.clientSecret = AESUtils.decrypt(clientSecret);
            this.tenantId = AESUtils.decrypt(tenantId);
            log.info("Successfully decrypted the client credentials");
        } catch (Exception e) {
            log.error("error in decrypting credentials : {}", e.getMessage(), e);
            throw new RuntimeException("error in decrypting graph credentials");
        }
    }

    int maxRetries = 5;  // Set maximum retry attempts
    int retryCount = 0;  // Keep track of retry attempts
    long retryDelay = 5000;  // Delay between retries in milliseconds

    // Method to retrieve the access token
    public synchronized String getAccessToken() throws InterruptedException {
        if (accessToken == null || Instant.now().isAfter(expiryTime)) {
            log.error("\n\n\nWARNING!!! Refreshing token from @method getAccessToken\n\n\n");
            refreshToken();
        }
        return accessToken;
    }

    // Scheduled task to refresh the access token
    @Scheduled(fixedRateString = "${token.refresh.interval}", initialDelay = 5000)
    public synchronized void refreshToken() throws InterruptedException {
        log.debug("Refreshing token...");

        //Resetting the variables...
        maxRetries = 5;  // Set maximum retry attempts
        retryCount = 0;  // Keep track of retry attempts
        retryDelay = 5000;  // Delay between retries in milliseconds

        accessToken = fetchNewAccessToken();
        //Keep expiryTime 300000ms more to match exact token expiry time
        expiryTime = Instant.now().plusMillis(refreshInterval + 300000);
    }

    public String fetchNewAccessToken() throws InterruptedException {
        log.debug("Inside @method fetchNewAccessToken");

        while (retryCount < maxRetries) {
            try {
                ConfidentialClientApplication app = ConfidentialClientApplication.builder(
                                clientId,
                                ClientCredentialFactory.createFromSecret(clientSecret))
                        .authority(String.format("https://login.microsoftonline.com/%s", tenantId))
                        .build();
                ClientCredentialParameters parameters = ClientCredentialParameters.builder(
                                Collections.singleton("https://graph.microsoft.com/.default"))
                        .build();
                CompletableFuture<IAuthenticationResult> future = app.acquireToken(parameters);
                IAuthenticationResult result = future.get();

                accessToken = result.accessToken();
                return accessToken;
            } catch (Exception e) {
                log.error("Exception occurred while refreshing token on attempt {}", retryCount + 1, e);
                retryCount++;

                if (retryCount < maxRetries) {
                    log.info("Retrying to fetch token in {} seconds... Attempt {} of {}", retryDelay / 1000, retryCount + 1, maxRetries);
                    Thread.sleep(retryDelay);  // Wait before retrying
                } else {
                    log.error("Maximum retry attempts reached. Unable to fetch new access token.", e);
                    throw new RuntimeException("Failed to fetch new access token after " + maxRetries + " attempts.", e);
                }
            }
        }

        return null;  // Fallback, should never reach here since we throw on final failure
    }


}
