package com.enttribe.emailagent.utils;

/**
 * The type Email agent logger constants.
 *  <AUTHOR>
 */
public class EmailAgentLoggerConstants {
    public static final String SUMMARIZE_ATTACHMENT = "SUMMARIZE_ATTACHMENT";
    public static final String CATEGORY = "CATEGORY";
    public static final String TAG = "TAG";
    public static final String GRAPH_NETWORK_CALL = "GRAPH_NETWORK_CALL";
    public static final String EWS_NETWORK_CALL = "EWS_NETWORK_CALL";
    public static final String PROCESS_ATTACHMENT = "PROCESS_ATTACHMENT";
    public static final String MEETING_FROM_CHAT = "SCHEDULE_MEETING";
    public static final String MEETING_DRAFT = "MEETING_DRAFT";
    public static final String PARSE_AI_RESPONSE = "PARSE_AI_RESPONSE";
    public static final String POLLING = "POLLING";
    public static final String EXCEPTION_MESSAGE = "exceptionMessage";
    public static final String EXCEPTION_TRACE = "exceptionTrace";
    public static final String MESSAGE_ID = "messageId";
    public static final String MESSAGE = "MESSAGE";
    public static final String EVENT = "EVENT";
    public static final String INTERNET_MESSAGE_ID = "internetMessageId";
    public static final String TYPE = "type";
    public static final String EMAIL = "email";
    public static final String EMAIL_SUBJECT = "emailSubject";
    public static final String CONVERSATION_ID = "conversationId";
    public static final String ATTACHMENT = "attachment";
    public static final String USER_MAIL_ATTACHMENT = "userMailAttachment";
    public static final String INTENT = "intent";
    public static final String ATTENDEES = "attendees";
    public static final String CUSTOM_EXCEPTION_MESSAGE = "customExceptionMessage";
    public static final String ERROR_DATE = "errorDate";
    public static final String GENERATE_DRAFT = "GENERATE_DRAFT";
    public static final String GENERATE_MEETING_DRAFT = "GENERATE_DRAFT";
    public static final String IMPROVE_DRAFT = "IMPROVE_DRAFT";
    public static final String FRESH_DRAFT = "FRESH_DRAFT";
    public static final String IMPROVE_FRESH_DRAFT = "IMPROVE_FRESH_DRAFT";
    public static final String IMPROVE_SELECTION = "IMPROVE_SELECTION";
    public static final String GENERATE_CATEGORY = "GENERATE_CATEGORY";
    public static final String GENERATE_OBJECTIVE = "GENERATE_OBJECTIVE";
    public static final String GENERATE_MAIL_SUMMARY = "GENERATE_MAIL_SUMMARY";
    public static final String GENERATE_THREAD_SHORT_SUMMARY = "GENERATE_THREAD_SHORT_SUMMARY";
    public static final String GENERATE_THREAD_LONG_SUMMARY = "GENERATE_THREAD_LONG_SUMMARY";
    public static final String GENERATE_TAG = "GENERATE_TAG";
    public static final String FLAG_GRAPH = "VOICE_FLAG_GRAPH";
    public static final String DELETED_EMAIL_GRAPH = "DELETED_EMAIL_GRAPH";
    public static final String FLAG_STATUS_GRAPH = "FLAG_STATUS_GRAPH";
    public static final String SET_CATEGORY_GRAPH = "SET_CATEGORY_GRAPH";
    public static final String GET_CALENDER_EVENT_GRAPH = "GET_CALENDER_EVENT_GRAPH";
    public static final String SCHEDULE_MEETING_GRAPH = "VOICE_SCHEDULE_MEETING_GRAPH";
    public static final String DRAFT_REPLY_GRAPH = "VOICE_DRAFT_REPLY_GRAPH";
    public static final String CREATE_DRAFT_GRAPH = "VOICE_CREATE_DRAFT_GRAPH";
    public static final String CREATE_REPLY_GRAPH = "CREATE_REPLY_GRAPH";
    public static final String USER_FOLDER_GRAPH = "USER_FOLDER_GRAPH";
    public static final String TAG_EMAIL_GRAPH = "TAG_EMAIL_GRAPH";
    public static final String UPLOAD_S3 = "UPLOAD_S3";
    public static final String SENT_ITEMS_GRAPH = "SENT_ITEMS_GRAPH";
    public static final String AVAILABLE_SLOT_GRAPH = "VOICE_AVAILABLE_SLOT_GRAPH";
    public static final String ACCEPT_MEETING_GRAPH = "ACCEPT_MEETING_GRAPH";
    public static final String DECLINE_MEETING_GRAPH = "DECLINE_MEETING_GRAPH";
    public static final String TENTATIVE_MEETING_ACCEPT_GRAPH = "TENTATIVE_MEETING_ACCEPT_GRAPH";
    public static final String GET_CALENDER_EVENT_EWS = "GET_CALENDER_EVENT_EWS";
    public static final String SET_CATEGORY_EWS = "SET_CATEGORY_EWS";
    public static final String SCHEDULE_MEETING_EWS = "VOICE_SCHEDULE_MEETING_EWS";
    public static final String ACCEPT_MEETING_EWS = "ACCEPT_MEETING_EWS";
    public static final String DECLINE_MEETING_EWS = "DECLINE_MEETING_EWS";
    public static final String TENTATIVE_MEETING_ACCEPT_EWS = "TENTATIVE_MEETING_ACCEPT_EWS";
    public static final String USER_FOLDER_EWS = "USER_FOLDER_EWS";
    public static final String CREATE_DRAFT_EWS = "VOICE_CREATE_DRAFT_EWS";
    public static final String CREATE_REPLY_EWS = "VOICE_CREATE_REPLY_EWS";
    public static final String AVAILABLE_SLOT_EWS = "VOICE_AVAILABLE_SLOT_EWS";
    public static final String FLAG_EWS = "VOICE_FLAG_EWS";
    public static final String FLAG_STATUS_EWS = "FLAG_STATUS_EWS";
    public static final String TAG_EMAIL_EWS = "TAG_EMAIL_EWS";
    public static final String BLACKLISTED_DOMAIN = "BLACKLISTED_DOMAIN";
    public static final String BLACKLISTED_SENDER = "BLACKLISTED_SENDER";
    public static final String BLACKLISTED_SUBJECT = "BLACKLISTED_SUBJECT";
    public static final String FORWARD_EWS = "FORWARD_EWS_CONSTANT";
    public static final String FORWARD_GRAPH = "FORWARD_GRAPH_CONSTANT";

}
