package com.enttribe.emailagent.utils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.security.SecureRandom;
import java.security.spec.KeySpec;
import java.util.Date;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;

import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.Properties;
import java.util.Set;


/**
 * Encoder/Decoder based on AES.
 *
 * <AUTHOR>
 */
public class AESUtils {


    private static final int KEY_SIZE = 128;
    private static final int ITERATION_COUNT = 10000;
    private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String SECRET_KEY_ALGORITHM = "PBKDF2WithHmacSHA256";
    private static final String PASSPHRASE = getDecodedPassphrase();
    public static String E_C = System.getenv("E_C");
    private static final String STATIC_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final String UTF_8 = "UTF-8";
    private static final SecretKeySpec STATIC_KEY_SPEC;
    private static final Cipher ENCRYPT_CIPHER;
    private static final Cipher DECRYPT_CIPHER;
    private static final Object LOCK = new Object();


    //    static {
//        try {
//            String staticSecretKey = decrypt("ThisisTest");
//            STATIC_KEY_SPEC = new SecretKeySpec(staticSecretKey.getBytes(UTF_8), "AES");
//
//            // Initialize cipher instances
//            ENCRYPT_CIPHER = Cipher.getInstance(STATIC_CIPHER_ALGORITHM);
//            ENCRYPT_CIPHER.init(Cipher.ENCRYPT_MODE, STATIC_KEY_SPEC);
//
//            DECRYPT_CIPHER = Cipher.getInstance(STATIC_CIPHER_ALGORITHM);
//            DECRYPT_CIPHER.init(Cipher.DECRYPT_MODE, STATIC_KEY_SPEC);
//        } catch (Exception e) {
//            throw new ExceptionInInitializerError("Failed to initialize encryption: " + e.getMessage());
//        }
//    }
    static {
        try {
            // Use a predefined static key (16 characters for AES-128)
            //System.setProperty(E_C, "DEH9a/7Q/SW2hZIPtqXfCw==:xN741eNOvC87XDQBB56uyQ==:qnGL14EFSSxOnisbU9InRg==");
            //	String staticSecretKey = decrypt("aFBX1UPu2xradKWscVV4Rg==:FMRxSG6iW1Rc+dxuC+DUZw==:/VX8rASXqapkSyEAgQ+0vfU6rZ2KFgnDV7+oWpPyNAo=");
            String staticSecretKey = decryptE_C(E_C);
            STATIC_KEY_SPEC = new SecretKeySpec(staticSecretKey.getBytes(UTF_8), "AES");
            System.out.println("STATIC_KEY_SPEC: " + STATIC_KEY_SPEC);

            ENCRYPT_CIPHER = Cipher.getInstance(STATIC_CIPHER_ALGORITHM);
            ENCRYPT_CIPHER.init(Cipher.ENCRYPT_MODE, STATIC_KEY_SPEC);
            System.out.println("ENCRYPT_CIPHER: " + ENCRYPT_CIPHER);

            DECRYPT_CIPHER = Cipher.getInstance(STATIC_CIPHER_ALGORITHM);
            DECRYPT_CIPHER.init(Cipher.DECRYPT_MODE, STATIC_KEY_SPEC);
            System.out.println("DECRYPT_CIPHER: " + DECRYPT_CIPHER);
        } catch (Exception e) {
            throw new ExceptionInInitializerError("Failed to initialize encryption: " + e.getMessage());
        }
    }


    private AESUtils() {
    }

    protected static String getDecodedPassphrase() {
        byte[] encodedBytes = {86, 71, 104, 112, 99, 121, 66, 112, 99, 121, 66, 109, 98, 51, 74, 108, 99, 50, 108, 110, 97,
                72, 81, 103, 90, 109, 57, 121, 73, 71, 86, 117, 89, 51, 74, 53, 99, 72, 81, 103, 89, 87, 53, 107, 73, 71, 82,
                108, 89, 51, 74, 53, 99, 72, 81, 61};
        return new String(Base64.decodeBase64(encodedBytes));
    }


    private static String encryptE_C(String plainText) throws Exception {
        SecureRandom secureRandom = new SecureRandom();
        byte[] iv = new byte[16]; // 16 bytes for a 128-bit AES block size
        byte[] salt = new byte[16];
        secureRandom.nextBytes(iv);
        secureRandom.nextBytes(salt);

        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        SecretKeySpec secretKeySpec = generateSecretKeySpec(salt);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes("UTF-8"));

        String ivBase64 = Base64.encodeBase64String(iv);
        String saltBase64 = Base64.encodeBase64String(salt);
        String encryptedBase64 = Base64.encodeBase64String(encryptedBytes);

        return ivBase64 + ":" + saltBase64 + ":" + encryptedBase64;
    }

    private static String decryptE_C(String cipherText) throws Exception {
        String[] parts = cipherText.split(":");
        if (parts.length != 3) {
            throw new IllegalArgumentException("Invalid encrypted text format");
        }

        byte[] iv = Base64.decodeBase64(parts[0]);
        byte[] salt = Base64.decodeBase64(parts[1]);
        byte[] encryptedBytes = Base64.decodeBase64(parts[2]);

        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        SecretKeySpec secretKeySpec = generateSecretKeySpec(salt);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, "UTF-8");
    }


    public static String encrypt(String plainText) throws Exception {
        return encryptWithStaticKey(plainText);
    }

    public static String decrypt(String cipherText) throws Exception {
        return decryptWithStaticKey(cipherText);
    }

    private static SecretKeySpec generateSecretKeySpec(byte[] salt) throws Exception {
        KeySpec keySpec = new PBEKeySpec(PASSPHRASE.toCharArray(), salt, ITERATION_COUNT, KEY_SIZE);
        SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance(SECRET_KEY_ALGORITHM);
        byte[] keyBytes = secretKeyFactory.generateSecret(keySpec).getEncoded();
        return new SecretKeySpec(keyBytes, "AES");
    }


    public static byte[] decryptFile(InputStream in) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            byte[] iv = new byte[16];
            byte[] salt = new byte[16];
            // Check return value of read for iv
            if (in.read(iv) != iv.length) {
                throw new IllegalArgumentException("Insufficient bytes read for IV");
            }
            // Check return value of read for salt
            if (in.read(salt) != salt.length) {
                throw new IllegalArgumentException("Insufficient bytes read for salt");
            }
            Cipher cipher = getChiperInstance(iv, salt, Cipher.DECRYPT_MODE);
            byte[] ibuf = new byte[1024];
            int len;
            while ((len = in.read(ibuf)) != -1) {
                byte[] obuf = cipher.update(ibuf, 0, len);
                if (obuf != null)
                    baos.write(obuf);
            }
            byte[] obuf = cipher.doFinal();
            if (obuf != null)
                baos.write(obuf);
            baos.flush();
        } catch (Exception e) {
            throw new Exception(e);
        }

        return baos.toByteArray();
    }


    public static void encryptFile(String filepath) throws Exception {
        SecureRandom secureRandom = new SecureRandom();
        byte[] iv = new byte[16]; // 16 bytes for a 128-bit AES block size
        byte[] salt = new byte[16];
        secureRandom.nextBytes(iv);
        secureRandom.nextBytes(salt);


        Cipher cipher = getChiperInstance(iv, salt, Cipher.ENCRYPT_MODE);
        File f = new File(filepath);
        String outFilename = (f.getParent() != null ? f.getParent() + File.separator : "") + "encoded_map_" + new Date().getTime() + ".txt";
        try (BufferedInputStream in = new BufferedInputStream(new FileInputStream(filepath));

             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(outFilename))) {
            bos.write(iv);
            bos.write(salt);
            byte[] ibuf = new byte[1024];
            int len;
            while ((len = in.read(ibuf)) != -1) {
                byte[] obuf = cipher.update(ibuf, 0, len);
                if (obuf != null)
                    bos.write(obuf);
            }
            byte[] obuf = cipher.doFinal();
            if (obuf != null)
                bos.write(obuf);
            bos.flush();
            bos.close();
        }


    }

    private static Cipher getChiperInstance(byte[] iv, byte[] salt, int mode) throws Exception {
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        SecretKeySpec secretKeySpec = generateSecretKeySpec(salt);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        cipher.init(mode, secretKeySpec, ivParameterSpec);
        return cipher;
    }


    public static String encryptWithStaticKey(String plainText) throws Exception {
        if (plainText == null) {
            return null;
        }

        byte[] encryptedBytes;
        synchronized (LOCK) {
            encryptedBytes = ENCRYPT_CIPHER.doFinal(plainText.getBytes(UTF_8));
        }
        return Base64.encodeBase64String(encryptedBytes);
    }

    public static String decryptWithStaticKey(String cipherText) throws Exception {
        if (cipherText == null) {
            return null;
        }

        byte[] decryptedBytes;
        synchronized (LOCK) {
            decryptedBytes = DECRYPT_CIPHER.doFinal(Base64.decodeBase64(cipherText));
        }
        return new String(decryptedBytes, UTF_8);
    }

    private static SecretKeySpec getStaticKeySpec() {
        return STATIC_KEY_SPEC;
    }

    public static void main(String[] args) {
        if (args.length < 2) {
            System.out.println("ArrayIndexOutOfBoundsException: 1, For Encrypt use command (sh encodeco.sh e '<input>') |  For Decrypt use command (sh encodeco.sh d '<input>')  | Note: Input must be in single quote");
        } else if (args[0].equals("e")) {
            System.out.println("Encrypted One");
            try {
                System.out.println(AESUtils.encrypt(args[1]));
            } catch (Exception e) {
                System.err.println("Encryption failed: " + e.getMessage());
            }
        } else if (args[0].equals("d")) {
            System.out.println("Decrypted One");
            try {
                if (args[1].contains(".properties")) {
                    System.out.println("Decrypted file " + args[1]);
                    decodePropertiesFile(args[1]);
                } else {
                    System.out.println(AESUtils.decrypt(args[1]));
                }
            } catch (Exception e) {
                System.err.println("Decryption failed: " + e.getMessage());
            }
        }
    }


    private static void decodePropertiesFile(String file) throws FileNotFoundException, IOException {
        FileReader reader = new FileReader(file);

        Properties p = new Properties();
        p.load(reader);
        Set s = p.keySet();
        for (Object o : s) {
            String v = (String) p.get(o);
            if (v.contains(",")) {
                String[] s1 = v.split(",");
                for (String s2 : s1) {
                    try {


                        System.out.println(o + "====" + s2 + "=======" + AESUtils.decrypt(s2.trim()));
                    } catch (Exception e) {
                        System.out.println(e.getMessage());
                    }
                }
            } else {

                try {

                    System.out.println(o + "====" + v + "=========" + AESUtils.decrypt(v.trim()));
                } catch (Exception e) {
                    System.out.println(e.getMessage());
                }
            }
        }
    }

}
