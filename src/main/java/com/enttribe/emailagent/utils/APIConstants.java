package com.enttribe.emailagent.utils;

/**
 * The type Api constants.
 *  <AUTHOR>
 */
public class APIConstants {
    public static final String INTERNET_MESSAGE_ID = "internetMessageId";
    public static final String MESSAGE_ID = "messageId";
    public static final String CONVERSATION_ID = "conversationId";
    public static final String DEFAULT = "default";
    
    // Email Preferences API Role Constants
    public static final String ROLE_API_UPDATE_TIME_ZONE = "ROLE_API_UPDATE_TIME_ZONE";
    public static final String ROLE_API_UPDATE_DEVICE_ID = "ROLE_API_UPDATE_DEVICE_ID";
    public static final String ROLE_API_CREATE_PREFERENCES = "ROLE_API_CREATE_PREFERENCES";
    public static final String ROLE_API_UPDATE_PREFERENCES = "ROLE_API_UPDATE_PREFERENCES";
    public static final String ROLE_API_SAVE_UPDATE_PREFERENCES = "ROLE_API_SAVE_UPDATE_PREFERENCES";
    public static final String ROLE_API_GET_PREFERENCES = "ROLE_API_GET_PREFERENCES";
    public static final String ROLE_API_CHECK_PREFERENCE = "ROLE_API_CHECK_PREFERENCE";
    public static final String ROLE_API_ADD_CONVERSATION_ID = "ROLE_API_ADD_CONVERSATION_ID";
    public static final String ROLE_API_DELETE_CONVERSATION_ID = "ROLE_API_DELETE_CONVERSATION_ID";
    public static final String ROLE_API_ADD_PREFERENCES = "ROLE_API_ADD_PREFERENCES";
    public static final String ROLE_API_DELETE_PREFERENCES = "ROLE_API_DELETE_PREFERENCES";
    public static final String ROLE_API_GET_TIME_ZONES = "ROLE_API_GET_TIME_ZONES";
    public static final String ROLE_API_SEARCH_PREFERENCES = "ROLE_API_SEARCH_PREFERENCES";
    public static final String ROLE_API_COUNT_PREFERENCES = "ROLE_API_COUNT_PREFERENCES";
    public static final String ROLE_API_PREFERENCE_BY_FILTER = "ROLE_API_PREFERENCE_BY_FILTER";
    public static final String ROLE_API_PREFERENCE_COUNT_BY_FILTER = "ROLE_API_PREFERENCE_COUNT_BY_FILTER";
    public static final String ROLE_API_UPDATE_NOTIFICATION_INFO = "ROLE_API_UPDATE_NOTIFICATION_INFO";

    // Email User API Role Constants
    public static final String ROLE_API_GET_ALL_USERS = "ROLE_API_GET_ALL_USERS";
    public static final String ROLE_API_GET_USER_BY_ID = "ROLE_API_GET_USER_BY_ID";
    public static final String ROLE_API_CREATE_USER = "ROLE_API_CREATE_USER";
    public static final String ROLE_API_SAVE_USER = "ROLE_API_SAVE_USER";
    public static final String ROLE_API_UPDATE_USER_STATUS = "ROLE_API_UPDATE_USER_STATUS";
    public static final String ROLE_API_CREATE_BULK_USERS = "ROLE_API_CREATE_BULK_USERS";
    public static final String ROLE_API_UPDATE_USER = "ROLE_API_UPDATE_USER";
    public static final String ROLE_API_UPDATE_USER_STATUS_BY_ID = "ROLE_API_UPDATE_USER_STATUS_BY_ID";
    public static final String ROLE_API_DELETE_USER = "ROLE_API_DELETE_USER";
    public static final String ROLE_API_GET_DISTINCT_BATCH_ID = "ROLE_API_GET_DISTINCT_BATCH_ID";
    public static final String ROLE_API_GET_ALL_BATCH_IDS = "ROLE_API_GET_ALL_BATCH_IDS";
    public static final String ROLE_API_SEARCH_USERS = "ROLE_API_SEARCH_USERS";
    public static final String ROLE_API_COUNT_USERS = "ROLE_API_COUNT_USERS";
    public static final String ROLE_API_GET_USERS_BY_FILTER = "ROLE_API_GET_USERS_BY_FILTER";
    public static final String ROLE_API_GET_USER_COUNT_BY_FILTER = "ROLE_API_GET_USER_COUNT_BY_FILTER";
    public static final String ROLE_API_SAVE_CONTACT_USERS = "ROLE_API_SAVE_CONTACT_USERS";

    // EWS API Role Constants
    public static final String ROLE_API_EWS_LOGIN = "ROLE_API_EWS_LOGIN";
    public static final String ROLE_API_EWS_REFRESH_TOKEN = "ROLE_API_EWS_REFRESH_TOKEN";
    public static final String ROLE_API_EWS_INTROSPECT = "ROLE_API_EWS_INTROSPECT";

    // Failure Logs API Role Constants
    public static final String ROLE_API_CREATE_FAILURE_LOG = "ROLE_API_CREATE_FAILURE_LOG";
    public static final String ROLE_API_GET_ALL_FAILURE_LOGS = "ROLE_API_GET_ALL_FAILURE_LOGS";
    public static final String ROLE_API_GET_FAILURE_LOG_BY_ID = "ROLE_API_GET_FAILURE_LOG_BY_ID";
    public static final String ROLE_API_GET_FAILURE_LOG_BY_EMAIL = "ROLE_API_GET_FAILURE_LOG_BY_EMAIL";
    public static final String ROLE_API_UPDATE_FAILURE_LOG = "ROLE_API_UPDATE_FAILURE_LOG";
    public static final String ROLE_API_DELETE_FAILURE_LOG = "ROLE_API_DELETE_FAILURE_LOG";
    public static final String ROLE_API_COUNT_FAILURE_LOGS = "ROLE_API_COUNT_FAILURE_LOGS";
    public static final String ROLE_API_GET_FAILURE_LOG_TYPES = "ROLE_API_GET_FAILURE_LOG_TYPES";
    public static final String ROLE_API_GET_FAILURE_LOGS_BY_FILTER = "ROLE_API_GET_FAILURE_LOGS_BY_FILTER";
    public static final String ROLE_API_COUNT_FAILURE_LOGS_BY_FILTER = "ROLE_API_COUNT_FAILURE_LOGS_BY_FILTER";
    public static final String ROLE_API_UPDATE_FAILURE_LOG_STATUS = "ROLE_API_UPDATE_FAILURE_LOG_STATUS";
    public static final String ROLE_API_GET_MAIL_SUMMARIES_BY_FILTER = "ROLE_API_GET_MAIL_SUMMARIES_BY_FILTER";
    public static final String ROLE_API_COUNT_MAIL_SUMMARIES_BY_FILTER = "ROLE_API_COUNT_MAIL_SUMMARIES_BY_FILTER";

    // Gmail API Role Constants
    public static final String ROLE_API_SUMMARIZE_MAIL_ATTACHMENT = "ROLE_API_SUMMARIZE_MAIL_ATTACHMENT";
    public static final String ROLE_API_POLL_GMAIL_FOLDER = "ROLE_API_POLL_GMAIL_FOLDER";
    public static final String ROLE_API_GET_GMAIL_LOGS = "ROLE_API_GET_GMAIL_LOGS";
    public static final String ROLE_API_GET_MESSAGE_BY_ID = "ROLE_API_GET_MESSAGE_BY_ID";
    public static final String ROLE_API_GET_MESSAGES = "ROLE_API_GET_MESSAGES";
    public static final String ROLE_API_FETCH_ATTACHMENT_BY_ID = "ROLE_API_FETCH_ATTACHMENT_BY_ID";
    public static final String ROLE_API_GET_EVENT = "ROLE_API_GET_EVENT";
    public static final String ROLE_API_GET_EVENT_BY_ID = "ROLE_API_GET_EVENT_BY_ID";
    public static final String ROLE_API_GET_LABELS = "ROLE_API_GET_LABELS";
    public static final String ROLE_API_GET_AVAILABLE_MEETING_SLOTS = "ROLE_API_GET_AVAILABLE_MEETING_SLOTS";
    public static final String ROLE_API_GET_ALL_SCHEDULED_MEETINGS = "ROLE_API_GET_ALL_SCHEDULED_MEETINGS";
    public static final String ROLE_API_CONSUME_EMAIL_MESSAGES = "ROLE_API_CONSUME_EMAIL_MESSAGES";
    public static final String ROLE_API_GET_VACATION_RESPONDER_SETTINGS = "ROLE_API_GET_VACATION_RESPONDER_SETTINGS";
    public static final String ROLE_API_SET_VACATION_RESPONDER = "ROLE_API_SET_VACATION_RESPONDER";
    public static final String ROLE_API_CREATE_MEETING_EVENT = "ROLE_API_CREATE_MEETING_EVENT";
    public static final String ROLE_API_GET_MESSAGE_DETAILS = "ROLE_API_GET_MESSAGE_DETAILS";
    public static final String ROLE_API_HANDLE_EVENT_NOTIFICATION = "ROLE_API_HANDLE_EVENT_NOTIFICATION";

    // Graph Integration API Role Constants
    public static final String ROLE_API_GET_EMAILS = "ROLE_API_GET_EMAILS";
    public static final String ROLE_API_GET_EMAILS_WITH_FILTER = "ROLE_API_GET_EMAILS_WITH_FILTER";
    public static final String ROLE_API_GET_EMAIL_BY_MESSAGE_ID = "ROLE_API_GET_EMAIL_BY_MESSAGE_ID";
    public static final String ROLE_API_CANCEL_AUTO_REPLY = "ROLE_API_CANCEL_AUTO_REPLY";
    public static final String ROLE_API_GET_AUTO_REPLY_SETTINGS = "ROLE_API_GET_AUTO_REPLY_SETTINGS";
    public static final String ROLE_API_GET_OUT_OF_OFFICE = "ROLE_API_GET_OUT_OF_OFFICE";
    public static final String ROLE_API_SET_AUTO_REPLY = "ROLE_API_SET_AUTO_REPLY";
    public static final String ROLE_API_FLAG_EMAIL = "ROLE_API_FLAG_EMAIL";
    public static final String ROLE_API_GET_FLAG_STATUS = "ROLE_API_GET_FLAG_STATUS";
    public static final String ROLE_API_GET_AVAILABLE_SLOTS_AND_CONFLICT = "ROLE_API_GET_AVAILABLE_SLOTS_AND_CONFLICT";
    public static final String ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V2 = "ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V2";
    public static final String ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V3 = "ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V3";
    public static final String ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V1 = "ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V1";
    public static final String ROLE_API_GET_CALENDAR_EVENTS = "ROLE_API_GET_CALENDAR_EVENTS";
    public static final String ROLE_API_GET_CALENDAR_EVENT_BY_EVENT_ID = "ROLE_API_GET_CALENDAR_EVENT_BY_EVENT_ID";
    public static final String ROLE_API_GET_DAY_SUMMARY_V2 = "ROLE_API_GET_DAY_SUMMARY_V2";
    public static final String ROLE_API_GET_SUMMARY = "ROLE_API_GET_SUMMARY";
    public static final String ROLE_API_GET_DAY_SUMMARY_STRUCTURED = "ROLE_API_GET_DAY_SUMMARY_STRUCTURED";
    public static final String ROLE_API_GET_UPCOMING_EVENTS = "ROLE_API_GET_UPCOMING_EVENTS";
    public static final String ROLE_API_GET_SUMMARY_BY_TYPE = "ROLE_API_GET_SUMMARY_BY_TYPE";
    public static final String ROLE_API_GET_DATE_RANGE_SUMMARY_BY_ACTION = "ROLE_API_GET_DATE_RANGE_SUMMARY_BY_ACTION";
    public static final String ROLE_API_GET_CALENDAR_EVENT_BY_ID = "ROLE_API_GET_CALENDAR_EVENT_BY_ID";
    public static final String ROLE_API_GET_EMAIL_BY_INTERNET_MESSAGE_ID = "ROLE_API_GET_EMAIL_BY_INTERNET_MESSAGE_ID";
    public static final String ROLE_API_DECLINE_MEETING = "ROLE_API_DECLINE_MEETING";
    public static final String ROLE_API_ACCEPT_MEETING = "ROLE_API_ACCEPT_MEETING";
    public static final String ROLE_API_TENTATIVELY_ACCEPT_MEETING = "ROLE_API_TENTATIVELY_ACCEPT_MEETING";
    public static final String ROLE_API_SCHEDULE_EVENT = "ROLE_API_SCHEDULE_EVENT";
    public static final String ROLE_API_SCHEDULE_EVENT_RECURRING = "ROLE_API_SCHEDULE_EVENT_RECURRING";
    public static final String ROLE_API_RESCHEDULE_EVENT = "ROLE_API_RESCHEDULE_EVENT";
    public static final String ROLE_API_CANCEL_EVENT = "ROLE_API_CANCEL_EVENT";
    public static final String ROLE_API_CREATE_CATEGORIES = "ROLE_API_CREATE_CATEGORIES";
    public static final String ROLE_API_DEPLOY_BUILD = "ROLE_API_DEPLOY_BUILD";
    public static final String ROLE_API_CHECK_BUILD_STATUS = "ROLE_API_CHECK_BUILD_STATUS";
    public static final String ROLE_API_GET_ATTACHMENT_STATUS = "ROLE_API_GET_ATTACHMENT_STATUS";
    public static final String ROLE_API_REPROCESS_EMAIL = "ROLE_API_REPROCESS_EMAIL";
    public static final String ROLE_API_GET_SUPPORTED_MEETING_TYPES = "ROLE_API_GET_SUPPORTED_MEETING_TYPES";
    public static final String ROLE_API_GET_ANSWER_FROM_SUMMARY = "ROLE_API_GET_ANSWER_FROM_SUMMARY";
    public static final String ROLE_API_REGENERATE_OBJECTIVES = "ROLE_API_REGENERATE_OBJECTIVES";
    public static final String ROLE_API_ON_DEMAND_PROCESS_EMAIL = "ROLE_API_ON_DEMAND_PROCESS_EMAIL";
    public static final String ROLE_API_GENERATE_MEETING_SUMMARY = "ROLE_API_GENERATE_MEETING_SUMMARY";
    public static final String ROLE_API_GET_EMAIL_STATS_BY_FILTER = "ROLE_API_GET_EMAIL_STATS_BY_FILTER";
    public static final String ROLE_API_GET_EMAIL_STATS_COUNT_BY_FILTER = "ROLE_API_GET_EMAIL_STATS_COUNT_BY_FILTER";
    public static final String ROLE_API_MARK_MAIL_READ = "ROLE_API_MARK_MAIL_READ";
    public static final String ROLE_API_DELETE_EMAIL = "ROLE_API_DELETE_EMAIL";
    public static final String ROLE_API_GET_AVAILABLE_SLOTS_AND_CONFLICT_V1 = "ROLE_API_GET_AVAILABLE_SLOTS_AND_CONFLICT_V1";
    public static final String ROLE_API_GET_AVAILABILITY = "ROLE_API_GET_AVAILABILITY";
    public static final String ROLE_API_FORWARD_EVENT = "ROLE_API_FORWARD_EVENT";

    // Mail Summary API Role Constants
    public static final String ROLE_API_GET_MAIL_SUMMARY = "ROLE_API_GET_MAIL_SUMMARY";
    public static final String ROLE_API_GET_ALL_MAIL_SUMMARY = "ROLE_API_GET_ALL_MAIL_SUMMARY";
    public static final String ROLE_API_SAVE_CONTACTS = "ROLE_API_SAVE_CONTACTS";
    public static final String ROLE_API_UPDATE_BATCH = "ROLE_API_UPDATE_BATCH";
    public static final String ROLE_API_SAVE_MAIL_SUMMARY = "ROLE_API_SAVE_MAIL_SUMMARY";
    public static final String ROLE_API_ENCRYPT_MAIL_SUMMARY = "ROLE_API_ENCRYPT_MAIL_SUMMARY";
    public static final String ROLE_API_ENCRYPT_THREAD_SUMMARY = "ROLE_API_ENCRYPT_THREAD_SUMMARY";

    // Mobile API Role Constants
    public static final String ROLE_API_DELETE_MAIL = "ROLE_API_DELETE_MAIL";
    public static final String ROLE_API_DOWNLOAD_FILE = "ROLE_API_DOWNLOAD_FILE";
    public static final String ROLE_API_DOWNLOAD_FILE_BY_DOC_ID = "ROLE_API_DOWNLOAD_FILE_BY_DOC_ID";
    public static final String ROLE_API_CREATE_DRAFT = "ROLE_API_CREATE_DRAFT";
    public static final String ROLE_API_CREATE_DRAFT_REPLY = "ROLE_API_CREATE_DRAFT_REPLY";
    public static final String ROLE_API_UPLOAD_ATTACHMENT = "ROLE_API_UPLOAD_ATTACHMENT";
    public static final String ROLE_API_FORWARD_EMAIL = "ROLE_API_FORWARD_EMAIL";

    // Organisation API Role Constants
    public static final String ROLE_API_CREATE_ORGANISATION = "ROLE_API_CREATE_ORGANISATION";
    public static final String ROLE_API_UPDATE_ORGANISATION = "ROLE_API_UPDATE_ORGANISATION";
    public static final String ROLE_API_UPDATE_ORGANISATION_STATUS = "ROLE_API_UPDATE_ORGANISATION_STATUS";
    public static final String ROLE_API_GET_ORGANISATIONS_BY_FILTER = "ROLE_API_GET_ORGANISATIONS_BY_FILTER";
    public static final String ROLE_API_GET_ORGANISATION_COUNT_BY_FILTER = "ROLE_API_GET_ORGANISATION_COUNT_BY_FILTER";

    // Record API Role Constants
    public static final String ROLE_API_VALIDATE_USER = "ROLE_API_VALIDATE_USER";
    public static final String ROLE_API_SEND_NOTIFICATION = "ROLE_API_SEND_NOTIFICATION";
    public static final String ROLE_API_INTROSPECT_EWS_TOKEN = "ROLE_API_INTROSPECT_EWS_TOKEN";
    public static final String ROLE_API_GET_QUESTIONS_BY_INTENT = "ROLE_API_GET_QUESTIONS_BY_INTENT";
    public static final String ROLE_API_GET_CHART_JSON = "ROLE_API_GET_CHART_JSON";
    public static final String ROLE_API_MARK_ACTION_TAKEN = "ROLE_API_MARK_ACTION_TAKEN";
    public static final String ROLE_API_MARK_ALL_ACTION_TAKEN = "ROLE_API_MARK_ALL_ACTION_TAKEN";
    public static final String ROLE_API_GET_EMAILS_BY_TAG = "ROLE_API_GET_EMAILS_BY_TAG";
    public static final String ROLE_API_GET_EMAILS_BY_TAG_V1 = "ROLE_API_GET_EMAILS_BY_TAG_V1";
    public static final String ROLE_API_TAG_EMAIL = "ROLE_API_TAG_EMAIL";
    public static final String ROLE_API_GET_USERS_MAIL_FOLDERS = "ROLE_API_GET_USERS_MAIL_FOLDERS";
    public static final String ROLE_API_UPDATE_FOLDER_STATUS = "ROLE_API_UPDATE_FOLDER_STATUS";
    public static final String ROLE_API_GET_USER_ACTIONS_BY_FILTER = "ROLE_API_GET_USER_ACTIONS_BY_FILTER";
    public static final String ROLE_API_GET_USER_ACTIONS_COUNT_BY_FILTER = "ROLE_API_GET_USER_ACTIONS_COUNT_BY_FILTER";
    public static final String ROLE_API_FILTER_EMAILS = "ROLE_API_FILTER_EMAILS";

    // User Folder API Role Constants
    public static final String ROLE_API_GET_USER_FOLDERS_BY_FILTER = "ROLE_API_GET_USER_FOLDERS_BY_FILTER";
    public static final String ROLE_API_GET_USER_FOLDER_COUNT_BY_FILTER = "ROLE_API_GET_USER_FOLDER_COUNT_BY_FILTER";

    // User Mail Attachment API Role Constants
    public static final String ROLE_API_CREATE_USER_MAIL_ATTACHMENT = "ROLE_API_CREATE_USER_MAIL_ATTACHMENT";
    public static final String ROLE_API_GET_USER_MAIL_ATTACHMENT_BY_ID = "ROLE_API_GET_USER_MAIL_ATTACHMENT_BY_ID";
    public static final String ROLE_API_UPDATE_USER_MAIL_ATTACHMENT = "ROLE_API_UPDATE_USER_MAIL_ATTACHMENT";
    public static final String ROLE_API_UPDATE_USER_MAIL_ATTACHMENT_STATUS = "ROLE_API_UPDATE_USER_MAIL_ATTACHMENT_STATUS";
    public static final String ROLE_API_SEARCH_USER_MAIL_ATTACHMENTS = "ROLE_API_SEARCH_USER_MAIL_ATTACHMENTS";
    public static final String ROLE_API_COUNT_USER_MAIL_ATTACHMENTS = "ROLE_API_COUNT_USER_MAIL_ATTACHMENTS";
    public static final String ROLE_API_GET_USER_MAIL_ATTACHMENTS_BY_FILTER = "ROLE_API_GET_USER_MAIL_ATTACHMENTS_BY_FILTER";
    public static final String ROLE_API_GET_USER_MAIL_ATTACHMENT_COUNT_BY_FILTER = "ROLE_API_GET_USER_MAIL_ATTACHMENT_COUNT_BY_FILTER";
    public static final String ROLE_API_RESET_USER_MAIL_ATTACHMENT = "ROLE_API_RESET_USER_MAIL_ATTACHMENT";

    // Template API Role Constants
    public static final String ROLE_API_ADD_TEMPLATE = "ROLE_API_ADD_TEMPLATE";
    public static final String ROLE_API_UPDATE_TEMPLATE = "ROLE_API_UPDATE_TEMPLATE";
    public static final String ROLE_API_GET_TEMPLATE = "ROLE_API_GET_TEMPLATE";
    public static final String ROLE_API_DELETE_TEMPLATE = "ROLE_API_DELETE_TEMPLATE";

    // Voice API Role Constants
    public static final String ROLE_API_FLAG_EMAIL_V1 = "ROLE_API_FLAG_EMAIL_V1";
    public static final String ROLE_API_FLAG_EMAIL_V3 = "ROLE_API_FLAG_EMAIL_V3";
    public static final String ROLE_API_CREATE_DRAFT_V1 = "ROLE_API_CREATE_DRAFT_V1";
    public static final String ROLE_API_CREATE_DRAFT_REPLY_V1 = "ROLE_API_CREATE_DRAFT_REPLY_V1";
    public static final String ROLE_API_CREATE_DRAFT_FORWARD_V1 = "ROLE_API_CREATE_DRAFT_FORWARD_V1";
    public static final String ROLE_API_GET_MAIL_SUMMARIES = "ROLE_API_GET_MAIL_SUMMARIES";
    public static final String ROLE_API_GET_ALL_SUMMARY_FOR_TODAY = "ROLE_API_GET_ALL_SUMMARY_FOR_TODAY";
    public static final String ROLE_API_GET_ALL_SUMMARY_BETWEEN_DATE = "ROLE_API_GET_ALL_SUMMARY_BETWEEN_DATE";
    public static final String ROLE_API_GET_DOC_SUMMARY = "ROLE_API_GET_DOC_SUMMARY";
    public static final String ROLE_API_SEARCH_CONTACTS = "ROLE_API_SEARCH_CONTACTS";
    public static final String ROLE_API_SEARCH_MULTIPLE_CONTACTS = "ROLE_API_SEARCH_MULTIPLE_CONTACTS";
    public static final String ROLE_API_GET_ANSWER_FROM_ATTACHMENT = "ROLE_API_GET_ANSWER_FROM_ATTACHMENT";
    public static final String ROLE_API_SCHEDULE_EVENT_V1 = "ROLE_API_SCHEDULE_EVENT_V1";
    public static final String ROLE_API_UPDATE_EXISTING_EVENT = "ROLE_API_UPDATE_EXISTING_EVENT";
    public static final String ROLE_API_GET_DAY_SUMMARY_V3 = "ROLE_API_GET_DAY_SUMMARY_V3";
    public static final String ROLE_API_SEND_DRAFT = "ROLE_API_SEND_DRAFT";
}
