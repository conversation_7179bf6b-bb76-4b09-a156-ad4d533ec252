package com.enttribe.emailagent.utils;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Converter
public class EmailSummaryEncryptor implements AttributeConverter<String, String> {

    private static final Logger log = LoggerFactory.getLogger(EmailSummaryEncryptor.class);

    @Override
    public String convertToDatabaseColumn(String attribute) {
        try {
            return attribute == null ? null : AESUtils.encrypt(attribute);
        } catch (Exception e) {
            log.error("error in encrypting message : {}", e.getMessage(), e);
            return attribute;
        }
    }

    @Override
    public String convertToEntityAttribute(String dbData) {
        try {
            return dbData == null ? null : AESUtils.decrypt(dbData);
        } catch (Exception e) {
            log.error("error in decrypting message : {}", e.getMessage(), e);
            return dbData;
        }
    }
}
