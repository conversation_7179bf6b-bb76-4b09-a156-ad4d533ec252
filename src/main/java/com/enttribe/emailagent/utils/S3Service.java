package com.enttribe.emailagent.utils;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;

/**
 * The type S 3 service.
 *  <AUTHOR> Dangi
 */
@Service
public class S3Service {

    private static final Logger log = LoggerFactory.getLogger(S3Service.class);
    private final AmazonS3 s3Client;

    public S3Service(
            @Value("${s3Region}") String s3Region,
            @Value("${s3User}") String s3User,
            @Value("${s3Checksum}") String s3Checksum,
            @Value("${s3Url}") String s3Url
    ) {
        System.out.println("Creating S3Service object");
        BasicAWSCredentials awsCreds = new BasicAWSCredentials(s3User, s3Checksum);

        this.s3Client = AmazonS3ClientBuilder.standard()
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(s3Url, s3Region))
                .withPathStyleAccessEnabled(true)
                .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                .build();
        System.out.println("Object Created");
    }

    public  String uploadFile(String bucketName, String key, String filePath) {
        log.debug("Inside @method uploadFile. @param : bucketName -> {} key -> {} filePath -> {}", bucketName, key, filePath);
        PutObjectRequest request = new PutObjectRequest(bucketName, key, new File(filePath));
        s3Client.putObject(request);
        String fileUrl = s3Client.getUrl(bucketName, key).toString();
        System.out.println(fileUrl);
        return fileUrl;
    }

    public String uploadFile(String bucketName, String key, InputStream inputStream, long contentLength) {
        log.debug("Inside @method uploadFile. @param : bucketName -> {} key -> {} contentLength -> {}", bucketName, key, contentLength);

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(contentLength);
        PutObjectRequest request = new PutObjectRequest(bucketName, key, inputStream, metadata);

        s3Client.putObject(request);
        String fileUrl = s3Client.getUrl(bucketName, key).toString();
        System.out.println(fileUrl);
        return fileUrl;
    }

    public static  String testuploadFile(String bucketName, String key, String filePath,AmazonS3 client) {
        PutObjectRequest request = new PutObjectRequest(bucketName, key, new File(filePath));
        client.putObject(request);
        String fileUrl = client.getUrl(bucketName, key).toString();
        System.out.println(fileUrl);
        return fileUrl;
    }
//    public static File downloadFile(String bucketName, String key, String downloadFilePath,AmazonS3 client) throws IOException {
//        System.out.println("Downloading file from S3...");
//        S3Object s3Object = client.getObject(bucketName, key);
//        InputStream inputStream = s3Object.getObjectContent();
//        File file = new File(downloadFilePath);
//        FileOutputStream outputStream = new FileOutputStream(file);
//        byte[] readBuf = new byte[1024];
//        int readLen;
//        while (true) {
//            try {
//                if (!((readLen = inputStream.read(readBuf)) > 0)) break;
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//            outputStream.write(readBuf, 0, readLen);
//        }
//        inputStream.close();
//        outputStream.close();
//        System.out.println("File downloaded to " + downloadFilePath);
//        return file;
//    }


    public  byte[] downloadFileAsBytes(String bucketName, String key) throws IOException {
        log.debug("Inside @method downloadFileAsBytes. @param : bucketName -> {} key -> {}", bucketName, key);

        S3Object s3Object = null;
        InputStream inputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            s3Object = s3Client.getObject(bucketName, key);
            inputStream = s3Object.getObjectContent();

            byte[] buffer = new byte[1024];
            int length;

            while ((length = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, length);
            }

        } catch (IOException e) {
            System.err.println("Error downloading file from S3: " + e.getMessage());
            throw e;
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    System.err.println("Error closing input stream: " + e.getMessage());
                }
            }
            if (s3Object != null) {
                s3Object.close();
            }
        }
        return byteArrayOutputStream.toByteArray();
    }

    public void deleteFileFromS3(String bucketName, String key) {
        log.debug("Inside @method deleteFileFromS3. @param : bucketName -> {} key -> {}", bucketName, key);

        try {
            s3Client.deleteObject(bucketName, key);
            System.out.println("Deleted file from S3: " + key);
        } catch (AmazonServiceException e) {
            System.err.println("Error deleting file from S3: " + e.getMessage());
            // Handle exception if deletion fails
        }
    }


//    public static void main(String[] args) {
//        BasicAWSCredentials awsCreds = new BasicAWSCredentials("bootadmin","bootadmin");
//
//        AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
//                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration("http://localhost:8888", "us-east-2"))
//                .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
//                .build();
//        try {
//            downloadFile("emailAttachments","<EMAIL>/attachments/20241002151800_Java%20Buzzwords.pdf","/Users/<USER>/Documents/visionwaves-Back-End/workspace/",s3Client);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//    }
}