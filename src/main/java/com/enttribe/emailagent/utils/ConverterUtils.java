package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.dto.AttendeeAndStatus;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.MailSummaryDto;
import com.enttribe.emailagent.entity.MailSummary;
import microsoft.exchange.webservices.data.core.service.item.Appointment;
import microsoft.exchange.webservices.data.property.complex.AttendeeCollection;
import microsoft.exchange.webservices.data.property.complex.EmailAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * The type Ews converter utils.
 *  <AUTHOR>
 */
public class ConverterUtils {

    private static final Logger log = LoggerFactory.getLogger(ConverterUtils.class);

    private ConverterUtils() {
    }

    public static EventDto convertToEventDto(Appointment appointment) throws Exception {
        EventDto eventDto = new EventDto();

        eventDto.setId(appointment.getId().toString());
        eventDto.setOrganizer(appointment.getOrganizer().getAddress());
        AttendeeCollection requiredAttendees = appointment.getRequiredAttendees();
        AttendeeCollection optionalAttendees = appointment.getOptionalAttendees();
        List<String> list = requiredAttendees.getItems().stream().map(EmailAddress::getAddress).toList();
        List<AttendeeAndStatus> required = requiredAttendees.getItems().stream().map(attendee -> new AttendeeAndStatus(attendee.getAddress(), attendee.getResponseType().toString())).toList();
        List<AttendeeAndStatus> optional = optionalAttendees.getItems().stream().map(attendee -> new AttendeeAndStatus(attendee.getAddress(), attendee.getResponseType().toString())).toList();
        eventDto.setAttendees(list);
        eventDto.setSubject(appointment.getSubject());
        String bodyPreview;
        try {
            bodyPreview = appointment.getBody().toString();
        } catch (Exception e) {
            bodyPreview = null;
        }
        eventDto.setBodyPreview(bodyPreview);
        eventDto.setJoinUrl(appointment.getMeetingWorkspaceUrl());
        eventDto.setHasAttachments(appointment.getHasAttachments());
        eventDto.setMeetingStartTime(appointment.getStart());
        eventDto.setMeetingEndTime(appointment.getEnd());
        eventDto.setCreatedDateTime(appointment.getDateTimeCreated());
        eventDto.setLastModifiedDateTime(appointment.getLastModifiedTime());
        eventDto.setRequiredAttendees(required);
        eventDto.setOptionalAttendees(optional);
        eventDto.setLocation(appointment.getLocation());
        log.info("Response is {}",appointment.getMyResponseType().toString());
        eventDto.setAccepted(appointment.getMyResponseType().toString());
        return eventDto;
    }

    public static MailSummaryDto convertToMailSummaryDto(MailSummary mailSummary) {

        return MailSummaryDto.builder()
                .id(mailSummary.getId())
                .messageId(mailSummary.getMessageId())
                .attachmentsList(mailSummary.getAttachmentsList())
                .priority(mailSummary.getPriority())
                .category(mailSummary.getCategory())
                .subject(mailSummary.getSubject())
                .fromUser(mailSummary.getFromUser())
                .toUser(mailSummary.getToUser())
                .ccUser(mailSummary.getCcUser())
                .mailReceivedTime(mailSummary.getMailReceivedTime())
                .messageSummary(mailSummary.getMessageSummary())
                .userActions(mailSummary.getUserActions())
                .userId(mailSummary.getUserId())
                .actionOwner(mailSummary.getActionOwner())
                .type(mailSummary.getType())
                .starMarked(mailSummary.getStarMarked())
                .build();

    }

    public static List<MailSummaryDto> convertToMailSummaryDtoList(List<MailSummary> mailSummaries) {
        return mailSummaries.stream().map(ConverterUtils::convertToMailSummaryDto).toList();
    }

}
