package com.enttribe.emailagent.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * The type Ews token utils.
 *  <AUTHOR> Sonsale
 */
@Service
public class EwsTokenUtils {

    private static  String SECRET_KEY ;

    @Value("${auth.secret.publicKey}")
    private void setSecretKey(String secretKey) {
        EwsTokenUtils.SECRET_KEY = secretKey;
    }

    private static final long ACCESS_TOKEN_EXPIRATION_TIME = 30 * 60 * 1000;
    private static final long REFRESH_TOKEN_EXPIRATION_TIME = 60 * 60 * 1000;

    public static String generateAccessToken(String username) {
        return Jwts.builder()
                .setSubject(username)
                .setExpiration(new Date(System.currentTimeMillis() + ACCESS_TOKEN_EXPIRATION_TIME))
                .signWith(SignatureAlgorithm.HS512, SECRET_KEY)
                .compact();
    }

    public static String generateRefreshToken(String username) {
        return Jwts.builder()
                .setSubject(username)
                .setExpiration(new Date(System.currentTimeMillis() + REFRESH_TOKEN_EXPIRATION_TIME))
                .signWith(SignatureAlgorithm.HS512, SECRET_KEY)
                .compact();
    }

    public static Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(SECRET_KEY)
                .parseClaimsJws(token)
                .getBody();
    }
}
