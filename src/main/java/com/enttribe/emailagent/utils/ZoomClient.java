package com.enttribe.emailagent.utils;

import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.json.JSONObject;


/**
 * The type Zoom client.
 *  <AUTHOR>
 */
@Slf4j
@Service
public class ZoomClient {

    private static String zoomClientId;

    private static String zoomClientSecret;

    private static String zoomAccountId;

    private static String zoomTokenUrl;

    private static String zoomApiUrl;

    @Value("${zoom.clientId}")
    public void setZoomClientId(String zoomClientId) {
        this.zoomClientId = zoomClientId;
    }

    @Value("${zoom.tokenUrl}")
    public void setZoomTokenUrl(String zoomTokenUrl) {
        this.zoomTokenUrl = zoomTokenUrl;
    }

    @Value("${zoom.apiURL}")
    public void setZoomAPIUrl(String zoomApiUrl) {
        this.zoomApiUrl = zoomApiUrl;
    }

    @Value("${zoom.clientSecret}")
    public void setZoomClientSecret(String zoomClientSecret) {
        this.zoomClientSecret = zoomClientSecret;
    }
    @Value("${zoom.accountId}")
    public void setZoomAccountId(String zoomAccountId) {
        this.zoomAccountId = zoomAccountId;
    }



    public static String getAccessToken() throws IOException, InterruptedException {


        // Create the HTTP client
        HttpClient client = HttpClient.newHttpClient();

        // Create the HTTP request
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(zoomTokenUrl+"?grant_type=account_credentials&account_id="+zoomAccountId))
                .header("Authorization", "Basic " + Base64.getEncoder().encodeToString((zoomClientId + ":" + zoomClientSecret).getBytes()))
                .header("Content-Type", "application/x-www-form-urlencoded")
                .POST(HttpRequest.BodyPublishers.ofString("grant_type=client_credentials"))
                .build();

        // Send the request and get the response
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

        // Extract the response body
        String responseBody = response.body();

        // Print the response
        JSONObject jsonResponse = new JSONObject(responseBody);

        // Extract the access token
        String accessToken = jsonResponse.getString("access_token");
        return  accessToken;

    }


    public static String getAccessToken1() throws IOException, InterruptedException {


        // Create the HTTP client
        HttpClient client = HttpClient.newHttpClient();

        // Create the HTTP request
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("https://zoom.us/oauth/token?grant_type=account_credentials&account_id=WDPKtUbyT36OI-qIvo5a_g"))
                .header("Authorization", "Basic " + Base64.getEncoder().encodeToString(( "ya067IWRRD6WhB58EVQxvQ:vhVnxUJUTA4HkehH3ELxv3Vv7CGdGDqA" ).getBytes()))
                .header("Content-Type", "application/x-www-form-urlencoded")
                .POST(HttpRequest.BodyPublishers.ofString("grant_type=client_credentials"))
                .build();

        // Send the request and get the response
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

        // Extract the response body
        String responseBody = response.body();

        // Print the response
        JSONObject jsonResponse = new JSONObject(responseBody);

        // Extract the access token
        String accessToken = jsonResponse.getString("access_token");
        return  accessToken;

    }
    // Method to schedule a Zoom meeting
    public static Map<String,String> scheduleMeeting(String emailId, String startTime, String endTime, String subject, String agenda, String timeZone) throws IOException, InterruptedException {
        log.debug("Parameters to schedule meeting on zoom are emailId {} startTime {} endTime {} subject {} agenda {} timeZone {} ",emailId,startTime,endTime,subject,agenda,timeZone);
        try {
            String accessToken = getAccessToken();
            // Meeting details

            HttpClient userClient = HttpClient.newHttpClient();

            HttpRequest userRequest = HttpRequest.newBuilder()
                    .uri(URI.create(zoomApiUrl + "/users"))
                    .header("Authorization", "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .GET()
                    .build();
            HttpResponse<String> userResponse = userClient.send(userRequest, HttpResponse.BodyHandlers.ofString());

            // Extract the response body
            String userResponseString = userResponse.body();
            JSONObject userObject = new JSONObject(userResponseString);
            JSONArray userList = userObject.getJSONArray("users");
            String userId;
            userId = userList.toList().stream()
                    .map(obj -> new JSONObject((java.util.Map) obj))
                    .filter(user -> emailId.equals(user.getString("email")))
                    .map(user -> user.getString("id"))
                    .findFirst()
                    .orElse(null);
            if(userId==null){
                userId="AFyNwp33RFKtr6U1sVoFFg";
            }
            log.debug("User id for zoom meeting is {}", userId);
//AFyNwp33RFKtr6U1sVoFFg
            // Create the HTTP client
            HttpClient client = HttpClient.newHttpClient();

            // Create the JSON payload for the meeting
            JSONObject meetingDetails = new JSONObject();
            meetingDetails.put("topic", subject);
            meetingDetails.put("type", 2); // Scheduled meeting
            meetingDetails.put("start_time", startTime);
            JSONObject recurrence = new JSONObject();
            recurrence.put("type", 1);
            recurrence.put("end_date_time", endTime);
            meetingDetails.put("recurrence", recurrence);
            meetingDetails.put("password", generatePasscode(6));
            //  meetingDetails.put("duration", duration);
            meetingDetails.put("agenda", agenda);
            meetingDetails.put("settings", new JSONObject().put("auto_recording", "none"));
            meetingDetails.put("timezone", timeZone);

            // Create the HTTP request to schedule the meeting
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(zoomApiUrl + "/users/" + userId + "/meetings"))
                    .header("Authorization", "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(meetingDetails.toString()))
                    .build();

            // Send the request and get the response
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            // Extract the response body
            String responseBody = response.body();
            System.out.println(responseBody);
            JSONObject responseObject = new JSONObject(responseBody);
            log.debug("Meeting JSON object is {}",responseObject.toString());
            Map<String, String> resultMap = new HashMap<>();
            if (responseObject.has("join_url")) {
                resultMap.put("join_url", responseObject.getString("join_url"));
            }
            if (responseObject.has("id")) {
                resultMap.put("meeting_id", responseObject.get("id").toString());
            }
            if (responseObject.has("password")) {
                resultMap.put("passcode", responseObject.getString("password"));
            }
            return resultMap;
        }
        catch (Exception e) {
            log.error("Error while scheduling meeting on zoom {}",e);
        }
        return null;
    }

    public static String generatePasscode(int length) {
        SecureRandom random = new SecureRandom();
        StringBuilder passcode = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int digit = random.nextInt(10);  // Generates a random number between 0 and 9
            passcode.append(digit);
        }

        return passcode.toString();
    }

    public static String meetingTranscript(String meetingId) {
        String fileContent = null;
        try {
            String accessToken = getAccessToken();
            HttpClient meetingRecoding = HttpClient.newHttpClient();
            fileContent = null;
            HttpRequest userRequest = HttpRequest.newBuilder()
                    .uri(URI.create("https://api.zoom.us/v2/meetings/" + meetingId + "/recordings"))
                    .header("Authorization", "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .GET()
                    .build();
            HttpResponse<String> recordingResponse = meetingRecoding.send(userRequest, HttpResponse.BodyHandlers.ofString());
            if (recordingResponse.statusCode() == 200) {
                String recordingResponseString = recordingResponse.body();
                JSONObject recordingObject = new JSONObject(recordingResponseString);
                JSONArray recordingFiles = recordingObject.getJSONArray("recording_files");
                for (int i = 0; i < recordingFiles.length(); i++) {
                    JSONObject recordingFile = recordingFiles.getJSONObject(i);
                    if (recordingFile.getString("recording_type").equalsIgnoreCase("audio_transcript") && recordingFile.getString("status").equalsIgnoreCase("completed")) {
                        String downloadUrl = recordingFile.getString("download_url");
                        fileContent=downloadFileWithAuth(downloadUrl,accessToken);
                        System.out.println(fileContent);
                    }
                }
            }
            // Extract the response body

        } catch (Exception e) {
            log.error("Error while getting meeting transcript from zoom {}", e);
        }
        return fileContent;
    }

    private static String downloadFileWithAuth(String urlStr, String accessToken) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setInstanceFollowRedirects(true); // Automatically follow redirects

        // Set authorization header with Bearer token
        connection.setRequestProperty("Authorization", "Bearer " + accessToken);

        // Check the response code
        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            // Read the file content
            InputStream inputStream = connection.getInputStream();

            // Read the InputStream as a UTF-8 string
            return new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                    .lines()
                    .collect(Collectors.joining("\n"));

        } else {
            System.out.println("Failed to retrieve the file. HTTP Response Code: " + responseCode);
        }
        return null;
    }




public static void main(String [] args) {
	System.out.println("hiii");
	String s="{\"arg0\":\"\"}";
	JSONObject jsonObject=new JSONObject(s);
	Iterator<String> keys = jsonObject.keys();
	while (keys.hasNext()) {
        String key = keys.next();
        Object value = jsonObject.get(key);  // Get the value for each key
        System.out.println("key is"+key+" value is "+value);
        if(value==null || value.toString().isBlank() || value.toString().isEmpty() ) {
        	System.out.println("value is"+value);
        }
}
}



}