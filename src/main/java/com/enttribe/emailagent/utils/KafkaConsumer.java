package com.enttribe.emailagent.utils;

import java.time.Duration;
import java.util.Collections;

import javax.annotation.PostConstruct;

import com.google.cloud.pubsub.v1.AckReplyConsumer;
import com.google.cloud.pubsub.v1.Subscriber;
import com.google.pubsub.v1.ProjectSubscriptionName;
import com.google.pubsub.v1.PubsubMessage;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.enttribe.emailagent.ai.polling.OutlookPollingAI;
import com.enttribe.emailagent.dto.KafkaMailWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.pubsub.v1.MessageReceiver;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.google.api.core.ApiService.Listener;
import com.google.api.core.ApiService.State;
import com.google.api.core.SettableApiFuture;
import com.google.cloud.pubsub.v1.AckReplyConsumer;
import com.google.cloud.pubsub.v1.Subscriber;
import com.google.pubsub.v1.ProjectSubscriptionName;
import com.google.pubsub.v1.PubsubMessage;
import java.util.concurrent.TimeUnit;


/**
 * The type Kafka consumer.
 *  <AUTHOR> Dangi
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KafkaConsumer {

    @Autowired
    private final KafkaUtils kafkaUtils;

    private final OutlookPollingAI polling;

    private static String emailKafkaTopic;

    @Value("${kafka.emailTopic}")
    public void setkafkaTopic(String emailKafkaTopic) {
        this.emailKafkaTopic = emailKafkaTopic;
    }


    private static String enableKafka;

    @Value("${kafka.enabled}")
    public void setKafkaEnabled(String enableKafka) {
        this.enableKafka = enableKafka;
    }

    private static String enablePubSub;

    @Value("${pubsub.enabled}")
    public void setPubSubEnabled(String enablePubSub) {
        this.enablePubSub = enablePubSub;
    }

    private static String pubSubProjectId;

    @Value("${pubsub.projectId}")
    public void setPubSubProjectId(String pubSubProjectId) {
        this.pubSubProjectId = pubSubProjectId;
    }

    private static String pubSubSubscriptionId;

    @Value("${pubsub.subscriptionId}")
    public void setPubSubSubscriptionId(String pubSubSubscriptionId) {
        this.pubSubSubscriptionId = pubSubSubscriptionId;
    }


    @PostConstruct

    public void consumeEmailMessages() {
        log.info("kafka enable is {} setPubSubEnabled is {}",enableKafka,enablePubSub);
        if (Boolean.valueOf(enableKafka)) {
            log.info("consumeEmailMessages is called");

            org.apache.kafka.clients.consumer.KafkaConsumer<String, String> consumer = kafkaUtils.getKafkaConsumerClient(emailKafkaTopic);

            // Topic to consume from
            //  String topic = "my-topic";

            // Subscribe to the topic
            consumer.subscribe(Collections.singletonList(emailKafkaTopic));
            new Thread(() -> {
                try {
                    consumeEmailMessages(consumer);
                } catch (Exception e) {
                    log.error("Exception in Kafka consumer thread", e);
                }
            }).start();
        }
        else if(Boolean.valueOf(enablePubSub)){
            subscribeMessages();
        }
        // Poll for new messages

    }


    public void consumeEmailMessages(org.apache.kafka.clients.consumer.KafkaConsumer<String, String> consumer) {


            ObjectMapper objectMapper = new ObjectMapper();

            while (true) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(100)); // Adjust the poll duration as needed

                for (ConsumerRecord<String, String> record : records) {
                	boolean processedSuccessfully = false;
                	log.debug("Consumed data from kafka for batchId {}", record.key());
                    KafkaMailWrapper mailWrapper = null;
                    try {
                        mailWrapper = objectMapper.readValue(record.value(), KafkaMailWrapper.class);
                    } catch (JsonProcessingException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                    try {
                    if (mailWrapper != null) {

                        polling.consumeKafka(mailWrapper.getEmailId(), mailWrapper.getUserId(), mailWrapper.getUserConversation(), mailWrapper.getFolderDisplayName());
                    }
                    }
                    catch(Exception e) {
                    	log.error("Failed to process record userId {} ",mailWrapper.getEmailId(), e);
                    }
                    processedSuccessfully = true;
                    try {
                        consumer.commitSync(); // Commit the offsets after processing the record
                    } catch (Exception e) {
                        log.error("Failed to commit offset for record with key: {}", record.key(), e);
                        // Optionally: Handle offset commit failures
                    }
                }
            }

    }


    public void subscribeMessages() {
        ProjectSubscriptionName subscriptionName = ProjectSubscriptionName.of(pubSubProjectId, pubSubSubscriptionId);
        ObjectMapper objectMapper = new ObjectMapper();
        // Message Receiver Implementation
        MessageReceiver receiver = (PubsubMessage message, AckReplyConsumer consumer) -> {
            String messageData = message.getData().toStringUtf8();
            log.debug("Received message: {}", messageData);

            KafkaMailWrapper mailWrapper = null;
            try {
                // Deserialize Pub/Sub message to KafkaMailWrapper
                mailWrapper = objectMapper.readValue(messageData, KafkaMailWrapper.class);
            } catch (JsonProcessingException e) {
                log.error("Error parsing Pub/Sub message: {}", messageData, e);
                consumer.nack(); // Negative acknowledge the message (won't commit)
                return;
            }

            try {
                if (mailWrapper != null) {
                    // Process the message with the polling service
                    polling.consumeKafka(mailWrapper.getEmailId(), mailWrapper.getUserId(),
                            mailWrapper.getUserConversation(), mailWrapper.getFolderDisplayName());

                    log.debug("Message processed successfully, Email ID: {}", mailWrapper.getEmailId());
                    consumer.ack(); // Acknowledge the message (marks as processed)
                }
            } catch (Exception e) {
                log.error("Failed to process message with Email ID: {}", mailWrapper.getEmailId(), e);
                consumer.nack(); // Negative acknowledge if processing failed
            }
        };

        // Create a subscriber for the subscription
        final Subscriber subscriber = Subscriber.newBuilder(subscriptionName, receiver).build();
        try {
            // Start the subscriber asynchronously
            subscriber.startAsync().awaitRunning();
            log.info("Listening for messages on {}", subscriptionName);

            // Allow the subscriber to run indefinitely or shutdown after a condition
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                subscriber.stopAsync();
            }));
        } catch (Exception e) {
            log.error("Error starting Pub/Sub subscriber", e);
            subscriber.stopAsync();
        }
    }
}
