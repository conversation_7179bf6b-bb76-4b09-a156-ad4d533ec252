package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.dto.ZonedMeeting;
import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.PropertySet;
import microsoft.exchange.webservices.data.core.enumeration.misc.ConnectingIdType;
import microsoft.exchange.webservices.data.core.enumeration.misc.ExchangeVersion;
import microsoft.exchange.webservices.data.core.enumeration.property.BodyType;
import microsoft.exchange.webservices.data.core.enumeration.property.MapiPropertyType;
import microsoft.exchange.webservices.data.core.enumeration.property.WellKnownFolderName;
import microsoft.exchange.webservices.data.core.enumeration.search.FolderTraversal;
import microsoft.exchange.webservices.data.core.enumeration.search.SortDirection;
import microsoft.exchange.webservices.data.core.service.folder.Folder;
import microsoft.exchange.webservices.data.core.service.item.EmailMessage;
import microsoft.exchange.webservices.data.core.service.item.Item;
import microsoft.exchange.webservices.data.core.service.schema.EmailMessageSchema;
import microsoft.exchange.webservices.data.core.service.schema.ItemSchema;
import microsoft.exchange.webservices.data.credential.WebCredentials;
import microsoft.exchange.webservices.data.misc.ImpersonatedUserId;
import microsoft.exchange.webservices.data.property.definition.ExtendedPropertyDefinition;
import microsoft.exchange.webservices.data.search.FindFoldersResults;
import microsoft.exchange.webservices.data.search.FindItemsResults;
import microsoft.exchange.webservices.data.search.FolderView;
import microsoft.exchange.webservices.data.search.ItemView;
import microsoft.exchange.webservices.data.search.filter.SearchFilter;

import java.net.URI;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Date utils.
 *
 * <AUTHOR> Dangi
 */
@Slf4j
public class DateUtils {

    private DateUtils() {

    }


    /**
     * This method converts date from Meeting object to string and also factors in the difference of time zone.
     *
     * @param meetings the meetings
     * @param zone     the zone
     * @return the list
     */
    public static List<ZonedMeeting> convertMeetingsToZonedMeetings(List<Meeting> meetings, String zone) {
        List<ZonedMeeting> zonedMeetings = new ArrayList<>();
        for (Meeting meeting: meetings) {
            String startTime = DateUtils.convertToTimeZoneString(meeting.getStartTime(), zone);
            String endTime = DateUtils.convertToTimeZoneString(meeting.getEndTime(), zone);
            ZonedMeeting zonedMeeting = new ZonedMeeting(startTime, endTime);
            zonedMeetings.add(zonedMeeting);
        }
        return zonedMeetings;
    }

    private static String convertToTimeZoneString(Date date, String targetZoneIdStr) {
        if (date == null) {
            throw new IllegalArgumentException("Date must not be null");
        }
        Instant instant = date.toInstant();
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        ZonedDateTime targetZonedDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of(targetZoneIdStr));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS);
        return targetZonedDateTime.format(formatter);
    }

    /**
     * Converts date to UTC string in format yyyy-MM-dd'T'HH:mm:ss'Z', also factors in time difference of time zones.
     *
     * @param date the date
     * @param zone the zone
     * @return the string
     */
    public static String convertToUTCString(String date, String zone) {
        log.debug("Inside @method convertToUTCString. @param : date -> {} zone -> {}", date, zone);
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS);
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_Z);

        LocalDateTime localDateTime = LocalDateTime.parse(date, inputFormatter);

        ZoneId zoneId = ZoneId.of(zone);
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        return utcDateTime.format(outputFormatter);
    }

    /**
     * Only takes time and zone and in output provides date with time taking time difference of time zones in consideration.
     *
     * @param time      the time
     * @param zone      the zone
     * @param daysToAdd the days to add
     * @return the string
     */
    public static String convertToUTCString(LocalTime time, String zone, int daysToAdd) {
        if (zone == null) zone = "UTC";
        LocalDate currentDate = LocalDate.now().plusDays(daysToAdd);
        LocalDateTime localDateTime = LocalDateTime.of(currentDate, time);
        ZoneId zoneId = ZoneId.of(zone);
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_Z);
        return utcDateTime.format(outputFormatter);
    }

    public static String convertToUTCString1(LocalTime time, String zone, int daysToAdd) {
        if (zone == null) zone = "UTC";
        LocalDate currentDate = LocalDate.now().plusDays(daysToAdd);
        LocalDateTime localDateTime = LocalDateTime.of(currentDate, time);
        localDateTime = roundOffTime(localDateTime);
        ZoneId zoneId = ZoneId.of(zone);
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_Z);
        return utcDateTime.format(outputFormatter);
    }

    public static String getUTCDateStringWithOffset() {
        LocalDateTime localDateTime = LocalDateTime.now();
        localDateTime = roundOffTime(localDateTime);
        return getUTCStringFromLocalDateTime(localDateTime);
    }

    /**
     * Takes date from string, time from localTime and prepares a date. Then the date is adjusted for the time difference of the timezone. It also round off the time to the nearest half hour or hour.
     *
     * @param dateString      the dateString
     * @param time      the time
     * @param zone      zone
     * @return the string
     */
    public static String convertToUTCString(String dateString, LocalTime time, String zone) {
        if (zone == null) zone = "UTC";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS);
        LocalDate localDate = LocalDateTime.parse(dateString, formatter).toLocalDate();
        LocalDateTime localDateTime = LocalDateTime.of(localDate, time);
        localDateTime = roundOffTime(localDateTime);
        ZoneId zoneId = ZoneId.of(zone);
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_Z);
        return utcDateTime.format(outputFormatter);
    }

    public static LocalDateTime roundOffTime(LocalDateTime localDateTime) {
        int minute = localDateTime.getMinute();

//        if (minute > 0 && minute <= 15) {
//            localDateTime = localDateTime.withMinute(15).withSecond(0);
//        } else if (minute > 15 && minute <= 30) {
//            localDateTime = localDateTime.withMinute(30).withSecond(0);
//        } else if (minute > 30 && minute <= 45) {
//            localDateTime = localDateTime.withMinute(45).withSecond(0);
//        } else if (minute > 45) {
//            localDateTime = localDateTime.plusHours(1).withMinute(0).withSecond(0);
//        }

        if (minute > 0 && minute <= 30) {
            localDateTime = localDateTime.withMinute(30).withSecond(0);
        } else {
            localDateTime = localDateTime.plusHours(1).withMinute(0).withSecond(0);
        }

        return localDateTime;
    }

    public static Integer calculateDurationInMinutes(String startDateTime, String endDateTime) {
        // Define the date-time formatter that matches the input format
        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

        // Parse the input strings into LocalDateTime objects
        LocalDateTime start = LocalDateTime.parse(startDateTime, formatter);
        LocalDateTime end = LocalDateTime.parse(endDateTime, formatter);

        // Calculate the duration between the two LocalDateTime objects
        Duration duration = Duration.between(start, end);

        // Return the duration in minutes
        return (int) duration.toMinutes();
    }

    public static Date parseDateFromString(String date, String timeZone) {
        // Define the formatter for the input date-time string
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS");

        // Parse the date-time string to a ZonedDateTime in the given time zone
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(date, formatter.withZone(ZoneId.of(timeZone)));

        // Convert the ZonedDateTime to an Instant and then to java.util.Date
        Instant instant = zonedDateTime.toInstant();
        return Date.from(instant);
    }


    /**
     * Takes date from input string, time from LocalTime and combines them. Also factors the time difference of time zone.
     *
     * @param dateString the date string
     * @param localTime  the local time
     * @param zone       the zone
     * @return the string
     */
    public static String addTimeWithoutChangingDate(String dateString, LocalTime localTime, String zone) {
        DateTimeFormatter format = DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_Z);
        LocalDate date = LocalDate.parse(dateString, format);
        LocalDateTime localDateTime = LocalDateTime.of(date, localTime);
        ZoneId zoneId = ZoneId.of(zone);
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        return utcDateTime.format(format);
    }
    /**
     * Gives earliest time of all the input times.
     *
     * @param times the times
     * @return the earliest time
     */
    public static LocalTime getEarliestTime(LocalTime... times) {
        if (times == null || times.length == 0) {
            return LocalTime.MAX;
        }
        return Collections.min(Arrays.asList(times), Comparator.naturalOrder());
    }

    /**
     * Returns UTC date string for now or n days ahead EOD.
     *
     * @param now           used to get current time
     * @param daysToAdd     the days to add
     * @param timeZone      used to determine the end time for that zone
     * @return the string
     */
    public static String getUTCString(boolean now, int daysToAdd, String timeZone) {
        ZonedDateTime utcDateTime;
        if (now) {
            utcDateTime = LocalDateTime.now().atZone(ZoneId.of(timeZone));
        } else {
            utcDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).plusDays(daysToAdd).atZone(ZoneId.of(timeZone)).withZoneSameInstant(ZoneId.of("UTC"));
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_Z);
        return utcDateTime.format(formatter);
    }

    /**
     * ..........
     *
     * @param localTime the local time
     * @param zone      the zone
     * @return the local time
     */
    public static LocalTime adjustTimeToZone(LocalTime localTime, String zone) {
        LocalDate currentDate = LocalDate.now();
        LocalDateTime localDateTime = LocalDateTime.of(currentDate, localTime);
        ZonedDateTime systemZonedDateTime = localDateTime.atZone(ZoneId.of(zone));
        ZonedDateTime targetZonedDateTime = systemZonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        return targetZonedDateTime.toLocalTime();
    }

    /**
     * ...............
     *
     * @param endOfDay the end of day
     * @param timeZone the time zone
     * @return the formatted date time
     */
    public static String getFormattedDateTime(boolean endOfDay, String timeZone) {
        LocalDateTime dateTime;
        if (endOfDay) {
            dateTime = LocalDateTime.now().toLocalDate().atTime(LocalTime.MAX);
        } else {
            dateTime = LocalDateTime.now().toLocalDate().atTime(LocalTime.now());
        }
        ZonedDateTime utcDateTime = dateTime.atZone(ZoneId.of(timeZone)).withZoneSameInstant(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_Z);
        return utcDateTime.format(formatter);
    }

    public static Date parseDate(String dateString) {
        SimpleDateFormat formatter = new SimpleDateFormat(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_Z);
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date date = null;
        try {
            date = formatter.parse(dateString);
        } catch (ParseException e) {
            log.error("Error inside @method parseDate.", e);
        }
        return date;
    }

    public static Date parseDateWithTimeZone(String dateString, String timeZoneString) throws ParseException {
        // Define the date format
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

        // Set the time zone for the date format
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneString);
        dateFormat.setTimeZone(timeZone);

        // Parse the date string into a Date object
        return dateFormat.parse(dateString);
    }

    public static Date parseDateWithoutTZ(String dateString) {
        SimpleDateFormat formatter = new SimpleDateFormat(EmailConstants.YYYY_MM_DD_T_HH_MM_SS);
//        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date date = null;
        try {
            date = formatter.parse(dateString);
        } catch (ParseException e) {
            log.error("Error inside @method parseDate.", e);
        }
        return date;
    }

    public static Date getDateNDaysBefore(int numberOfDays) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -numberOfDays);
        return calendar.getTime();
    }

    public static String getUTCStringFromLocalDateTime(LocalDateTime dateTime) {
        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        // Format the input LocalDateTime to the desired format
        return dateTime.format(formatter);
    }

    /**
     * New
     * This method is used to provide UTC date string (for current time or end of the day) for a specific time zone.
     *
     * @param endOfDay the end of day (true for end of the day and false for now)
     * @param timeZone the time zone (time zone for which we want to get the UTC string)
     * @return the utc date string in yyyy-MM-dd'T'HH:mm:ss'Z' format
     */
    public static String getUTCDateTime(boolean endOfDay, String timeZone) {
        // Create ZoneId from the provided time zone string
        ZoneId zoneId = ZoneId.of(timeZone);

        ZonedDateTime zonedDateTime;
        if (endOfDay) {
            // Get the end of the day for the current date in the specified time zone
            LocalDateTime localEndOfDay = LocalDate.now(zoneId).atTime(23, 59, 59, 999_999_999);
            zonedDateTime = localEndOfDay.atZone(zoneId);
        } else {
            // Get the current time in the specified time zone
            zonedDateTime = ZonedDateTime.now(zoneId);
        }

        // Convert the ZonedDateTime to UTC
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));

        // Format the ZonedDateTime to the required format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        return utcDateTime.format(formatter);
    }

    public static LocalDateTime convertToLocalDateTime(String dateString) {
        // Parse the string into a ZonedDateTime with UTC (Z)
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateString, DateTimeFormatter.ISO_DATE_TIME);

        // Convert ZonedDateTime to LocalDateTime (in the system default time zone)
        return zonedDateTime.withZoneSameInstant(ZoneId.of("UTC")).toLocalDateTime();
    }

    /**
     * Converts a LocalDateTime to a UTC ISO 8601 string with 'Z' at the end.
     * Example output: "2025-05-26T14:30:00Z"
     *
     * @param localDateTime LocalDateTime object
     * @return ISO 8601 formatted string in UTC
     */
    public static String convertToUtcIsoString(LocalDateTime localDateTime) {
        ZonedDateTime utcZoned = localDateTime.atZone(ZoneOffset.UTC);
        return utcZoned.format(DateTimeFormatter.ISO_INSTANT);
    }

    public static LocalDateTime parseDateToUTC(String dateString, String timeZone, LocalTime localTime) {
        // Define the pattern for the date
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Parse the date string to a LocalDate
        LocalDate localDate = LocalDate.parse(dateString, dateFormatter);

        // Combine LocalDate and LocalTime to get LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);

        // Create a ZonedDateTime based on the provided time zone
        ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime, ZoneId.of(timeZone));

        // Convert the ZonedDateTime to UTC
        ZonedDateTime utcZonedDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));

        // Return the LocalDateTime part of the UTC ZonedDateTime
        return utcZonedDateTime.toLocalDateTime();
    }

    // Method to return the earliest LocalDateTime
    public static LocalDateTime getEarliestLocalDateTime(List<LocalDateTime> dateTimes) {
        // Use Collections.min with Comparator to find the earliest LocalDateTime
        return Collections.min(dateTimes, Comparator.naturalOrder());
    }

    // Method to return the latest LocalDateTime
    public static LocalDateTime getLatestLocalDateTime(List<LocalDateTime> dateTimes) {
        // Use Collections.max with Comparator to find the latest LocalDateTime
        return Collections.max(dateTimes, Comparator.naturalOrder());
    }

    public static boolean isToday(String dateString) {
        // Define the date format pattern (yyyy-MM-dd)
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            // Parse the input string to LocalDate
            LocalDate inputDate = LocalDate.parse(dateString, dateFormatter);

            // Get today's date
            LocalDate today = LocalDate.now();

            // Compare the input date with today's date
            return inputDate.equals(today);
        } catch (DateTimeParseException e) {
            // Handle the case where the date string is not in the correct format
            System.out.println("Invalid date format: " + dateString);
            return false;
        }
    }

    //System time is in UTC so when given timezone of the user then it gives current date in that time zone
    public static String getUserLocalDate(String timeZone) {
        // Get the current date and time in the user's time zone
        ZonedDateTime userDateTime = ZonedDateTime.now(ZoneId.of(timeZone));
        // Extract the local date part from ZonedDateTime
        LocalDate userDate = userDateTime.toLocalDate();
        // Format the date in yyyy-MM-dd format
        return userDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    public static LocalDateTime getDayStartInUtc(String timeZone) {
        // Get the current date (e.g., 2024-10-10) in the specified time zone
        ZonedDateTime startOfDayInTimeZone = ZonedDateTime.now(ZoneId.of(timeZone))
                .truncatedTo(ChronoUnit.DAYS); // Set time to start of the day (00:00:00)

        // Convert the start of the day in the given time zone to UTC
        ZonedDateTime startOfDayInUtc = startOfDayInTimeZone.withZoneSameInstant(ZoneId.of("UTC"));

        // Return the UTC time as LocalDateTime
        return startOfDayInUtc.toLocalDateTime();
    }

    public static String getMinutesBehindCurrentTime(Integer minutes) {
        if (minutes == null) minutes = 60;
        // Get the current time
        Instant now = Instant.now();

        // Subtract one hour from the current time
        Instant oneHourBehind = now.minusSeconds(minutes * 60);

        // Format the time to ISO 8601 UTC format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        ZonedDateTime utcTime = oneHourBehind.atZone(ZoneOffset.UTC);

        return formatter.format(utcTime);
    }

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));

        String timeZone = "Asia/Kolkata";
        LocalDateTime dayStartInUtc = getDayStartInUtc(timeZone);
        System.out.println(dayStartInUtc);
        System.out.println(dayStartInUtc.plusDays(1));

//        String date1 = "2024-09-11";  // Sample input
//        String date2 = "2024-09-24";  // Change this to today's date to test
//        String invalidDate = "2024-09-XX";  // Invalid format
//
//        System.out.println("Is date1 today? " + isToday(date1)); // Expected output: false
//        System.out.println("Is date2 today? " + isToday(date2)); // Expected output: true/false based on today's date
//        System.out.println("Is invalidDate today? " + isToday(invalidDate)); // Expected output: false

    }

    public static void getEmails(String email) throws Exception {
        try {
            ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2010_SP2);
            service.setCredentials(new WebCredentials("<EMAIL>", "Vision@1234"));
            service.setUrl(new URI("https://exchange-server.vision.com/EWS/Exchange.asmx"));
            ImpersonatedUserId impersonatedUserId = new ImpersonatedUserId(ConnectingIdType.SmtpAddress, email);
            service.setImpersonatedUserId(impersonatedUserId);
            // Define the view for the items to be returned
            ItemView view = new ItemView(10); // Change 10 to the number of items you want to fetch
            view.getOrderBy().add(ItemSchema.DateTimeReceived, SortDirection.Descending);

//            FolderId folderId = new FolderId();
//            Folder folder = Folder.bind(service, folderId);

            SearchFilter filter = new SearchFilter.IsEqualTo(EmailMessageSchema.Subject, "Notification Test");

            // Find items in the Inbox
            Folder folder = Folder.bind(service, WellKnownFolderName.valueOf("Inbox"));
            FindItemsResults<Item> findResults = service.findItems(folder.getId(), filter, view);

//            PropertySet propertySet = new PropertySet(ItemSchema.Subject, ItemSchema.Body);
//            propertySet.setRequestedBodyType(BodyType.Text);
//
//            // Load additional properties for the items (optional)
//            service.loadPropertiesForItems(findResults, propertySet);

            PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
            propertySet.setRequestedBodyType(BodyType.Text);
            service.loadPropertiesForItems(findResults, propertySet);

            ExtendedPropertyDefinition flagStatusProperty = new ExtendedPropertyDefinition(0x1090, MapiPropertyType.Integer);

            // Print the subject and body of each email
            for (Item item : findResults) {
                if (item instanceof EmailMessage) {
                    EmailMessage email1 = (EmailMessage) item;
//                    email1.setExtendedProperty(flagStatusProperty, 2);
//                    email1.update(ConflictResolutionMode.AutoResolve);
                    String subject = email1.getSubject();
                    System.out.println("Id: " + email1.getId().getUniqueId());
                    System.out.println("Subject: " + subject);
                    System.out.println("Body: " + email1.getBody().toString());
                }
            }
        } catch (Exception e) {
            System.out.println("An error occurred while fetching emails: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static ExchangeService getService(String email) throws Exception {
        ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2010_SP2);
        service.setCredentials(new WebCredentials("<EMAIL>", "Vwaves@#10"));
        service.setUrl(new URI("https://exchange-server.vision.com/EWS/Exchange.asmx"));
        ImpersonatedUserId impersonatedUserId = new ImpersonatedUserId(ConnectingIdType.SmtpAddress, email);
        service.setImpersonatedUserId(impersonatedUserId);
        return service;
    }

    public static List<Map<String, String>> getAllFolders(String email) throws Exception {
        // Define the view for finding folders (maximum number of folders to retrieve)
        ExchangeService service = getService(email);
        FolderView view = new FolderView(Integer.MAX_VALUE);

        // Set the traversal property to include all folders in the search
        view.setTraversal(FolderTraversal.Shallow);

        // Find all folders under the root folder
        FindFoldersResults findFolderResults = service.findFolders(WellKnownFolderName.MsgFolderRoot, view);

        // Loop through the results and print folder names
        List<Map<String, String>> result = new ArrayList<>();
        for (Folder folder : findFolderResults.getFolders()) {
            Map<String, String> mailFolderMap = new HashMap<>();
            mailFolderMap.put("id", folder.getId().getUniqueId());
            mailFolderMap.put("displayName", folder.getDisplayName());
            result.add(mailFolderMap);
        }
        return result;
    }


    /**
     * Extracts the date from a given date-time string by attempting to parse it with multiple formats.
     *
     * @param dateTimeString The date-time string to be parsed.
     * @return The extracted LocalDate if parsing is successful; otherwise, null.
     */
    public static LocalDate extractDate(String dateTimeString) {
        List<DateTimeFormatter> formatters = getFormatters();
        for (DateTimeFormatter formatter : formatters) {
            LocalDate date = tryParse(dateTimeString, formatter);
            if (date != null) {
                return date;
            }
        }
        return null; // Parsing failed for all formats
    }


    /**
     * Returns a list of possible DateTimeFormatter objects to handle different date-time formats.
     *
     * @return A list of DateTimeFormatter instances.
     */
    private static List<DateTimeFormatter> getFormatters() {
        return List.of(
                DateTimeFormatter.ISO_DATE_TIME, // e.g., 2024-09-02T18:30:13Z
                DateTimeFormatter.ISO_ZONED_DATE_TIME, // e.g., 2024-09-02T18:30:13+01:00[Europe/London]
                DateTimeFormatter.ISO_INSTANT, // e.g., 2024-09-02T18:30:13Z (used for UTC)
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"), // e.g., 2024-09-02 18:30:13
                DateTimeFormatter.ofPattern("yyyy-MM-dd"), // e.g., 2024-09-02
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")

        );
    }

    /**
     * Attempts to parse the date-time string with the provided formatter.
     *
     * @param dateTimeString The date-time string to be parsed.
     * @param formatter The DateTimeFormatter to use for parsing.
     * @return The LocalDate if parsing is successful; otherwise, null.
     */
    private static LocalDate tryParse(String dateTimeString, DateTimeFormatter formatter) {
        try {
            if (isZonedOrInstantFormatter(formatter)) {
                ZonedDateTime zdt = ZonedDateTime.parse(dateTimeString, formatter);
                return zdt.toLocalDate();
            } else {
                LocalDateTime ldt = LocalDateTime.parse(dateTimeString, formatter);
                return ldt.toLocalDate();
            }
        } catch (DateTimeParseException e) {
            return null; // Parsing failed for this formatter
        }
    }

    /**
     * Checks if the formatter is one of the zoned or instant types.
     *
     * @param formatter The DateTimeFormatter to check.
     * @return True if the formatter is a zoned or instant type, false otherwise.
     */
    private static boolean isZonedOrInstantFormatter(DateTimeFormatter formatter) {
        return formatter == DateTimeFormatter.ISO_DATE_TIME ||
                formatter == DateTimeFormatter.ISO_INSTANT ||
                formatter == DateTimeFormatter.ISO_ZONED_DATE_TIME;
    }

    /**
     * Extracts the time (HH:mm:ss) from a given date-time string by attempting to parse it with multiple formats.
     *
     * @param dateTimeString The date-time string to be parsed.
     * @return The extracted time in HH:mm:ss format if parsing is successful; otherwise, null.
     */
    public static String extractTime(String dateTimeString) {
        List<DateTimeFormatter> formatters = getFormatters();

        for (DateTimeFormatter formatter : formatters) {
            LocalTime time = tryParseTime(dateTimeString, formatter);
            if (time != null) {
                return time.toString(); // Returns time in HH:mm:ss format
            }
        }

        return null; // Parsing failed for all formats
    }

    /**
     * Attempts to parse the time from the date-time string using the provided formatter.
     *
     * @param dateTimeString The date-time string to be parsed.
     * @param formatter The DateTimeFormatter to use for parsing.
     * @return The LocalTime if parsing is successful; otherwise, null.
     */
    private static LocalTime tryParseTime(String dateTimeString, DateTimeFormatter formatter) {
        try {
            ZonedDateTime zdt = ZonedDateTime.parse(dateTimeString, formatter);
            return zdt.toLocalTime(); // Extracts the time part (HH:mm:ss)
        } catch (DateTimeParseException e) {
            return null; // Parsing failed for this formatter
        }
    }

}
