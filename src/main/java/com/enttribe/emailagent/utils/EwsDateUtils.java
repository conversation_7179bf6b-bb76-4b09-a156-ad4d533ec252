package com.enttribe.emailagent.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

/**
 * The type Ews date utils.
 *  <AUTHOR> Dangi
 */
public class EwsDateUtils {


    public static Date getFormattedDateTime(boolean endOfDay, String timeZone) {
        // Create ZoneId from the provided time zone string
        ZoneId zoneId = ZoneId.of(timeZone);

        ZonedDateTime zonedDateTime;
        if (endOfDay) {
            // Get the end of the day for the current date in the specified time zone
            LocalDateTime localEndOfDay = LocalDate.now(zoneId).atTime(LocalTime.MAX);
            zonedDateTime = localEndOfDay.atZone(zoneId);
        } else {
            // Get the current time in the specified time zone
            zonedDateTime = ZonedDateTime.now(zoneId);
        }

        // Convert the ZonedDateTime to UTC
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneOffset.UTC);

        // Return the Date object
        return Date.from(utcDateTime.toInstant());
    }

    public static Date parseDateWithTimeZone(String dateStr, String timeZone) {
        // Parse the input date string (assuming it's in ISO 8601 format: yyyy-MM-dd'T'HH:mm:ss)
        LocalDateTime localDateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);

        // Apply the provided time zone to the local date-time
        ZoneId zoneId = ZoneId.of(timeZone);
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);

        // Convert the ZonedDateTime to UTC
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneOffset.UTC);

        // Return as a Date object
        return Date.from(utcDateTime.toInstant());
    }


}
