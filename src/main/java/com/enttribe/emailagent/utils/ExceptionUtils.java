package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.entity.FailureLogs;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;

/**
 * The type Exception utils.
 *  <AUTHOR>
 */
public class ExceptionUtils {

    public static String getStackTraceAsString(Throwable throwable) {
        StringWriter stringWriter = new StringWriter();
        throwable.printStackTrace(new PrintWriter(stringWriter));
        return stringWriter.toString();
    }

    public static FailureLogs getFailureLogObject(String email, String emailSubject, String messageId, String internetMessageId, String conversationId, String type, Throwable e) {
        return null;
    }


}
