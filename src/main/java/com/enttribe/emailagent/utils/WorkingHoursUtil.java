package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.dto.WorkingHoursSummary;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.TreeMap;


public class WorkingHoursUtil {

    // Create a HashMap for time zone mappings
    public static final Map<String, String> GRAPH_API_TO_IANA_TIMEZONE = new HashMap<>();
    private static final Logger log = LoggerFactory.getLogger(WorkingHoursUtil.class);

    static {
        // North America
        GRAPH_API_TO_IANA_TIMEZONE.put("Pacific Standard Time", "America/Los_Angeles");
        GRAPH_API_TO_IANA_TIMEZONE.put("US Mountain Standard Time", "America/Denver");
        GRAPH_API_TO_IANA_TIMEZONE.put("Mountain Standard Time", "America/Denver");
        GRAPH_API_TO_IANA_TIMEZONE.put("Central Standard Time", "America/Chicago");
        GRAPH_API_TO_IANA_TIMEZONE.put("Eastern Standard Time", "America/New_York");
        GRAPH_API_TO_IANA_TIMEZONE.put("US Eastern Standard Time", "America/New_York");
        GRAPH_API_TO_IANA_TIMEZONE.put("Atlantic Standard Time", "America/Halifax");
        GRAPH_API_TO_IANA_TIMEZONE.put("Hawaiian Standard Time", "Pacific/Honolulu");
        GRAPH_API_TO_IANA_TIMEZONE.put("Alaskan Standard Time", "America/Anchorage");

        // Europe
        GRAPH_API_TO_IANA_TIMEZONE.put("Greenwich Standard Time", "Etc/Greenwich");
        GRAPH_API_TO_IANA_TIMEZONE.put("Central European Standard Time", "Europe/Brussels");
        GRAPH_API_TO_IANA_TIMEZONE.put("Eastern European Standard Time", "Europe/Athens");
        GRAPH_API_TO_IANA_TIMEZONE.put("Romance Standard Time", "Europe/Paris");
        GRAPH_API_TO_IANA_TIMEZONE.put("Central European Summer Time", "Europe/Brussels");
        GRAPH_API_TO_IANA_TIMEZONE.put("Western European Summer Time", "Europe/Lisbon");

        // Africa
        GRAPH_API_TO_IANA_TIMEZONE.put("W. Central Africa Standard Time", "Africa/Lagos");
        GRAPH_API_TO_IANA_TIMEZONE.put("Central Africa Time", "Africa/Lagos");
        GRAPH_API_TO_IANA_TIMEZONE.put("South Africa Standard Time", "Africa/Johannesburg");
        GRAPH_API_TO_IANA_TIMEZONE.put("Egypt Standard Time", "Africa/Cairo");

        // Middle East
        GRAPH_API_TO_IANA_TIMEZONE.put("Arab Standard Time", "Asia/Riyadh");
        GRAPH_API_TO_IANA_TIMEZONE.put("Arabian Standard Time", "Asia/Riyadh");
        GRAPH_API_TO_IANA_TIMEZONE.put("Israel Standard Time", "Asia/Jerusalem");
        GRAPH_API_TO_IANA_TIMEZONE.put("Georgian Standard Time", "Asia/Tbilisi");

        // Asia
        GRAPH_API_TO_IANA_TIMEZONE.put("India Standard Time", "Asia/Kolkata");
        GRAPH_API_TO_IANA_TIMEZONE.put("Sri Lanka Standard Time", "Asia/Colombo");
        GRAPH_API_TO_IANA_TIMEZONE.put("Nepal Standard Time", "Asia/Kathmandu");
        GRAPH_API_TO_IANA_TIMEZONE.put("China Standard Time", "Asia/Shanghai");
        GRAPH_API_TO_IANA_TIMEZONE.put("Singapore Standard Time", "Asia/Singapore");
        GRAPH_API_TO_IANA_TIMEZONE.put("Western Standard Time", "Australia/Perth");
        GRAPH_API_TO_IANA_TIMEZONE.put("AUS Central Standard Time", "Australia/Darwin");
        GRAPH_API_TO_IANA_TIMEZONE.put("E. Australia Standard Time", "Australia/Sydney");
        GRAPH_API_TO_IANA_TIMEZONE.put("Tasmania Standard Time", "Australia/Hobart");

        // Pacific Islands
        GRAPH_API_TO_IANA_TIMEZONE.put("New Zealand Standard Time", "Pacific/Auckland");
        GRAPH_API_TO_IANA_TIMEZONE.put("UTC", "UTC");
        GRAPH_API_TO_IANA_TIMEZONE.put("UTC+01:00", "Europe/Paris");

        // Russian Time Zones
        GRAPH_API_TO_IANA_TIMEZONE.put("Samara Time", "Europe/Samara");
        GRAPH_API_TO_IANA_TIMEZONE.put("Volgograd Time", "Europe/Volgograd");
        GRAPH_API_TO_IANA_TIMEZONE.put("Kamchatka Time", "Asia/Kamchatka");

        // Cape Verde & Azores
        GRAPH_API_TO_IANA_TIMEZONE.put("Cape Verde Standard Time", "Atlantic/Cape_Verde");
    }

    public static WorkingHoursSummary getWorkingHoursSummary(List<JSONObject> userWorkingHoursList) {
        Set<String> commonWorkDays = null;
        Map<String, List<WorkingHoursSummary.TimeRange>> dailyRanges = new HashMap<>();

        for (JSONObject user : userWorkingHoursList) {
            JSONArray daysArray = user.getJSONArray("daysOfWeek");
            Set<String> userWorkDays = new HashSet<>();
            for (int i = 0; i < daysArray.length(); i++) {
                userWorkDays.add(daysArray.getString(i).toLowerCase());
            }

            if (commonWorkDays == null) {
                commonWorkDays = new HashSet<>(userWorkDays);
            } else {
                commonWorkDays.retainAll(userWorkDays);
            }

            String timeZoneStr = user.getJSONObject("timeZone").getString("name");
            ZoneId zoneId = ZoneId.of(GRAPH_API_TO_IANA_TIMEZONE.getOrDefault(timeZoneStr, "UTC"));

            LocalTime localStart = LocalTime.parse(user.getString("startTime").substring(0, 5));
            LocalTime localEnd = LocalTime.parse(user.getString("endTime").substring(0, 5));

            for (String day : userWorkDays) {
                List<WorkingHoursSummary.TimeRange> utcRanges = convertToUTCRanges(localStart, localEnd, zoneId, day);
                dailyRanges.merge(day, new ArrayList<>(utcRanges), (oldList, newList) -> {
                    oldList.addAll(newList);
                    return oldList;
                });
            }
        }

        // Intersect ranges for each common day
        Map<String, List<WorkingHoursSummary.TimeRange>> intersected = new HashMap<>();
        for (String day : commonWorkDays) {
            List<WorkingHoursSummary.TimeRange> allRanges = dailyRanges.getOrDefault(day, List.of());
            List<WorkingHoursSummary.TimeRange> intersection = intersectTimeRanges(allRanges, userWorkingHoursList.size());
            if (!intersection.isEmpty()) {
                intersected.put(day, intersection);
            }
        }

        return new WorkingHoursSummary(commonWorkDays, intersected);
    }

    private static List<WorkingHoursSummary.TimeRange> convertToUTCRanges(LocalTime start, LocalTime end, ZoneId zoneId, String day) {
        DayOfWeek dayOfWeek = DayOfWeek.valueOf(day.toUpperCase());
        LocalDate referenceDate = LocalDate.now().with(TemporalAdjusters.nextOrSame(dayOfWeek));
        ZonedDateTime startZdt = ZonedDateTime.of(referenceDate, start, zoneId).withZoneSameInstant(ZoneOffset.UTC);
        ZonedDateTime endZdt = ZonedDateTime.of(referenceDate, end, zoneId).withZoneSameInstant(ZoneOffset.UTC);

        int startMinutes = startZdt.getHour() * 60 + startZdt.getMinute();
        int endMinutes = endZdt.getHour() * 60 + endZdt.getMinute();

        if (endMinutes <= startMinutes) {
            // Midnight crossing: split into two ranges
            return List.of(
                    new WorkingHoursSummary.TimeRange(startMinutes, 1440),
                    new WorkingHoursSummary.TimeRange(0, endMinutes)
            );
        } else {
            return List.of(new WorkingHoursSummary.TimeRange(startMinutes, endMinutes));
        }
    }

    private static List<WorkingHoursSummary.TimeRange> intersectTimeRanges(List<WorkingHoursSummary.TimeRange> ranges, int userCount) {
        Map<Integer, Integer> timeline = new TreeMap<>();
        for (WorkingHoursSummary.TimeRange r : ranges) {
            timeline.put(r.getStartMinutesUTC(), timeline.getOrDefault(r.getStartMinutesUTC(), 0) + 1);
            timeline.put(r.getEndMinutesUTC(), timeline.getOrDefault(r.getEndMinutesUTC(), 0) - 1);
        }

        List<WorkingHoursSummary.TimeRange> result = new ArrayList<>();
        int active = 0;
        Integer start = null;

        for (Map.Entry<Integer, Integer> entry : timeline.entrySet()) {
            int minute = entry.getKey();
            active += entry.getValue();

            if (active == userCount && start == null) {
                start = minute;
            } else if (active < userCount && start != null) {
                result.add(new WorkingHoursSummary.TimeRange(start, minute));
                start = null;
            }
        }

        return result;
    }
}