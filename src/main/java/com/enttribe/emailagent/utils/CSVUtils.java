package com.enttribe.emailagent.utils;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * The type Csv utils.
 *  <AUTHOR> <PERSON>ak
 */
public class CSVUtils {

    private CSVUtils() {
    }

    public static <T> ResponseEntity<Resource> exportCSV(List<T> recordList, List<String> csvColumnHeader, String fileName, List<Function<T, Object>> fieldExtractors) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(outputStream), CSVFormat.DEFAULT.withHeader(csvColumnHeader.toArray(new String[0])))) {

            for (T records : recordList) {
                List<String> rowData = new ArrayList<>();
                for (Function<T, Object> fieldExtractor : fieldExtractors) {
                    Object value = fieldExtractor.apply(records);
                    rowData.add(value != null ? value.toString() : null);
                }
                csvPrinter.printRecord(rowData);
            }

            csvPrinter.flush();
            byte[] dataBytes = outputStream.toByteArray();
            ByteArrayResource resource = new ByteArrayResource(dataBytes);

            return ResponseEntity
                    .ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName + ".csv")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

}
