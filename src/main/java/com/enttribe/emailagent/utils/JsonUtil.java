package com.enttribe.emailagent.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import microsoft.exchange.webservices.data.core.service.item.EmailMessage;
import microsoft.exchange.webservices.data.property.complex.EmailAddress;

import java.util.List;
import java.util.stream.Collectors;

/**
 * The type Json util.
 *  <AUTHOR>
 */
public  class JsonUtil {

    public static ObjectNode convertEmailMessageToJson(EmailMessage email,String type) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode emailJson = mapper.createObjectNode();

        try {
            if(type.equalsIgnoreCase("Short")) {
//                email.load(new PropertySet(ItemSchema.UniqueBody));

                emailJson.put("originalMessage", email.getBody().toString());
            }

//            email.load();
            if(type.equalsIgnoreCase("Long"))  {
                emailJson.put("bodyPreview", email.getBody().toString());

            }
            emailJson.put("id", email.getId().getUniqueId());
            emailJson.put("categories",email.getCategories().toString());
            emailJson.put("receivedDateTime", email.getDateTimeReceived().toString());
            emailJson.put("sentDateTime", email.getDateTimeSent().toString());
            emailJson.put("hasAttachments", email.getHasAttachments());
            emailJson.put("subject", email.getSubject());

            emailJson.put("importance", email.getImportance().toString());
            emailJson.put("conversationId", email.getConversationId().getUniqueId());
            emailJson.put("isRead", email.getIsRead());
            emailJson.put("isDraft", email.getIsDraft());

            ObjectNode bodyJson = mapper.createObjectNode();
            emailJson.set("body", bodyJson);

            ObjectNode senderJson = mapper.createObjectNode();
            senderJson.set(EmailConstants.EMAIL_ADDRESS, createEmailAddressJson(email.getSender()));
            emailJson.set("sender", senderJson);

            ObjectNode fromJson = mapper.createObjectNode();
            fromJson.set(EmailConstants.EMAIL_ADDRESS, createEmailAddressJson(email.getFrom()));
            emailJson.set("from", fromJson);

            emailJson.set("toRecipients", createRecipientsJson(email.getToRecipients().getItems()));
            emailJson.set("ccRecipients", createRecipientsJson(email.getCcRecipients().getItems()));
            emailJson.set("bccRecipients", createRecipientsJson(email.getBccRecipients().getItems()));
            emailJson.set("replyTo", createRecipientsJson(email.getReplyTo().getItems()));

            return emailJson;
        }
        catch (Exception e){
            e.printStackTrace();
            return null;
        }

    }

    private static ObjectNode createEmailAddressJson(EmailAddress address) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode emailAddressJson = mapper.createObjectNode();
        emailAddressJson.put("name", address.getName());
        emailAddressJson.put("address", address.getAddress());
        return emailAddressJson;
    }

    private static ObjectNode createRecipientJson(EmailAddress address) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode recipientJson = mapper.createObjectNode();
        recipientJson.set(EmailConstants.EMAIL_ADDRESS, createEmailAddressJson(address));
        return recipientJson;
    }

    private static ArrayNode createRecipientsJson(List<EmailAddress> addresses) {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode recipientsJson = mapper.createArrayNode();
        for (EmailAddress address : addresses) {
            recipientsJson.add(createRecipientJson(address));
        }
        return recipientsJson;
    }

    public static String createRecipientsString(List<EmailAddress> addresses) {

        // Collect recipients as comma-separated string using Java 8 streams
        String recipientsString = addresses.stream()
                .map(EmailAddress::getAddress)
                .collect(Collectors.joining(", "));

        // Print or use the collected string
        System.out.println("To Recipients: " + recipientsString);
        return recipientsString;
    }



}
