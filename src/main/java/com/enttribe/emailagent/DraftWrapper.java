package com.enttribe.emailagent;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * The type Draft wrapper.
 *
 *   <AUTHOR>
 */
@Setter
@Getter
public class DraftWrapper implements Serializable {

    private String conversationId;
    private String userEmail;
    private String objective;
    private String userPrompt;
    private String draft;
    private String selection;
    private String messageId;
    private String internetMessageId;
    private String status;
    private String length;
    private String tone;
    private String previousDraft;
    private Boolean forward;
    private String content;
    private String recipients;

}
