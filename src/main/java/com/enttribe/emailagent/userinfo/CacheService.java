package com.enttribe.emailagent.userinfo;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.security.PublicKey;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * The type Cache service.
 *  <AUTHOR> Sonsale
 */
@Service
@Slf4j
public class CacheService {

    @Value("${user.info.cache.evict.time}")
    private int evictTime;

    private final Map<String, UserInfo> cache = new ConcurrentHashMap<>();

    /**
     * Validate and cache user user info.
     *
     * @param email    the email
     * @param userInfo the user info
     * @param token    the token
     * @return the user info
     */
    public UserInfo validateAndCacheUser(String email, UserInfo userInfo, String token) {

        log.debug("Inside @method validateAndCacheUser. @param : email -> {} Time -> {}", email, LocalDateTime.now());
        UserInfo info = cache.get(email);
        if (info != null) {
            log.debug("Cache size is : {}", cache.size());
            log.debug("User {} found in cache", email);
            return info;
        }

        // Make HTTP request to Microsoft Graph API
        log.debug("User {} is not found in cache. Making HTTP request...", email);
        try {
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

                HttpGet request = new HttpGet("https://graph.microsoft.com/v1.0/me");
                request.addHeader("Authorization", "Bearer " + token);

                // Execute the request
                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    int statusCode = response.getStatusLine().getStatusCode();
                    log.debug("username -> {}, statusCode -> {}", email, statusCode);
                    if (statusCode == 200 || statusCode == 403) { // Check if authentication is successful
                        // Set user info properties...
                        userInfo.setExpiration(LocalDateTime.now().plusMinutes(evictTime));
                        cache.put(email, userInfo);
                        return userInfo;
                    } else {
                        String errorResponse = EntityUtils.toString(response.getEntity());
                        log.error("\nAuthentication failed.\nHTTP Status Code: {} response: {}", statusCode, errorResponse);
                        return null;
                    }
                }
            }
        } catch (Exception e) {
            log.error("\nException inside @method validateAndCacheUser.\nException message : {}", e.getMessage());
            return null;
        }
    }

    /**
     * Evict expired entries.
     */
    @Scheduled(fixedDelay = 10 * 60 * 1000) // 10 (10 * 60 * 1000) minutes in milliseconds
    public void evictExpiredEntries() {
        log.debug("\nEvicting the old userInfo from cache...\n");
        LocalDateTime currentTime = LocalDateTime.now();
        cache.entrySet().removeIf(entry -> entry.getValue().getExpiration().isBefore(currentTime));
    }

    private static PublicKey getPublicKeyFromCertificateString(String certificateString) throws Exception {
        byte[] decoded = Base64.getDecoder().decode(certificateString);
        CertificateFactory factory = CertificateFactory.getInstance("X.509");
        X509Certificate certificate = (X509Certificate) factory.generateCertificate(new ByteArrayInputStream(decoded));
        return certificate.getPublicKey();
    }

}

