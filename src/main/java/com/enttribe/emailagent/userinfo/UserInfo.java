package com.enttribe.emailagent.userinfo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * The type User info.
 *  <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
public class UserInfo {

//    private String name;
    private String id;
    private String email;
    private LocalDateTime expiration;

}
