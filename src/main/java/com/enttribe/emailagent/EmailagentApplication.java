package com.enttribe.emailagent;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

/**
 * The type Emailagent application.
 *
 *  <AUTHOR>
 */
@SpringBootApplication
@EnableAsync
@ComponentScan(basePackages = {"com.enttribe.emailagent", "com.enttribe.commons.ai"})
@EntityScan(basePackages = {"com.enttribe.emailagent"})
@EnableScheduling
@EnableCaching
public class EmailagentApplication {


    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        SpringApplication.run(EmailagentApplication.class, args);
    }

}
