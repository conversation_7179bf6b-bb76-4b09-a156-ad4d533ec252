package com.enttribe.emailagent.integration.impl;
import com.enttribe.emailagent.ai.dto.meeting.AvailableTimeSlots;
import com.enttribe.emailagent.dao.UserMailAttachmentRepository;
import com.enttribe.emailagent.dto.*;

import javax.activation.*;
import javax.mail.BodyPart;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.internet.*;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.UserMailAttachment;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.*;
import com.enttribe.emailagent.wrapper.*;
import com.fasterxml.jackson.core.StreamReadConstraints;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.googleapis.json.GoogleJsonError;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.client.http.HttpRequestInitializer;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.client.util.DateTime;
import com.google.api.services.admin.directory.Directory;
import com.google.api.services.admin.directory.DirectoryScopes;
import com.google.api.services.admin.directory.model.User;
import com.google.api.services.admin.directory.model.Users;
import com.google.api.services.calendar.CalendarScopes;
import com.google.api.services.calendar.model.*;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.GmailScopes;
import com.google.api.services.calendar.Calendar;
import com.google.api.services.gmail.model.*;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.PKCEProvider;
import jakarta.activation.DataSource;
import jakarta.mail.MessagingException;
import jakarta.mail.Part;
import jakarta.mail.util.ByteArrayDataSource;
import org.apache.logging.log4j.LogManager;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Base64;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.GeneralSecurityException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import com.enttribe.emailagent.exception.EmailAgentLogger;

import static com.enttribe.emailagent.utils.CommonUtils.*;
import static com.enttribe.emailagent.utils.DateUtils.extractDate;
import static com.enttribe.emailagent.utils.DateUtils.extractTime;
import static com.enttribe.emailagent.utils.EmailConstants.*;
import static com.enttribe.emailagent.utils.GmailUtils.*;
import static com.enttribe.emailagent.utils.GmailUtils.ACCEPTED;
import static com.google.common.base.Throwables.getStackTraceAsString;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import com.google.api.services.calendar.model.EventReminder;
import com.google.api.services.calendar.model.Event.Reminders;
import com.google.api.services.calendar.model.Event;
import com.google.api.services.calendar.model.EventDateTime;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Stream;

/**
 * Service implementation for interacting with Gmail API.
 * This service is used to retrieve emails, process attachments, and handle Gmail-related operations.
 *  <AUTHOR>
 */
@Service
public class GmailIntegrationImpl extends GmailGraphIntegration implements GmailIntegration {

    private static final Logger log = EmailAgentLogger.getLogger(GmailIntegrationImpl.class);
//    private static final org.apache.logging.log4j.Logger log = LogManager.getLogger(GmailIntegrationImpl.class);


//    @Value("${service.account.key}")
    private String SERVICE_ACCOUNT_KEY_FILE_PATH = "/tmp/gmail-credentials/gmailassistant-credentials.json";
//  private String SERVICE_ACCOUNT_KEY_FILE_PATH = "/Users/<USER>/Downloads/emailassistant-1f35aa9e9c8c.json";

    @Value("${attachment.file.path}")
    private  String attachmentFilePath="/tmp/gmail-credentials/";


    @Value("${app.supported.formats}")
    private List<String> supportedFormats;

    @Autowired
    private UserMailAttachmentRepository userMailAttachmentRepository;

    @Autowired
    private UserContextHolder userContextHolder;

    @Autowired
    private EmailUserDao emailUserDao;

    @Autowired
    private IEmailPreferencesDao preferencesDao;

    @Value("${pollingMode}")
    private String pollingMode;

    @Autowired
    private S3Service s3Service;

    @Value("${org.domain.name}")
    private List<String> orgDomainName;


    @Value("${outsiders.events.lookup.allow}")
    private Boolean allowOutsiders;

    @Autowired
    private IMailSummaryDao mailSummaryDao;




    private final GoogleCredentials credentials;
    private final HttpTransport transport;
    private final JsonFactory jsonFactory;

    private static final String s3BucketName = "emailAttachments";

    GmailIntegrationImpl() {
        GoogleCredentials tempCredentials = null;
        HttpTransport tempTransport = null;
        JsonFactory tempJsonFactory = null;
        try {

            log.info("Inside Gmail credential creation");
            tempCredentials = GoogleCredentials.fromStream(new FileInputStream(SERVICE_ACCOUNT_KEY_FILE_PATH))
                    .createScoped(Arrays.asList(DirectoryScopes.ADMIN_DIRECTORY_USER_READONLY, GmailScopes.GMAIL_SETTINGS_BASIC, GmailScopes.GMAIL_MODIFY, GmailScopes.GMAIL_READONLY, GmailScopes.MAIL_GOOGLE_COM,DirectoryScopes.ADMIN_DIRECTORY_USER_READONLY,CalendarScopes.CALENDAR));
            tempTransport = GoogleNetHttpTransport.newTrustedTransport();
            tempJsonFactory = GsonFactory.getDefaultInstance();
        } catch (IOException | GeneralSecurityException e) {
            log.error("Error during Gmail credential creation: {}", e.getMessage());
        }
        credentials = tempCredentials;
        transport = tempTransport;
        jsonFactory = tempJsonFactory;
    }


    private Gmail getGmailService(String userId) {
        try {
            GoogleCredentials delegatedCredentials = credentials.createDelegated(userId);
            delegatedCredentials.refreshIfExpired();
            AccessToken token = delegatedCredentials.getAccessToken();
            log.info("expiration time is {}", token.getExpirationTime());
            return new Gmail.Builder(transport, jsonFactory, new HttpCredentialsAdapter(delegatedCredentials))
                    .setApplicationName(APPLICATION_NAME)
                    .build();
        } catch (Exception e) {
            log.error("Error while creating Gmail service: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }


    private Calendar createCalendarService(String userId) {
        try {
            GoogleCredentials delegatedCredentials = credentials.createDelegated(userId);
            delegatedCredentials.refreshIfExpired();
            AccessToken token = delegatedCredentials.getAccessToken();
            log.info("Expiration time is {}", token.getExpirationTime());

            return new Calendar.Builder(transport, jsonFactory, new HttpCredentialsAdapter(delegatedCredentials))
                    .setApplicationName(APPLICATION_NAME)
                    .build();
        } catch (IOException e) {
            log.error("IOException occurred while creating Calendar service for user {}: {}", userId, e.getMessage());
            throw new RuntimeException(e);
        }
    }



    /**
     * Retrieves a Gmail message by its ID for a specified user.
     *
     * @param emailId   User's email address.
     * @param messageId ID of the message to retrieve.
     * @return Retrieved message or null if an error occurs.
     */
    @Override
    public Message getMessageById(String emailId,String messageId) {
        try {
            Gmail gmailService=getGmailService(emailId);
            return gmailService.users().messages().get(emailId, messageId).execute();
        } catch (GoogleJsonResponseException e) {
            log.error("Google API Error retrieving message with ID {} for user {}: {}", e.getMessage(),getStackTraceAsString(e),messageId,messageId,GET_MESSAGE,emailId,null,null,false ,null,null,null);
        } catch (IOException e) {
            log.error("IOException Error retrieving message with ID {} for user {}: {}", e.getMessage(),getStackTraceAsString(e),messageId,messageId,GET_MESSAGE,emailId,null,null,false ,null,null,null);
        } catch (Exception e) {
            log.error("Exception Error retrieving message with ID {} for user {}: {}", e.getMessage(),getStackTraceAsString(e),messageId,messageId,GET_MESSAGE,emailId,null,null,false ,null,null,null);
        }
        return null;
    }


    public boolean deleteMessageById(String emailId, String messageId, Boolean softDelete) {
        try {
            Gmail gmailService = getGmailService(emailId);
            if (Boolean.TRUE.equals(softDelete)) {
                gmailService.users().messages().trash(emailId, messageId).execute();
                MailSummary message = mailSummaryDao.findByMessageId(messageId, emailId);
                message.setDeleted(true);
                mailSummaryDao.save(message);
                log.info("Message with ID : {} moved to Trash for user - {}", messageId, emailId);
            } else {
                gmailService.users().messages().delete(emailId, messageId).execute();
                log.info("Message with ID {}  deleted for user {}", messageId, emailId);
            }
            return true;
        } catch (GoogleJsonResponseException e) {
            log.error("Google API Error deleting message with ID {} for user {}: {}", messageId, emailId, e.getMessage());
        } catch (IOException e) {
            log.error("IOException Error deleting message with ID {} for user {}: {}", messageId, emailId, e.getMessage());
        } catch (Exception e) {
            log.error("Exception Error deleting message with ID {} for user {}: {}", messageId, emailId, e.getMessage());
        }
        return false;
    }

    @Override
    public List<UnReadEmail> getUnreadEmails(String email, String folderId) {
        List<UnReadEmail> unReadEmails = new ArrayList<>();
        try {
            List<MessageWrapper> messages = getUnReadMessagesByLabel(email, folderId);
            if (messages == null || messages.isEmpty()) {
                return unReadEmails;
            }
            for (MessageWrapper messageWrapper : messages) {
                try {
                    UnReadEmail unReadEmail = new UnReadEmail();
                    unReadEmail.setIsUnread(messageWrapper.getIsRead());
                    unReadEmail.setSubject(messageWrapper.getSubject());
                    unReadEmail.setInternetMessageId(messageWrapper.getInternetMessageId());
                    unReadEmails.add(unReadEmail);
                } catch (Exception e) {
                    log.error("Error processing messageWrapper: {}", messageWrapper, e);
                }
            }
        } catch (Exception e) {
            log.error("Error retrieving unread emails for {} in folder {}: {}", email, folderId, e.getMessage(), e);
        }
        return unReadEmails;
    }


    private List<MessageWrapper> getMessagesByLabel(String email, String folderId) {

        try {
            List<MessageWrapper> messages = new ArrayList<>();
            log.info("folderId {}",folderId);
            Gmail gmailService=getGmailService(email);
            if (!checkLabelExists(email, folderId)) {
                log.info("Folder does not exist");
                return null;
            }
            var filter = FILTER_LABEL + folderId;
            log.info("Inside pollGmailFolderWise filter: {}", filter);
            var messagesResponse = gmailService.users().messages().list(email).setQ(filter).execute();
            for (var message : messagesResponse.getMessages()) {
                log.info("message is {}",message);
                var fullMessage = gmailService.users().messages().get(email, message.getId()).execute();
                messages.add(parseGmailMessageToMessageWrapper(email,fullMessage));
            }
            return messages;
        } catch (GoogleJsonResponseException e) {
            log.error("IO Exception while creating Gmail service: {}", e.getMessage(), e);
            return null;
        }catch (IOException e) {
            log.error("IO Exception while creating Gmail service: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("Error inside getMessagesByLabel: {}", e.getMessage(), e);
            return null;
        }
    }

    private List<MessageWrapper> getUnReadMessagesByLabel(String email, String folderId) {
        try {
            List<MessageWrapper> messages = new ArrayList<>();
            log.info("folderId {}", folderId);
            Gmail gmailService = getGmailService(email);
            if (!checkLabelExists(email, folderId)) {
                log.info("Folder does not exist");
                return null;
            }

            String filter = FILTER_LABEL + folderId + " is:unread";
            log.info("Inside pollGmailFolderWise filter: {}", filter);
            var messagesResponse = gmailService.users().messages().list(email).setQ(filter).execute();
            for (var message : messagesResponse.getMessages()) {
                log.info("message is {}", message);
                var fullMessage = gmailService.users().messages().get(email, message.getId()).execute();
                messages.add(parseGmailMessageToMessageWrapper(email, fullMessage));
            }
            return messages;
        } catch (GoogleJsonResponseException e) {
            log.error("Google JSON Response Exception while fetching Gmail messages: {}", e.getMessage(), e);
            return null;
        } catch (IOException e) {
            log.error("IO Exception while fetching Gmail messages: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("Error inside getMessagesByLabel: {}", e.getMessage(), e);
            return null;
        }
    }


    @Override
    public VacationSettings getVacationResponderSettings(String emailId) {
        try {
            Gmail gmailService = getGmailService(emailId);
            return gmailService.users().settings().getVacation(emailId).execute();
        } catch (IOException e) {
            log.error("Error retrieving Vacation Responder settings for user {}: {}", emailId, e.getMessage());
        } catch (Exception e) {
            log.error("Exception while retrieving Vacation Responder settings for user {}: {}", emailId, e.getMessage());
        }
        return null;
    }

    @Override
    public Map<String, Object> getAutoReplySettingsForUser(String userEmail) {
        try {
            //userEmail="<EMAIL>";
            Gmail gmailService = getGmailService(userEmail);
            VacationSettings vacationSettings=gmailService.users().settings().getVacation(userEmail).execute();
            return convertDateStringToLong(vacationSettings);
        } catch (IOException e) {
            e.printStackTrace();
            log.error("Error getAutoReplySettingsForUser settings for user {}: {}", userEmail, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Exception while retrieving getAutoReplySettingsForUser settings for user {}: {}", userEmail, e.getMessage());
        }
        return Map.of();
    }


    @Override
    public String setVacationResponder(String emailId,VacationSettings vacationSettings) {
        try {
            Gmail gmailService = getGmailService(emailId);
            gmailService.users().settings().updateVacation(emailId, vacationSettings).execute();
            log.info("Vacation responder updated successfully.");
            return SUCCESS;
        } catch (IOException e) {
            log.error("Error setting Vacation Responder for user {}: {}", emailId, e.getMessage());
        } catch (Exception e) {
            log.error("Exception while setting Vacation Responder for user {}: {}", emailId, e.getMessage());
        }
        return FAILED;
    }

    @Override
    public String cancelAutoReply(String emailId) {
        try {
            Gmail gmailService = getGmailService(emailId);
            VacationSettings vacationSettings=gmailService.users().settings().getVacation(emailId).execute();
            vacationSettings.setEnableAutoReply(false);
            gmailService.users().settings().updateVacation(emailId, vacationSettings).execute();
            log.info("Vacation responder updated successfully.");
            return SUCCESS;
        } catch (IOException e) {
            log.error("Error setting Vacation Responder for user {}: {}", emailId, e.getMessage());
        } catch (Exception e) {
            log.error("Exception while setting Vacation Responder for user {}: {}", emailId, e.getMessage());
        }
        return FAILED;
    }

    @Override
    public String setAutoReplyForUser(String emailId, String internalReplyMessage, String externalReplyMessage, String scheduledStartDateTime, String scheduledEndDateTime, String timeZone) {
        try {
            Gmail gmailService = getGmailService(emailId);
            VacationSettings vacationSettings = new VacationSettings();
            vacationSettings.setEnableAutoReply(true);
            log.info("scheduledStartDateTime {}",scheduledStartDateTime);
            log.info("scheduledEndDateTime {}",scheduledEndDateTime);
            vacationSettings.setStartTime(convertDateStringToLong(scheduledStartDateTime));
            vacationSettings.setEndTime(convertDateStringToLong(scheduledEndDateTime));
            log.info("vacationSettings start date  {}",vacationSettings.getStartTime());
            vacationSettings.setResponseSubject(internalReplyMessage);
            vacationSettings.setResponseBodyPlainText(externalReplyMessage);
            vacationSettings.setRestrictToContacts(false);
            vacationSettings.setRestrictToDomain(false);
            gmailService.users().settings().updateVacation(emailId, vacationSettings).execute();
            return SUCCESS;
        } catch (IOException e) {
            log.error("Error setting setAutoReplyForUser Vacation Responder for user {}: {}", emailId, e.getMessage());
        } catch (Exception e) {
            log.error("Exception while  setAutoReplyForUser setting Vacation Responder for user {}: {}", emailId, e.getMessage());
        }
        return FAILED;
    }

    /**
     * Enable the auto-forwarding for an account.
     *
     * @param forwardingEmail - Email address of the recipient whose email will be forwarded.
     * @return forwarding id and metadata, {@code null} otherwise.
     * @throws IOException - if service account credentials file not found.
     */
    public static AutoForwarding enableAutoForwarding(String forwardingEmail) throws IOException {
        /* Load pre-authorized user credentials from the environment.
           TODO(developer) - See https://developers.google.com/identity for
            guides on implementing OAuth2 for your application. */
        GoogleCredentials credentials = GoogleCredentials.getApplicationDefault().createScoped(GmailScopes.GMAIL_SETTINGS_SHARING);
        HttpRequestInitializer requestInitializer = new HttpCredentialsAdapter(credentials);
        Gmail service = new Gmail.Builder(new NetHttpTransport(), GsonFactory.getDefaultInstance(), requestInitializer).setApplicationName("Gmail samples").build();
        try {
            ForwardingAddress address = new ForwardingAddress().setForwardingEmail(forwardingEmail);
            ForwardingAddress createAddressResult = service.users().settings().forwardingAddresses().create("me", address).execute();
            if (createAddressResult.getVerificationStatus().equals("accepted")) {
                AutoForwarding autoForwarding = new AutoForwarding().setEnabled(true).setEmailAddress(address.getForwardingEmail()).setDisposition("trash");
                autoForwarding = service.users().settings().updateAutoForwarding("me", autoForwarding).execute();
                System.out.println(autoForwarding.toPrettyString());
                return autoForwarding;
            }
        } catch (GoogleJsonResponseException e) {
            // TODO(developer) - handle error appropriately
            GoogleJsonError error = e.getDetails();
            if (error.getCode() == 403) {
                System.err.println("Unable to enable forwarding: " + e.getDetails());
            } else {
                throw e;
            }
        }
        return null;
    }

    @Override
    public ListMessagesResponse getMessages(String emailId) {
        Gmail gmailService=getGmailService(emailId);
        ListMessagesResponse messagesResponse = null;
        try {
            messagesResponse = gmailService.users().messages().list(emailId).execute();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return messagesResponse;
    }

    @Override
    public UserEmailDto getMessageById(String messageId) {
        String email = userContextHolder.getCurrentUser().getEmail();
        Gmail gmailService=getGmailService(email);
        try {
            return parseGmailMessageToUserEmailDto(gmailService.users().messages().get(email, messageId).setFormat("full").execute());
        } catch (GoogleJsonResponseException e) {
            log.error("Google API Error retrieving message with ID {} for user {}: {}", e.getMessage(),getStackTraceAsString(e),messageId,messageId,GET_MESSAGE,email,null,null,false ,null,null,null);
        } catch (IOException e) {
            log.error("IOException Error retrieving message with ID {} for user {}: {}", e.getMessage(),getStackTraceAsString(e),messageId,messageId,GET_MESSAGE,email,null,null,false ,null,null,null);
        } catch (Exception e) {
            log.error("Exception Error retrieving message with ID {} for user {}: {}", e.getMessage(),getStackTraceAsString(e),messageId,messageId,GET_MESSAGE,email,null,null,false ,null,null,null);
        }
        return null;
    }

    @Override
    public UserEmailDto getMessageByIdAndFolderName(String messageId, String folderName) {
        log.info("inside getMessageByIdAndFolderName for {},{}",messageId,folderName);
        String email = userContextHolder.getCurrentUser().getEmail();
        String labelId;
        try {
            labelId = getLabelIdByName(email, folderName);
            if (labelId == null) {
                log.error("Label with name '{}' not found for user '{}'", folderName, email);
                return null;
            }
        } catch (IOException e) {
            throw new RuntimeException("Error fetching label ID", e);
        }

        Gmail gmailService = getGmailService(email);
        try {
            // Retrieve the specific message by its ID
            Message message = gmailService.users().messages().get(email, messageId).setFormat("full").execute();

            // Check if the message contains the specified label ID
            if (message.getLabelIds() != null && message.getLabelIds().contains(labelId)) {
                return parseGmailMessageToUserEmailDto(message);
            } else {
                log.info("Message with ID '{}' does not belong to the label '{}' for user '{}'", messageId, folderName, email);
            }
        } catch (GoogleJsonResponseException e) {
            log.error("Google API Error retrieving message with ID {} for user {}: {}", messageId, email, e.getMessage(), e);
        } catch (IOException e) {
            log.error("IOException retrieving message with ID {} for user {}: {}", messageId, email, e.getMessage(), e);
        } catch (Exception e) {
            log.error("Unexpected error retrieving message with ID {} for user {}: {}", messageId, email, e.getMessage(), e);
        }
        return null;
    }




    @Override
    public UserEmailDto getMessageByInternetMessageId(String internetMessageId) {
        String email = userContextHolder.getCurrentUser().getEmail();
        Gmail gmailService = getGmailService(email);
        try {
            ListMessagesResponse messagesResponse = gmailService.users().messages().list(email)
                    .setQ("rfc822msgid:" + internetMessageId)
                    .execute();
            List<Message> messages = messagesResponse.getMessages();
            if (messages != null && !messages.isEmpty()) {
                String messageId = messages.get(0).getId();
                return parseGmailMessageToUserEmailDto(gmailService.users().messages().get(email, messageId).setFormat("full").execute());
            } else {
                log.warn("No message found with internetMessageId {}", internetMessageId);
            }
        } catch (GoogleJsonResponseException e) {
            log.error("Google API Error retrieving message with internetMessageId {} for user {}: {}", internetMessageId, email, getStackTraceAsString(e));
        } catch (IOException e) {
            log.error("IOException Error retrieving message with internetMessageId {} for user {}: {}", internetMessageId, email, getStackTraceAsString(e));
        } catch (Exception e) {
            log.error("Exception Error retrieving message with internetMessageId {} for user {}: {}", internetMessageId, email, getStackTraceAsString(e));
        }
        return null;
    }


    @Override
    public UserEmailDto getMessageByInternetMessageId(String internetMessageId, String folderName) {
        String email = userContextHolder.getCurrentUser().getEmail();
        Gmail gmailService = getGmailService(email);

        try {
            // Get the label ID for the specified folder name
            String labelId = getLabelIdByName(email, folderName);

            // Construct the query string with both the rfc822msgid and the label
            String query = "rfc822msgid:" + internetMessageId + " label:" + labelId;
            ListMessagesResponse messagesResponse = gmailService.users().messages().list(email)
                    .setQ(query)
                    .execute();

            List<Message> messages = messagesResponse.getMessages();

            if (messages != null && !messages.isEmpty()) {
                String messageId = messages.get(0).getId();
                return parseGmailMessageToUserEmailDto(gmailService.users().messages().get(email, messageId).setFormat("full").execute());
            } else {
                log.warn("No message found with internetMessageId {} in folder {}", internetMessageId, folderName);
            }
        } catch (GoogleJsonResponseException e) {
            log.error("Google API Error retrieving message with internetMessageId {} for user {}: {}", internetMessageId, email, getStackTraceAsString(e));
        } catch (IOException e) {
            log.error("IOException Error retrieving message with internetMessageId {} for user {}: {}", internetMessageId, email, getStackTraceAsString(e));
        } catch (Exception e) {
            log.error("Exception Error retrieving message with internetMessageId {} for user {}: {}", internetMessageId, email, getStackTraceAsString(e));
        }
        return null;
    }


    @Override
    public MessageWrapper getMessageWrapperById(String messageId) {
        log.info("inside getMessageWrapperById");
        String email = userContextHolder.getCurrentUser().getEmail();
        Gmail gmailService=getGmailService(email);
        try {
            Message message =  gmailService.users().messages().get(email, messageId).setFormat("full").execute();
            return GmailUtils.parseGmailMessageToMessageWrapper(email,message);
        } catch (GoogleJsonResponseException e) {
            e.printStackTrace();
            log.error("Google API Error retrieving message with ID {} for user {}: {}", e.getMessage(),getStackTraceAsString(e),messageId,messageId,GET_MESSAGE,email,null,null,false ,null,null,null);
        } catch (IOException e) {
            e.printStackTrace();
            log.error("IOException Error retrieving message with ID {} for user {}: {}", e.getMessage(),getStackTraceAsString(e),messageId,messageId,GET_MESSAGE,email,null,null,false ,null,null,null);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Exception Error retrieving message with ID {} for user {}: {}", e.getMessage(),getStackTraceAsString(e),messageId,messageId,GET_MESSAGE,email,null,null,false ,null,null,null);
        }
        return new MessageWrapper();
    }



    /**
     * Polls the Gmail inbox for all users and processes messages, checking for attachments and calendar events.
     *
     * @return A UserEmailResponseDto object containing email data or null if an error occurs.
     */
    @Override
    public UserEmailResponseDto pollGmailInbox() {
        try {
            String userId=userContextHolder.getCurrentUser().getEmail();
            GoogleCredential credential = GoogleCredential.fromStream(new FileInputStream(SERVICE_ACCOUNT_KEY_FILE_PATH)).createScoped(Collections.singleton(DirectoryScopes.ADMIN_DIRECTORY_USER_READONLY)).createDelegated(userId);
            Directory directoryService = new Directory.Builder(credential.getTransport(), credential.getJsonFactory(), credential).setApplicationName(APPLICATION_NAME).build();
            Users users = directoryService.users().list().setCustomer(MY_CUSTOMER).execute();
            for (User user : users.getUsers()) {
                String userEmail = user.getPrimaryEmail();
                Gmail gmailService=getGmailService(userEmail);
                ListMessagesResponse messagesResponse = gmailService.users().messages().list(userEmail).execute();
                for (Message message : messagesResponse.getMessages()) {
                    Message fullMessage = gmailService.users().messages().get(userEmail, message.getId()).execute();
                    log.info("fullMessage getLabelIds is {}",fullMessage.getLabelIds());
                    checkForAttachments(fullMessage);
                    checkForCalendarEvents(fullMessage);
                }
            }
        }
        catch (Exception e){
            e.printStackTrace();
            log.error("Error inside method pollGmailInbox is {}",e.getMessage());
        }
        return null;
    }


    /**
     * Polls the Gmail inbox for a specified user and processes messages, checking for attachments and calendar events.
     *
     * @param userEmail User's email address.
     * @return A UserEmailResponseDto object containing email data or null if an error occurs.
     */
    @Override
    public UserEmailResponseDto pollGmailInbox(String userEmail) {
        log.info("inside pollOutlookInbox");
        try {
            Gmail gmailService=getGmailService(userEmail);
            ListMessagesResponse messagesResponse = gmailService.users().messages().list(userEmail).execute();
            List<UserEmailDto> userEmailDtos = new ArrayList<>();
            for (Message message : messagesResponse.getMessages()) {
                Message fullMessage = gmailService.users().messages().get(userEmail, message.getId()).execute();
                log.info("fullMessage is {}",fullMessage);
                userEmailDtos.add(parseGmailMessageToUserEmailDto(fullMessage));
                checkForAttachments(fullMessage);
                checkForCalendarEvents(fullMessage);
            }
            return parseToUserEmailResponseDto(userEmail,userEmail,userEmailDtos);
        }
        catch (Exception e){
            e.printStackTrace();
            log.error("Error inside pollGmailInbox is {}",e.getMessage());
        }
        return null;
    }


    /**
     * Polls the Gmail inbox for a specified user and processes messages received after a specific date and time.
     *
     * @param userEmail User's email address.
     * @param time      Date and time filter.
     * @return A UserEmailResponseDto object containing email data or null if an error occurs.
     */
    @Override
    public UserEmailResponseDto pollGmailInbox(String userEmail,String time) {
        log.info("inside pollOutlookInbox");
        try {
            String filter = FILTER_AFTER+ time;
            Gmail gmailService=getGmailService(userEmail);
            ListMessagesResponse messagesResponse = gmailService.users().messages().list(userEmail).setQ(filter).execute();
            List<UserEmailDto> userEmailDtos = new ArrayList<>();
            for (Message message : messagesResponse.getMessages()) {
                Message fullMessage = gmailService.users().messages().get(userEmail, message.getId()).execute();
                userEmailDtos.add(parseGmailMessageToUserEmailDto(fullMessage));
                checkForAttachments(fullMessage);
                checkForCalendarEvents(fullMessage);
            }
            return parseToUserEmailResponseDto(userEmail,userEmail,userEmailDtos);
        }
        catch (Exception e){
            e.printStackTrace();
            log.error("Error inside pollGmailInbox is {}",e.getMessage());
        }
        return null;
    }

    public static String getDateOnly(String dateTime) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateTime);
        LocalDate date = localDateTime.toLocalDate();
        return date.toString();
    }


    /**
     * Polls the Gmail inbox for a specified user and processes messages received after a specific date and time.
     *
     * @param userEmail User's email address.
     * @param time      Date and time filter.
     * @param folderId  Folder id filter.
     * @return A UserEmailResponseDto object containing email data or null if an error occurs.
     */
    @Override
    public UserEmailResponseDto pollGmailFolderWise(String userEmail, String time, String folderId) {
        try {
            log.info("folderId {}",folderId);
            Gmail gmailService=getGmailService(userEmail);
            if (!checkLabelExists(userEmail, folderId)) {
                log.info("Folder does not exist");
                return null;
            }
            var startDate = Objects.requireNonNull(extractDate(time));
            var startTime = Objects.requireNonNull(extractTime(time));
            var startDateTime = LocalDateTime.of(startDate, LocalTime.parse(startTime));
            var filter = FILTER_AFTER + startDate + SPACE + FILTER_LABEL + folderId;
            log.info("Inside pollGmailFolderWise filter: {}", filter);
            var messagesResponse = gmailService.users().messages().list(userEmail).setQ(filter).execute();
            var userEmailDtos = processMessages(userEmail, messagesResponse, startDateTime);
            return parseToUserEmailResponseDto(userEmail, userEmail, userEmailDtos);
        } catch (GoogleJsonResponseException e) {
            log.error("IO Exception while creating Gmail service: {}", e.getMessage(), e);
            return null;
        }catch (IOException e) {
            log.error("IO Exception while creating Gmail service: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("Error inside pollGmailFolderWise: {}", e.getMessage(), e);
            return null;
        }
    }

    private List<UserEmailDto> processMessages(String userEmail, ListMessagesResponse messagesResponse, LocalDateTime startDateTime) throws IOException {
        Gmail gmailService=getGmailService(userEmail);
        var userEmailDtos = new ArrayList<UserEmailDto>();
        if (messagesResponse == null || messagesResponse.getMessages() == null)  return userEmailDtos;
        for (var message : messagesResponse.getMessages()) {
            log.info("message is {}",message);
            var fullMessage = gmailService.users().messages().get(userEmail, message.getId()).execute();
            var messageDateTime = parseMessageDate(fullMessage);
            if (messageDateTime.isAfter(startDateTime.minusNanos(1)) || messageDateTime.toLocalDate().isAfter(startDateTime.toLocalDate())) {
                userEmailDtos.add(parseGmailMessageToUserEmailDto(fullMessage));
                checkForAttachments(fullMessage);
                checkForCalendarEvents(fullMessage);
            }
        }
        return userEmailDtos;
    }

    /*
        private LocalDateTime parseMessageDate(Message message) {
            String dateHeader = message.getPayload().getHeaders().stream()
                    .filter(header -> "Date".equals(header.getName()))
                    .map(header -> header.getValue())
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("No Date header found"));
            DateTimeFormatter formatter = DateTimeFormatter.RFC_1123_DATE_TIME;
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateHeader, formatter);
            return zonedDateTime.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
        }
     */
    private LocalDateTime parseMessageDate(Message message) {
        String dateHeader = message.getPayload().getHeaders().stream()
                .filter(header -> "Date".equals(header.getName()))
                .map(header -> header.getValue())
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No Date header found"));
        String normalizedDate = dateHeader.replaceAll("\\(.*\\)", "").trim();
        DateTimeFormatter formatter = DateTimeFormatter.RFC_1123_DATE_TIME;
        try {
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(normalizedDate, formatter);
            return zonedDateTime.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
        } catch (DateTimeParseException e) {
            e.printStackTrace();
            log.error("DateTimeParseException is {}",e);
            throw new RuntimeException("Failed to parse date: " + dateHeader, e);
        }
    }


    /**
     * Summarizes the attachments in an email message by retrieving the message and processing the attachments.
     *
     * @param conversationId Conversation ID.
     * @param emailId        User's email address.
     * @param messageId      Message ID.
     * @return A list of UserMailAttachment objects representing the attachments.
     */
    @Override
    public List<UserMailAttachment> summarizeMailAttachment(String conversationId, String emailId, String messageId) {
        Message message=getMessageById(emailId,messageId);
        log.info("message is {}",message.toString());
        try {
            return getAttachmentByMessage(emailId,message,conversationId);
        } catch (IOException e) {
            log.error("Error inside summarizeMailAttachment is {}",e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * Retrieves the attachments in an email message by its ID and returns them as a list of InputStreamResource objects.
     *
     * @param emailId        User's email address.
     * @param messageId      Message ID.
     * @param conversationId Conversation ID.
     * @return A list of InputStreamResource objects representing the attachments.
     * @throws IOException if an I/O error occurs.
     */
    @Override
    public List<InputStreamResource> getAttachmentByMessageId(String emailId, String messageId,String conversationId) throws IOException {
        Message message = getMessageById(emailId,messageId);
        UserEmailDto userEmailDto= parseGmailMessageToUserEmailDto(message);
        List<InputStreamResource> attachments = new ArrayList<>();
        EmailUser emailUser=emailUserDao.findByEmail(emailId);
        for (MessagePart part : message.getPayload().getParts()) {
            if (isAttachment(part)) {
                String attachmentId = part.getBody().getAttachmentId();
                String filename = part.getFilename();
                String contentType = part.getMimeType();
                log.info("attachmentId is {}",attachmentId);
                String attachmentName = userEmailDto.getInternetMessageId() + DASH + filename;
                if (isAttachmentAlreadyProcessed(attachmentName, emailId)) {
                    log.debug("Attachment already processed: {}", attachmentName);
                    continue;
                }
                byte[] fileBytes = fetchAttachmentData(emailId, message.getId(), attachmentId);
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
                Path tempFile = createTempFile(fileBytes, filename,timestamp);
                try {
                    String s3Key = uploadToS3(tempFile, emailId, timestamp, filename);
                    log.info("s3Key is {}",s3Key);
                    userMailAttachmentRepository.save(createUserMailAttachment(emailUser,emailId, userEmailDto, filename, contentType, attachmentId, conversationId,s3Key,attachmentName));
                    attachments.add(new InputStreamResource(new FileInputStream(tempFile.toFile())));
                } catch (IOException e) {
                    log.error("An error occurred while processing the file: {}", filename, e);
                } finally {
                    deleteTempFile(tempFile);
                }
            }
        }
        return attachments;
    }

    /**
     * Retrieves an attachment from a Gmail message by its ID and returns it as an InputStreamResource.
     *
     * @param emailId      User's email address.
     * @param messageId    Message ID.
     * @param attachmentId Attachment ID.
     * @return An InputStreamResource containing the attachment data.
     * @throws IOException if an I/O error occurs during the retrieval.
     */
    @Override
    public InputStreamResource getAttachmentById(String emailId,String messageId,String attachmentId) throws IOException {
        return null;
    }

    private boolean isAttachmentAlreadyProcessed(String attachmentName, String userId) {
        String status = userMailAttachmentRepository.processingStatusForDocument(attachmentName);
        return status != null && (status.equalsIgnoreCase("NEW") || status.equalsIgnoreCase("IN_PROGRESS") ||
                status.equalsIgnoreCase("COMPLETED") || status.equalsIgnoreCase("ERROR"));
    }

    /**
     * Retrieves and processes the attachments from a Gmail message. The attachments are saved and associated with the given conversation.
     *
     * @param emailId        User's email address.
     * @param message        Gmail message containing the attachments.
     * @param conversationId Conversation ID to associate with the attachments.
     * @return A list of UserMailAttachment objects representing the processed attachments.
     * @throws IOException if an I/O error occurs during the processing of attachments.
     */
    private List<UserMailAttachment> getAttachmentByMessage(String emailId, Message message, String conversationId) throws IOException {
        List<UserMailAttachment> attachments = new ArrayList<>();
        UserEmailDto userEmailDto= parseGmailMessageToUserEmailDto(message);
        EmailUser emailUser=emailUserDao.findByEmail(emailId);
        for (MessagePart part : message.getPayload().getParts()) {
            if (isAttachment(part) && isValidAttachment(part)) {
                String attachmentId = part.getBody().getAttachmentId();
                String filename = part.getFilename();
                String contentType = part.getMimeType();
                log.info("attachmentId is {}",attachmentId);
                String attachmentName = userEmailDto.getInternetMessageId() + DASH + filename;
                if (isAttachmentAlreadyProcessed(attachmentName, emailId)) {
                    log.debug("Attachment already processed: {}", attachmentName);
                    continue;
                }
                byte[] fileBytes = fetchAttachmentData(emailId, message.getId(), attachmentId);
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
                Path tempFile = createTempFile(fileBytes, filename,timestamp);
                log.info("tempFile is {}",tempFile);
                try {
                    String s3Key = uploadToS3(tempFile, emailId, timestamp, filename);
                    log.info("s3Key is {}",s3Key);
                    attachments.add(userMailAttachmentRepository.save(createUserMailAttachment(emailUser,emailId, userEmailDto, filename, contentType, attachmentId, conversationId,s3Key,attachmentName)));
                } catch (Exception e) {
                    log.error("An error occurred while processing the file: {}", filename, e);
                } finally {
                    deleteTempFile(tempFile);
                }
            }
        }
        return attachments;
    }

    /**
     * Determines if a MessagePart contains an attachment based on the presence of a filename.
     *
     * @param part MessagePart to check.
     * @return True if the part contains an attachment, false otherwise.
     */
    private boolean isAttachment(MessagePart part) {
        return part.getFilename() != null && !part.getFilename().isEmpty();
    }

    private boolean isValidAttachment(MessagePart part) {
        String format = getFileFormat(part.getFilename());
        return supportedFormats.contains(format);
    }

    private String getFileFormat(String name) {
        int indexOfDot = name.lastIndexOf(".");
        return (indexOfDot != -1) ? name.substring(indexOfDot + 1) : "";
    }

    public EventDto fetchAttachmentBtId(String emailId, String messageId, String attachmentId)  {
        EventDto event=new EventDto();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            StreamReadConstraints constraints = StreamReadConstraints.builder().maxStringLength(1500000).build();
            objectMapper.getFactory().setStreamReadConstraints(constraints);
            Gmail gmailService=getGmailService(emailId);
            MessagePartBody attachment = gmailService.users().messages().attachments().get(emailId, messageId, attachmentId).execute();
            byte[] decodedBytes = Base64.getUrlDecoder().decode(attachment.getData());
            String icsContent = new String(decodedBytes, StandardCharsets.UTF_8);
            String eventId = ICSParser.getEventIdFromICS(icsContent);
            if(eventId!=null && !eventId.isEmpty()){
                Event gmailEvent = getEventById(emailId,eventId);
                if(gmailEvent != null) {
                    return parseToEventDto(gmailEvent,emailId);
                }
            }else {
                return ICSParser.byObject(event,icsContent,emailId);
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("Failed to fetch attachment with ID: {}", attachmentId, e);
        }
        return event;
    }


    /**
     * Fetches the attachment data from a Gmail message by its ID and returns it as a byte array.
     *
     * @param emailId      User's email address.
     * @param messageId    Message ID.
     * @param attachmentId Attachment ID.
     * @return A byte array containing the attachment data.
     * @throws IOException if an I/O error occurs during the retrieval.
     */
    private byte[] fetchAttachmentData(String emailId, String messageId, String attachmentId) throws IOException {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            StreamReadConstraints constraints = StreamReadConstraints.builder().maxStringLength(1500000).build();
            objectMapper.getFactory().setStreamReadConstraints(constraints);
            Gmail gmailService=getGmailService(emailId);
            log.info("For getting attachment");
            MessagePartBody attachment = gmailService.users().messages().attachments().get(emailId, messageId, attachmentId).execute();
            log.info("Complete getting attachment");
            return Base64.getUrlDecoder().decode(attachment.getData());
        } catch (IOException e) {
            log.error("Failed to fetch attachment with ID: {}", attachmentId, e);
            throw e;
        }
    }

    /**
     * Creates a temporary file to store the attachment data and writes the data to the file.
     *
     * @param fileBytes Byte array containing the attachment data.
     * @param filename  Name of the file to create.
     * @return A Path object representing the location of the created temporary file.
     * @throws IOException if an I/O error occurs during file creation or writing.
     */
    private Path createTempFile(byte[] fileBytes, String filename,String timestamp) throws IOException {
        Path tempFile = Files.createTempFile(Paths.get(attachmentFilePath), ATTACHMENT, DASH +timestamp+DASH+ filename);
        try (FileOutputStream fos = new FileOutputStream(tempFile.toFile())) {
            fos.write(fileBytes);
        }
        return tempFile;
    }

    /**
     * Creates a UserMailAttachment object representing an attachment in a Gmail message.
     *
     * @param emailId        User's email address.
     * @param userEmailDto        Gmail message containing the attachment.
     * @param filename       Name of the attachment file.
     * @param contentType    MIME type of the attachment.
     * @param attachmentId   ID of the attachment.
     * @param conversationId Conversation ID to associate with the attachment.
     * @return A UserMailAttachment object populated with the attachment details.
     */
    private UserMailAttachment createUserMailAttachment(EmailUser emailUser,String emailId, UserEmailDto userEmailDto, String filename, String contentType, String attachmentId, String conversationId,String s3Key,String attachmentName) {
        UserMailAttachment userMailAttachment = new UserMailAttachment();
        userMailAttachment.setUserId(emailId);
        userMailAttachment.setMessageId(userEmailDto.getId());
        userMailAttachment.setName(filename);
        userMailAttachment.setUniqueName(attachmentName);
        userMailAttachment.setType(contentType);
        userMailAttachment.setAttachmentId(attachmentId);
        userMailAttachment.setConversationId(conversationId);
        userMailAttachment.setShortSummary(null);
        userMailAttachment.setLongSummary(null);
        userMailAttachment.setRagDocumentId(null);
        userMailAttachment.setCreationTime(new Date());
        userMailAttachment.setModifiedTime(new Date());
        userMailAttachment.setSubject(userEmailDto.getSubject());
        userMailAttachment.setInternetMessageId(userEmailDto.getInternetMessageId());
        userMailAttachment.setDocPath(s3Key);
        userMailAttachment.setBatchId(emailUser.getBatchId());
        userMailAttachment.setProcessingStatus("NEW");
        return userMailAttachment;
    }

    /**
     * Updates a UserMailAttachment object with details from the upload response.
     *
     * @param userMailAttachment UserMailAttachment object to update.
     * @param uploadResponse     UploadResponse object containing the details to update.
     */
    private void updateUserMailAttachment(UserMailAttachment userMailAttachment, UploadResponse uploadResponse) {
        if (uploadResponse==null) return;
        if(uploadResponse.getLong_summary()!=null) userMailAttachment.setLongSummary(uploadResponse.getLong_summary());
        if(uploadResponse.getShort_summary()!=null) userMailAttachment.setShortSummary(uploadResponse.getShort_summary());
        userMailAttachment.setRagDocumentId(uploadResponse.getId());
    }
    /**
     * Flags an email with a custom label. If the label does not exist, it will be created.
     *
     * @param userId    The email address of the user whose email needs to be flagged.
     * @param messageId The ID of the message to which the label will be applied.
     * @param labelName The name of the label to apply to the email.
     */
    @Override
    public Boolean flagEmailWithCustomLabel(String userId, String messageId, String labelName) {
        try {
            Gmail gmailService=getGmailService(userId);
            String labelId = ensureLabelExists(userId, labelName);
            ModifyMessageRequest mods = new ModifyMessageRequest().setAddLabelIds(Collections.singletonList(labelId));
            gmailService.users().messages().modify(userId, messageId, mods).execute();
            log.info("Email flagged with label {}" ,labelName , " successfully.");
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error occurred while flagging the email: ",e.getMessage());
            return false;
        }
    }

    /**
     * Ensures that a label with the specified name exists. If it does not, a new label will be created.
     *
     * @param userId   The email address of the user for whom the label is to be checked/created.
     * @param labelName The name of the label to check or create.
     * @return The ID of the label. If a new label was created, this will be the ID of the newly created label.
     * @throws IOException If there is an error communicating with the Gmail API.
     */
    private String ensureLabelExists(String userId, String labelName) throws IOException {
        Gmail gmailService=getGmailService(userId);
        ListLabelsResponse labelsResponse = gmailService.users().labels().list(userId).execute();
        for (Label label : labelsResponse.getLabels()) {
            if (label.getName().equalsIgnoreCase(labelName)) {
                return label.getId();
            }
        }
        Label newLabel = new Label().setName(labelName).setLabelListVisibility(LABEL_SHOW).setMessageListVisibility(SHOW);
        Label createdLabel = gmailService.users().labels().create(userId, newLabel).execute();
        return createdLabel.getId();
    }
    /**
     * Ensures that a label with the specified name exists. If it does not, a new label will be created.
     *
     * @param userId   The email address of the user for whom the label is to be checked/created.
     * @param labelName The name of the label to check or create.
     * @return The ID of the label. If a new label was created, this will be the ID of the newly created label.
     * @throws IOException If there is an error communicating with the Gmail API.
     */
    public Boolean checkLabelExists(String userId, String labelName) throws IOException {
        try{
            Gmail gmailService=getGmailService(userId);
            ListLabelsResponse labelsResponse = gmailService.users().labels().list(userId).execute();
            // System.out.println("my label is  {}"+labelName);
            for (Label label : labelsResponse.getLabels()) {
                // System.out.println("label name is {}"+label.getName());
                if (label.getName().equalsIgnoreCase(labelName)) {
                    return true;
                }
            }
            return false;
        }catch (GoogleJsonResponseException e) {
            log.error("IO Exception while checkLabelExists Gmail service: {}", e.getMessage(), e);
            return false;
        }
        catch (Exception e) {
            log.error("IO Exception while checkLabelExists Gmail service: {}", e.getMessage(), e);
            return false;
        }
    }
    /**
     * Checks if a Gmail message is starred based on its message ID.
     *
     * @param userId    The Gmail user ID (use "me" to indicate the authenticated user).
     * @param messageId The unique ID of the Gmail message.
     * @return {@code true} if the message is starred, {@code false} otherwise.
     */
    @Override
    public  String getFlagStatus(String userId, String messageId) {
        try {
            log.info("getFlagStatus userId is {} and messageId is {}",userId,messageId);
            Gmail gmailService=getGmailService(userId);
            Message message = gmailService.users().messages().get(userId, messageId).setFormat(METEDATA).execute();
            List<String> labelIds = message.getLabelIds();
            if (labelIds != null && labelIds.contains(STARRED)) return FLAGGED;
            else return NOT_FLAGGED;
        }catch (GoogleJsonResponseException e) {
            if (e.getStatusCode() == 400 && "failedPrecondition".equals(e.getDetails().getErrors().get(0).getReason())) {
                log.error("Mail service not enabled for user {}: {}", userId, e.getDetails().getMessage());
                e.printStackTrace();
            } else {
                log.error("Error occurred while checking if the email is starred: {}", e.getMessage());
                e.printStackTrace();
            }
            return NOT_FLAGGED;
        } catch (Exception e) {
            log.error("Error occurred while checking if the email is starred: {}", e.getMessage());
            e.printStackTrace();
            return NOT_FLAGGED;
        }
    }
    /**
     * Stars an email by adding the "STARRED" label.
     *
     * @param userId    The email address of the user whose email needs to be starred.
     * @param messageId The ID of the message to star.
     */
    @Override
    public Boolean starEmail(String userId, String messageId) {
        try {
            Gmail gmailService=getGmailService(userId);

            ModifyMessageRequest mods = new ModifyMessageRequest().setAddLabelIds(Collections.singletonList(STARRED));
            gmailService.users().messages().modify(userId, messageId, mods).execute();
            log.info("Email starred successfully.");
            MailSummary message = mailSummaryDao.findByMessageId(messageId, userId);
            message.setFlagStatus("Flagged");
            mailSummaryDao.save(message);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error occurred while starring the email: ",e.getMessage());
            return false;
        }
    }

    /**
     * Stars an email by adding the "STARRED" label.
     *
     * @param userId    The email address of the user whose email needs to be starred.
     * @param databaseId The database id (primary key) used to determine messageId of email.
     */
    @Override
    public Boolean starEmail(String userId, Integer databaseId) {
        try {
            Gmail gmailService=getGmailService(userId);
            MailSummary message = mailSummaryDao.findById(databaseId)
                    .orElseThrow(() -> new ResourceNotFoundException("No database record found for the given id."));

            ModifyMessageRequest mods = new ModifyMessageRequest().setAddLabelIds(Collections.singletonList(STARRED));
            gmailService.users().messages().modify(userId, message.getMessageId(), mods).execute();
            log.info("Email starred successfully.");
            message.setFlagStatus("Flagged");
            mailSummaryDao.save(message);
            return true;
        } catch (Exception e) {
            log.error("Error occurred while starring the email: ",e.getMessage());
            return false;
        }
    }

    /**
     * Stars an email by adding the "STARRED" label.
     *
     * @param userId    The email address of the user whose email needs to be starred.
     * @param messageId The ID of the message to star.
     */
    @Override
    public Boolean starAndUnstarEmail(String userId, String messageId,String mode) {

        return mode.equalsIgnoreCase(FLAGGED)?starEmail(userId,messageId):unstarEmail(userId,messageId);
    }

    /**
     * Stars an email by adding the "STARRED" label.
     *
     * @param userId    The email address of the user whose email needs to be starred.
     * @param databaseId The database id (primary key) used to determine messageId of email.
     */
    @Override
    public Boolean starAndUnstarEmail(String userId, Integer databaseId,String mode) {

        return mode.equalsIgnoreCase(FLAGGED)?starEmail(userId,databaseId):unstarEmail(userId,databaseId);
    }

    /**
     * Removes the 'STARRED' label from an email message.
     *
     * <p>This method uses the Gmail API to modify the labels of a specified email message.
     * It attempts to remove the 'STARRED' label from the message identified by the provided
     * user ID and message ID.</p>
     *
     * @param userId The email address of the user whose message label is to be modified.
     *               This should be in the format of a full email address (e.g., "<EMAIL>").
     * @param messageId The unique ID of the email message from which the label is to be removed.
     *                  This ID is typically obtained from the Gmail API when listing or retrieving messages.
     * @return {@code true} if the label was successfully removed from the email;
     *         {@code false} if there was an error during the process.
     * @throws IOException If there is an issue communicating with the Gmail API or processing the request.
     */
    @Override
    public Boolean unstarEmail(String userId, String messageId) {
        try {
            Gmail gmailService=getGmailService(userId);
            ModifyMessageRequest mods = new ModifyMessageRequest().setRemoveLabelIds(Collections.singletonList(STARRED));
            gmailService.users().messages().modify(userId, messageId, mods).execute();
            log.info("Email unstarred successfully.");
            MailSummary message = mailSummaryDao.findByMessageId(messageId, userId);
            message.setFlagStatus("notFlagged");
            mailSummaryDao.save(message);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error occurred while unstarring the email: ", e.getMessage());
            return false;
        }
    }

    /**
     * Removes the 'STARRED' label from an email message.
     *
     * <p>This method uses the Gmail API to modify the labels of a specified email message.
     * It attempts to remove the 'STARRED' label from the message identified by the provided
     * user ID and message ID.</p>
     *
     * @param userId The email address of the user whose message label is to be modified.
     *               This should be in the format of a full email address (e.g., "<EMAIL>").
     * @param databaseId The database id (primary key) used to determine messageId of email.
     * @return {@code true} if the label was successfully removed from the email;
     *         {@code false} if there was an error during the process.
     * @throws IOException If there is an issue communicating with the Gmail API or processing the request.
     */
    @Override
    public Boolean unstarEmail(String userId, Integer databaseId) {
        try {
            Gmail gmailService=getGmailService(userId);
            MailSummary message = mailSummaryDao.findById(databaseId)
                    .orElseThrow(() -> new ResourceNotFoundException("No database record found for the given id."));

            ModifyMessageRequest mods = new ModifyMessageRequest().setRemoveLabelIds(Collections.singletonList(STARRED));
            gmailService.users().messages().modify(userId, message.getMessageId(), mods).execute();
            log.info("Email unstarred successfully.");
            message.setFlagStatus("notFlagged");
            mailSummaryDao.save(message);
            return true;
        } catch (Exception e) {
            log.error("Error occurred while unstarring the email: ", e.getMessage());
            return false;
        }
    }

    /**
     * Pulls emails from a specific folder (label) in Gmail.
     *
     * @param userId    The email address of the user whose emails need to be pulled.
     * @param folderName The name of the label (folder) from which to pull emails.
     * @return A list of messages from the specified folder.
     * @throws IOException If an error occurs while retrieving the emails.
     */
    @Override
    public List<Message> pullEmailsFromFolder(String userId, String folderName) throws IOException {
        Gmail gmailService=getGmailService(userId);
        String labelId = getLabelIdByName(userId, folderName);
        String query = "label:" + labelId;
        log.info("query is {}",query);
        ListMessagesResponse messagesResponse = gmailService.users().messages().list(userId).setQ(query).execute();
        List<Message> messages = messagesResponse.getMessages();
        if (messages != null && !messages.isEmpty()) {
            for (Message message : messages) {
                Message fullMessage = gmailService.users().messages().get(userId, message.getId()).execute();
                // printAllFields(fullMessage);
                // log.info("Found message with ID: ",fullMessage.getId());
            }
        } else {
            log.info("No messages found in folder '" + folderName + "'.");
        }
        return messages;
    }

    /**
     * Retrieves the label ID for a given label name.
     *
     * @param userId    The email address of the user.
     * @param labelName The name of the label.
     * @return The label ID.
     * @throws IOException If an error occurs while retrieving the label.
     */
    private String getLabelIdByName(String userId, String labelName) throws IOException {
        if(labelName == null || labelName.trim().isEmpty()) return null;
        Gmail gmailService=getGmailService(userId);
        ListLabelsResponse labelsResponse = gmailService.users().labels().list(userId).execute();
        for (Label label : labelsResponse.getLabels()) {
            if (label.getName().equalsIgnoreCase(labelName)) {
                return label.getId();
            }
        }
        throw new IOException("Label with name '" + labelName + "' not found.");
    }

    /**
     * Retrieves all label.
     *
     * @param userId    The email address of the user.
     * @return list of Labels.
     * @throws IOException If an error occurs while retrieving the label.
     */
    public List<Label> getLabels(String userId) {
        Gmail gmailService=getGmailService(userId);
        ListLabelsResponse labelsResponse = null;
        try {
            labelsResponse = gmailService.users().labels().list(userId).execute();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        for (Label label : labelsResponse.getLabels()) {
            log.info("label name is {}",label.getName());
        }
        return labelsResponse.getLabels();
    }

    @Override
    public List<Map<String, String>> usersMailFolders(String userId) {
        List<Label> labelList = getLabels(userId);
        return labelList.stream()
                .filter(label -> !excludedLabels.contains(label.getName()))  // Exclude certain labels
                .flatMap(label -> {
                    if (containsSlash(label.getName()))return splitStringIfContainsSlash(label.getName()).stream().map(sublabel -> createFolderMap(sublabel, sublabel));
                    else return Stream.of(createFolderMap(label.getId(), label.getName()));
                })
                .collect(Collectors.toList());
    }


    private Map<String, String> createFolderMap(String id, String displayName) {
        Map<String, String> folderMap = new HashMap<>();
        folderMap.put(EmailConstants.ID, id);
        folderMap.put(EmailConstants.DISPLAY_NAME, displayName);
        return folderMap;
    }
    /**
     * Retrieves a list of calendar events for a given email within a specified time range.
     * If the startDateTime or endDateTime is null, the method will use the default
     * UTC date time based on user preferences.
     *
     * @param email         the email of the user whose calendar events are to be retrieved
     * @param startDateTime the start time for the event search in ISO format, can be null
     * @param endDateTime   the end time for the event search in ISO format, can be null
     * @return a list of {@link EventDto} objects representing the user's calendar events
     * @throws URISyntaxException if there is an error in forming the URI for calendar events
     */
    public List<EventDto> getCalendarEvents(String email, String startDateTime, String endDateTime) {
        log.info("Inside @method getCalendarEvents. @param: email -> {} startDateTime -> {}, endDateTime -> {}", email, startDateTime, endDateTime);
        if (!EmailUtils.checkDomain(email, orgDomainName)) return new ArrayList<>();

        if (!allowOutsiders) {
            boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
            if (!userExists) return new ArrayList<>();
        }

        if (startDateTime == null || endDateTime == null) {
            try {
                EmailPreferences preferences =preferencesDao.getEmailPreferencesByUserId(email);
                startDateTime = DateUtils.getUTCDateTime(false, preferences.getTimeZone());
                endDateTime = DateUtils.getUTCDateTime(true, preferences.getTimeZone());
            } catch (Exception e) {
                log.error("inside set startDateTime and endDateTime");
            }
        }
        try {
            return parseToEventDto(getAllScheduledMeetings(email, startDateTime, endDateTime));
        }  catch (Exception e) {
            return  new ArrayList<>();
        }
    }

    @Override
    public List<EventDto> getCalendarEventsByMessageId(String emailId, String messageId) {
        List<EventDto> eventDtos = new ArrayList<>();
        log.info("getCalendarEventsByMessageId messageId is {}",messageId);
        try {
            Message message = getMessageById(emailId, messageId);
            if (message == null || message.getPayload() == null || message.getPayload().getParts() == null) {
                return eventDtos;
            }
            List<MessagePart> messageParts = message.getPayload().getParts();
            messageParts.forEach(part -> {
                if (checkFileIsICS(part.getFilename())) {
                    EventDto eventDto = handleICSFile(part, emailId, messageId);
                    eventDto.setBodyPreview(GmailUtils.getHtmlBodyFromMessage(messageParts,false));
                    eventDtos.add(eventDto);
                }
            });
        } catch (Exception e) {
            log.error("Error occurred while getting calendar events: ", e);
        }

        return eventDtos;
    }

    @Override
    public Boolean checkMessageIsEvent(String email, String messageId) {
        log.info("checkMessageIsEvent messageId is {}", messageId);
        try {
            Message message = getMessageById(email, messageId);
            if (message == null || message.getPayload() == null || message.getPayload().getParts() == null) return false;
            for (MessagePart part : message.getPayload().getParts()) {
                if (checkFileIsICS(part.getFilename())) return TRUE;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error occurred checkMessageIsEvent  calendar events: ", e);
        }
        return FALSE;
    }

    private EventDto handleICSFile(MessagePart messagePart,String emailId,String messageId){
        String attachmentId=messagePart.getBody().getAttachmentId();
        return fetchAttachmentBtId(emailId,messageId,attachmentId);
    }

    @Override
    public String updatePriority(String messageId, String priority) {
        return null;//flagEmailWithCustomLabel();
    }

    /**
     * Retrieves available meeting slots for a list of emails between the specified start and end date-times.
     * If the start or end date-time is not provided, defaults will be used based on the user's preferences.
     *
     * @param emails         A list of email addresses to retrieve meeting slots for.
     * @param startDateTime  The start date-time for the period to check (in ISO 8601 format). If null, defaults are used.
     * @param endDateTime    The end date-time for the period to check (in ISO 8601 format). If null, defaults are used.
     * @param slotDuration   The duration of each slot in minutes.
     * @return A list of available meeting slots across all specified emails, where each slot is represented by a Meeting object.
     * @throws Exception If an error occurs while retrieving or processing the meetings.
     */
    public List<Meeting> getAvailableMeetingSlots(List<String> emails, String startDateTime, String endDateTime, int slotDuration)  {
        if (startDateTime == null || endDateTime == null) {
            try {
                startDateTime = getDefaultStartDateTime();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            try {
                endDateTime = getDefaultEndDateTime();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        log.info("startDateTime is  {} and endDateTime is {}",startDateTime,endDateTime);
        Map<String, List<Meeting>> meetingsByEmail = new HashMap<>();
        for (String email : emails) {
            List<EventDto> calendarEvents = parseToEventDto(getAllScheduledMeetings(email, startDateTime, endDateTime));
            List<Meeting> meetings = convertEventDtosToMeetings(calendarEvents);
            meetingsByEmail.put(email, meetings);
        }
        Date start = convertStringToDate(startDateTime);
        Date end = convertStringToDate(endDateTime);
        long durationMillis = (long) slotDuration * 60 * 1000;
        return findAvailableTimeSlots(meetingsByEmail, start, end, durationMillis);
    }


    public List<Meeting> getAvailableMeetingSlotsOFF(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        try {
            if (startDateTime == null) {
                startDateTime = getDefaultStartDateTime();
            }
            if (endDateTime == null) {
                endDateTime = getDefaultEndDateTime();
            }
        } catch (Exception e) {
            throw new RuntimeException("Error fetching default date times: " + e.getMessage(), e);
        }

        log.info("startDateTime is {} and endDateTime is {}", startDateTime, endDateTime);

        Map<String, List<Meeting>> meetingsByEmail = new HashMap<>();

        for (String email : emails) {
            try {
                List<EventDto> calendarEvents = parseToEventDto(getAllScheduledMeetings(email, startDateTime, endDateTime));
                List<Meeting> meetings = convertEventDtosToMeetings(calendarEvents);

                try {
                    OutOfOfficeDto outOfOffice = getOutOfOfficeHours(email);
                    if (outOfOffice != null) {
                        Meeting meeting = new Meeting();
                        meeting.setStartTime(outOfOffice.getOutOfOfficeStart());
                        meeting.setEndTime(outOfOffice.getOutOfOfficeEnd());
                        meetings.add(meeting);
                    }
                } catch (Exception e) {
                    log.warn("Error fetching out-of-office hours for email {}: {}", email, e.getMessage());
                }

                meetingsByEmail.put(email, meetings);
            } catch (Exception e) {
                log.error("Error processing meetings for email {}: {}", email, e.getMessage());
            }
        }

        Date start;
        Date end;
        try {
            start = convertStringToDate(startDateTime);
            end = convertStringToDate(endDateTime);
        } catch (Exception e) {
            throw new RuntimeException("Error converting date strings to Date objects: " + e.getMessage(), e);
        }

        long durationMillis = (long) slotDuration * 60 * 1000;

        return findAvailableTimeSlots(meetingsByEmail, start, end, durationMillis);
    }



    private String[] setupDateTimeRange(String startDateTime, String endDateTime)  {
        if (startDateTime == null || endDateTime == null) {
            String email = userContextHolder.getCurrentUser().getEmail();
            EmailUser user = emailUserDao.findByEmail(email);
            String userId = user.getUserId();
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
            startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
            endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
        }
        return new String[]{startDateTime, endDateTime};
    }

    @Override
    public AvailableSlots getAvailableSlotsAndConflict(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        log.debug("Inside @method getAvailableSlotsAndConflict. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
        String[] dateTimeRange = setupDateTimeRange(startDateTime, endDateTime);
        startDateTime = dateTimeRange[0];
        endDateTime = dateTimeRange[1];
        Map<String, List<Meeting>> result = fetchCalendarEventsForEmails(emails, startDateTime, endDateTime);
        Date start = convertStringToDate(startDateTime);
        Date end = convertStringToDate(endDateTime);
        long durationMillis = (long) slotDuration * 60 * 1000;
        AvailableSlots availableSlots = new AvailableSlots();
        availableSlots.setAvailableSlots(findAvailableTimeSlots(result, start, end, durationMillis));
        availableSlots.setConflictMeeting(findUnavailableTimeSlots(result, start, end));
        availableSlots.setOutOfOffice(getOutOfOfficeDetails(result, start, end));
        return availableSlots;
    }

    @Override
    public List<OutOfOffice> getOutOfOfficeDetails(Map<String, List<Meeting>> result, Date start, Date end) {
        List<OutOfOffice> outOfOffice = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"); // Assuming ISO 8601 format
        try {
            for (Map.Entry<String, List<Meeting>> entry : result.entrySet()) {
                String key = entry.getKey();
                Map<String, Object> userSettings = getAutoReplySettingsForUser(key);
                log.info("userSettings is {}", userSettings);
                if (userSettings != null) {
                    log.info("userSettings not null");
                    OutOfOffice office = convertToOutOfOffice(key, userSettings);
                    Date oooStart = null;
                    Date oooEnd = null;
                    if (office.getStatus()!=null && office.getStatus().equalsIgnoreCase("Enabled")  && office.getStartDateTime() != null && office.getEndDateTime() != null) {
                        oooStart = dateFormat.parse(office.getStartDateTime());
                        oooEnd = dateFormat.parse(office.getEndDateTime());
                    } else {
                        EmailPreferences emailPreferences = preferencesDao.getEmailPreferencesByUserId(key);
                        oooStart = convertLocalTimeToDate(emailPreferences.getCheckin());
                        oooEnd = convertLocalTimeToDate(emailPreferences.getCheckout());
                    }
                    if ((oooStart.before(end) || oooStart.equals(end)) && (oooEnd.after(start) || oooEnd.equals(start)))
                        outOfOffice.add(office);
                }
            }
        } catch (Exception e) {
            log.error("error inside getAutoReplySettingsForUser", e);
        }
        return outOfOffice;
    }

    private  Map<String, List<Meeting>> fetchCalendarEventsForEmails(List<String> emails,String startDateTime, String endDateTime){
        log.info("inside fetchCalendarEventsForEmails startDateTime is {} and endDateTime is {}", startDateTime, endDateTime);
        Map<String, List<Meeting>> meetingsByEmail = new HashMap<>();
        for (String email : emails) {
            try {
                List<EventDto> calendarEvents = parseToEventDto(getAllScheduledMeetings(email, startDateTime, endDateTime));
                List<Meeting> meetings = convertEventDtosToMeetings(calendarEvents);
                if(meetings.isEmpty()){
                    meetingsByEmail.put(email,new ArrayList<>());
                }else {
                    meetingsByEmail.put(email, meetings);
                }
            } catch (Exception e) {
                log.error("Error processing email {}: {}", email, e.getMessage());
            }
        }
        return meetingsByEmail;
    }


    /**
     * Replies to all recipients of the original message.
     *
     * @param userId    The user's email address. The special value "me" can be used to indicate the authenticated user.
     * @param messageId The ID of the message to reply to.
     * @param replyBody The body content of the reply.
     * @return The sent message.
     * @throws MessagingException If an error occurs when creating the reply message.
     * @throws IOException        If an error occurs when sending the message.
     */
    public  String replyAll(String userId, String messageId, String replyBody,Boolean isDraft)  {
        try{
            Gmail gmailService=getGmailService(userId);
            Message originalMessage = gmailService.users().messages().get(userId, messageId).execute();
            MimeMessage originalMimeMessage = getMimeMessage(gmailService, userId, originalMessage);
            String subject = originalMimeMessage.getSubject();
            String from = getHeaderValue(originalMessage, "From");
            String to = getHeaderValue(originalMessage, "To");
            String cc = getHeaderValue(originalMessage, "Cc");
            String messageIdHeader = getHeaderValue(originalMessage, "Message-ID");
            MimeMessage replyMimeMessage = new MimeMessage(Session.getDefaultInstance(new Properties()));
            replyMimeMessage.setFrom(new InternetAddress(userId));
            replyMimeMessage.setReplyTo(originalMimeMessage.getReplyTo());
            replyMimeMessage.addRecipients(javax.mail.Message.RecipientType.TO, to);
            if (cc != null) replyMimeMessage.addRecipients(javax.mail.Message.RecipientType.CC, cc);
            replyMimeMessage.setSubject("Re: " + subject);
            replyMimeMessage.setText(replyBody);
            replyMimeMessage.setHeader("In-Reply-To", messageIdHeader);
            replyMimeMessage.setHeader("References", messageIdHeader);
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            replyMimeMessage.writeTo(buffer);
            byte[] rawMessageBytes = buffer.toByteArray();
            String encodedEmail =Base64.getUrlEncoder().encodeToString(rawMessageBytes);
            Message message = new Message();
            message.setRaw(encodedEmail);
            if(isDraft) createReplyDraft(gmailService,userId,message);
            else gmailService.users().messages().send(userId, message).execute();
            return EmailConstants.SUCCESS;
        }catch (Exception e){
            e.printStackTrace();
            log.error("inside error replyAll {}",e.getMessage());
            return EmailConstants.FAILED;
        }
    }

    private void createReplyDraft( Gmail gmailService,String userId,Message message){
        Draft draft = new Draft();
        draft.setMessage(message);
        try {
            gmailService.users().drafts().create(userId, draft).execute();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("inside createReplyDraft");
        }
    }

    @Override
    public String createDraftReply(Map<String, String> requestBody,Boolean isReplyAll) {
        String email = userContextHolder.getCurrentUser().getEmail();
        MessageWrapper messageWrapper=convertToGmailMessageWrapper(requestBody);
        log.info("MessageWrapper is {}",messageWrapper);
        messageWrapper.setUserId(email);
        if(isReplyAll) {
            return replyToEmail( messageWrapper.getMessageId(),email, messageWrapper.getReplyBody(),messageWrapper.getAttachmentPathList(), messageWrapper.getIsDraft());
        }else  {
            return replyToEmail(messageWrapper.getMessageId(),email, messageWrapper.getReplyBody(),messageWrapper.getAttachmentPathList(), messageWrapper.getIsDraft());

        }

    }
    /**
     * Retrieves a MimeMessage from the Gmail API message.
     *
     * @param service     The Gmail API service instance.
     * @param userId      The user's email address.
     * @param message     The Gmail message to convert.
     * @return The MimeMessage object.
     * @throws IOException        If an error occurs when reading the message.
     * @throws MessagingException If an error occurs when converting the message.
     */
    private static MimeMessage getMimeMessage(Gmail service, String userId, Message message)
            throws  javax.mail.MessagingException {
        byte[] emailBytes = Base64.getDecoder().decode(message.getRaw());
        return new MimeMessage(Session.getDefaultInstance(new Properties()), new ByteArrayInputStream(emailBytes));
    }
    @Override
    public String getLogs() {
        try {
            EmailUser emailUser=emailUserDao.findById(1).get();
            System.out.println("emailUser is {}"+emailUser);
            emailUserDao.save(null);
        }catch (Exception e){
            log.error("Error while storeing email user {}",e.getMessage(),getStackTraceAsString(e), "messageId","internetMessageId","type","<EMAIL>","emailSubject","conversationId",true,null,"intent","attendees");
        }
        return "Succesfully";
    }

    /**
     * Retrieves the default start date-time based on the current user's email preferences.
     *
     * @return The default start date-time in ISO 8601 format.
     * @throws Exception If an error occurs while retrieving user preferences or converting to UTC.
     */
    private String getDefaultStartDateTime() throws Exception {
//        String email = userContextHolder.getCurrentUser().getEmail();
//        EmailUser user = emailUserDao.findByEmail(email);
//        String userId = user.getUserId();
//        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);

        return DateUtils.convertToUTCString(LocalTime.now(), "Asia/Kolkata", 0);
    }

    /**
     * Retrieves the default end date-time based on the current user's email preferences.
     *
     * @return The default end date-time in ISO 8601 format.
     * @throws Exception If an error occurs while retrieving user preferences or converting to UTC.
     */
    private String getDefaultEndDateTime() throws Exception {
//        String email = userContextHolder.getCurrentUser().getEmail();
//        EmailUser user = emailUserDao.findByEmail(email);
//        String userId = user.getUserId();
//        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
        return DateUtils.convertToUTCString(LocalTime.now(), "Asia/Kolkata", 2);
    }

    /**
     * Converts a list of EventDto objects to a list of Meeting objects.
     *
     * @param eventDtos The list of EventDto objects to convert.
     * @return A list of Meeting objects.
     */
    private List<Meeting> convertEventDtosToMeetings(List<EventDto> eventDtos) {
        List<Meeting> meetings = new ArrayList<>();
        for (EventDto eventDto : eventDtos) {
            Meeting meeting = new Meeting();
            meeting.setStartTime(eventDto.getMeetingStartTime());
            meeting.setEndTime(eventDto.getMeetingEndTime());
            meetings.add(meeting);
        }
        return meetings;
    }

    public OutOfOfficeDto getOutOfOfficeHours(String userEmail) {
        log.debug("inside @method getOutOfOfficeHours. @param : userEmail -> {}", userEmail);
        Map<String, Object> autoReplySettings = null;
        try {
            autoReplySettings = getAutoReplySettingsForUser(userEmail);
        } catch (Exception e) {
            log.error("Error fetching auto-reply settings for user {}: {}", userEmail, e.getMessage(), e);
            return null;
        }

        if (autoReplySettings == null || "disabled".equals(autoReplySettings.get("status"))) {
            return null;
        }

        String start = (String) autoReplySettings.get("scheduledStartDateTime");
        String end = (String) autoReplySettings.get("scheduledEndDateTime");
        String timeZone = (String) autoReplySettings.get("scheduledEndTimeZone");

        Date startTime = null;
        Date endTime = null;

        try {
            startTime = parseDateFromString(start, timeZone);
        } catch (Exception e) {
            log.error("Error parsing start time for user {}: {}", userEmail, e.getMessage(), e);
            return null;
        }

        try {
            endTime = parseDateFromString(end, timeZone);
        } catch (Exception e) {
            log.error("Error parsing end time for user {}: {}", userEmail, e.getMessage(), e);
            return null;
        }

        OutOfOfficeDto outOfOfficeDto = new OutOfOfficeDto();
        outOfOfficeDto.setOutOfOfficeStart(startTime);
        outOfOfficeDto.setOutOfOfficeEnd(endTime);
        return outOfOfficeDto;
    }




    /**
     * Sets reminders for an event.
     * @param event The event to set reminders on.
     * @param userEmails List of email addresses for which to set reminders.
     * @param reminderMinutes The number of minutes before the event to trigger the reminder.
     * @return The updated Event object with reminders set.
     */
    public static Event setEventReminders(Event event, List<String> userEmails, int reminderMinutes) {
        EventReminder popupReminder = new EventReminder()
                .setMethod("popup")
                .setMinutes(reminderMinutes);

        EventReminder emailReminder = new EventReminder()
                .setMethod("email")
                .setMinutes(reminderMinutes);

        Reminders reminders = new Reminders()
                .setUseDefault(false)
                .setOverrides(Arrays.asList(popupReminder, emailReminder));
        event.setReminders(reminders);

        return event;
    }

    List<Meeting> findAvailableTimeSlots(Map<String, List<Meeting>> meetings, Date from, Date till, long meetingDurationMillis) {
        List<Meeting> availableSlots = new ArrayList<>();
        List<Meeting> allMeetings = new ArrayList<>();
        log.info("meetings values {}",meetings.values());
        for (List<Meeting> userMeetings : meetings.values()) {
            allMeetings.addAll(userMeetings);
        }
        allMeetings.sort(Comparator.comparing(Meeting::getStartTime));
        Date currentStart = from;
        for (Meeting meeting : allMeetings) {
            if (meeting.getStartTime().after(currentStart)) {
                long gap = meeting.getStartTime().getTime() - currentStart.getTime();
                while (gap >= meetingDurationMillis) {
                    Date potentialEnd = new Date(currentStart.getTime() + meetingDurationMillis);
                    if (potentialEnd.after(meeting.getStartTime())) {
                        break;
                    }
                    availableSlots.add(new Meeting(currentStart, potentialEnd));
                    currentStart = potentialEnd;
                    gap = meeting.getStartTime().getTime() - currentStart.getTime();
                }
            }
            if (meeting.getEndTime().after(currentStart)) {
                currentStart = meeting.getEndTime();
            }
        }
        while (currentStart.before(till) && (till.getTime() - currentStart.getTime() >= meetingDurationMillis)) {
            Date potentialEnd = new Date(currentStart.getTime() + meetingDurationMillis);
            if (potentialEnd.after(till)) {
                break;
            }
            availableSlots.add(new Meeting(currentStart, potentialEnd));
            currentStart = potentialEnd;
        }
        log.info("availableSlots size {}",availableSlots.size());
        availableSlots.forEach(meeting -> {
            log.info("meeting {}",meeting.toString());
        });
        return availableSlots;
    }

    List<AvailableTimeSlots> findAvailableTimeSlotsV2(Map<String, List<Meeting>> meetings, List<String> users, Date from, Date till, long meetingDurationMillis) {
        List<AvailableTimeSlots> availableSlots = new ArrayList<>();
        if (meetings == null || users == null || from == null || till == null) {
            throw new IllegalArgumentException("Input parameters must not be null");
        }
        try {
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(from);
            while (cal.getTime().before(till)) {
                Date slotStart = cal.getTime();
                cal.add(java.util.Calendar.MILLISECOND, (int) meetingDurationMillis);
                Date slotEnd = cal.getTime();
                if (slotEnd.after(till)) {
                    break;
                }

                List<String> availableUsers = new ArrayList<>();
                List<String> unavailableUsers = new ArrayList<>();

                for (String user : users) {
                    List<Meeting> userMeetings = meetings.getOrDefault(user, new ArrayList<>());
                    log.info("userMeetings is {}",userMeetings);
                    if (isUserAvailableInSlot(userMeetings, slotStart, slotEnd)) {
                        availableUsers.add(user);
                    } else {
                        unavailableUsers.add(user);
                    }
                }
                availableSlots.add(new AvailableTimeSlots(slotStart, slotEnd, availableUsers, unavailableUsers));
            }
        } catch (NullPointerException e) {
            log.error("A null pointer exception occurred: {}", e.getMessage());
            throw new RuntimeException("A null value was encountered", e);
        } catch (Exception e) {
            log.error("An error occurred while finding available time slots: {}", e.getMessage());
            throw new RuntimeException("An unexpected error occurred", e);
        }
        return availableSlots;
    }

    private static boolean isUserAvailableInSlot(List<Meeting> meetings, Date slotStart, Date slotEnd) {
        for (Meeting meeting : meetings) {
            if (meeting.getStartTime().before(slotEnd) && meeting.getEndTime().after(slotStart)) {
                return false;
            }
        }
        return true;
    }


    public List<Event> getAllScheduledMeetings(String userId, String startDateTime, String endDateTime) {
        try {
            Calendar calendarService = createCalendarService(userId);
            Events events = calendarService.events().list(userId).setOrderBy("startTime").setSingleEvents(true).setTimeMin(new DateTime(startDateTime)).setTimeMax(new DateTime(endDateTime)).execute();
            List<Event> filteredEvents = new ArrayList<>();
            for (Event event : events.getItems()) {
                log.info("Event: {} | Start Time: {} | End Time: {}",
                        event.getSummary(),
                        event.getStart().getDateTime() != null ? event.getStart().getDateTime() : event.getStart().getDate(),
                        event.getEnd().getDateTime() != null ? event.getEnd().getDateTime() : event.getEnd().getDate());
                boolean isOrganizer = userId.equals(event.getOrganizer().getEmail());
                boolean isAccepted = false;
                if (event.getAttendees() != null) {
                    for (EventAttendee attendee : event.getAttendees()) {
                        if (userId.equals(attendee.getEmail()) && !EmailConstants.DECLINED.equalsIgnoreCase(attendee.getResponseStatus())) {
                            isAccepted = true;
                            break;
                        }
                    }
                }
                if (isOrganizer || isAccepted) {
                    filteredEvents.add(event);
                }
            }
            return filteredEvents;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public List<AvailableTimeSlots> getAvailableMeetingSlotsV2(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        log.info("emails are {}", emails);

        // Handle startDateTime and endDateTime with try-catch
        try {
            if (startDateTime == null) {
                startDateTime = getDefaultStartDateTime();
            }
            if (endDateTime == null) {
                endDateTime = getDefaultEndDateTime();
            }
        } catch (Exception e) {
            log.error("Error retrieving default date times: {}", e.getMessage());
            throw new RuntimeException("Failed to get default date times", e);
        }

        log.info("startDateTime is {} and endDateTime is {}", startDateTime, endDateTime);
        Map<String, List<Meeting>> meetingsByEmail = new HashMap<>();

        for (String email : emails) {
            try {
                List<EventDto> calendarEvents = parseToEventDto(getAllScheduledMeetings(email, startDateTime, endDateTime));
                List<Meeting> meetings = convertEventDtosToMeetings(calendarEvents);
                OutOfOfficeDto outOfOffice = getOutOfOfficeHours(email);
                if (outOfOffice != null) {
                    Meeting meeting = new Meeting();
                    meeting.setStartTime(outOfOffice.getOutOfOfficeStart());
                    meeting.setEndTime(outOfOffice.getOutOfOfficeEnd());
                    meetings.add(meeting);
                }
                if(meetings.isEmpty()){
                    meetingsByEmail.put(email,new ArrayList<>());
                }else {
                    meetingsByEmail.put(email, meetings);
                }
            } catch (Exception e) {
                log.error("Error processing email {}: {}", email, e.getMessage());
            }
        }

        Date start;
        Date end;
        try {
            start = convertStringToDate(startDateTime);
            end = convertStringToDate(endDateTime);
        } catch (Exception e) {
            log.error("Error converting date strings to Date objects: {}", e.getMessage());
            throw new RuntimeException("Invalid date format for start or end time", e);
        }

        long durationMillis = (long) slotDuration * 60 * 1000;

        try {
            return findAvailableTimeSlotsV2(meetingsByEmail, emails, start, end, durationMillis);
        } catch (Exception e) {
            log.error("Error finding available time slots: {}", e.getMessage());
            throw new RuntimeException("Failed to find available time slots", e);
        }
    }



    /**
     * Creates a Google Calendar event with a reminder set to notify the user before the event.
     * This method is useful for setting reminders for tasks or deadlines that need to be managed
     * within Google Calendar.
     *
     * @param summary          A brief title or summary for the reminder event.
     * @param description      A detailed description of the reminder event.
     * @param reminderDateTime The date and time for the reminder in ISO 8601 format (e.g., "2024-08-30T09:00:00-07:00").
     * @return The created Event object with the specified summary, description, and reminder set.
     */
    public static Event createReminder(String summary, String description, String reminderDateTime,int popupReminderMinutes,int emailReminderMinutes) {
        Event event = new Event()
                .setSummary(summary)
                .setDescription(description);

        DateTime startDateTime = new DateTime(reminderDateTime);

        EventDateTime start = new EventDateTime()
                .setDateTime(startDateTime)
                .setTimeZone("America/Los_Angeles");
        event.setStart(start);

        EventDateTime end = new EventDateTime()
                .setDateTime(startDateTime)
                .setTimeZone("America/Los_Angeles");
        event.setEnd(end);

        event.setReminders(new Reminders()
                .setUseDefault(false)
                .setOverrides(Arrays.asList(
                        new EventReminder().setMethod("popup").setMinutes(popupReminderMinutes),
                        new EventReminder()
                                .setMethod("email")
                                .setMinutes(emailReminderMinutes)
                )));

        return event;
    }


    public  void getCalendarsEvents() throws IOException, GeneralSecurityException {
        String userId=userContextHolder.getCurrentUser().getEmail();
        Calendar service = createCalendarService(userId);
        String pageToken = null;
        do {
            CalendarList calendarList = service.calendarList().list().setPageToken(pageToken).execute();
            List<CalendarListEntry> items = calendarList.getItems();
            for (CalendarListEntry calendarListEntry : items) {
                System.out.println(calendarListEntry.getSummary());
            }
            pageToken = calendarList.getNextPageToken();
        } while (pageToken != null);

    }


    /**
     * Creates a Google Calendar meeting event with specified details, including an agenda, guest permissions, and a Google Meet link.
     * This method creates an event with a summary, location, description, start and end times, and a list of attendees.
     * It also sets up a Google Meet link for the event, sets guest permissions, and sends updates to all attendees.
     *
     * @param gmailMeetingWrapper A wrapper object containing event details such as summary, location, description, start/end times, attendees, etc.
     * @return The created Event object with all specified details, including a Google Meet link and attendees, or null if an error occurs.
     */
    public String createMeetingEvent(GmailMeetingWrapper gmailMeetingWrapper) {
        try {
            log.info("Inside createMeetingEvent. Current userId: {}", gmailMeetingWrapper.getUserId());
            if (gmailMeetingWrapper.getUserId() == null || gmailMeetingWrapper.getUserId().isEmpty()) throw new IllegalArgumentException("User ID cannot be null or empty");
            if (gmailMeetingWrapper == null) throw new IllegalArgumentException("GmailMeetingWrapper cannot be null");
            Calendar calendarService = createCalendarService(gmailMeetingWrapper.getUserId());
            Event event = createBaseEvent(gmailMeetingWrapper);
            setEventTimes(event, gmailMeetingWrapper);
            setEventAttendees(event, gmailMeetingWrapper);
            setRecurrenceRule(event, gmailMeetingWrapper);
            setConferenceData(event);
            checkMeetingType(event,gmailMeetingWrapper);
            return insertEventIntoCalendar(calendarService, event);
        } catch (IOException e) {
            log.error("IOException occurred while creating meeting event", e);
            return  EmailConstants.FAILED;
        } catch (IllegalArgumentException e) {
            log.error("Invalid input: {}", e.getMessage());
            return  EmailConstants.FAILED;
        }
    }
    @Override
    public String createMeetingEvent(String userId, Map<String, Object> requestBody) {
        if (userId == null || userId.isEmpty()) throw new IllegalArgumentException("User ID cannot be null when fetching preferences");
        GmailMeetingWrapper gmailMeetingWrapper=convertToGmailMeetingWrapper(requestBody);
        gmailMeetingWrapper.setUserId(userId);
        setDefaultTimeZoneIfMissing(gmailMeetingWrapper);
        return createMeetingEvent(gmailMeetingWrapper);
    }
    private void checkMeetingType( Event event,GmailMeetingWrapper gmailMeetingWrapper){
        if (gmailMeetingWrapper.getMeetingType()!=null&&gmailMeetingWrapper.getMeetingType().equalsIgnoreCase(ZOOM)) return;
    }

    /**
     * Creates the base event object with summary, location, description, and guest permissions.
     *
     * @param gmailMeetingWrapper The wrapper containing event details.
     * @return A base Event object.
     */
    private Event createBaseEvent(GmailMeetingWrapper gmailMeetingWrapper) {
        Event event = new Event();
        if (gmailMeetingWrapper.getSummary() != null) {
            event.setSummary(gmailMeetingWrapper.getSummary());
        } else {
            log.warn("Event summary is null");
        }

        if (gmailMeetingWrapper.getLocation() != null) {
            event.setLocation(gmailMeetingWrapper.getLocation());
        } else {
            log.warn("Event location is null");
        }

        if (gmailMeetingWrapper.getDescription() != null) {
            event.setDescription(gmailMeetingWrapper.getDescription());
        } else {
            log.warn("Event description is null");
        }
        event.setGuestsCanModify(false);
        event.setGuestsCanInviteOthers(false);
        event.setGuestsCanSeeOtherGuests(true);
        return event;
    }
    /**
     * Sets the start and end times for the event.
     *
     * @param event              The event to which the times will be set.
     * @param gmailMeetingWrapper The wrapper containing the start and end times.
     */
    private void setEventTimes(Event event, GmailMeetingWrapper gmailMeetingWrapper) {
        String timeZone = gmailMeetingWrapper.getTimeZone();

        if (gmailMeetingWrapper.getStartDateTime() != null) {
            try {
                LocalDateTime localStart = LocalDateTime.parse(gmailMeetingWrapper.getStartDateTime());
                ZonedDateTime zonedStart = localStart.atZone(ZoneId.of(timeZone));
                DateTime startDateTimeObj = new DateTime(zonedStart.toInstant().toEpochMilli());

                EventDateTime start = new EventDateTime()
                        .setDateTime(startDateTimeObj)
                        .setTimeZone(timeZone);
                event.setStart(start);

                log.info("Original start time: {}, TimeZone: {}",
                        gmailMeetingWrapper.getStartDateTime(), timeZone);
                log.info("Zoned DateTime: {}", zonedStart);

            } catch (DateTimeParseException e) {
                log.error("Error parsing start time: {}", e.getMessage());
            }
        } else {
            log.warn("Event start time is null");
        }

        if (gmailMeetingWrapper.getEndDateTime() != null) {
            try {
                LocalDateTime localEnd = LocalDateTime.parse(gmailMeetingWrapper.getEndDateTime());
                ZonedDateTime zonedEnd = localEnd.atZone(ZoneId.of(timeZone));
                DateTime endDateTimeObj = new DateTime(zonedEnd.toInstant().toEpochMilli());

                EventDateTime end = new EventDateTime()
                        .setDateTime(endDateTimeObj)
                        .setTimeZone(timeZone);
                event.setEnd(end);

                log.info("Original end time: {}, TimeZone: {}",
                        gmailMeetingWrapper.getEndDateTime(), timeZone);
                log.info("Zoned DateTime: {}", zonedEnd);

            } catch (DateTimeParseException e) {
                log.error("Error parsing end time: {}", e.getMessage());
            }
        } else {
            log.warn("Event end time is null");
        }
    }
    /**
     * Sets the attendees for the event.
     *
     * @param event              The event to which the attendees will be added.
     * @param gmailMeetingWrapper The wrapper containing the required and optional attendees.
     */
    private void setEventAttendees(Event event, GmailMeetingWrapper gmailMeetingWrapper) {
        List<MeetingWrapper> meetingWrappers = new ArrayList<>();
        setAttendees(meetingWrappers, gmailMeetingWrapper.getRequiredAttendees(), gmailMeetingWrapper.getOptionalAttendees());
        if (!meetingWrappers.isEmpty()) {
            List<EventAttendee> attendees = new ArrayList<>();
            meetingWrappers.forEach(meetingWrapper ->
                    attendees.add(new EventAttendee()
                            .setOptional("optional".equals(meetingWrapper.getType()))
                            .setEmail(meetingWrapper.getEmail())));
            event.setAttendees(attendees);
        } else {
            log.warn("No attendees provided");
        }
    }

    /**
     * Sets the recurrence rule for the event if provided.
     *
     * @param event              The event to which the recurrence rule will be set.
     * @param gmailMeetingWrapper The wrapper containing the recurrence rule.
     */
    private void setRecurrenceRule(Event event, GmailMeetingWrapper gmailMeetingWrapper) {
        if (gmailMeetingWrapper.getRecurrenceRule() != null && !gmailMeetingWrapper.getRecurrenceRule().isEmpty()) {
            event.setRecurrence(Arrays.asList(gmailMeetingWrapper.getRecurrenceRule()));
        } else {
            log.warn("No recurrence rule provided");
        }
    }

/**
 * Sets the Google Meet conference data for the event.
 *
 * @param event The event to which the conference data will be added.
 */
private void setConferenceData(Event event) {
    String requestId = UUID.randomUUID().toString();
    ConferenceData conferenceData = new ConferenceData().setCreateRequest(
            new CreateConferenceRequest()
                    .setConferenceSolutionKey(new ConferenceSolutionKey().setType("hangoutsMeet"))
                    .setRequestId(requestId)
    );
    event.setConferenceData(conferenceData);
}

/**
 * Inserts the event into the user's Google Calendar.
 *
 * @param calendarService The initialized Google Calendar service.
 * @param event           The event to be inserted.
 * @return The inserted Event object, or null if an error occurs.
 * @throws IOException If an error occurs while inserting the event.
 */
private String insertEventIntoCalendar(Calendar calendarService, Event event) throws IOException {
    try {
        calendarService.events().insert("primary", event).setConferenceDataVersion(1).setSendUpdates("all").execute();
        return  EmailConstants.SUCCESS;
    } catch (IOException e) {
        log.error("Error inserting event into calendar", e);
        return  EmailConstants.FAILED;
    }
}



/**
 * Sends an email using the Gmail API with specified recipients, subject, and body.
 * This method initializes the Gmail service using a service account and delegates it
 * to the specified admin user. The email is then sent to multiple recipients, including
 * "To", "Cc", and "Bcc" recipients.
 *
 * @return the sent {@link Message} object containing details of the sent email.
 * @throws Exception if an error occurs while sending the email or initializing the Gmail service.
 */
@Override
public Message sendGmail() throws Exception {

    // updateMeetingStatus("<EMAIL>","dsfcdjjngpj41h08p9d7cr8ebg",ACCEPTED);
//        String[] attendeesEmails = {"<EMAIL>"};
//        Event event = createMeetingEvent("<EMAIL>",
//                "from mohit to vitthsl",
//                "Virtual",
//                "Virtual Meeting by mohit to vitthsl",
//                "Discussing project milestones",
//                "Discussing project milestones",
//                "2027-01-01T10:00:00-07:00",
//                "2027-01-01T11:00:00-07:00",
//                attendeesEmails
//        );



//        String userId = "<EMAIL>";
//        String subject = "draft mail";
//        String bodyText = "This is draft mail test";
//        List<String> toRecipients = new ArrayList<>();
//        toRecipients.add("<EMAIL>");
//        List<String> attachmentPaths = new ArrayList<>();
//        attachmentPaths.add("/Users/<USER>/Downloads/residential-lease-agreement.pdf");

// Calling the createDraft method
//        Draft draft = createDraft(userId, subject, bodyText, toRecipients, null, null, attachmentPaths);
//        GmailUtils.printAllFields(event);
    log.info("inside sendGmail");

//        String userId = "<EMAIL>";
//        String subject = "test mail 2";
//        String body = "This is second test";
//        List<String> toRecipients = new ArrayList<>();
//        toRecipients.add("<EMAIL>");
//        List<String> attachmentPaths = new ArrayList<>();
//        attachmentPaths.add("/Users/<USER>/Downloads/residential-lease-agreement.pdf");
//        return sendEmailWithAttachment(userId, subject, body, toRecipients, null, null,attachmentPaths);
    return null;
}


/**
 * Sends an email using the Gmail API.
 * This method creates a MIME message with the given subject, body text, and recipients,
 * encodes it, and then sends it using the Gmail service.
 *
 * @param userId       the email address of the sender (typically the user's email address).
 * @param subject      the subject of the email.
 * @param bodyText     the body text of the email.
 * @param toRecipients a list of "To" recipients' email addresses.
 * @param ccRecipients a list of "Cc" recipients' email addresses (can be null).
 * @param bccRecipients a list of "Bcc" recipients' email addresses (can be null).
 * @return the sent {@link Message} object containing details of the sent email.
 * @throws Exception if an error occurs while creating or sending the email.
 */
public Message sendEmail(String userId, String subject, String bodyText,
                         List<String> toRecipients, List<String> ccRecipients, List<String> bccRecipients) throws Exception {

    Gmail gmailService=getGmailService(userId);
    MimeMessage email = createEmail(subject, bodyText, toRecipients, ccRecipients, bccRecipients);

    ByteArrayOutputStream buffer = new ByteArrayOutputStream();
    email.writeTo(buffer);
    byte[] rawMessageBytes = buffer.toByteArray();
    String encodedEmail = Base64.getUrlEncoder().encodeToString(rawMessageBytes);

    Message message = new Message();
    message.setRaw(encodedEmail);

    return gmailService.users().messages().send(userId, message).execute();
}






/**
 * Creates a MIME email message with the specified subject, body text, and recipients.
 * This method sets up the email subject, body, and adds the specified "To", "Cc", and "Bcc"
 * recipients to the email.
 *
 * @param subject      the subject of the email.
 * @param bodyText     the body text of the email.
 * @param toRecipients a list of "To" recipients' email addresses.
 * @param ccRecipients a list of "Cc" recipients' email addresses (can be null).
 * @param bccRecipients a list of "Bcc" recipients' email addresses (can be null).
 * @return the created {@link jakarta.mail.internet.MimeMessage} object ready to be sent.
 * @throws Exception if an error occurs while creating the MIME message.
 */
private static MimeMessage createEmail(String subject, String bodyText,
                                       List<String> toRecipients, List<String> ccRecipients,
                                       List<String> bccRecipients) throws Exception {
    Properties props = new Properties();
    Session session = Session.getDefaultInstance(props, null);

    MimeMessage email = new MimeMessage(session);

    email.setSubject(subject);
    email.setContent(bodyText, "text/html");

    for (String to : toRecipients) {
        email.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(to));
    }

    if (ccRecipients != null) {
        for (String cc : ccRecipients) {
            email.addRecipient(javax.mail.Message.RecipientType.CC, new InternetAddress(cc));
        }
    }

    if (bccRecipients != null) {
        for (String bcc : bccRecipients) {
            email.addRecipient(javax.mail.Message.RecipientType.BCC, new InternetAddress(bcc));
        }
    }

    return email;
}




/**
 * Sends an email with an attachment using the Gmail API.
 *
 * @param userId       the email address of the sender (typically the user's email address).
 * @param subject      the subject of the email.
 * @param bodyText     the body text of the email.
 * @param toRecipients a list of "To" recipients' email addresses.
 * @param ccRecipients a list of "Cc" recipients' email addresses (can be null).
 * @param bccRecipients a list of "Bcc" recipients' email addresses (can be null).
 * @param attachmentFilePaths a list of file paths for the attachments to include in the email.
 * @return the sent {@link Message} object containing details of the sent email.
 * @throws Exception if an error occurs while creating or sending the email.
 */
public Message sendEmailWithAttachment( String userId, String subject, String bodyText,
                                        List<String> toRecipients, List<String> ccRecipients, List<String> bccRecipients,
                                        List<String> attachmentFilePaths) throws Exception {
    Gmail gmailService=getGmailService(userId);
    MimeMessage email = createEmailWithAttachment(subject, bodyText, toRecipients, ccRecipients, bccRecipients, attachmentFilePaths);

    ByteArrayOutputStream buffer = new ByteArrayOutputStream();
    email.writeTo(buffer);
    byte[] rawMessageBytes = buffer.toByteArray();
    String encodedEmail = Base64.getUrlEncoder().encodeToString(rawMessageBytes);

    Message message = new Message();
    message.setRaw(encodedEmail);

    return gmailService.users().messages().send(userId, message).execute();
}


/**
 * Creates a MIME email message with the specified subject, body text, recipients, and an attachment.
 *
 * @param subject      the subject of the email.
 * @param bodyText     the body text of the email.
 * @param toRecipients a list of "To" recipients' email addresses.
 * @param ccRecipients a list of "Cc" recipients' email addresses (can be null).
 * @param bccRecipients a list of "Bcc" recipients' email addresses (can be null).
 * @param attachmentFilePaths a list of file paths for the attachments to include in the email.
 * @return the created {@link jakarta.mail.internet.MimeMessage} object ready to be sent.
 * @throws Exception if an error occurs while creating the MIME message.
 */
private static MimeMessage createEmailWithAttachment(String subject, String bodyText,
                                                     List<String> toRecipients, List<String> ccRecipients,
                                                     List<String> bccRecipients, List<String> attachmentFilePaths) throws Exception {
    Properties props = new Properties();
    Session session = Session.getDefaultInstance(props, null);

    MimeMessage email = new MimeMessage(session);

    email.setSubject(subject);

    MimeBodyPart textBodyPart = new MimeBodyPart();
    textBodyPart.setText(bodyText);

    Multipart multipart = new MimeMultipart();
    multipart.addBodyPart(textBodyPart);

    for (String attachmentFilePath : attachmentFilePaths) {
        MimeBodyPart attachmentBodyPart = new MimeBodyPart();
        FileDataSource source = new FileDataSource(attachmentFilePath);
        attachmentBodyPart.setDataHandler(new DataHandler(source));
        attachmentBodyPart.setFileName(new File(attachmentFilePath).getName());
        multipart.addBodyPart(attachmentBodyPart);
    }
    email.setContent(multipart);

    for (String to : toRecipients) {
        email.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(to));
    }

    if (ccRecipients != null) {
        for (String cc : ccRecipients) {
            email.addRecipient(javax.mail.Message.RecipientType.CC, new InternetAddress(cc));
        }
    }

    if (bccRecipients != null) {
        for (String bcc : bccRecipients) {
            email.addRecipient(javax.mail.Message.RecipientType.BCC, new InternetAddress(bcc));
        }
    }

    return email;
}


/**
 * Reschedules an existing event in a Google Calendar by updating its start and end times.
 *
 * <p>This method authenticates using a service account and updates the event with the specified
 * {@code eventId} in the specified {@code calendarId}. The new start and end times for the event
 * are provided as strings in ISO 8601 format. The updated event is returned after the update is
 * applied.</p>
 *
 * @param calendarId The ID of the calendar containing the event to be rescheduled. This is typically
 *                   a calendar ID obtained from the Google Calendar API.
 * @param eventId The ID of the event to be rescheduled. This ID is obtained from the Google Calendar
 *                API when the event is initially created or retrieved.
 * @param newStartDateTime The new start date and time for the event, in ISO 8601 format (e.g., "2024-08-28T10:00:00+05:30").
 * @param newEndDateTime The new end date and time for the event, in ISO 8601 format (e.g., "2024-08-28T11:00:00+05:30").
 *
 * @return The updated {@link Event} object representing the rescheduled event, or {@code null} if
 *         an error occurs during the update process.
 *
 * @throws IOException If an error occurs while accessing the Google Calendar API or updating the event.
 */
public Map<String,String> rescheduleEvent(String calendarId, String eventId, String newStartDateTime, String newEndDateTime) {
    try {
        String userId=userContextHolder.getCurrentUser().getEmail();
        log.info("newStartDateTime is {} and newEndDateTime is {}", newStartDateTime, newEndDateTime);


        Calendar calendarService  = createCalendarService(userId);

        Event event = calendarService.events().get(calendarId, eventId).execute();

        DateTime startDateTime = new DateTime(newStartDateTime);
        EventDateTime start = new EventDateTime()
                .setDateTime(startDateTime)
                .setTimeZone("Asia/Kolkata");
        event.setStart(start);

        DateTime endDateTime = new DateTime(newEndDateTime);
        EventDateTime end = new EventDateTime()
                .setDateTime(endDateTime)
                .setTimeZone("Asia/Kolkata");
        event.setEnd(end);
        event = calendarService.events().update(calendarId, eventId, event)
                .setSendUpdates("all")
                .execute();
        return Map.of("result", EmailConstants.SUCCESS);
    } catch (IOException e) {
        e.printStackTrace();
        return Map.of("result", EmailConstants.FAILED);
    }
}

@Override
public Map<String, String> rescheduleEvent(GmailMeetingWrapper gmailMeetingWrapper) {
    try {
        log.info("newStartDateTime is {} and newEndDateTime is {}", gmailMeetingWrapper.getStartDateTime(), gmailMeetingWrapper.getEndDateTime());
        Calendar calendarService  = createCalendarService(gmailMeetingWrapper.getUserId());
        Event event = calendarService.events().get(gmailMeetingWrapper.getCalendarId() != null && !gmailMeetingWrapper.getCalendarId().isEmpty() ? gmailMeetingWrapper.getCalendarId() :PRIMARY, gmailMeetingWrapper.getEventId()).execute();

        DateTime startDateTime = new DateTime(gmailMeetingWrapper.getStartDateTime());
        EventDateTime start = new EventDateTime().setDateTime(startDateTime).setTimeZone(gmailMeetingWrapper.getTimeZone());
        event.setStart(start);
        if (gmailMeetingWrapper.getDescription() != null && !gmailMeetingWrapper.getDescription().isEmpty()) {
            event.setDescription(gmailMeetingWrapper.getDescription());
        }
        DateTime endDateTime = new DateTime(gmailMeetingWrapper.getEndDateTime());
        EventDateTime end = new EventDateTime().setDateTime(endDateTime).setTimeZone(gmailMeetingWrapper.getTimeZone());
        event.setEnd(end);
        calendarService.events().update(gmailMeetingWrapper.getCalendarId() != null && gmailMeetingWrapper.getCalendarId().isEmpty() ? gmailMeetingWrapper.getCalendarId() :PRIMARY, gmailMeetingWrapper.getEventId(), event).setSendUpdates("all").execute();
        return Map.of("result", EmailConstants.SUCCESS);
    } catch (IOException e) {
        e.printStackTrace();
        return Map.of("result", EmailConstants.FAILED);
    }
}

@Override
public Map<String, String> rescheduleEvent(String userId, Map<String, String> requestBody) {
    if (userId == null || userId.isEmpty()) throw new IllegalArgumentException("User ID cannot be null when fetching preferences");
    GmailMeetingWrapper gmailMeetingWrapper = convertToGmailRescheduleMeetingWrapper(requestBody);
    gmailMeetingWrapper.setUserId(userId);
    setDefaultTimeZoneIfMissing(gmailMeetingWrapper);
    return rescheduleEvent(gmailMeetingWrapper);
}

private void setDefaultTimeZoneIfMissing(GmailMeetingWrapper gmailMeetingWrapper) {
    log.info("check setDefaultTimeZoneIfMissing is {}",gmailMeetingWrapper.getTimeZone());
    if (gmailMeetingWrapper == null || gmailMeetingWrapper.getTimeZone() == null || gmailMeetingWrapper.getTimeZone()==EMPTY || gmailMeetingWrapper.getTimeZone().isEmpty()) {
        String timeZone = Optional.ofNullable(preferencesDao.getEmailPreferencesByUserId(gmailMeetingWrapper.getUserId())).map(EmailPreferences::getTimeZone).filter(tz -> tz != null && !tz.isEmpty()).orElse(DEFAULT_TIME_ZONE);
        System.out.println("timeZone is {}"+timeZone);
        gmailMeetingWrapper.setTimeZone(timeZone);
    }
}




    public String replyToEmail(String originalMessageId, String userId, String bodyText, List<String> attachmentPathList, Boolean isDraft) {
        Gmail gmailService = getGmailService(userId);

        try {
            Message originalMessage = gmailService.users().messages().get("me", originalMessageId).execute();
            log.info("inside replyToEmail");

            String threadId = originalMessage.getThreadId();
            log.info("threadId is {}", threadId);

            String originalSender = getHeaderValue(originalMessage, "From");
            List<String> toRecipients = getHeaderValues(originalMessage, "To");
            List<String> ccRecipients = getHeaderValues(originalMessage, "Cc");
            List<String> bccRecipients = getHeaderValues(originalMessage, "Bcc");

            Properties props = new Properties();
            Session session = Session.getDefaultInstance(props, null);
            MimeMessage reply = new MimeMessage(session);

            reply.setFrom(new InternetAddress(userId));
            reply.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(originalSender));

            for (String recipient : toRecipients) {
                reply.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(recipient));
            }
            for (String recipient : ccRecipients) {
                reply.addRecipient(javax.mail.Message.RecipientType.CC, new InternetAddress(recipient));
            }
            for (String recipient : bccRecipients) {
                reply.addRecipient(javax.mail.Message.RecipientType.BCC, new InternetAddress(recipient));
            }

            String originalSubject = getHeaderValue(originalMessage, "Subject");
               reply.setSubject("Re: " + originalSubject);
            if(bodyText == null ||bodyText.isEmpty())
            {
                reply.setText("");
            }else {
                reply.setContent(bodyText, "text/html");;
            }
            reply.setHeader("In-Reply-To", originalMessage.getId());
            reply.setHeader("References", originalMessage.getId());

            if (attachmentPathList != null && !attachmentPathList.isEmpty()) {
                reply = addAttachmentToMail(reply, attachmentPathList);
            }

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            reply.writeTo(baos);
            String rawMessage = Base64.getUrlEncoder().encodeToString(baos.toByteArray());

            Message replyMessage = new Message();
            replyMessage.setRaw(rawMessage);
            replyMessage.setThreadId(threadId);
            replyMessage.setId(originalMessageId);

            if (!isDraft) {
                createReplyDraft(gmailService, userId, replyMessage);
                return  SUCCESS;
            } else {
                gmailService.users().messages().send(userId, replyMessage).execute();
                return  SUCCESS;


            }

        } catch (IOException e) {
            log.error("IOException occurred while processing the email: {},{}", e.getMessage(), ExceptionUtils.getStackTraceAsString(e),e);
            return FAILED;
        } catch (Exception e) {
            log.error("An unexpected error occurred: {},{}",e.getMessage(),ExceptionUtils.getStackTraceAsString(e), e);
            return FAILED;

        }
    }


/**
 * Adds attachments to a given email.
 *
 * @param email The MimeMessage to which attachments will be added.
 * @param attachmentPathList A list of file paths for the attachments to be added.
 * @return The MimeMessage with the added attachments.
 * @throws javax.mail.MessagingException If an error occurs with the JavaMail API while adding the attachments.
 * @throws IOException If an I/O error occurs while reading the attachment files.
 */
private static MimeMessage addAttachmentToMail(MimeMessage email, List<String> attachmentPathList) throws IOException, javax.mail.MessagingException {
    Multipart multipart = new MimeMultipart();

    BodyPart messageBodyPart = new MimeBodyPart();
    messageBodyPart.setText(email.getContent().toString());
    multipart.addBodyPart(messageBodyPart);

    for (String path : attachmentPathList) {
        MimeBodyPart attachPart = new MimeBodyPart();
//        s3Service.downloadFileAsBytes(s3BucketName, path);
        byte[] attachmentBytes = null;

        DataSource dataSource = new ByteArrayDataSource(attachmentBytes, "application/octet-stream");

        attachPart.setDataHandler(new DataHandler((javax.activation.DataSource) dataSource));

        String fileName = path.substring(path.lastIndexOf('/') + 1);
        attachPart.setFileName(fileName);

        multipart.addBodyPart(attachPart);
    }

    email.setContent(multipart);
    return email;
}



/**
 * Creates a reply email based on the original message.
 *
 * @param originalMessage The original message to which the reply will be made.
 * @param bodyText The text content of the reply.
 * @param userId The email address of the sender of the reply.
 * @return The MimeMessage representing the reply email.
 * @throws javax.mail.MessagingException If an error occurs with the JavaMail API while creating the reply email.
 */
private MimeMessage createReplyEmail(Message originalMessage, String bodyText,String userId) throws javax.mail.MessagingException {
    Properties props = new Properties();
    Session session = Session.getDefaultInstance(props, null);
    MimeMessage reply = new MimeMessage(session);
    reply.setFrom(new InternetAddress(userId));
    reply.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(getHeaderValue(originalMessage, "From")));
    String originalSubject = getHeaderValue(originalMessage, "Subject");
    reply.setSubject(originalSubject);
    reply.setText(bodyText);
    String originalMessageId = getHeaderValue(originalMessage, "Message-ID");
    reply.setHeader("In-Reply-To", originalMessageId);
    reply.setHeader("References", originalMessageId);

    return reply;
}

/**
 * Retrieves the value of a specific header from a message.
 *
 * @param message The message from which to retrieve the header value.
 * @param headerName The name of the header whose value is to be retrieved.
 * @return The value of the specified header, or an empty string if the header is not found.
 */
private static String getHeaderValue(Message message, String headerName) {
    return message.getPayload().getHeaders().stream()
            .filter(header -> headerName.equals(header.getName()))
            .map(MessagePartHeader::getValue)
            .findFirst()
            .orElse("");
}

/**
 * Retrieves the values of a specific header from a message, splitting by commas if necessary.
 *
 * @param message The message from which to retrieve the header values.
 * @param headerName The name of the header whose values are to be retrieved.
 * @return A list of values for the specified header, split by commas.
 */
private List<String> getHeaderValues(Message message, String headerName) {
    return message.getPayload().getHeaders().stream()
            .filter(header -> headerName.equals(header.getName()))
            .map(MessagePartHeader::getValue)
            .flatMap(value -> Arrays.stream(value.split(",")))
            .map(String::trim)
            .collect(Collectors.toList());
}



/**
 * Cancels a Google Calendar meeting event and sends cancellation emails to all attendees.
 *
 * @param userId     The email address of the user who owns the event.
 * @param eventId    The unique ID of the event to be canceled.
 * @return true if the event was successfully canceled, false otherwise.
 */
public boolean cancelMeetingEvent(String userId, String eventId) {
    try {
        log.info("Inside cancelMeetingEvent");
        log.info("Current userId is {}", userId);


        Calendar calendarService  = createCalendarService(userId);

        // Use the 'delete' method to cancel the event
        calendarService.events()
                .delete("primary", eventId) // 'primary' refers to the primary calendar of the user
                .setSendUpdates("all")      // Ensures emails are sent to all attendees
                .execute();

        log.info("Event with ID {} has been successfully canceled.", eventId);
        return true;
    } catch (IOException e) {
        e.printStackTrace();
        log.error("Error canceling event with ID {}", eventId, e);
        return false;
    }
}



/**
 * Retrieves a Google Calendar event by its event ID.
 *
 * @param userId     The email address of the user who owns the calendar.
 * @param eventId    The unique ID of the event to retrieve.
 * @return The Event object representing the retrieved event, or null if not found.
 */
public Event getEventByEventId(String userId, String eventId) {
    try {
        log.info("Inside getEventByEventId");
        log.info("Current userId is {}", userId);

        Calendar calendarService = createCalendarService(userId);
        Event event = calendarService.events().get("primary", eventId).execute();
        log.info("Event retrieved: {}", event.getSummary());
        return event;
    } catch (IOException e) {
        e.printStackTrace();
        log.error("Error retrieving event with ID {}", eventId, e);
        return null;
    }
}




/**
 * Prints the message IDs and subjects of all emails in the user's mailbox.
 *
 * @param userId The email address of the user (e.g., "me" for authenticated user).
 */
@Override
public void printAllMessageIdsAndSubjects(String userId) throws IOException {

    GoogleCredential gmailCredential = GoogleCredential.fromStream(new FileInputStream(SERVICE_ACCOUNT_KEY_FILE_PATH)).createScoped(Arrays.asList(DirectoryScopes.ADMIN_DIRECTORY_USER_READONLY, GmailScopes.GMAIL_MODIFY)).createDelegated(userId);
    Gmail service = new Gmail.Builder(gmailCredential.getTransport(), gmailCredential.getJsonFactory(), gmailCredential).setApplicationName(APPLICATION_NAME).build();


    ListMessagesResponse response = service.users().messages().list(userId).execute();
    List<Message> messages = response.getMessages();

    if (messages == null || messages.isEmpty()) {
        System.out.println("No messages found.");
        return;
    }
    for (Message message : messages) {
        String messageId = message.getId();

        Message fullMessage = service.users().messages().get(userId, messageId).execute();
        printAllFields(fullMessage);
    }
}






private static MimeMessage createEmailContent(String userId, String subject, String bodyText,
                                              List<String> toRecipients, List<String> ccRecipients, List<String> bccRecipients) throws javax.mail.MessagingException, IOException {
    Properties props = new Properties();
    Session session = Session.getDefaultInstance(props, null);

    MimeMessage email = new MimeMessage(session);
    email.setFrom(new InternetAddress(userId));
    email.setSubject(subject);
    email.setText(bodyText);

    for (String to : toRecipients) {
        email.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(to));
    }

    if (ccRecipients != null) {
        for (String cc : ccRecipients) {
            email.addRecipient(javax.mail.Message.RecipientType.CC, new InternetAddress(cc));
        }
    }

    if (bccRecipients != null) {
        for (String bcc : bccRecipients) {
            email.addRecipient(javax.mail.Message.RecipientType.BCC, new InternetAddress(bcc));
        }
    }
    return email;
}

public String createDraft(String userId, String subject, String bodyText,
                          List<String> toRecipients, List<String> ccRecipients, List<String> bccRecipients,List<String> attachmentPathList,Boolean isDraft)  {
    try{
        Gmail gmailService=getGmailService(userId);
        MimeMessage email =  createEmailContent(userId, subject, bodyText,toRecipients, ccRecipients,bccRecipients);
        if(attachmentPathList != null && !attachmentPathList.isEmpty()) email = addAttachmentToMail(email,attachmentPathList);
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        email.writeTo(buffer);
        byte[] rawMessageBytes = buffer.toByteArray();
        String encodedEmail = Base64.getUrlEncoder().encodeToString(rawMessageBytes);
        Message message = new Message();
        message.setRaw(encodedEmail);
        Draft draft = new Draft();
        draft.setMessage(message);
        if(isDraft)  gmailService.users().drafts().create(userId, draft).execute();
        else gmailService.users().messages().send(userId,message).execute();
        return EmailConstants.SUCCESS;
    }catch (Exception e){
        return EmailConstants.FAILED;
    }
}



@Override
public String createDraft(Map<String, String> requestBody)  {
    String email = userContextHolder.getCurrentUser().getEmail();
    MessageWrapper messageWrapper =convertToGmailMessageWrapper(requestBody);
    messageWrapper.setUserId(email);
    return createDraft(messageWrapper.getUserId(),messageWrapper.getSubject(),messageWrapper.getContent(),messageWrapper.getToRecipients(),messageWrapper.getCcRecipients(),messageWrapper.getBccRecipients(),messageWrapper.getAttachmentPathList(),messageWrapper.getIsDraft());
}

@Override
public Map<String,String> updateMeetingStatus(String meetingEventId, String status) {
    try {
        String userId = userContextHolder.getCurrentUser().getEmail();
        log.info("Updating meeting status for user: {}, meeting ID: {}, status: {}", userId, meetingEventId, status);
        Calendar calendarService  = createCalendarService(userId);
        Event event = calendarService.events().get(userId, meetingEventId).execute();
        List<EventAttendee> attendees = event.getAttendees();
        if (attendees != null) {
            for (EventAttendee attendee : attendees) {
                if (userId.equals(attendee.getEmail())) {
                    attendee.setResponseStatus(status);
                    break;
                }
            }
        } else {
            log.warn("No attendees found for the event.");
            return Map.of("result", EmailConstants.FAILED);
        }
        calendarService.events().update(userId, meetingEventId, event).setSendUpdates("all").execute();
        log.info("Meeting status updated successfully.");
        return Map.of("result", EmailConstants.SUCCESS);
    } catch (IOException e) {
        log.error("Error updating meeting status.", e);
        return Map.of("result", EmailConstants.FAILED);
    }
}


public Events getEvent(String userId) {
    try {

        Calendar calendarService  = createCalendarService(userId);
        Events events = calendarService.events().list(userId).execute();

        return events;
    } catch (IOException e) {
        e.printStackTrace();
        return null;
    }
}



public Event getEventById(String userId,String eventId) {
    try {

        Calendar calendarService  = createCalendarService(userId);
        return calendarService.events().get(userId, eventId).execute();
    } catch (IOException e) {
        e.printStackTrace();
        return null;
    }
}









private String uploadToS3(Path tempFile, String userId, String timestamp, String fileName) {
    String s3BucketName = "emailAttachments";
    String s3Key = userId + "/attachments/" + timestamp + "_" + fileName;
    try {
        s3Service.uploadFile(s3BucketName, s3Key, tempFile.toString());
        return   s3Key;
    } catch (Exception e) {
        log.error("Error uploading file to S3", e);
        throw new RuntimeException("Failed to upload file to S3", e);
    }
}




/**
 * Forwards an email with the specified message ID.
 *
 * @param originalMessageId The ID of the original message to be forwarded.
 * @param userId The email address of the sender of the forwarded email.
 * @param additionalBodyText The text content to include in the forwarded email.
 * @param toRecipients A list of "To" recipients' email addresses for the forwarded message.
 */
public String forwardEmail(String originalMessageId, String userId, String additionalBodyText, String toRecipients) {
    try {


        Gmail gmailService = getGmailService(userId);
        Message originalMessage = gmailService.users().messages().get(userId, originalMessageId).execute();

        String originalSubject = getHeaderValue(originalMessage, "Subject");
        String originalBody = getOriginalMessageBody(originalMessage);
        List<String> toRecipientList = CommonUtils.getListFromCommaSeparatedString(toRecipients);

        String bodyText = "---------- Forwarded message ---------" +"\n\n" + originalBody + "\n\n---\n\n";

        MimeMessage forward = createEmail("Fwd: " + originalSubject, bodyText, toRecipientList, null, null);

        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        forward.writeTo(buffer);
        byte[] rawMessageBytes = buffer.toByteArray();
        String encodedEmail = Base64.getUrlEncoder().encodeToString(rawMessageBytes);

        Message forwardMessage = new Message();
        forwardMessage.setRaw(encodedEmail);

        gmailService.users().messages().send(userId, forwardMessage).execute();
        return SUCCESS;
    }catch (IOException | javax.mail.MessagingException e){
        log.error("expetion -> ",e);
        return FAILED;


    } catch (Exception e) {
        return FAILED;
    }

}

/**
 * Extracts the body of the original message, handling both plain text and HTML formats,
 * as well as attachments.
 *
 * @param originalMessage The original message from which to extract the body.
 * @return The extracted body text (either plain text or HTML).
 * @throws IOException If an I/O error occurs while processing the message.
 */
private String getOriginalMessageBody(Message originalMessage) throws IOException {
    log.info("inside getOriginalMessageBody");
    StringBuilder bodyBuilder = new StringBuilder();
    MessagePart payload = originalMessage.getPayload();
    extractBodyFromPart(payload, bodyBuilder);
    return bodyBuilder.toString();
}

/**
 * Recursively extracts the body from a MessagePart.
 *
 * @param part The MessagePart from which to extract the body.
 * @param bodyBuilder A StringBuilder to append the extracted body content.
 */
private void extractBodyFromPart(MessagePart part, StringBuilder bodyBuilder) {
    String mimeType = part.getMimeType();
    log.info("inside extractBodyFromPart");
    try {
        if (mimeType.contains("multipart")) {
            List<MessagePart> parts = part.getParts();
            for (MessagePart subPart : parts) {
                extractBodyFromPart(subPart, bodyBuilder);
            }
        } else {
            if (part.getBody() != null && part.getBody().getData() != null) {
                byte[] decodedBytes = Base64.getDecoder().decode(part.getBody().getData().replace('-', '+').replace('_', '/'));
                String body = new String(decodedBytes, StandardCharsets.UTF_8);
                if ("text/html".equalsIgnoreCase(mimeType)) {
                    log.info("inside html");
                    bodyBuilder.append(body); // HTML content
                } else if ("text/plain".equalsIgnoreCase(mimeType)) {
                    log.info("inside text/plain");
//                        bodyBuilder.append("<pre>").append(body).append("</pre>"); // Plain text
                }
            }
        }
    }catch (Exception e){
        log.error("There is an exeption -> ",e);
    }
}

    public String markMessageReadUnread(String email, String messageId, boolean markAsRead) {
        try {
            Gmail gmailService = getGmailService(email);
            if (messageId != null) {
                ModifyMessageRequest mods = new ModifyMessageRequest().setRemoveLabelIds(
                        markAsRead ? Collections.singletonList("UNREAD") : new ArrayList<>()
                ).setAddLabelIds(
                        markAsRead ? new ArrayList<>() : Collections.singletonList("UNREAD")
                );
                Message message = gmailService.users().messages().modify(email, messageId, mods).execute();
                log.info("Message with ID {} marked as {}", messageId, markAsRead ? "read" : "unread");
                return SUCCESS;
            } else {
                log.warn("No message found with internetMessageId {}", messageId);
                return FAILED;
            }
        } catch (Exception e) {
            log.error("Error marking Gmail message as {}: {}", markAsRead ? "read" : "unread", e.getMessage());
            log.error("Error marking Gmail message", e.getMessage(), getStackTraceAsString(e), null, null, "ERROR_MARKING_GMAIL_MESSAGE", email, null, null, null, null, null, null);
            return FAILED;
        }
    }




}
