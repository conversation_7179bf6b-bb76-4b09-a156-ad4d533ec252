package com.enttribe.emailagent.integration.impl;

import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.utils.EmailConstants;
import com.enttribe.emailagent.utils.GmailUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import java.net.URI;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * The type Gmail graph integration.
 *  <AUTHOR> Geetey
 */
@Service
@Slf4j
public class GmailGraphIntegration {
String token="eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImUyS2pDcVE5Y2hjdVIrc0UrQzJidGFVMVhrMD0iLCJ4NXQiOiJlMktqQ3FROWNoY3VSK3NFK0MyYnRhVTFYazA9Iiwibm9uY2UiOiJRdFpsUEtveGlLLXd6WWdWczFHWXNyNVBVQUpWOVlFdGhyS242MUtmV1YwaV9vZEVGUXZDR1N6Y0lOWWRyOXhEdTNQSG9qY1dGQWxqbVFwZEJ1ZnVmeUNERmpySFFVRlV3Zk1EenQ0WWc2QlZuNmFlTTlYTi1XS1pDcjhNQTl3czJ0OFpLOE5DTmxOQkRiTFNhUjlQalR2bmk2VUhKa2EyRGI4STcza3ZkN3MiLCJpc3Nsb2MiOiJQVVpQUjAxTUI1MTQ0Iiwic3JzbiI6NjM4NjE2NDY1ODI3MTQ3MzY0fQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QrU1WMX--SE20uwlwbCz5ejINY8XR_020vPsAsOnlfjybZrawaRXmG7gr_YBq9QFR6xoMbzqvfn_lr3bU1dYEAXOBFSP37vzQB_eCsAMurUXETq-Oh1eIedcq-Apoe5nKIDuqDzjMmfcB7rhO5rM4nHKjDAUzCMkDiipg3wl0JKl4u44313CK2RR-hJ8mSETHK5wz_J0CWHGm6wnYL3hhRgsb2k462BC-B1gsQymKscrOKZh6_xu098oTPZvD5mcsQDthI3ikO_6pwicAR6pzztO9tlNAhqb1-7bovxjMSxMRW2V8_zdPVGffo8uJVTijNrTgc59LDPC51QbJgGzaA";

    /**
     * Gets gmail event by graph.
     *
     * @param email     the email
     * @param messageId the message id
     * @return the gmail event by graph
     */
    public List<EventDto> getGmailEventByGraph(String email, String messageId) {
        log.debug("Inside @method getEventDetailsByMeetingRequest. @param: email -> {}, messageId -> {}", email, messageId);
        try {
            email="<EMAIL>";
            messageId="AQMkAGM4YzkwMAItOTkAMmEtNDViMy0wMAItMDAKAEYAAAOHL4YmFVbfR6Mt9smMURKiBwAXB21Epp6dR54QKxJVhXXIAAACAQkAAAAXB21Epp6dR54QKxJVhXXIAAAABAMUNQAAAA==";
            URIBuilder uriBuilder = new URIBuilder(String.format("https://outlook.office365.com/api/v2.0/users/%s/messages/%s", email, messageId));
            URI uri = uriBuilder.build();
            String url = uri.toString();
            log.debug("URL to get event message: {}", url);
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet request = new HttpGet(url);
                request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + token);
                request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
                // Execute the request
                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                        String responseBody = EntityUtils.toString(entity);
                        log.info("responseBody is {}",responseBody);
                        JSONObject emailJson = new JSONObject(responseBody);
                        if (emailJson.has("meetingMessageType") || emailJson.has("meetingRequestType")) {
                            String subject = emailJson.getString("subject");
                            String startDateTime = emailJson.getJSONObject("startDateTime").getString("dateTime");
                            String endDateTime = emailJson.getJSONObject("endDateTime").getString("dateTime");
                            return getEventDetailsBySubjectAndTime(email, subject, startDateTime, endDateTime);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error while fetching calendar events", e);
            throw new BusinessException(String.format("Unable to fetch calendar event for email: %s with messageId: %s", email, messageId));
        }
        return new ArrayList<>();
    }
    public List<EventDto> getEventDetailsBySubjectAndTime(String email, String subject, String startDateTime, String endDateTime) {
        log.debug("Fetching event details for subject: {}, start: {}, end: {}", subject, startDateTime, endDateTime);

        try {
            // Build the URI to search for the event in the user's calendar using subject and time filter
            URIBuilder uriBuilder = new URIBuilder(String.format("https://outlook.office365.com/api/v2.0/users/%s/calendar/events", email));

            // Add filters for time, subject, and location
            uriBuilder.addParameter("$filter", String.format("(start/dateTime ge '%s' and end/dateTime le '%s' and subject eq '%s')", startDateTime, endDateTime, subject));
            URI uri = uriBuilder.build();
            String url = uri.toString();
            log.debug("URL to fetch event details: {}", url);

            // Create an HttpClient instance and send a GET request
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet request = new HttpGet(url);
                request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + token);
                request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

                // Execute the request
                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                        String responseBody = EntityUtils.toString(entity);
                        return convertJsonToEventDto(responseBody); // Convert response to EventDto list
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error while fetching event details", e);
            throw new BusinessException(String.format("Unable to fetch event details for subject: %s", subject));
        }
        return new ArrayList<>();
    }

    private List<EventDto> convertJsonToEventDto(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);

        JSONArray messages = jsonObject.getJSONArray(EmailConstants.VALUE);

        List<EventDto> resultList = new ArrayList<>();

        for (int i = 0; i < messages.length(); i++) {
            JSONObject message = messages.getJSONObject(i);

            EventDto eventDto = getEventDto(message);

            resultList.add(eventDto);
        }
        return resultList;
    }
    private EventDto getEventDto(JSONObject message) {
        String id = message.getString("id");
        String organizer = null;
        if (message.getJSONObject("organizer").getJSONObject(EmailConstants.EMAIL_ADDRESS).has(EmailConstants.ADDRESS)) {
            organizer = message.getJSONObject("organizer").getJSONObject(EmailConstants.EMAIL_ADDRESS).getString(EmailConstants.ADDRESS);
        }
        List<String> attendees = getRecipients(message, "attendees");
        String subject = null;
        try {
            subject = message.getString(EmailConstants.SUBJECT);
        } catch (JSONException e) {
            subject = "";
        }
        JSONObject body = message.getJSONObject("body");
        String bodyPreview = body.getString(EmailConstants.CONTENT);
        String joinUrl = null;
        if (message.getBoolean("isOnlineMeeting")) {
            joinUrl = message.getJSONObject("onlineMeeting").getString("joinUrl");
        }
        Boolean hasAttachments = message.getBoolean("hasAttachments");

        Date meetingStartTime = getDateFromJSONObject(message, "start");
        Date meetingEndTime = getDateFromJSONObject(message, "end");
        // Assuming 'createdDateTime' is in ISO 8601 format
        Date createdDateTime = Date.from(Instant.parse(message.getString("createdDateTime")));
        Date lastModifiedDateTime = Date.from(Instant.parse(message.getString("lastModifiedDateTime")));
        String accepted = message.getJSONObject("responseStatus").getString("response");

        EventDto eventDto = new EventDto();
        eventDto.setId(id);
        eventDto.setOrganizer(organizer);
        eventDto.setAttendees(attendees);
        eventDto.setSubject(subject);
        eventDto.setAccepted(accepted);
        eventDto.setBodyPreview(bodyPreview);
        eventDto.setJoinUrl(joinUrl);
        eventDto.setHasAttachments(hasAttachments);
        eventDto.setMeetingStartTime(meetingStartTime);
        eventDto.setMeetingEndTime(meetingEndTime);
        eventDto.setCreatedDateTime(createdDateTime);
        eventDto.setLastModifiedDateTime(lastModifiedDateTime);
        return eventDto;
    }
    private Date getDateFromJSONObject(JSONObject jsonObject, String key) {
        Date date = null;
        if (jsonObject.has(key)) {
            String dueDateTime = jsonObject.getJSONObject(key).getString(EmailConstants.DATE_TIME);
            String timeZone = jsonObject.getJSONObject(key).getString("timeZone");
            date = GmailUtils.parseDateFromString(dueDateTime, timeZone);
        }
        return date;
    }

    private List<String> getRecipients(JSONObject message, String recipientType) {
        JSONArray toRecipientsArray = message.getJSONArray(recipientType);
        List<String> toRecipients = new ArrayList<>();
        for (int j = 0; j < toRecipientsArray.length(); j++) {
            String recipientAddress = null;
            if (toRecipientsArray.getJSONObject(j).getJSONObject(EmailConstants.EMAIL_ADDRESS).has(EmailConstants.ADDRESS)) {
                recipientAddress = toRecipientsArray.getJSONObject(j).getJSONObject(EmailConstants.EMAIL_ADDRESS).getString(EmailConstants.ADDRESS);
            }
            toRecipients.add(recipientAddress);
        }
        return toRecipients;
    }
}
