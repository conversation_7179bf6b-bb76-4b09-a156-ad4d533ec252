package com.enttribe.emailagent.integration;

import com.enttribe.emailagent.ai.dto.meeting.AvailableTimeSlots;
import com.enttribe.emailagent.dto.*;
import com.enttribe.emailagent.entity.UserMailAttachment;
import com.enttribe.emailagent.integration.impl.GmailGraphIntegration;
import com.enttribe.emailagent.wrapper.*;
import com.google.api.services.calendar.model.Event;
import com.google.api.services.calendar.model.Events;
import com.google.api.services.gmail.model.*;
import jakarta.mail.MessagingException;
import org.springframework.core.io.InputStreamResource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * The interface Gmail integration.
 *  <AUTHOR>
 */
public interface GmailIntegration  {
    UserEmailResponseDto pollGmailInbox(String emailId);
    UserEmailResponseDto pollGmailInbox(String userEmail,String time);
    UserEmailResponseDto pollGmailInbox();
    List<UserMailAttachment> summarizeMailAttachment(String conversationId, String emailId, String messageId);
    EventDto fetchAttachmentBtId(String emailId, String messageId, String attachmentId);
    Message getMessageById(String messageId, String emailId);
    MessageWrapper getMessageWrapperById(String messageId);
    ListMessagesResponse getMessages(String emailId);
    UserEmailDto getMessageById(String messageId);
    UserEmailDto getMessageByInternetMessageId(String internetMessageId);
    Boolean checkMessageIsEvent(String email, String messageId);

    UserEmailDto getMessageByIdAndFolderName(String messageId,String folderName);

    UserEmailDto getMessageByInternetMessageId(String internetMessageId, String folderName);
    List<InputStreamResource> getAttachmentByMessageId(String emailId, String messageId,String conversationId) throws IOException;
    InputStreamResource getAttachmentById(String emailId,String messageId,String attachmentId) throws IOException;
    Boolean flagEmailWithCustomLabel(String userId, String messageId, String labelName);
    Boolean starEmail(String userId, String messageId);
    Boolean starEmail(String userId, Integer databaseId);
    Boolean unstarEmail(String userId, String messageId);
    Boolean unstarEmail(String userId, Integer databaseId);
    List<Message> pullEmailsFromFolder(String userId, String folderName) throws IOException;
    List<Meeting> getAvailableMeetingSlots(List<String> emails, String startDateTime, String endDateTime, int slotDuration);
    String getLogs();
    UserEmailResponseDto pollGmailFolderWise(String userEmail,String time,String folderId);
    Map<String,String> rescheduleEvent(String calendarId, String eventId, String newStartDateTime, String newEndDateTime);
    Map<String,String> rescheduleEvent(GmailMeetingWrapper gmailMeetingWrapper);
    Map<String,String> rescheduleEvent(String userId,Map<String, String> requestBody);
    Boolean checkLabelExists(String userId, String labelName) throws IOException;
    Message sendGmail() throws Exception;
    String replyAll(String userId, String messageId, String replyBody,Boolean isDraft);
    String createDraftReply(Map<String, String> requestBody,Boolean isReplyAll);
    String replyToEmail(String originalMessageId,String userId, String bodyText, List<String> attachmentPathList, Boolean isDraft) throws MessagingException, IOException, javax.mail.MessagingException;
    void printAllMessageIdsAndSubjects(String userId) throws IOException;
    Boolean starAndUnstarEmail(String userId, String messageId,String mode);
    Boolean starAndUnstarEmail(String userId, Integer databaseId,String mode);
    String createDraft(String userId, String subject, String bodyText, List<String> toRecipients, List<String> ccRecipients, List<String> bccRecipients, List<String> attachmentPathList, Boolean isDraft) throws Exception;
    String createDraft(Map<String, String> requestBody);
    List<EventDto> getCalendarEvents(String email, String startDateTime, String endDateTime);
    List<EventDto> getCalendarEventsByMessageId(String email,String messageId);
    String updatePriority(String messageId, String priority);
    String getFlagStatus(String userId, String messageId);
    String createMeetingEvent(GmailMeetingWrapper gmailMeetingWrapper);
    String createMeetingEvent(String userId,Map<String, Object> requestBody);
    Map<String,String> updateMeetingStatus(String meetingEventId, String status);
    List<EventDto> getGmailEventByGraph(String email, String messageId);
    Events getEvent(String userId);
    VacationSettings getVacationResponderSettings(String emailId);
    Map<String, Object> getAutoReplySettingsForUser(String userEmail);
    String setVacationResponder(String emailId,VacationSettings vacationSettings);
    String cancelAutoReply(String emailId);
    String setAutoReplyForUser(String userEmail, String internalReplyMessage, String externalReplyMessage, String scheduledStartDateTime, String scheduledEndDateTime, String timeZone);
    Event getEventById(String userId,String eventId);
    List<Label> getLabels(String userId);
    List<Map<String, String>> usersMailFolders(String userId);
    List<Event> getAllScheduledMeetings(String userId, String startDateTime, String endDateTime);
    List<AvailableTimeSlots> getAvailableMeetingSlotsV2(List<String> participants, String startTime, String endTime, int meetingDuration);
    List<Meeting> getAvailableMeetingSlotsOFF(List<String> emails, String startDateTime, String endDateTime, int slotDuration);
    AvailableSlots getAvailableSlotsAndConflict(List<String> emails, String startDateTime, String endDateTime, int slotDuration);
    List<OutOfOffice> getOutOfOfficeDetails(Map<String, List<Meeting>> result, Date start, Date end);
    String forwardEmail(String originalMessageId, String userId, String additionalBodyText, String toRecipients);
    boolean deleteMessageById(String emailId, String messageId, Boolean softDelete);

    List<UnReadEmail> getUnreadEmails(String email, String folderId);

    String markMessageReadUnread(String email, String messageId, boolean markAsRead);
}
