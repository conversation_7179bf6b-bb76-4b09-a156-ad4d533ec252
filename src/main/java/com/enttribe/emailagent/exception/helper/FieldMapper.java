package com.enttribe.emailagent.exception.helper;

import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import org.slf4j.Logger;
import java.lang.reflect.Field;
import java.util.*;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.*;

/**
 * The type Field mapper.
 *  <AUTHOR>
 */
public class FieldMapper {
    private static final Logger log = EmailAgentLogger.getLogger(FieldMapper.class);
    private static final Set<String> argumentFieldSet = new LinkedHashSet<>();
    static {
        argumentFieldSet.add(EXCEPTION_MESSAGE);
        argumentFieldSet.add(EXCEPTION_TRACE);
        argumentFieldSet.add(MESSAGE_ID);
        argumentFieldSet.add(INTERNET_MESSAGE_ID);
        argumentFieldSet.add(TYPE);
        argumentFieldSet.add(EMAIL);
        argumentFieldSet.add(EMAIL_SUBJECT);
        argumentFieldSet.add(CONVERSATION_ID);
        argumentFieldSet.add(ATTACHMENT);
        argumentFieldSet.add(USER_MAIL_ATTACHMENT);
        argumentFieldSet.add(INTENT);
        argumentFieldSet.add(ATTENDEES);
    }


    public static Map<String, Object> mapArgumentsToValues(String customMsg, Object... arguments) {
        Map<String, Object> argumentMap = new HashMap<>();
        try {
            argumentMap.put(CUSTOM_EXCEPTION_MESSAGE, customMsg);
            if (arguments.length <=12) log.info("Insufficient arguments provided");
            int index = 0;
            for (String key : argumentFieldSet) {
                Object value = arguments[index++];
               switch (key) {
                    case "attachment": {
                        if(value!=null) {
                            argumentMap.put(key, (value instanceof Boolean) ? value : Boolean.parseBoolean(value.toString()));
                        }
                        break;
                    }
                    case "userMailAttachment":
                        // Assuming UserMailAttachment is a class that should be resolved or cast
                        //   argumentMap.put(key, value instanceof UserMailAttachment ? value : null);
                        break;
                    default:
                        argumentMap.put(key, value);
                        break;
                }
            }
            return argumentMap;
        }catch (Exception e){
            e.printStackTrace();
            return argumentMap;
        }
    }
    public static FailureLogs createFailureLogsFromMap(Map<String, Object> argumentMap) {
        try {
            FailureLogs failureLogs = new FailureLogs();

            for (Map.Entry<String, Object> entry : argumentMap.entrySet()) {
                String fieldName = entry.getKey();
                Object fieldValue = entry.getValue();
                Field field = FailureLogs.class.getDeclaredField(fieldName);
                field.setAccessible(true);
                if (field.getType().equals(Boolean.class) || field.getType().equals(boolean.class)) {
                    field.set(failureLogs, fieldValue instanceof Boolean ? fieldValue : Boolean.parseBoolean(fieldValue.toString()));
                }
                /*
                else if (field.getType().equals(UserMailAttachment.class)) {
                    field.set(failureLogs, fieldValue instanceof UserMailAttachment ? fieldValue : null);
                }
                 */
                else if (field.getType().equals(Date.class)) {
                    field.set(failureLogs, fieldValue instanceof Date ? fieldValue : null);
                } else {
                    field.set(failureLogs, fieldValue);
                }
            }

            return failureLogs;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("Error inside createFailureLogsFromMap");
            return null;
        }
    }

    public static FailureLogs createFailureLogsFromMap(String customMsg,Object... arguments) {
        return createFailureLogsFromMap(mapArgumentsToValues(customMsg,arguments));
    }
}
