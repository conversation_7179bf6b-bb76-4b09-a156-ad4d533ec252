package com.enttribe.emailagent.exception;

import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.exception.helper.SpringContextHelper;
import com.enttribe.emailagent.service.impl.FailureLogsServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.Marker;
import static com.enttribe.emailagent.exception.helper.FieldMapper.createFailureLogsFromMap;
import static com.enttribe.emailagent.utils.CommonUtils.isFailureLog;
import org.springframework.scheduling.annotation.Async;
/**
 * Custom Logger implementation that adds a prefix to log messages.
 * <p>
 * This class wraps around the SLF4J Logger and provides additional functionality to
 * prepend a custom prefix to log messages. It implements all methods from the SLF4J {@link Logger}
 * interface and delegates actual logging to the underlying SLF4J Logger instance.
 * </p>
 *
 * @see org.slf4j.Logger
 */
public class EmailAgentLogger implements Logger {
    private  Logger logger;
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);
    /**
     * Static method to obtain a logger instance for the specified class.
     *
     * @param clazz The class for which the logger is created.
     * @return A new {@code EmailAgentLogger} instance.
     */
    public static EmailAgentLogger getLogger(Class<?> clazz) {
        return new EmailAgentLogger(clazz);
    }

    /**
     * Constructs a new {@code EmailAgentLogger} instance.
     *
     * @param clazz The class for which the logger is created. This will be used to obtain
     *              the underlying SLF4J Logger instance.
     */
    public EmailAgentLogger(Class<?> clazz) {
        this.logger = LoggerFactory.getLogger(clazz);
    }

    public EmailAgentLogger() {
    }

    /**
     * Adds a custom prefix to the given log message.
     *
     * @param message The original log message.
     * @return The log message with the custom prefix added.
     */
    private String addPrefix(String message) {
        return "[Email Agent Logs] " + message;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void error(String msg) {
        logger.error(addPrefix(msg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void error(String format, Object arg) {
        logger.error(addPrefix(format), arg);
        executorService.submit(() -> checkAndIngestFailureLogs(format, arg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void error(String format, Object arg1, Object arg2) {
        logger.error(addPrefix(format), arg1, arg2);
        executorService.submit(() -> checkAndIngestFailureLogs(format, arg1));
        executorService.submit(() -> checkAndIngestFailureLogs(format, arg2));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void error(String format, Object... arguments) {
      //  logger.error(addPrefix(format), arguments);
        executorService.submit(() -> checkAndIngestFailureLogs(format, arguments));
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void error(String msg, Throwable t) {
        logger.error("msg {}, t {}", addPrefix(msg), t);
    }

    @Override
    public boolean isErrorEnabled(Marker marker) {
        return false;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void error(Marker marker, String msg) {
        logger.error("marker {}, msg {}", marker, addPrefix(msg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void error(Marker marker, String format, Object arg) {
        logger.error(marker, addPrefix(format), arg);
        executorService.submit(() -> checkAndIngestFailureLogs(format, arg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void error(Marker marker, String format, Object arg1, Object arg2) {
        logger.error(marker, addPrefix(format), arg1, arg2);
        executorService.submit(() -> checkAndIngestFailureLogs(format, arg1));
        executorService.submit(() -> checkAndIngestFailureLogs(format, arg2));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void error(Marker marker, String format, Object... arguments) {
        logger.error(marker, addPrefix(format), arguments);
        executorService.submit(() -> checkAndIngestFailureLogs(format, arguments));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void error(Marker marker, String msg, Throwable t) {
        logger.error("marker {}, msg {}, t {}",marker, addPrefix(msg), t);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void warn(String msg) {
        logger.warn("msg {}",addPrefix(msg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void warn(String format, Object arg) {
        logger.warn("format {}, arg  {}",addPrefix(format), arg);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void warn(String format, Object arg1, Object arg2) {
        logger.warn("format {}, arg1 {}, arg2 {}",addPrefix(format), arg1, arg2);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void warn(String format, Object... arguments) {
        logger.warn("format {},  arguments {}",addPrefix(format), arguments);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void warn(String msg, Throwable t) {
        logger.warn("msg {}, t {}",addPrefix(msg), t);
    }

    @Override
    public boolean isWarnEnabled(Marker marker) {
        return logger.isWarnEnabled(marker);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void warn(Marker marker, String msg) {
        logger.warn("marker {}, msg {}",marker, addPrefix(msg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void warn(Marker marker, String format, Object arg) {
        logger.warn("marker {},  format {}, arg {}",marker, addPrefix(format), arg);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void warn(Marker marker, String format, Object arg1, Object arg2) {
        logger.warn("marker{}, format  {}, arg1  {}, arg2 {}",marker, addPrefix(format), arg1, arg2);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void warn(Marker marker, String format, Object... arguments) {
        logger.warn("marker {} , format {}, arguments {}",marker, addPrefix(format), arguments);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void warn(Marker marker, String msg, Throwable t) {
        logger.warn("marker {}, msg  {}, t {}",marker, addPrefix(msg), t);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void info(String msg) {
        logger.info(addPrefix(msg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void info(String format, Object arg) {
        logger.info("format {},  arg {}",addPrefix(format), arg);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void info(String format, Object arg1, Object arg2) {
        logger.info(" format {}, arg1 {}, arg2 {}",addPrefix(format), arg1, arg2);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void info(String format, Object... arguments) {
        logger.info("format {}, arguments {}",addPrefix(format), arguments);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void info(String msg, Throwable t) {
        logger.info("msg {} , t  {}",addPrefix(msg), t);
    }

    @Override
    public boolean isInfoEnabled(Marker marker) {
        return logger.isInfoEnabled(marker);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void info(Marker marker, String msg) {
        logger.info("marker {}, msg  {}",marker, addPrefix(msg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void info(Marker marker, String format, Object arg) {
        logger.info("marker {}, format{}, arg {}",marker, addPrefix(format), arg);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void info(Marker marker, String format, Object arg1, Object arg2) {
        logger.info("marker{}, format {}, arg1 {}, arg2 {}",marker, addPrefix(format), arg1, arg2);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void info(Marker marker, String format, Object... arguments) {
        logger.info("marker {} , format {} , arguments {}",marker, addPrefix(format), arguments);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void info(Marker marker, String msg, Throwable t) {
        logger.info("marker {},msg  {}, t {}",marker, addPrefix(msg), t);
    }

    // Debug level logging methods

    /**
     * {@inheritDoc}
     */
    @Override
    public void debug(String msg) {
        logger.debug(addPrefix(msg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void debug(String format, Object arg) {
        logger.debug("format {}, arg {}",addPrefix(format), arg);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void debug(String format, Object arg1, Object arg2) {
        logger.debug("format {}, arg1 {}, arg2 {}",addPrefix(format), arg1, arg2);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void debug(String format, Object... arguments) {
        logger.debug("format {}, arguments {}",addPrefix(format), arguments);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void debug(String msg, Throwable t) {
        logger.debug("msg {} , t {}",addPrefix(msg), t);
    }

    @Override
    public boolean isDebugEnabled(Marker marker) {
        return logger.isDebugEnabled(marker);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void debug(Marker marker, String msg) {
        logger.debug("marker {}, msg  {}",marker, addPrefix(msg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void debug(Marker marker, String format, Object arg) {
        logger.debug("marker {},  format {}, arg {}",marker, addPrefix(format), arg);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void debug(Marker marker, String format, Object arg1, Object arg2) {
        logger.debug("marker {} , format{},  arg1 {}, arg2 {}",marker, addPrefix(format), arg1, arg2);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void debug(Marker marker, String format, Object... arguments) {
        logger.debug("marker {} , format {} , arguments {}",marker, addPrefix(format), arguments);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void debug(Marker marker, String msg, Throwable t) {
        logger.debug("marker {} , msg {}, t {}",marker, addPrefix(msg), t);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void trace(String msg) {
        logger.trace(addPrefix(msg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void trace(String format, Object arg) {
        logger.trace("format {}, arg {}",addPrefix(format), arg);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void trace(String format, Object arg1, Object arg2) {
        logger.trace("format {} , arg1 {} , arg2 {}",addPrefix(format), arg1, arg2);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void trace(String format, Object... arguments) {
        logger.trace("format  {} , arguments {}",addPrefix(format), arguments);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void trace(String msg, Throwable t) {
        logger.trace("msg {} , t {}",addPrefix(msg), t);
    }

    @Override
    public boolean isTraceEnabled(Marker marker) {
        return logger.isTraceEnabled(marker);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void trace(Marker marker, String msg) {
        logger.trace(" marker {} , msg {}",marker, addPrefix(msg));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void trace(Marker marker, String format, Object arg) {
        logger.trace("marker {} , format  {} , arg {}",marker, addPrefix(format), arg);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void trace(Marker marker, String format, Object arg1, Object arg2) {
        logger.trace(" marker {}, format  {} , arg1 {},  arg2 {}", marker, addPrefix(format), arg1, arg2);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void trace(Marker marker, String format, Object... arguments) {
        logger.trace("marker {}, format {},arguments {}",marker, addPrefix(format), arguments);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void trace(Marker marker, String msg, Throwable t) {
        logger.trace("marker {}, msg {}, t {}",marker, addPrefix(msg), t);
    }

    // Level enabled checks

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isErrorEnabled() {
        return logger.isErrorEnabled();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isWarnEnabled() {
        return logger.isWarnEnabled();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isInfoEnabled() {
        return logger.isInfoEnabled();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isDebugEnabled() {
        return logger.isDebugEnabled();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isTraceEnabled() {
        return logger.isTraceEnabled();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getName() {
        return logger.getName();
    }

    /**
     * Checks if any of the provided arguments are instances of {@link FailureLogs}.
     * If a {@link FailureLogs} instance is found, it updates the custom exception message
     * and ingests the logs. If no {@link FailureLogs} instance is found, it ingests logs
     * created from the arguments.
     *
     * @param format The format string for the log message.
     * @param arguments The arguments to be logged, which may include instances of {@link FailureLogs}.
     */
    private void checkAndIngestFailureLogs(String format, Object... arguments) {
        boolean hasFailureLog = false;
        for (Object arg : arguments) {
            if (isFailureLog(arg)) {
                hasFailureLog = true;
                FailureLogs failureLog = (FailureLogs) arg;
                failureLog.setCustomExceptionMessage(format);
                ingestLogs(failureLog);
                break;
            }
        }
        if (!hasFailureLog) {
            ingestLogs(createFailureLogsFromMap(format, arguments));
        }
    }


    /**
     * Checks if any of the provided arguments are instances of {@link FailureLogs}.
     * If a {@link FailureLogs} instance is found, it updates the custom exception message
     * and ingests the logs. If no {@link FailureLogs} instance is found, it ingests logs
     * created from the arguments.
     *
     * @param format The format string for the log message.
     * @param arguments The arguments to be logged, which may include instances of {@link FailureLogs}.
     */
    @Async
    private void checkAndIngestFailureLogs(String format, Object arguments) {
            if (isFailureLog(arguments)) {
                FailureLogs failureLog = (FailureLogs) arguments;
                failureLog.setCustomExceptionMessage(format);
                ingestLogs(failureLog);
            }
    }

    private void ingestLogs(FailureLogs failureLogs){
        if (failureLogs == null) return;
        try{
            FailureLogsServiceImpl failureLogsService = SpringContextHelper.getBean(FailureLogsServiceImpl.class);
            failureLogs.setErrorDate(new Date());
            failureLogsService.ingestFailureLog(failureLogs);
        }catch (Exception e){
            e.printStackTrace();
            logger.info("Error inside ingestFailureLog");
        }
    }
    public void sleepFor(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            // Handle the interruption if necessary
            Thread.currentThread().interrupt(); // Restore the interrupted status
            System.err.println("Thread was interrupted during sleep.");
        }
    }


}
