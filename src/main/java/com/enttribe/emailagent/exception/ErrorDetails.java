package com.enttribe.emailagent.exception;

import lombok.Data;

import java.util.Date;

/**
 * The type Error details.
 * <AUTHOR>
 */
@Data
public class ErrorDetails {
    private Date timestamp;
    private String message;
    private String details;

    public ErrorDetails(Date timestamp, String message, String details) {
        super();
        this.timestamp = timestamp;
        this.message = message;
        this.details = details;
    }

    // Getters and Setters
}