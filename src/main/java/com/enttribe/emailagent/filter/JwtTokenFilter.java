package com.enttribe.emailagent.filter;

import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.userinfo.UserContext;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * The type Jwt token filter.
 *  <AUTHOR>
 */
@Component
@Slf4j
public class JwtTokenFilter extends OncePerRequestFilter {

    @Autowired
    private EmailUserDao userDao;

    @Value("${org.admin.list}")
    private List<String> admins;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        // Extract token from header
        UserContext.setUser(null);
        String header = request.getHeader("Authorization");
        if (header == null || header.isBlank()) header = request.getHeader("access-token");

//        log.debug("\n\nHeader inside filter : {}\n\n", header);
        if (header != null && header.startsWith("Bearer ")) {
            String token = header.substring(7);

            // Validate and parse token (custom logic)
            try {
                log.debug("Inside try block");
                // Split the token into its parts
                String[] parts = token.split("\\.");
                // Decode the payload part (second part)
                String payload = new String(org.apache.commons.codec.binary.Base64.decodeBase64(parts[1]));
                // Parse the JSON payload
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> claims = objectMapper.readValue(payload, Map.class);

                // Set user info in custom object
                UserInfo userInfo = new UserInfo();
                String email = (String) claims.get("email");
                if (email == null) {
                    email = (String) claims.get("unique_name");
                }
                if (email == null) {
                    email = (String) claims.get("upn");
                }
                if (email == null) {
                    email = (String) claims.get("smtp");
                }
                if (email == null) {
                    email = (String) claims.get("preferred_username");
                }
                if (email == null) {
                    String map = (String) claims.get("appctx");
                    if (map != null) {
                        Map<String, Object> claims1 = objectMapper.readValue(map, Map.class);
                        email = (String) claims1.get("smtp");
                    }
                }
                if (email == null) {
                    email = (String) claims.get("sub");
                }

//                if (request.getRequestURI().contains("/admin/") && !admins.contains(email)) {
//                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
//                    response.getWriter().write("{\"error\":\"You are not allowed to access the API.\"}");
//                    response.setContentType("application/json");
//                    return;
//                }

                userInfo.setEmail(email);
                userInfo.setId(email);
                UserContext.setUser(userInfo);

            } catch (Exception e) {
                log.error("Error inside @method doFilterInternal. Exception message -> {}", e.getMessage());
            }
        }

        filterChain.doFilter(request, response);
    }
}
