package com.enttribe.emailagent.masking;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * The type Http client utlis.
 *  <AUTHOR> Sonsale
 */
@Slf4j
public class HttpClientUtlis {


	public static String sendPost(String body,String url) {
		log.debug("body is {}",body);
		HttpClient client = HttpClient.newHttpClient();
		HttpRequest request = HttpRequest.newBuilder()
	            .uri(URI.create(url))
	            .header("Content-Type", "application/json; utf-8")
	            .POST(HttpRequest.BodyPublishers.ofString(body))
	            .build();
	        
	        try {
	            // Send the request synchronously and receive the response
	            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
	            
	            // Print the response status code and body
	            log.debug("Response Code from analyzer is {}",response.statusCode());
	            if(response.statusCode()==200) {
	            	log.debug("response coming from analyzer is {}",response.body());
	            	return response.body();
	            }
	            else {
	            	log.error("Error getting response from analyzer {}",response.body());
	            	return null;
	            }
	        } catch (Exception e) {
	        	log.error("Exception occured while gettingresponse from analyzer",e);
	        	return null;
	        }
	}
	
}
