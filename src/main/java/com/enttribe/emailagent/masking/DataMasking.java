package com.enttribe.emailagent.masking;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * The type Data masking.
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataMasking {

    private static String analyzerUrl;
    private static String maskingUrl;
    private static String encryptedEntities;
    private static String encryptionKey;

    @Value("${analyzerUrl}")
    public void setAnalyzerUrl(String analyzerUrl) {
        DataMasking.analyzerUrl = analyzerUrl;
    }

    @Value("${maskingUrl}")
    public void setMaskingUrl(String maskingUrl) {
        DataMasking.maskingUrl = maskingUrl;
    }

    @Value("${encryptionKey}")
    public void setEncryptionKey(String encryptionKey) {
        DataMasking.encryptionKey = encryptionKey;
    }

    @Value("${encryptedEntities}")
    public void setEncryptedEntities(String encryptedEntities) {
        DataMasking.encryptedEntities = encryptedEntities;
    }

    /**
     * Analyze sensitive data json object.
     *
     * @param textToAnalyze the text to analyze
     * @return the json object
     */
    public static JSONObject analyzeSensitiveData(String textToAnalyze) {
        log.error("Entry inside method analyze text {}", textToAnalyze);
        try {
            JSONObject requestObject = new JSONObject();
            requestObject.put("text", textToAnalyze);
            requestObject.put("language", "en");
            String[] items = encryptedEntities.split(",");
            JSONArray entitiesArray = new JSONArray();
            for (String item : items) {
                entitiesArray.put(item.trim());
            }
            requestObject.put("entities", entitiesArray);
            String responseString = HttpClientUtlis.sendPost(requestObject.toString(), analyzerUrl);
            JSONArray responseArray = new JSONArray(responseString);
            log.debug("responseString is empty {} {} {}", responseString.isBlank(), responseString.isEmpty(), responseArray.length());
            if (!responseArray.isEmpty()) {
                JSONArray anonymizeObject = getAnonymizeObject(responseArray);
                if (!anonymizeObject.isEmpty()) {
                    JSONObject annonymizerObject = new JSONObject();
                    annonymizerObject.put("text", textToAnalyze);
                    annonymizerObject.put("analyzer_results", anonymizeObject);
                    JSONObject defaultObject = new JSONObject();
                    JSONObject tempObject = new JSONObject();
                    tempObject.put("type", "encrypt");
                    tempObject.put("key", encryptionKey);
                    defaultObject.put("DEFAULT", tempObject);
                    annonymizerObject.put("anonymizers", defaultObject);
                    log.debug("annonymizerObject is {}", annonymizerObject);
                    String maskData = HttpClientUtlis.sendPost(annonymizerObject.toString(), maskingUrl + "anonymize");
                    if (maskData != null) {
                        return new JSONObject(maskData);
                    }
                }
            } else {
                return new JSONObject().put("text", textToAnalyze);
            }
        } catch (Exception e) {
            log.error("Error inside method analyzeSensitiveData", e);
        }
        return null;
    }

    /**
     * Gets anonymize object.
     *
     * @param responseJsonArray the response json array
     * @return the anonymize object
     */
    public static JSONArray getAnonymizeObject(JSONArray responseJsonArray) {
        JSONArray anonymizeObject = new JSONArray();
        for (int i = 0; i < responseJsonArray.length(); i++) {
            JSONObject analyzerObject = responseJsonArray.getJSONObject(i);
            JSONObject tempObject = new JSONObject();
            if (analyzerObject.has("start")) {
                tempObject.put("start", analyzerObject.optInt("start"));
            }
            if (analyzerObject.has("end")) {
                tempObject.put("end", analyzerObject.optInt("end"));
            }
            if (analyzerObject.has("entity_type")) {
                tempObject.put("entity_type", analyzerObject.optString("entity_type"));
            }
            if (analyzerObject.has("score")) {
                tempObject.put("score", analyzerObject.optDouble("score"));
            }
            anonymizeObject.put(tempObject);
        }
        return anonymizeObject;
    }

    /**
     * Gets de anonymize object.
     *
     * @param responseJsonArray the response json array
     * @return the de anonymize object
     */
    public static JSONObject getDeAnonymizeObject(JSONArray responseJsonArray) {
        JSONObject finalObject = new JSONObject();
        JSONObject tempObject = new JSONObject();
        tempObject.put("type", "decrypt");
        tempObject.put("key", encryptionKey);
        JSONObject denonymizerObject = new JSONObject();
        JSONArray annonymizeObject = new JSONArray();
        for (int i = 0; i < responseJsonArray.length(); i++) {
            JSONObject analyzerObject = responseJsonArray.getJSONObject(i);
            JSONObject object = new JSONObject();
            if (analyzerObject.has("entity_type")) {
                String entityType = analyzerObject.optString("entity_type");
                denonymizerObject.put(entityType, tempObject);
                object.put("entity_type", entityType);
            }
            if (analyzerObject.has("start")) {
                object.put("start", analyzerObject.optInt("start"));
            }
            if (analyzerObject.has("end")) {
                object.put("end", analyzerObject.optInt("end"));
            }
            annonymizeObject.put(object);
        }
        finalObject.put("deanonymizers", denonymizerObject);
        finalObject.put("anonymizer_results", annonymizeObject);
        log.debug("finalObject is {}", finalObject);
        return finalObject;
    }

    /**
     * Decrypt sensitive data .
     *
     * @param text the text
     * @param body the body
     * @return the string
     */
    public static String decryptSensitiveData(String text, String body) {
        log.debug("text to decrypt is {} with body {}", text, body);
        try {
            JSONObject requestObject = new JSONObject(body);
            requestObject.put("text", text);
            log.debug("requestBody is {}", requestObject);
            String responseString = HttpClientUtlis.sendPost(requestObject.toString(), maskingUrl + "deanonymize");
            if (responseString != null) {
                JSONObject responseJson = new JSONObject(responseString);
                return responseJson.getString("text");
            }
        } catch (Exception e) {
            log.error("Error inside method decryptSensitiveData", e);
        }
        return null;
    }
}
