package com.enttribe.emailagent.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * The type Dynamic date serializer.
 * <AUTHOR> Dangi
 */
public class DynamicDateSerializer extends JsonSerializer<Date> {

    @Override
    public void serialize(Date value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // Get the timezone from the request context
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        String timezone = null;
        if (attributes != null && attributes.getRequest().getAttribute("timeZone") != null) {
            timezone = attributes.getRequest().getAttribute("timeZone").toString();
        }

        // If timezone is null
        if (timezone == null) {
            // Ensure Jackson's default ISO-8601 format is used instead of a timestamp
            SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
            isoFormat.setTimeZone(TimeZone.getTimeZone("UTC")); // You can adjust the default timezone as needed
            gen.writeString(isoFormat.format(value));
            return;
        }

        // Default pattern
        String pattern = "yyyy-MM-dd'T'HH:mm:ss";
        SimpleDateFormat formatter = new SimpleDateFormat(pattern);
        formatter.setTimeZone(TimeZone.getTimeZone(timezone));

        gen.writeString(formatter.format(value));
    }
}
