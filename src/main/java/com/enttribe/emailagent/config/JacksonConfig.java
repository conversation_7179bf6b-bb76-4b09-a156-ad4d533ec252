package com.enttribe.emailagent.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Date;

@Configuration
public class JacksonConfig {

    /**
     * Object mapper object mapper.
     *
     * @return the object mapper
     * <AUTHOR> Dangi
     */
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // Custom module for `java.util.Date`
        SimpleModule customDateModule = new SimpleModule();
        customDateModule.addSerializer(Date.class, new DynamicDateSerializer());

        // Register the custom module for `Date`
        mapper.registerModule(customDateModule);

        // Register the JavaTimeModule for `LocalDateTime`, `LocalDate`, etc.
        mapper.registerModule(new JavaTimeModule());

        // Optional: Handle serialization/deserialization of ISO-8601 for Java 8 date/time
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        return mapper;
    }

}
