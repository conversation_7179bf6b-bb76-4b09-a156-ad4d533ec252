package com.enttribe.emailagent;


import com.enttribe.emailagent.utils.EmailConstants;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import jakarta.mail.search.AndTerm;
import jakarta.mail.search.ComparisonTerm;
import jakarta.mail.search.ReceivedDateTerm;
import jakarta.mail.search.SearchTerm;
import lombok.extern.slf4j.Slf4j;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * The type Gmail polling.
 *
 *  <AUTHOR>
 */
@Slf4j
public class GmailPolling {
    public static void main(String[] args) {
    	String host = "imap.gmail.com";
        String username = "<EMAIL>";
        String password = "VisionWaves@123";

        Date startDate = new Date(2024 - 1900, 4, 20); 
        Date endDate = new Date(2024 - 1900, 4, 22);

        generateEmailObjectives(host, username, password, startDate, endDate);        
    }

    public static void pollGmailInboxGroup(String host, String user, String password, Date startDate, Date endDate) {
        try {
            Properties properties = new Properties();
            properties.put(EmailConstants.MAIL_IMAP_HOST, host);
            properties.put(EmailConstants.MAIL_IMAP_PORT, "993");
            properties.put(EmailConstants.MAIL_IMAP_STARTTLS_ENABLE, "true");
            properties.put(EmailConstants.MAIL_IMAP_SSL_TRUST, host);

            Session emailSession = Session.getDefaultInstance(properties);
            Store store = emailSession.getStore(EmailConstants.IMAPS);
            store.connect(host, user, password);

            Folder emailFolder = store.getFolder(EmailConstants.INBOX);
            emailFolder.open(Folder.READ_ONLY);

            SearchTerm startTerm = new ReceivedDateTerm(ComparisonTerm.GE, startDate);
            SearchTerm endTerm = new ReceivedDateTerm(ComparisonTerm.LE, endDate);
            SearchTerm dateRangeTerm = new AndTerm(startTerm, endTerm);

            Message[] messages = emailFolder.search(dateRangeTerm);
            Map<String, List<Message>> groupedEmails = Arrays.stream(messages)
                    .collect(Collectors.groupingBy(GmailPolling::getSubject));
        
            JsonObject jsonObject = new JsonObject();
            groupedEmails.forEach((subject, messageList) -> {
                JsonArray jsonArray = new JsonArray();
                Collections.reverse(messageList);
                for (int i = 0; i < messageList.size(); i++) {
                    Message message = messageList.get(i);
                    try {
                        JsonObject emailJson = new JsonObject();
                        emailJson.addProperty("sequence", i + 1);
                        emailJson.addProperty("email", getTextFromMessage(message));
                        jsonArray.add(emailJson);
                    } catch (IOException | MessagingException e) {
                        e.printStackTrace();
                    }
                }
                jsonObject.add(subject, jsonArray);
            });
            emailFolder.close(false);
            store.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String getSubject(Message message) {
        try {
            return message.getSubject();
        } catch (MessagingException e) {
            e.printStackTrace();
            return "";
        }
    }
    

    public static void pollGmailInbox(String host, String user, String password) {
        try {
            Properties properties = new Properties();
            properties.put(EmailConstants.MAIL_IMAP_HOST, host);
            properties.put(EmailConstants.MAIL_IMAP_PORT, "993");
            properties.put(EmailConstants.MAIL_IMAP_STARTTLS_ENABLE, "true");
            properties.put(EmailConstants.MAIL_IMAP_SSL_TRUST, host);

            Session emailSession = Session.getDefaultInstance(properties);

            Store store = emailSession.getStore(EmailConstants.IMAPS);
            store.connect(host, user, password);

            Folder emailFolder = store.getFolder(EmailConstants.INBOX);
            emailFolder.open(Folder.READ_ONLY);

            Message[] messages = emailFolder.getMessages();

            for (int i = 0; i < messages.length; i++) {
                Message message = messages[i];
            }
            emailFolder.close(false);
            store.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public static String generateEmailObjectives(String host, String user, String password, Date startDate, Date endDate) {
    	
    	 try {
             Properties properties = new Properties();
             properties.put(EmailConstants.MAIL_IMAP_HOST, host);
             properties.put(EmailConstants.MAIL_IMAP_PORT, "993");
             properties.put(EmailConstants.MAIL_IMAP_STARTTLS_ENABLE, "true");
             properties.put(EmailConstants.MAIL_IMAP_SSL_TRUST, host);

             Session emailSession = Session.getDefaultInstance(properties);

             Store store = emailSession.getStore(EmailConstants.IMAPS);
             store.connect(host, user, password);

             Folder emailFolder = store.getFolder(EmailConstants.INBOX);
             emailFolder.open(Folder.READ_ONLY);

             SearchTerm startTerm = new ReceivedDateTerm(ComparisonTerm.GE, startDate);
             SearchTerm endTerm = new ReceivedDateTerm(ComparisonTerm.LE, endDate);
             SearchTerm dateRangeTerm = new AndTerm(startTerm, endTerm);

             // Fetch messages from the server with the search term
             Message[] messages = emailFolder.search(dateRangeTerm);
             log.debug("Total Messages: {}", messages.length);

             // Sort messages in reverse order to get the latest emails first
             for (int i = messages.length - 1; i >= 0; i--) {
                 Message message = messages[i];
                String airesponse =  AIClient.generateAiResponse(getTextFromMessage(message), "",message.getFrom()[0].toString(),message.getReplyTo()[0].toString(),"Category", null, null);
               String objectiveResponse = AIClient.parseResponse(airesponse);
               String objectives=  parseAIResponse(objectiveResponse);
               break;
             }

             // Close the folder and store objects
             emailFolder.close(false);
             store.close();

         } catch (Exception e) {
             e.printStackTrace();
         }
     
    	
    	return null;
    	
    }
    
    
    public static String parseAIResponse(String response) throws JSONException {
        JSONArray jsonArray = new JSONArray(response);
        StringBuilder objectives = new StringBuilder();

        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String objective = jsonObject.optString("Objectives");
            if (!objective.isEmpty()) {
                if (objectives.length() > 0) {
                    objectives.append(", ");
                }
                objectives.append(objective);
            }
        }

        return objectives.toString();
    }
    
    
    public static void pollGmailInbox(String host, String user, String password, Date startDate, Date endDate) {
        try {
            Properties properties = new Properties();
            properties.put(EmailConstants.MAIL_IMAP_HOST, host);
            properties.put(EmailConstants.MAIL_IMAP_PORT, "993");
            properties.put(EmailConstants.MAIL_IMAP_STARTTLS_ENABLE, "true");
            properties.put(EmailConstants.MAIL_IMAP_SSL_TRUST, host);

            Session emailSession = Session.getDefaultInstance(properties);

            Store store = emailSession.getStore(EmailConstants.IMAPS);
            store.connect(host, user, password);

            Folder emailFolder = store.getFolder(EmailConstants.INBOX);
            emailFolder.open(Folder.READ_ONLY);

            SearchTerm startTerm = new ReceivedDateTerm(ComparisonTerm.GE, startDate);
            SearchTerm endTerm = new ReceivedDateTerm(ComparisonTerm.LE, endDate);
            SearchTerm dateRangeTerm = new AndTerm(startTerm, endTerm);

            Message[] messages = emailFolder.search(dateRangeTerm);

            for (int i = messages.length - 1; i >= 0; i--) {
                Message message = messages[i];
               String airesponse =  AIClient.generateAiResponse(getTextFromMessage(message), "",message.getFrom()[0].toString(),message.getReplyTo()[0].toString(),"", null, null);
              String draftres= AIClient.parseResponse(airesponse);
               
               break;
            }
            emailFolder.close(false);
            store.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static String getTextFromMessage(Message message) throws IOException, MessagingException {
        String result = "";
        if (message.isMimeType("text/plain")) {
            result = message.getContent().toString();
        } else if (message.isMimeType("multipart/*")) {
            MimeMultipart mimeMultipart = (MimeMultipart) message.getContent();
            result = getTextFromMimeMultipart(mimeMultipart);
        }
        return result;
    }

    public static String getTextFromMimeMultipart(MimeMultipart mimeMultipart) throws IOException, MessagingException {
        StringBuilder result = new StringBuilder();
        int count = mimeMultipart.getCount();
        
        for (int i = 0; i < count; i++) {
            BodyPart bodyPart = mimeMultipart.getBodyPart(i);
            if (bodyPart.isMimeType("text/plain")) {
                result.append("\n").append(bodyPart.getContent());
                break;
            } else if (bodyPart.isMimeType("text/html")) {
                String html = (String) bodyPart.getContent();
                result.append("\n").append(org.jsoup.Jsoup.parse(html).text());
            } else if (bodyPart.getContent() instanceof MimeMultipart mimePart) {
                result.append(getTextFromMimeMultipart(mimePart));
            }
        }
        return result.toString();
    }

    public static void writeDraftResponse(Session session, String user, String password, Message originalMessage, String draftContent) {
        try {
            // Set properties for SMTP
            Properties properties = new Properties();
            properties.put("mail.smtp.auth", "true");
            properties.put("mail.smtp.starttls.enable", "true");
            properties.put("mail.smtp.host", "smtp.gmail.com");
            properties.put("mail.smtp.port", "587");

            // Get the session object
            Session smtpSession = Session.getInstance(properties, new Authenticator() {
                
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(user, password);
                }
            });

            // Create a new draft message
            MimeMessage draftMessage = new MimeMessage(smtpSession);
            draftMessage.setFrom(new InternetAddress(user));
            draftMessage.setRecipients(Message.RecipientType.TO, originalMessage.getReplyTo());
            draftMessage.setSubject("Re: " + originalMessage.getSubject());
            draftMessage.setText(draftContent);

            // Save the draft to the "Drafts" folder
            Store store = smtpSession.getStore(EmailConstants.IMAPS);
            store.connect("imap.gmail.com", user, password);
            Folder draftsFolder = store.getFolder("[Gmail]/Drafts");
            draftsFolder.open(Folder.READ_WRITE);
            draftMessage.setFlag(Flags.Flag.DRAFT, true);
            draftsFolder.appendMessages(new Message[]{draftMessage});
            draftsFolder.close(false);
            store.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
}
