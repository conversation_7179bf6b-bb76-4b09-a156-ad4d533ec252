package com.enttribe.emailagent;

import com.enttribe.emailagent.utils.EmailConstants;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.Instant;

/**
 * The type Ai client.
 *
 *  <AUTHOR> <PERSON>ak
 *
 */
@Slf4j
@Component
public class AIClient {
    private AIClient() {}

    private static boolean auditEnabled;

    @Value("${audit.enabled}")
    public static void setAuditEnabled(Boolean auditEnabled) {
        AIClient.auditEnabled = auditEnabled;
    }


    private static final String API_URL = "https://api.together.xyz/api/inference";
    private static final String AUTH_TOKEN = "49e78184beb55456026c82e8f5a2550d7cd974bcdfc5a272a10ea926dabe8994";


    
    public static String getAiResponse(String userPrompt,String systemPrompt) throws Exception {
        Instant start;
        HttpResponse<String> response;
        try (HttpClient client = HttpClient.newHttpClient()) {

            ObjectMapper mapper = new ObjectMapper();
            ObjectNode requestBody = mapper.createObjectNode();
            requestBody.put("model", "mistralai/Mixtral-8x22B-Instruct-v0.1");
            requestBody.put("max_tokens", 750);
            requestBody.put("prompt", "");
            requestBody.put("request_type", "language-model-inference");
            requestBody.put("temperature", 0.3);
            requestBody.put("top_p", 0.7);
            requestBody.put("top_k", 50);
            requestBody.put("repetition_penalty", 1);
            requestBody.putArray("stop").add("<|eot_id|>");
            requestBody.put("negative_prompt", "");
            requestBody.put("type", "chat");
            requestBody.put("prompt_format_string", "<human>: {prompt}\\n<bot>:");

            ObjectNode systemMessage = mapper.createObjectNode();
            systemMessage.put(EmailConstants.PROMPT_CONTENT, systemPrompt);
            systemMessage.put("role", "system");

            ObjectNode userMessage = mapper.createObjectNode();
            userMessage.put(EmailConstants.PROMPT_CONTENT, userPrompt);
            userMessage.put("role", "user");

            ArrayNode messages = mapper.createArrayNode();
            messages.add(systemMessage);
            messages.add(userMessage);

            requestBody.set("messages", messages);

            String requestBodyString = mapper.writeValueAsString(requestBody);
           log.debug("Request Body is {} " ,requestBodyString);

            start = Instant.now();

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(new URI(API_URL))
                    .header("Accept", EmailConstants.HTTP_JSON_HEADER)
                    .header("Content-Type", EmailConstants.HTTP_JSON_HEADER)
                    .header("Authorization", "Bearer " + AUTH_TOKEN)
                    .POST(HttpRequest.BodyPublishers.ofString(requestBodyString))
                    .build();

            response = client.send(request, HttpResponse.BodyHandlers.ofString());
        }

        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);
        long millis = duration.toMillis();

        return parseResponse(response.body());
    }
    
    
    public static String generateAiResponse(String emailContent, String conversationHistory,String fromEmail,String toEmail,String type, String extra1, String extra2) throws Exception {
        String systemPrompt;
        String userPrompt;
        Instant start;
        HttpResponse<String> response;
        try (HttpClient client = HttpClient.newHttpClient()) {
            systemPrompt = VelocityTemplateUtil.generateSystemPrompt(type);
            userPrompt = VelocityTemplateUtil.generateUserPrompt(emailContent, conversationHistory, fromEmail, toEmail, type, extra1, extra2);
            // Use Jackson to create the JSON request body
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode requestBody = mapper.createObjectNode();

            requestBody.put("model", "mistralai/Mixtral-8x7B-Instruct-v0.1");
            requestBody.put("max_tokens", 2512);
            requestBody.put("request_type", "language-model-inference");
            requestBody.put("temperature", 0.3);
            requestBody.put("top_p", 0.7);
            requestBody.put("top_k", 50);
            requestBody.put("repetition_penalty", 1.0);
            ArrayNode stopArray = JsonNodeFactory.instance.arrayNode();
            stopArray.add("[/INST]");
            stopArray.add("</s>");
            requestBody.put("stop", stopArray);
            requestBody.put("negative_prompt", "");
            requestBody.put("type", "chat");
            requestBody.put("prompt_format_string", "[INST]  {prompt}\\n [/INST]");

            ObjectNode systemMessage = mapper.createObjectNode();
            systemMessage.put("content", systemPrompt);
            systemMessage.put("role", "system");

            ObjectNode userMessage = mapper.createObjectNode();
            userMessage.put("content", userPrompt);
            userMessage.put("role", "user");

            ArrayNode messages = mapper.createArrayNode();
            messages.add(systemMessage);
            messages.add(userMessage);

            requestBody.set("messages", messages);

            String requestBodyString = mapper.writeValueAsString(requestBody);

            start = Instant.now();

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(new URI(API_URL))
                    .header("Accept", "application/json")
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + AUTH_TOKEN)
                    .POST(HttpRequest.BodyPublishers.ofString(requestBodyString))
                    .build();

            response = client.send(request, HttpResponse.BodyHandlers.ofString());
        }

        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);
        long millis = duration.toMillis();

        return response.body();
    }
    

    

    

    public static String parseResponse(String response) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(response);

        JsonNode choices = rootNode.path("output").path("choices");
        if (choices.isArray()) {
            for (JsonNode choice : choices) {
                try {
                    return choice.path("text").asText().trim();
                   
                } catch (Exception e) {
                    log.error("Error {} ",e.getMessage());
                }
            }
        }
        return null;
    }

}
