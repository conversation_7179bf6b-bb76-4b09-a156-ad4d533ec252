package com.enttribe.emailagent.service;
import com.enttribe.emailagent.dto.EmailUserDto;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailUser;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * The interface Email user service.
 *  <AUTHOR> <PERSON>gi
 */
public interface EmailUserService {

    /**
     * Retrieves all EmailUser entities.
     *
     * @return a list of EmailUser objects.
     */
    List<EmailUser> findAll();

    /**
     * Retrieves an EmailUser entity by its ID.
     *
     * @param id the ID of the EmailUser.
     * @return an EmailUser object.
     */
    EmailUser findById(Integer id);

    /**
     * Creates or saves an EmailUser entity.
     *
     * @param emailUser the EmailUser object to save.
     * @return the saved EmailUser object.
     */
    EmailUser save(EmailUser emailUser);

    EmailUser saveEmailUser(EmailUserDto emailUser);



    List<Map<String, String>> createUsersFromCsv(MultipartFile file) throws IOException;




    /**
     * Updates an existing EmailUser entity.
     *
     * @param id the ID of the EmailUser to update.
     * @param emailUserDetails the updated EmailUser object.
     * @return the updated EmailUser object.
     */
    EmailUserDto update(Integer id, EmailUserDto emailUserDetails);

    EmailUser updateStatus(Integer id, boolean deleted);

    EmailUser updateBatch(Integer id, String batch);


    /**
     * Deletes an EmailUser entity by its ID.
     *
     * @param id the ID of the EmailUser to delete.
     * @return true if the deletion was successful, false otherwise.
     */
    Boolean deleteById(Integer id);

    /**
     * Searches for EmailUser entities based on the provided criteria.
     *
     * @param id the ID of the EmailUser.
     * @param userId the userId of the EmailUser.
     * @param email the email of the EmailUser.
     * @param type the type of the EmailUser.
     * @param deleted the deleted status of the EmailUser.
     * @param name the name of the EmailUser.
     * @return a list of EmailUser objects that match the search criteria.
     */
    List<EmailUser> search(Integer id, String userId, String email, String type, Boolean deleted, String name,int lLimit,int uLimit);

    /**
     * Searches for EmailUser entities based on the provided criteria.
     *
     * @param id the ID of the EmailUser.
     * @param userId the userId of the EmailUser.
     * @param email the email of the EmailUser.
     * @param type the type of the EmailUser.
     * @param deleted the deleted status of the EmailUser.
     * @param name the name of the EmailUser.
     * @return a list of EmailUser objects that match the search criteria.
     */
    Long count(Integer id, String userId, String email, String type, Boolean deleted, String name,int lLimit,int uLimit);


        List<EmailUserDto> emailUserByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap);


     long emailUserCountByFilter(Map<String, Object> filterMap);

     List<String> getDistinctBatchId(String batchId);
     List<String> getBatchId();

    void saveContactsForUsers();

    Map<String, String> updateUserStatus(String email, String status);
}
