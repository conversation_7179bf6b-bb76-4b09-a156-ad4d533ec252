package com.enttribe.emailagent.service;

import com.enttribe.emailagent.ai.utils.AIUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.UUID;

/**
 * The type Prompt audit service.
 *  <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PromptAuditService {

    private final RestTemplate restTemplate;

    @Value("${spring.ai.mistralai.chat.options.model}")
    private String engine;

    @Value("${promptAuditUrl}")
    private String url;

    /**
     * Send prompt audit.
     *
     * @param systemPrompt the system prompt
     * @param userPrompt   the user prompt
     * @param response     the response
     */
    @Async
    public void sendPromptAudit(Resource systemPrompt, String userPrompt, String response) {
        log.debug("Inside @method sendPromptAudit");
        String systemPromptString = AIUtils.getPromptString(systemPrompt);
        AuditRequest auditRequest = new AuditRequest(engine, systemPromptString + userPrompt, response);
        sendCapturedData(auditRequest);
    }

    /**
     * Send prompt audit.
     *
     * @param systemPrompt the system prompt
     * @param userPrompt   the user prompt
     * @param response     the response
     */
    @Async
    public void sendPromptAudit(String systemPrompt, String userPrompt, String response) {
        log.debug("Inside @method sendPromptAudit...");
        AuditRequest auditRequest = new AuditRequest(engine, systemPrompt + userPrompt, response);
        sendCapturedData(auditRequest);
    }

    /**
     * Send prompt audit.
     *
     * @param auditRequest the audit request
     */
    @Async
    public void sendPromptAudit(AuditRequest auditRequest) {
        log.debug("Inside @method sendPromptAudit.");
        sendCapturedData(auditRequest);
    }

    private void sendCapturedData(AuditRequest auditRequest) {
        log.debug("AI model is: {}", engine);
        // Example: Sending a POST request with the captured data
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<AuditRequest> request = new HttpEntity<>(auditRequest, headers);

            ResponseEntity<Void> response = restTemplate.postForEntity(url, request, Void.class);
            log.debug("Response code : {}", response.getStatusCode());
        } catch (RestClientException e) {
            log.error("Error inside @method sendCapturedData. Exception message : {}", e.getMessage());
        }
    }


    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuditRequest {
        private final String agentName =  "email-assistant";
        private final String tags = " email-assistant";
        private final String requestId = "email-assistant-" + UUID.randomUUID();

        private String engine;
        private String requestText;
        private String responseText;
        private String question;
        private String uuid;
        private Long totalToken;
        private Long promptToken;
        private Long completionToken;
        private Double responseTime;
        private String failureCategory;
        private String remark;
        private String toolName;
        private String status;
        private Date requestStartTime;
        private Date requestEndTime;

        public AuditRequest(String engine, String requestText, String responseText) {
            this.engine = engine;
            this.requestText = requestText;
            this.responseText = responseText;
        }

    }

}
