package com.enttribe.emailagent.service;

import com.enttribe.emailagent.dto.UserEmailDto;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.UserActionsDao;
import com.enttribe.emailagent.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * The type Scheduler service.
 *  <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "kafka.enabled", havingValue = "false")
public class SchedulerService {

    private final GraphIntegrationService graphIntegrationService;
    private final EwsService ewsService;
    private final IMailSummaryDao mailSummaryDao;
    private final UserActionsDao userActionsDao;
    private final EmailUserDao emailUser;
    private final RecordService recordService;

    @Value("${pollingMode}")
    private String pollingMode;

    /**
     * Mark deleted email.
     */
    @Scheduled(fixedDelay = 60 * 60 * 1000, initialDelay = 3 * 60 * 1000) // Runs every hour with a 3-minute initial delay
    public void markDeletedEmail() {
        log.debug("Inside @method markDeletedEmail in schedulerService");
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = LocalDateTime.of(today, LocalTime.MIDNIGHT);
        String dateFilter = DateUtils.getUTCStringFromLocalDateTime(startOfDay);

        List<EmailUser> emailUsers = emailUser.findAll("Office365");

        for (EmailUser emailUser : emailUsers) {

            String email = emailUser.getEmail();
            try {
                List<UserEmailDto> deletedEmailsOfUser;
                if (pollingMode.equalsIgnoreCase("EWS")) {
                    deletedEmailsOfUser = ewsService.getDeletedEmailsOfUser(email, dateFilter);
                } else {
                    deletedEmailsOfUser = graphIntegrationService.getDeletedEmailsOfUser(email, dateFilter);
                }
                if (deletedEmailsOfUser == null) continue;
                List<String> internetMessageIds = deletedEmailsOfUser.stream().map(UserEmailDto::getInternetMessageId).toList();
                int deleted = mailSummaryDao.markDeleted(email, internetMessageIds);
                int deletedActions = userActionsDao.markDeleted(email, internetMessageIds);
                log.debug("Total {} mails marked deleted for user : {}", deleted, email);
                log.debug("Total {} actions marked deleted for user : {}", deletedActions, email);
            } catch (Exception e) {
                log.error("Error inside @method markDeletedEmail. Exception message : {}", e.getMessage());
            }
        }
    }

    /**
     * Retain latest records.
     */
    @Scheduled(fixedDelay = 60 * 60 * 1000, initialDelay = 2 * 60 * 1000) // Runs every hour with a 2-minute initial delay
    public void retainLatestRecords() {
        log.debug("Inside @method retainLatestRecords in Scheduler Service.");
        recordService.retainLatestRecords();
    }

}
