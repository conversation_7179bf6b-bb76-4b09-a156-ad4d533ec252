package com.enttribe.emailagent.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.UserMailAttachment;

/**
 * The interface User mail attachment service.
 *  <AUTHOR> Sonsale
 */
public interface UserMailAttachmentService {


UserMailAttachment save(UserMailAttachment userMailAttachment);

UserMailAttachment findById(Integer id);

UserMailAttachment update(Integer id, UserMailAttachment userMailAttachment);

String updateStatus(Integer id, boolean deleted);


List<UserMailAttachment> search(
    Integer id,String userId,String name,  String uniqueName, String type, String messageId, String attachmentId, String ragDocumentId,String conversationId,String internetMessageId, String processingStatus,String processingError, Date creationTime, Date modifiedTime
);


long count(
    Integer id,String userId,String name,  String uniqueName, String type, String messageId, String attachmentId, String ragDocumentId,String conversationId,String internetMessageId, String processingStatus,String processingError, Date creationTime, Date modifiedTime
);

    UserMailAttachment userMailAttachmentByAttachmentId(String attachmentId);




    List<UserMailAttachment> userMailAttachmentByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap);

    long userMailAttachmentCountByFilter( Map<String, Object> filterMap);

    String reset(Integer id);

}
