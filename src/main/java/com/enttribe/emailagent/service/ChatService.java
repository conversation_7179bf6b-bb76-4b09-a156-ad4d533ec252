package com.enttribe.emailagent.service;

import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.emailagent.ai.dto.draft.DraftResponse;
import com.enttribe.emailagent.ai.dto.meeting.AvailableTimeSlots;
import com.enttribe.emailagent.ai.dto.meeting.IntentResponseNew;
import com.enttribe.emailagent.ai.service.DraftService;
import com.enttribe.emailagent.ai.service.MeetingService;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.DateUtils;
import com.enttribe.emailagent.utils.EmailConstants;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.MEETING_DRAFT;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.MEETING_FROM_CHAT;
import static com.google.common.base.Throwables.getStackTraceAsString;

/**
 * The type Chat service.
 *  <AUTHOR> Dangi
 */
@Service
@Slf4j
public class ChatService {

    @Value("classpath:system_prompts/ask.st")
    private Resource askSystemPrompt;

    @Value("${pollingMode}")
    private String pollingMode;

    @Value("${org.domain.name}")
    private List<String> orgDomainName;

    private static final Logger auditLog = EmailAgentLogger.getLogger(ChatService.class);

    private final UserContextHolder userContextHolder;
    private final DraftService draftServiceAI;
    private final MeetingService meetingService;
    private final IMailSummaryDao mailSummary;
    private final IEmailPreferencesDao preferencesDao;
    private final EwsService ewsService;
    private final GmailIntegration gmailIntegration;
    private final GraphIntegrationService graphIntegrationService;

    private final ChatClient chatClient;
    private final AiChatModel chatModel;

    @Value("${askSystemPromptId}")
    private String askSystemPromptId;


    // Map to store memory for each user, identified by userId
    private final Cache<String, MessageChatMemoryAdvisor> chatMemoryByUser;


    /**
     * Instantiates a new Chat service.
     *
     * @param builder                 the builder
     * @param userContextHolder       the user context holder
     * @param draftServiceAI          the draft service ai
     * @param meetingService          the meeting service
     * @param mailSummary             the mail summary
     * @param preferencesDao          the preferences dao
     * @param ewsService              the ews service
     * @param graphIntegrationService the graph integration service
     * @param gmailIntegration        the gmail integration
     */
    public ChatService(ChatClient.Builder builder, UserContextHolder userContextHolder, DraftService draftServiceAI,
                       MeetingService meetingService, IMailSummaryDao mailSummary, IEmailPreferencesDao preferencesDao,
                       EwsService ewsService, GraphIntegrationService graphIntegrationService, GmailIntegration gmailIntegration,
                       AiChatModel chatModel) {
        this.chatClient = builder.build();
        this.userContextHolder = userContextHolder;
        this.draftServiceAI = draftServiceAI;
        this.meetingService = meetingService;
        this.mailSummary = mailSummary;
        this.preferencesDao = preferencesDao;
        this.ewsService = ewsService;
        this.graphIntegrationService = graphIntegrationService;
        this.gmailIntegration = gmailIntegration;
        this.chatModel = chatModel;

        // Initialize the Caffeine cache
        this.chatMemoryByUser = Caffeine.newBuilder()
                .expireAfterWrite(3, TimeUnit.MINUTES)  // Automatically expire 3 minutes after the last write
                .maximumSize(100)  // Limit cache size to 100 entries
                .build();
    }

    /**
     * Gets chat response.
     *
     * @param queryMap the query map
     * @return the chat response
     */
    public Map<String, Object> getChatResponse(Map<String, String> queryMap) {
        String email = userContextHolder.getCurrentUser().getEmail();
        String query = queryMap.get("query");
        String response = chatModel.chatCompletion(askSystemPromptId, "ask_" + email, Map.of("userMessage", query), String.class);
        return Map.of("response", response);

    }


    /**
     * Intent based search v 2 map.
     *
     * @param emailId           the email id
     * @param userPrompt        the user prompt
     * @param previousPrompt    the previous prompt
     * @param internetMessageId the internet message id
     * @param userId            the user id
     * @return the map
     */
    public Map<String, Object> intentBasedSearchV2(String emailId, String userPrompt, String previousPrompt, String internetMessageId, String userId, String messageId) {
        log.debug("Inside @method intentBasedSearchV2 in Chat Service. @param : emailId -> {} userPrompt -> {} previousPrompt -> {}", emailId, userPrompt, previousPrompt);
//        userPrompt = previousPrompt + " userQuestion : " + userPrompt;
//        log.debug("user prompt after : {}", userPrompt);

        Map<String, String> auditMap = new HashMap<>();
        try {
            HashMap<String, Object> resultMap = new HashMap<>();

            MailSummary mailSummaryObject = mailSummary.findByInternetMessageId(internetMessageId, userId);
            if(mailSummaryObject == null){
                mailSummaryObject = mailSummary.findByMessageId(messageId,emailId);
            }

            if (mailSummaryObject != null) {
                auditMap = CommonUtils.getAuditMap(userId, mailSummaryObject.getSubject(), mailSummaryObject.getInternetMessageId(), MEETING_FROM_CHAT, userPrompt, null, mailSummaryObject.getMessageId(), mailSummaryObject.getConversationId());
            } else {
                auditMap = CommonUtils.getAuditMap(userId, null, null, MEETING_FROM_CHAT, userPrompt, null, null, null);
            }

            if (mailSummaryObject != null) {
                if (!CommonUtils.containsEmail(userPrompt)) {
                    log.debug("Not received email, adding participants from mail summary");
                    String requiredParticipants = " Required participants are: " + mailSummaryObject.getToUser() + " " + mailSummaryObject.getFromUser();
                    userPrompt = userPrompt.concat(requiredParticipants);
                    if (mailSummaryObject.getCcUser() != null && !mailSummaryObject.getCcUser().isBlank()) {
                        String optionalParticipants = " Optional participants are: " + mailSummaryObject.getCcUser();
                        userPrompt = userPrompt.concat(optionalParticipants);
                    }
                }
                String subject = mailSummaryObject.getSubject();
                userPrompt = userPrompt.concat("  subject of the meeting is : " + subject);
            }

            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);

            Integer meetingDuration = preferences.getPreferredMeetingDuration();
            meetingDuration = meetingDuration != null ? meetingDuration : 30;

            log.debug("User prompt for intentResponse : {}", userPrompt);
            IntentResponseNew intentResponse = meetingService.intentResponse(userPrompt, meetingDuration, auditMap, preferences.getTimeZone());
//            intentResponse.setBody("");
            log.debug("Intent response : {}", intentResponse);

            String timeZone = preferences.getTimeZone();
            if (intentResponse.getTimeZone() != null && !intentResponse.getTimeZone().isBlank()) {
                timeZone = intentResponse.getTimeZone();
            }
            intentResponse.setTimeZone(timeZone);

            List<AvailableTimeSlots> meetingSlots;
            if (!intentResponse.getRequiredAttendees().isEmpty() && intentResponse.getStartTimeProvided() != null && !intentResponse.getStartTimeProvided()) {
                List<String> participants = new ArrayList<>(intentResponse.getRequiredAttendees());
                participants.addAll(intentResponse.getOptionalAttendees());
                participants.add(emailId);
                String startTime;
                String meetingDate = intentResponse.getMeetingDate();
                if (meetingDate == null || meetingDate.isBlank()) {
                    meetingDate = DateUtils.getUTCDateStringWithOffset();
                }
//                if (intentResponse.getMeetingStartTime() != null && !intentResponse.getMeetingStartTime().isBlank()) {
//                    meetingDate = intentResponse.getMeetingStartTime().substring(0, intentResponse.getMeetingStartTime().indexOf("T"));
//                } else {
//                    meetingDate = meetingDate.substring(0, meetingDate.indexOf("T"));
//                }
                if (DateUtils.isToday(meetingDate)) {
                    startTime = DateUtils.getUTCDateStringWithOffset();
                } else {
                    List<LocalDateTime> checkinTimesOfUsers = getCheckInTimesOfUsers(participants, meetingDate);
                    LocalDateTime latestTime = DateUtils.getLatestLocalDateTime(checkinTimesOfUsers);
                    startTime = DateUtils.getUTCStringFromLocalDateTime(latestTime);
                }
                List<Meeting> availableSlots = get3AvailableSlots(participants, startTime, meetingDuration);
                resultMap.put("availableSlots", availableSlots);
            }
            if (!intentResponse.getRequiredAttendees().isEmpty() && intentResponse.getMeetingStartTime() != null && !intentResponse.getMeetingStartTime().isBlank() && intentResponse.getMeetingEndTime() != null) {
                String startTime = DateUtils.convertToUTCString(intentResponse.getMeetingStartTime(), timeZone);
                String endTime = DateUtils.convertToUTCString(intentResponse.getMeetingEndTime(), timeZone);
                List<String> participants = new ArrayList<>(intentResponse.getRequiredAttendees());
                participants.addAll(intentResponse.getOptionalAttendees());
                participants.add(emailId);
                List<String> outsiders = CommonUtils.filterEmailsByDomain(participants, orgDomainName);
                participants.removeAll(outsiders);
                if (pollingMode.equalsIgnoreCase("EWS")) {
                    meetingSlots = ewsService.getAvailableMeetingSlotsV2(participants, startTime, endTime, intentResponse.getMeetingDuration());
                } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                    meetingSlots = gmailIntegration.getAvailableMeetingSlotsV2(participants, startTime, endTime, intentResponse.getMeetingDuration());
                } else {
                    meetingSlots = graphIntegrationService.getAvailableMeetingSlotsV2(participants, startTime, endTime, intentResponse.getMeetingDuration());
                }

                if (meetingSlots != null && !meetingSlots.isEmpty()) {
                    meetingSlots.getFirst().setUnknown(outsiders);
                }
                resultMap.put("slot", meetingSlots);
            }

            if (Boolean.FALSE.equals(intentResponse.getStartTimeProvided())) {
                intentResponse.setMeetingStartTime(null);
                intentResponse.setMeetingEndTime(null);
            }
//            resultMap.put(EmailConstants.RESULT, intentResponse);
            if (mailSummaryObject != null) {
                resultMap.put("subject", mailSummaryObject.getSubject());
                auditMap.put("type", "MEETING_DRAFT");
                try {
                    DraftResponse agenda = draftServiceAI.generateMeetingDraft(userPrompt, mailSummaryObject.getMessageSummary(), auditMap);
                    resultMap.put("agenda", agenda.getEmail());

                } catch (Exception e) {
                    auditLog.error("Error inside @method meeting draft", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), internetMessageId, MEETING_DRAFT, emailId, auditMap.get("subject"), auditMap.get("conversationId"), null, null, Map.of("userPrompt", userPrompt, "summary", mailSummaryObject.getMessageSummary()), null);
                }
            } else if (intentResponse.getMeetingAgenda() != null && !intentResponse.getMeetingAgenda().isBlank()) {
                String meetingAgenda = intentResponse.getMeetingAgenda();
                resultMap.put("subject", intentResponse.getSubjectForMeeting());
                auditMap.put("type", "MEETING_DRAFT");
                try {
                    DraftResponse agenda = draftServiceAI.generateMeetingDraft(meetingAgenda, null, auditMap);
                    resultMap.put("agenda", agenda.getEmail());

                } catch (Exception e) {
                    auditLog.error("Error inside @method meeting draft", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), internetMessageId, MEETING_DRAFT, emailId, auditMap.get("subject"), auditMap.get("conversationId"), null, null, Map.of("userPrompt", userPrompt, "agenda", intentResponse.getMeetingAgenda()), null);
                }
            } else {
                resultMap.put("subject", intentResponse.getSubjectForMeeting());
            }

            resultMap.put(EmailConstants.RESULT, intentResponse);
            resultMap.put("description", "final object");
            return resultMap;

        } catch (Exception e) {
            log.error("Error inside chat meeting", e);
            auditLog.error("Error inside @method intentBasedSearch", e.getMessage(), getStackTraceAsString(e), null, internetMessageId, MEETING_FROM_CHAT, emailId, null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            throw new RuntimeException(e);
        }
    }

    /**
     * Gets 3 available slots.
     *
     * @param emails        the emails of participants for which the slots needs to be searched
     * @param startDateTime this is the point of time since when next 3 slots needs to be searched
     * @return the 3 available slots
     */
    public List<Meeting> get3AvailableSlots(List<String> emails, String startDateTime, Integer meetingDuration) {
        log.debug("Inside @method get3AvailableSlots. @param : emails -> {} startDateTime -> {} meetingDuration -> {}", emails, startDateTime, meetingDuration);
        if (startDateTime == null) {
            startDateTime = DateUtils.getUTCStringFromLocalDateTime(LocalDateTime.now());
        }

        // Start by extracting the meeting date
        String meetingDate = startDateTime.substring(0, startDateTime.indexOf("T"));
        List<Meeting> availableMeetingSlots = new ArrayList<>();

        // Variables to store the number of slots found and the current date being searched
        int slotsFound = 0;
        LocalDate currentDate = LocalDate.parse(meetingDate); // Start with the provided or today's date

        int daysSearched = 0;
        int maxDaysToSearch = 7;
        while (slotsFound < 3 && daysSearched < maxDaysToSearch) {
            // Get check-in and check-out times for the current date
            List<LocalDateTime> checkinTimesOfUsers = getCheckInTimesOfUsers(emails, currentDate.toString());
            List<LocalDateTime> checkoutTimesOfUsers = getCheckoutTimesOfUsers(emails, currentDate.toString());

            LocalDateTime earliestTime = DateUtils.getEarliestLocalDateTime(checkoutTimesOfUsers);
            LocalDateTime latestTime = DateUtils.getLatestLocalDateTime(checkinTimesOfUsers);

            // For today, search from current time
            String startTime;
            if (DateUtils.isToday(currentDate.toString())) {
                startTime = DateUtils.getUTCDateStringWithOffset(); // Current time in UTC if today
            } else {
                startTime = DateUtils.getUTCStringFromLocalDateTime(latestTime); // From latest check-in on other days
            }
            startTime = determineStartTime(startTime, startDateTime);
            // Always search until the earliest checkout time
            String endTime = DateUtils.getUTCStringFromLocalDateTime(earliestTime);

            log.debug("Meeting slots are less than 3. new params are : emails -> {} startTime -> {} endTime -> {} meetingDuration -> {}",
                    emails, startTime, endTime, meetingDuration);
            List<Meeting> daySlots;
            // Get available slots for the current date
            if (pollingMode.equalsIgnoreCase("EWS")) {
                daySlots = ewsService.getAvailableMeetingSlotsOOF(emails, startTime, endTime, meetingDuration);
            } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                daySlots = gmailIntegration.getAvailableMeetingSlotsOFF(emails, startTime, endTime, meetingDuration);
            } else {
                daySlots = graphIntegrationService.getAvailableMeetingSlotsOOF(emails, startTime, endTime, meetingDuration);
            }
            log.debug("Total slots found for users {} between range {} and {} are : {}", emails, startTime, endTime, daySlots.size());

            // Add the slots found to the result list
            for (Meeting slot : daySlots) {
                if (slotsFound < 3) {
                    log.debug("Meeting slots are less than 3");
                    availableMeetingSlots.add(slot);
                    slotsFound++;
                    log.debug("Total meeting slots till now: {}", availableMeetingSlots.size());
                }
                if (slotsFound >= 3) break;
            }

            // If we found 3 slots, exit the loop
            if (slotsFound >= 3) break;

            // Move to the next day and repeat the process if fewer than 3 slots found
            currentDate = currentDate.plusDays(1);
            daysSearched++;
        }

        if (slotsFound < 3) {
            log.debug("Could not find 3 available slots within {} days", maxDaysToSearch);
        }
        return availableMeetingSlots;
    }

    private String determineStartTime(String startTime1, String startTime2) {
        LocalDateTime localDateTime1 = DateUtils.convertToLocalDateTime(startTime1);
        LocalDateTime localDateTime2 = DateUtils.convertToLocalDateTime(startTime2);
        LocalDateTime localDateTime = localDateTime1.isAfter(localDateTime2) ? localDateTime1 : localDateTime2;
        return DateUtils.getUTCStringFromLocalDateTime(localDateTime);
    }

    private List<LocalDateTime> getCheckoutTimesOfUsers(List<String> emails, String meetingDate) {
        if (meetingDate == null || meetingDate.isBlank()) meetingDate = LocalDate.now().toString();
        List<LocalDateTime> localTimes = new ArrayList<>();
        for (String email : emails) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            if (preferences != null && preferences.getCheckout() != null) {
                try {
                    //Here we convert user's checkout time to utc local time
                    LocalDateTime toUTCZone = DateUtils.parseDateToUTC(meetingDate, preferences.getTimeZone(), preferences.getCheckout());
                    localTimes.add(toUTCZone);
                } catch (Exception e) {
                    log.error("Error inside @method getCheckoutTimesOfUsers. @param : emails -> {} meetingDate -> {}", emails, meetingDate);
                    log.error("Error while getting getCheckoutTimesOfUsers", e);
                }
            }

        }
        return localTimes;
    }

    private List<LocalDateTime> getCheckInTimesOfUsers(List<String> emails, String meetingDate) {
        if (meetingDate == null || meetingDate.isBlank()) meetingDate = LocalDate.now().toString();
        List<LocalDateTime> localTimes = new ArrayList<>();
        for (String email : emails) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            if (preferences != null && preferences.getCheckout() != null) {
                //Here we convert user's checkout time to utc local time
                LocalDateTime toUTCZone = DateUtils.parseDateToUTC(meetingDate, preferences.getTimeZone(), preferences.getCheckin());
                localTimes.add(toUTCZone);
            }

        }
        return localTimes;
    }

}
