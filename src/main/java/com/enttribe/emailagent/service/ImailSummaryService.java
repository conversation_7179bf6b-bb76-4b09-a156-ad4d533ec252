package com.enttribe.emailagent.service;

import com.enttribe.emailagent.dto.ActionItemDto;
import com.enttribe.emailagent.dto.ConversationEmailsDTO;
import com.enttribe.emailagent.dto.DaySummary;
import com.enttribe.emailagent.entity.MailSummary;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * The interface Imail summary service.
 * <AUTHOR> Sonsale
 */
public interface ImailSummaryService {

    List<MailSummary> getAllMailSummary( Integer llimit,Integer ulimit,String folderName);

    List<DaySummary> getDaySummary(String userId, String emailId, String dateString);

    List<MailSummary> getDaySummaryV3(String userId, String type, String date);

    List<DaySummary> getDaySummaryBetweenDates(String userId, String emailId, String startDate1, String endDate1);

    List<DaySummary> getDaySummaryV2(String userId, String emailId, String type, String date);

    //current
    List<MailSummary> getMailSummaries(String emailId, String startDateStr, String endDateStr, String timeZone);

    void addAttachmentDetails(List<MailSummary> mailSummaryList);

//    List<MailSummary> getSummaryByType(String type, String action);

    List<MailSummary> getSummaryByTypeV1(String type, String action, String date);

    long countAllMailOfUser(String userId, String type, String date);

    Map<String, Object> getMailSummaryAndThreadSummary(String userId, String internetMessageId);

    void saveContactSummary();
    
    void updateBatchByAvgTime();

    List<ActionItemDto> getActionItemDetails(String userId, String conversationId, String subject);

    List<ConversationEmailsDTO> getConversations(String userId, Integer lowerLimit, Integer upperLimit);

    List<String> getConversationIdsOfUser(String userId, Integer lowerLimit, Integer upperLimit);

    List<MailSummary> getMailsInConversation(String email, String conversationId, Integer lowerLimit, Integer upperLimit);

    Map<String, String> saveMailSummary(MailSummary mailSummary);

    List<DaySummary> getDateRangeSummary(String email, LocalDateTime startDate, LocalDateTime endDate);

    List<MailSummary> getDateRangeSummaryByAction(String userId, String startDate, String endDate, String action);

    Map<String, Object> encryptExistingMailSummary(Integer startId, Integer endId);

    Map<String, Object> encryptExistingThreadSummary(Integer startId, Integer endId);
}
