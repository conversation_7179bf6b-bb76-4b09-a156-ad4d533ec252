package com.enttribe.emailagent.service;

import com.enttribe.emailagent.ai.polling.OutlookPollingAI;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.PollTimeInfo;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.PollTimeInfoRepository;
import com.enttribe.emailagent.repository.impl.EmailPreferenceDaoImpl;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.EmailConstants;

import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.enttribe.emailagent.utils.ExceptionUtils.getStackTraceAsString;

/**
 * The type Email preferences service.
 *  <AUTHOR> Sonsale
 */
@Service
@Slf4j
public class EmailPreferencesService {

    @Autowired
    private IEmailPreferencesDao emailPreferencesDao;

    @Autowired
    private EmailPreferenceDaoImpl daoImpl;


    @Autowired
    private IMailSummaryDao mailSummaryDao;

    @Autowired
    private UserContextHolder userContextHolder;

    @Autowired
    private PollTimeInfoRepository pollTimeInfoRepository;

    private static final Logger auditLog = EmailAgentLogger.getLogger(EmailPreferencesService.class);


    /**
     * Update time zone email preferences.
     *
     * @param timeZone the time zone
     * @return the email preferences
     */
    public EmailPreferences updateTimeZone(String timeZone) {
        String userId = userContextHolder.getCurrentUser().getId();
        try {
            EmailPreferences preferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);
            if (preferences != null) {
                preferences.setTimeZone(timeZone);
                preferences.setModifiedTime(new Date());
                return emailPreferencesDao.save(preferences);
            } else {
                throw new ResourceNotFoundException("Email preferences not found for user : " + userId);
            }
        } catch (Exception e) {
            log.error("Error inside @method updateTimeZone", e);
            auditLog.error("Error while updating timezone", e.getMessage(), getStackTraceAsString(e), null, null, "EMAIL_PREFERENCE_TIMEZONE_UPDATE", userId, null, null, null, null, null, null);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * Update device id .
     *
     * @param deviceId the device id
     * @return the string
     */
    public String updateDeviceId(String deviceId) {
        String userId = userContextHolder.getCurrentUser().getId();
        try {
            EmailPreferences preferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);
            if (preferences != null) {
                preferences.setDeviceId(deviceId);
                preferences.setModifiedTime(new Date());
                emailPreferencesDao.save(preferences);
                return "success";
            } else {
                throw new ResourceNotFoundException("Email preferences not found for user : " + userId);
            }
        } catch (Exception e) {
            log.error("Error inside @method updateDeviceId", e);
            auditLog.error("Error while updating deviceId", e.getMessage(), getStackTraceAsString(e), null, null, "EMAIL_PREFERENCE_DEVICE_ID_UPDATE", userId, null, null, null, null, null, null);
            throw new BusinessException(e.getMessage());
        }
    }


    /**
     * Create email preferences.
     *
     * @param preferences the preferences
     * @return the email preferences
     */
    public EmailPreferences create(EmailPreferences preferences) {
        preferences.setCreatedTime(new Date());
        preferences.setModifiedTime(new Date());
        return emailPreferencesDao.save(preferences);
    }

    /**
     * Update email preferences.
     *
     * @param preferences the preferences
     * @return the email preferences
     */
    public EmailPreferences update(EmailPreferences preferences) {
        preferences.setModifiedTime(new Date());
        return emailPreferencesDao.save(preferences);

    }

    /**
     * Save or update email preferences email preferences.
     *
     * @param preferences the preferences
     * @return the email preferences
     */
    public EmailPreferences saveOrUpdateEmailPreferences(EmailPreferences preferences) {
        log.debug("Inside @method saveOrUpdateEmailPreferences {}", preferences);
        String userId = userContextHolder.getCurrentUser().getId();
        try {
            EmailPreferences savedPreferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);
            if (savedPreferences != null) {
                if (preferences.getEmailSender() != null)
                    savedPreferences.setEmailSender(preferences.getEmailSender().toLowerCase());
                if (preferences.getSenderCompany() != null)
                    savedPreferences.setSenderCompany(preferences.getSenderCompany().toLowerCase());
                savedPreferences.setImportantTags(preferences.getImportantTags());
                savedPreferences.setMeetingType(preferences.getMeetingType());
                savedPreferences.setFontFamily(preferences.getFontFamily());
                savedPreferences.setFontSize(preferences.getFontSize());
                savedPreferences.setCheckin(preferences.getCheckin());
                savedPreferences.setCheckout(preferences.getCheckout());
                savedPreferences.setTimeZone(preferences.getTimeZone());
                savedPreferences.setFontColor(preferences.getFontColor());
                savedPreferences.setMaskContent(preferences.getMaskContent());
                savedPreferences.setContactNumber(preferences.getContactNumber());
                savedPreferences.setPreferredMeetingDuration(preferences.getPreferredMeetingDuration());
                savedPreferences.setDateFormat(preferences.getDateFormat());
                savedPreferences.setTimeFormat(preferences.getTimeFormat());
                savedPreferences.setKeyboardShortcuts(preferences.getKeyboardShortcuts());
                savedPreferences.setIsCategoryEnabled(preferences.getIsCategoryEnabled());
                savedPreferences.setIsPriorityEnabled(preferences.getIsPriorityEnabled());
                savedPreferences.setIsToneEnabled(preferences.getIsToneEnabled());
                if (preferences.getBlackListedSender() == null || preferences.getBlackListedSender().isBlank()) {
                    savedPreferences.setBlackListedSender(null);
                } else {
                    savedPreferences.setBlackListedSender(preferences.getBlackListedSender().toLowerCase());
                }

                if (preferences.getBlackListedDomain() == null || preferences.getBlackListedDomain().isBlank()) {
                    savedPreferences.setBlackListedDomain(null);
                } else {
                    savedPreferences.setBlackListedDomain(preferences.getBlackListedDomain().toLowerCase());
                }

                if (preferences.getBlackListedSubject() == null || preferences.getBlackListedSubject().isBlank()) {
                    savedPreferences.setBlackListedSubject(null);
                } else {
                    savedPreferences.setBlackListedSubject(preferences.getBlackListedSubject().toLowerCase());
                }

                savedPreferences.setModifiedTime(new Date());
                savedPreferences.setDisplayName(preferences.getDisplayName());
                return emailPreferencesDao.save(savedPreferences);
            } else {
                preferences.setCreatedTime(new Date());
                preferences.setModifiedTime(new Date());
                preferences.setUserId(userId);
                return emailPreferencesDao.save(preferences);
            }
        } catch (Exception e) {
            log.error("Error inside @method saveOrUpdateEmailPreferences", e);
            auditLog.error("Error while saving email preference", e.getMessage(), getStackTraceAsString(e), null, null, "EMAIL_PREFERENCE", userId, null, null, null, null, AIUtils.convertToJSON(preferences, true), null);
            throw new BusinessException(e.getMessage());
        }
    }


    /**
     * Gets email preferences.
     *
     * @return the email preferences
     */
    public EmailPreferences getEmailPreferences() {
        log.debug("Inside @method getEmailPreferences");
        String userId = userContextHolder.getCurrentUser().getId();
        EmailPreferences emailPreferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);
        Optional<PollTimeInfo> pollTime = pollTimeInfoRepository.findFirstByEmailOrderByStartTimeDesc(userId);
        if (pollTime.isPresent()) emailPreferences.setLastPollTime(pollTime.get().getStartTime());
        return emailPreferences;
    }

    /**
     * Check preference boolean.
     *
     * @param preference the preference
     * @param type       the type
     * @return the boolean
     */
    public boolean checkPreference(String preference, String type) {
        String userId = userContextHolder.getCurrentUser().getId();
        log.debug("Inside @method checkPreference. @param : preference -> {} type -> {} userId -> {}", preference, type, userId);
        EmailPreferences preferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);
        if (preferences == null) {
            throw new ResourceNotFoundException(EmailConstants.THERE_IS_ON_PREFERENCE_RECORD_AVAILABLE_FOR_THE_USER);
        }
        if (type.equalsIgnoreCase(EmailConstants.DOMAIN)) {
            String senderCompany = preferences.getSenderCompany();
            return senderCompany != null && senderCompany.contains(preference);
        } else if (type.equalsIgnoreCase(EmailConstants.SENDER)) {
            String emailSender = preferences.getEmailSender();
            return emailSender != null && emailSender.contains(preference);
        } else if (type.equalsIgnoreCase("conversationId")) {
            String conversationId = preferences.getConversationId();
            return conversationId != null && conversationId.contains(preference);
        } else if (type.equalsIgnoreCase("blackListedDomain")) {
            String blackListedDomain = preferences.getBlackListedDomain();
            return blackListedDomain != null && blackListedDomain.contains(preference);
        } else if (type.equalsIgnoreCase("blackListedSubject")) {
            String blackListedSubject = preferences.getBlackListedSubject();
            return blackListedSubject != null && blackListedSubject.contains(preference);
        } else if (type.equalsIgnoreCase("blackListedSender")) {
            String blackListedSender = preferences.getBlackListedSender();
            return blackListedSender != null && blackListedSender.contains(preference);
        }
        throw new ResourceNotFoundException(EmailConstants.WRONG_TYPE_IS_PROVIDED);
    }

    /**
     * Add conversation id map.
     *
     * @param conversationId the conversation id
     * @return the map
     */
    public Map<String, Object> addConversationId(String conversationId) {
        String userId = userContextHolder.getCurrentUser().getId();
        log.debug("Inside @method addConversationId. @param : conversationId -> {} userId -> {}", conversationId, userId);

        EmailPreferences preferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);
        if (preferences == null) {
            throw new ResourceNotFoundException(EmailConstants.THERE_IS_ON_PREFERENCE_RECORD_AVAILABLE_FOR_THE_USER);
        }

        String savedIds = preferences.getConversationId();
        boolean isAdded = false;

        if (savedIds == null || savedIds.isEmpty()) {
            preferences.setConversationId(conversationId);
            isAdded = true;
        } else {
            List<String> conversationIdList = new ArrayList<>(Arrays.asList(savedIds.split(",")));
            if (!conversationIdList.contains(conversationId)) {
                conversationIdList.add(conversationId);
                String updatedIds = String.join(",", conversationIdList);
                preferences.setConversationId(updatedIds);
                isAdded = true;
            }
        }

        emailPreferencesDao.save(preferences);
        mailSummaryDao.setStarMarkedByConversationId(true, conversationId);

        Map<String, Object> response = new HashMap<>();
        response.put("result", isAdded ? "success" : "failed");

        return response;
    }

    /**
     * Delete conversation id map.
     *
     * @param conversationId the conversation id
     * @return the map
     */
    public Map<String, String> deleteConversationId(String conversationId) {
        String userId = userContextHolder.getCurrentUser().getId();
        log.debug("Inside @method deleteConversationId. @param : conversationId -> {} userId -> {}", conversationId, userId);

        EmailPreferences preferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);
        if (preferences == null) {
            throw new ResourceNotFoundException(EmailConstants.THERE_IS_ON_PREFERENCE_RECORD_AVAILABLE_FOR_THE_USER);
        }

        String savedIds = preferences.getConversationId();
        if (savedIds == null || savedIds.isEmpty()) {
            Map<String, String> response = new HashMap<>();
            response.put("result", "failed");
            return response;
        }

        List<String> conversationIdList = new ArrayList<>(Arrays.asList(savedIds.split(",")));
        boolean removed = conversationIdList.remove(conversationId);
        if (!removed) {
            Map<String, String> response = new HashMap<>();
            response.put("result", "failed");
            return response;
        }

        String updatedIds = String.join(",", conversationIdList);
        preferences.setConversationId(updatedIds);
        emailPreferencesDao.save(preferences);
        mailSummaryDao.setStarMarkedByConversationId(false, conversationId);

        Map<String, String> response = new HashMap<>();
        response.put("result", "success");
        return response;
    }

    /**
     * Add preferences email preferences.
     *
     * @param preference the preference
     * @param type       the type
     * @return the email preferences
     */
    public EmailPreferences addPreferences(String preference, String type) {
        String userId = userContextHolder.getCurrentUser().getId();
        log.debug("Inside @method addPreferences. @param : preference -> {} type -> {} userId -> {}", preference, type, userId);
        EmailPreferences preferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);
        if (preferences == null) {
            throw new ResourceNotFoundException(EmailConstants.THERE_IS_ON_PREFERENCE_RECORD_AVAILABLE_FOR_THE_USER);
        }
        if (type.equalsIgnoreCase(EmailConstants.SENDER)) {
            String savedSenders = preferences.getEmailSender();
            if (savedSenders == null || savedSenders.isEmpty()) {
                preferences.setEmailSender(preference);
            } else {
                List<String> senderList = new ArrayList<>(Arrays.asList(savedSenders.split(",")));
                if (!senderList.contains(preference)) {
                    senderList.add(preference);
                    String updatedSenders = String.join(",", senderList);
                    preferences.setEmailSender(updatedSenders);
                }
            }

            emailPreferencesDao.save(preferences);
            return preferences;

        } else if (type.equalsIgnoreCase(EmailConstants.DOMAIN)) {
            String savedDomains = preferences.getSenderCompany();

            if (savedDomains == null || savedDomains.isEmpty()) {
                preferences.setSenderCompany(preference);
            } else {
                List<String> domainList = new ArrayList<>(Arrays.asList(savedDomains.split(",")));
                if (!domainList.contains(preference)) {
                    domainList.add(preference);
                    String updatedDomains = String.join(",", domainList);
                    preferences.setSenderCompany(updatedDomains);
                }
            }

            emailPreferencesDao.save(preferences);
            return preferences;


        } else if (type.equalsIgnoreCase("blackListedDomain")) {
            String blackListedDomain = preferences.getBlackListedDomain();

            if (blackListedDomain == null || blackListedDomain.isEmpty()) {
                preferences.setBlackListedDomain(preference);
            } else {
                List<String> domainList = new ArrayList<>(Arrays.asList(blackListedDomain.split(",")));
                if (!domainList.contains(preference)) {
                    domainList.add(preference);
                    String updatedDomains = String.join(",", domainList);
                    preferences.setBlackListedDomain(updatedDomains);
                }
            }

            emailPreferencesDao.save(preferences);
            return preferences;
        } else if (type.equalsIgnoreCase("blackListedSubject")) {
            String blackListedSubject = preferences.getBlackListedSubject();

            if (blackListedSubject == null || blackListedSubject.isEmpty()) {
                preferences.setBlackListedSubject(preference);
            } else {
                List<String> domainList = new ArrayList<>(Arrays.asList(blackListedSubject.split(",")));
                if (!domainList.contains(preference)) {
                    domainList.add(preference);
                    String updatedDomains = String.join(",", domainList);
                    preferences.setBlackListedSubject(updatedDomains);
                }
            }

            emailPreferencesDao.save(preferences);
            return preferences;
        } else if (type.equalsIgnoreCase("blackListedSender")) {
            String blackListedSender = preferences.getBlackListedSender();

            if (blackListedSender == null || blackListedSender.isEmpty()) {
                preferences.setBlackListedSender(preference);
            } else {
                List<String> domainList = new ArrayList<>(Arrays.asList(blackListedSender.split(",")));
                if (!domainList.contains(preference)) {
                    domainList.add(preference);
                    String updatedDomains = String.join(",", domainList);
                    preferences.setBlackListedSender(updatedDomains);
                }
            }

            emailPreferencesDao.save(preferences);
            return preferences;
        }
        throw new ResourceNotFoundException(EmailConstants.WRONG_TYPE_IS_PROVIDED);
    }

    /**
     * Delete preferences email preferences.
     *
     * @param preference the preference
     * @param type       the type
     * @return the email preferences
     */
    public EmailPreferences deletePreferences(String preference, String type) {
        String userId = userContextHolder.getCurrentUser().getId();
        log.debug("Inside @method deletePreferences. @param : preference -> {} type -> {} userId -> {}", preference, type, userId);
        EmailPreferences preferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);
        if (preferences == null) {
            throw new ResourceNotFoundException(EmailConstants.THERE_IS_ON_PREFERENCE_RECORD_AVAILABLE_FOR_THE_USER);
        }
        if (type.equalsIgnoreCase(EmailConstants.SENDER)) {
            String savedSenders = preferences.getEmailSender();
            if (savedSenders == null || savedSenders.isEmpty()) {
                return preferences; // No sender to delete from
            }

            List<String> senderList = new ArrayList<>(Arrays.asList(savedSenders.split(",")));
            boolean removed = senderList.remove(preference);
            if (!removed) {
                throw new ResourceNotFoundException("The sender to delete was not found.");
            }

            String updatedSenders = String.join(",", senderList);
            preferences.setEmailSender(updatedSenders);

            emailPreferencesDao.save(preferences);
            return preferences;

        } else if (type.equalsIgnoreCase(EmailConstants.DOMAIN)) {
            String senderCompanies = preferences.getSenderCompany();
            if (senderCompanies == null || senderCompanies.isEmpty()) {
                return preferences; // No domain to delete from
            }

            List<String> companyList = new ArrayList<>(Arrays.asList(senderCompanies.split(",")));
            boolean removed = companyList.remove(preference);
            if (!removed) {
                throw new ResourceNotFoundException("The domain to delete was not found.");
            }

            String updatedCompanies = String.join(",", companyList);
            preferences.setSenderCompany(updatedCompanies);

            emailPreferencesDao.save(preferences);
            return preferences;
        }
        throw new ResourceNotFoundException(EmailConstants.WRONG_TYPE_IS_PROVIDED);
    }


    /**
     * Search list.
     *
     * @param id                 the id
     * @param userId             the user id
     * @param emailSubject       the email subject
     * @param senderCompany      the sender company
     * @param importantTags      the important tags
     * @param fontFamily         the font family
     * @param fontSize           the font size
     * @param emailSender        the email sender
     * @param blackListedDomain  the black listed domain
     * @param blackListedSubject the black listed subject
     * @param timeZone           the time zone
     * @param createdTime        the created time
     * @param modifiedTime       the modified time
     * @param conversationId     the conversation id
     * @param checkin            the checkin
     * @param checkout           the checkout
     * @param fontColor          the font color
     * @param displayName        the display name
     * @return the list
     */
    public List<EmailPreferences> search(Integer id, String userId, String emailSubject, String senderCompany, String importantTags, String fontFamily, String fontSize, String emailSender, String blackListedDomain, String blackListedSubject, String timeZone, Date createdTime, Date modifiedTime, String conversationId, LocalTime checkin, LocalTime checkout, String fontColor, String displayName) {

        return emailPreferencesDao.search(id, userId, emailSubject, senderCompany, importantTags, fontFamily, fontSize, emailSender, blackListedDomain, blackListedSubject, timeZone, createdTime, modifiedTime, conversationId, checkin, checkout, fontColor, displayName);

    }

    /**
     * Count long.
     *
     * @param id                 the id
     * @param userId             the user id
     * @param emailSubject       the email subject
     * @param senderCompany      the sender company
     * @param importantTags      the important tags
     * @param fontFamily         the font family
     * @param fontSize           the font size
     * @param emailSender        the email sender
     * @param blackListedDomain  the black listed domain
     * @param blackListedSubject the black listed subject
     * @param timeZone           the time zone
     * @param createdTime        the created time
     * @param modifiedTime       the modified time
     * @param conversationId     the conversation id
     * @param checkin            the checkin
     * @param checkout           the checkout
     * @param fontColor          the font color
     * @param displayName        the display name
     * @return the long
     */
    public long count(Integer id, String userId, String emailSubject, String senderCompany, String importantTags, String fontFamily, String fontSize, String emailSender, String blackListedDomain, String blackListedSubject, String timeZone, Date createdTime, Date modifiedTime, String conversationId, LocalTime checkin, LocalTime checkout, String fontColor, String displayName) {

        return emailPreferencesDao.count(id, userId, emailSubject, senderCompany, importantTags, fontFamily, fontSize, emailSender, blackListedDomain, blackListedSubject, timeZone, createdTime, modifiedTime, conversationId, checkin, checkout, fontColor, displayName);

    }


    /**
     * Preference by filter list.
     *
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @param filterMap the filter map
     * @return the list
     */
    public List<EmailPreferences> preferenceByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap) {
        List<EmailPreferences> failureMap = null;
        failureMap = daoImpl.Filteredpreference(filterMap, llimit, ulimit);
        return failureMap;
    }


    /**
     * Preference count by filter .
     *
     * @param filterMap the filter map
     * @return the long
     */
    public long preferenceCountByFilter(Map<String, Object> filterMap) {
        List<EmailPreferences> failureMap = null;
        failureMap = daoImpl.FilteredpreferenceCount(filterMap);
        return failureMap.size();
    }


    /**
     * Gets time zones like.
     *
     * @param pattern the pattern
     * @return the time zones like
     */
    public List<String> getTimeZonesLike(String pattern) {
        List<String> timeZoneList = EmailConstants.getListOftimezones();

        log.info("allTimeZones {}", timeZoneList);

        // Automatically append % wildcard and convert the pattern to a case-insensitive regular expression for filtering
        String regexPattern = "(?i).*" + pattern.replace("%", ".*").replace("_", ".") + ".*";

        return timeZoneList.stream()
                .filter(zoneId -> zoneId.matches(regexPattern))
                .collect(Collectors.toList());
    }

    /**
     * Update notification info email preferences.
     *
     * @param deviceId the device id
     * @param allowNotification    the allowNotification
     * @return the email preferences
     */
    public String updateNotificationInfo(Boolean allowNotification, String deviceId) {
        String userId = userContextHolder.getCurrentUser().getId();
        log.debug("Inside @method updateNotificationInfo. @params: email -> {} allowNotification -> {} deviceId -> {}", userId, allowNotification, deviceId);
        try {
            EmailPreferences preferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);
            if (preferences != null) {
                if (allowNotification != null) {
                    preferences.setAllowNotification(allowNotification);
                }
                if (deviceId != null) {
                    preferences.setDeviceId(deviceId);
                }
                preferences.setModifiedTime(new Date());
                emailPreferencesDao.save(preferences);
                return EmailConstants.SUCCESS;
            } else {
                throw new ResourceNotFoundException("Email preferences not found for user : " + userId);
            }
        } catch (Exception e) {
            log.error("Error inside @method updateDeviceInfo", e);
            auditLog.error("Error while updating device info", e.getMessage(), getStackTraceAsString(e), null, null, "EMAIL_PREFERENCE_DEVICE_UPDATE", userId, null, null, null, null, null, null);
            throw new BusinessException(e.getMessage());
        }
    }

    public void saveEmailPreferences(EmailPreferences preferences) {
        log.debug("Inside @method saveEmailPreferences {}", preferences);
        String userId = preferences.getUserId();
        try {
            EmailPreferences savedPreferences = emailPreferencesDao.getEmailPreferencesByUserId(userId);

            if (savedPreferences != null) {
                // Update existing preferences
                savedPreferences.setTimeZone(preferences.getTimeZone());
                savedPreferences.setCheckin(preferences.getCheckin());
                savedPreferences.setCheckout(preferences.getCheckout());
                savedPreferences.setContactNumber(preferences.getContactNumber());
                savedPreferences.setModifiedTime(new Date());

                emailPreferencesDao.save(savedPreferences);
            } else {
                // Create new preferences
                preferences.setCreatedTime(new Date());
                preferences.setModifiedTime(new Date());
                preferences.setUserId(userId);

                emailPreferencesDao.save(preferences);
                log.info("Email preferences created for user: {}", userId);
            }
        } catch (Exception e) {
            log.error("Error inside @method saveEmailPreferences", e);
            throw new BusinessException("Error saving email preferences: " + e.getMessage());
        }
    }


}
