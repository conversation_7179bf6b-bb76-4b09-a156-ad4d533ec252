package com.enttribe.emailagent.service;

import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.UserMailAttachment;
import org.springframework.http.ResponseEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service interface for managing FailureLogs.
 * This interface defines the contract for operations on FailureLogs such as create, read, update, and delete.
 *  <AUTHOR>
 */
public interface FailureLogsService {

    /**
     * Saves a new FailureLog.
     *
     * @param failureLog the FailureLog entity to save
     * @return the saved FailureLog entity
     */
    FailureLogs save(FailureLogs failureLog);
    FailureLogs ingestFailureLog(FailureLogs failureLog);

    /**
     * Processes and ingests a new FailureLog into the system.
     * This method takes various details about the failure event and creates a new `FailureLogs` entry in the database.
     *
     * @param internetMessageId the unique identifier for the email message
     * @param email the email address associated with the failure
     * @param emailSubject the subject of the email
     * @param messageId the unique identifier for the message within the email
     * @param conversationId the unique identifier for the email conversation
     * @param type the type of failure or error encountered
     * @param exceptionMessage a message describing the exception or error
     * @param customExceptionMessage any custom exception message to provide additional context
     * @param exceptionTrace the stack trace of the exception for debugging purposes
     * @param attachment a boolean indicating if there was an attachment involved in the failure
     * @param userMailAttachment an optional attachment associated with the failure
     * @param intent the intent or purpose of the email if relevant
     * @param attendees a list of attendees related to the email or failure
     * @param errorDate the date and time when the error occurred
     * @return the created `FailureLogs` entity
     * @throws IllegalArgumentException if any of the provided parameters are invalid or inconsistent
  //   * @throws DatabaseAccessException if there is an issue accessing or saving data in the database
     */
    FailureLogs ingestFailureLog(String internetMessageId, String email, String emailSubject, String messageId, String conversationId, String type, String exceptionMessage, String customExceptionMessage, String exceptionTrace, Boolean attachment, String userMailAttachment, String intent, String attendees, Date errorDate);


    /**
     * Retrieves all FailureLogs.
     *
     * @return a list of all FailureLogs
     */
    List<FailureLogs> findAll();

    /**
     * Retrieves a FailureLog by its ID.
     *
     * @param id the ID of the FailureLog to retrieve
     * @return the FailureLog entity with the specified ID
     */
    FailureLogs findById(Integer id);

    /**
     * Updates an existing FailureLog by its ID.
     *
     * @param id the ID of the FailureLog to update
     * @param failureLogDetails the FailureLog entity containing updated details
     * @return a ResponseEntity containing the updated FailureLog entity
     */
    FailureLogs update(Integer id, FailureLogs failureLogDetails);

    /**
     * Deletes a FailureLog by its ID.
     *
     * @param id the ID of the FailureLog to delete
     * @return true if the FailureLog was successfully deleted, false otherwise
     */
    Boolean deleteById(Integer id);
    List<FailureLogs> search(
            Integer id,
            String internetMessageId,
            String email,
            String emailSubject,
            String messageId,
            String conversationId,
            String type,
            String exceptionMessage,
            String customExceptionMessage,
            String exceptionTrace,
            String errorDate
    );

    List<FailureLogs> getFailureLogByEmail(String email);

    Long count(Integer id,String internetMessageId,String email, String emailSubject,String messageId,String conversationId,String type,String exceptionMessage,String customExceptionMessage,String exceptionTrace,String errorDate);
 

    List<FailureLogs> failureLogByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap);

    long failureLogCountByFilter( Map<String, Object> filterMap);
    List<MailSummary> mailSummaryByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap);

    long mailSummaryCountByFilter( Map<String, Object> filterMap);

    List<String> typesOfFailureLogs();

    String updateStatus(Integer id, String status);
}
