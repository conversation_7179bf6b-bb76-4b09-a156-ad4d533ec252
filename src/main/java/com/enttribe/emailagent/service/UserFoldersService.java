package com.enttribe.emailagent.service;

import com.enttribe.emailagent.dto.UserFolderDto;
import com.enttribe.emailagent.dto.UserFoldersRecord;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.entity.UserFolders;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.repository.impl.UserFolderDaoImpl;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.EmailConstants;

import lombok.RequiredArgsConstructor;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Collections;


/**
 * The type User folders service.
 *
 *  <AUTHOR> Dangi
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserFoldersService {

    @Autowired
    private UserFolderDaoImpl daoImpl;

    private final UserFoldersRepository repository;
    
    private final UserContextHolder userContextHolder;
    private final GraphIntegrationService graphIntegrationService;
    private final GmailIntegration gmailIntegration;
    private final EmailUserDao emailUserDao;
    private final EwsService ewsService;

    @Value("${pollingMode}")
    private String pollingMode;


    /**
     * Gets users mail folders.
     *
     * @return the users mail folders
     */
    public List<UserFolders> getUsersMailFolders() {
        log.debug("Inside @method getUsersMailFolders.");
        String email = userContextHolder.getCurrentUser().getEmail();

        log.debug("Getting folders for : {}", email);
        boolean result = refreshUsersFolder(email);
        List<UserFolders> folders = result ? repository.findByEmail(email) : new ArrayList<>();

        List<String> priorityFolders = Arrays.asList("Inbox", "Sent Items");
        folders.sort((f1, f2) -> {
            // Check if either folder name is in the priority list
            int index1 = priorityFolders.indexOf(f1.getDisplayName());
            int index2 = priorityFolders.indexOf(f2.getDisplayName());

            // If both folders are in the priority list, sort by their index in the list
            if (index1 != -1 && index2 != -1) {
                return Integer.compare(index1, index2);
            }
            // If only one folder is in the priority list, it comes first
            if (index1 != -1) {
                return -1;
            }
            if (index2 != -1) {
                return 1;
            }
            // If neither folder is in the priority list, sort alphabetically
            return f1.getDisplayName().compareToIgnoreCase(f2.getDisplayName());
        });

        return folders;
    }

    /**
     * Refresh users folder boolean.
     *
     * @param email the email
     * @return the boolean
     */
    public boolean refreshUsersFolder(String email) {
        log.debug("Inside @method updateUsersFolder.");

        List<String> exclusions = getExclusions();

        try {
            // Fetch existing folders from the database
            List<UserFolders> existingUserFolders = repository.findByEmail(email);

            // Fetch current folders from the email server
            List<Map<String, String>> usersMailFolders;
            if (pollingMode.equalsIgnoreCase("EWS")) {
                usersMailFolders = ewsService.getUsersMailFolders(email);
            }
            else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                usersMailFolders = gmailIntegration.usersMailFolders(email);
            }
            else {
                usersMailFolders = graphIntegrationService.getUsersMailFolders(email);
            }

            // Convert current mail folders to a map for easy lookup
            Map<String, String> currentMailFolders = usersMailFolders.stream()
                    .collect(Collectors.toMap(
                            folder -> folder.get(EmailConstants.DISPLAY_NAME),
                            folder -> folder.get("id"),
                            (existingValue, newValue) -> {
                                return existingValue;
                            }
                    ));

            // Update existing folders and add new ones
            for (Map<String, String> mailFolder : usersMailFolders) {
                String displayName = mailFolder.get(EmailConstants.DISPLAY_NAME);
                if (exclusions.contains(displayName)) continue;
                String id = mailFolder.get("id");

                UserFolders userFolders = repository.findByDisplayNameAndEmail(displayName, email);
                if (userFolders == null) {
                    userFolders = new UserFolders();
                    userFolders.setFolderId(id);
                    userFolders.setEmail(email);
                    userFolders.setDisplayName(displayName);
                    userFolders.setActive(displayName.equalsIgnoreCase("Inbox") || displayName.equals("Sent Items") || displayName.equals("SENT"));
                    repository.save(userFolders);
                }
            }

            // Remove folders from the database that no longer exist on the email server
            log.debug("folder from api response is {}", usersMailFolders);
            log.debug("existing user folder is {}", existingUserFolders.toString());
            for (UserFolders userFolder : existingUserFolders) {
                if (!currentMailFolders.containsKey(userFolder.getDisplayName())) {
                    repository.delete(userFolder);
                    log.debug("Deleted folder from database: {}", userFolder.getDisplayName());
                }
            }

            return true;
        } catch (Exception e) {
            log.error("Error inside @method updateUsersFolder", e);
            return false;
        }
    }

    private List<String> getExclusions() {

        if (pollingMode.equalsIgnoreCase("EWS")) {
            return List.of(
                    "Calendar", "Contacts", "Conversation Action Settings",
                    "Deleted Items", EmailConstants.DRAFTS, "ExternalContacts", "Files", "Journal",
                    "Junk Email", "Notes", EmailConstants.OUTBOX, "Tasks", "Yammer Root",
                    "{06967759-274D-40B2-A3EB-D7F9E73727D7}", "{A9E2BC46-B3A0-4243-B315-60D991004455}",
                    "Companies", "GAL Contacts", "Organizational Contacts", "PeopleCentricConversation Buddies",
                    "Recipient Cache", "Feeds", "Inbound", "Outbound", "Quick Step Settings",
                    "Local Failures", "Server Failures", "Sync Issues", "Conflicts", "Birthdays" ,"RSS Subscriptions"
            );
        } else {
            return List.of(
                    "Conversation History", "Deleted Items", EmailConstants.DRAFTS,
                    "Junk Email", EmailConstants.OUTBOX, "Snoozed", "RSS Feeds",
                    "Local Failures", "Server Failures", "Conflicts", "Friends", "Sync Issues", "Birthdays", "RSS Subscriptions"
            );
        }
    }

    /**
     * Update folder status boolean.
     *
     * @param foldersRecords the folders records
     * @return the boolean
     */
    public boolean updateFolderStatus(List<UserFoldersRecord> foldersRecords) {
        log.debug("Inside @method updateFolderStatus.");
        String email = userContextHolder.getCurrentUser().getEmail();
        log.debug("Updating folders status for : {}", email);

        for (UserFoldersRecord record : foldersRecords) {
            UserFolders userFolder = repository.findByIdAndEmail(record.id(), email).orElse(null);
            if (userFolder != null) {
                userFolder.setActive(record.active());
                repository.save(userFolder);
            } else {
                log.error("UserFolder with id {} not found", record.id());
                return false;
            }
        }
        return true;
    }

    /**
     * Save users folder.
     *
     * @throws Exception the exception
     */
//    @Scheduled(fixedRate = 10000)
    //    @Scheduled(cron = "0 0 0 * * ?")
    public void saveUsersFolder() throws Exception {
        List<String> exclusions = List.of(
                "ConversationHistory", "DeletedItems", EmailConstants.DRAFTS,
                "JunkEmail", EmailConstants.OUTBOX, "SentItems", "Snoozed"
        );

        List<EmailUser> allUsers = emailUserDao.findAll();
        List<String> emailList = allUsers.stream().map(EmailUser::getEmail).toList();

        for (String email : emailList) {
            List<Map<String, String>> usersMailFolders = graphIntegrationService.getUsersMailFolders(email);
            for (Map<String, String> mailFolder : usersMailFolders) {
                String displayName = mailFolder.get("displayName").replace(" ", "");
                if (exclusions.contains(displayName)) continue;
                String id = mailFolder.get("id");
                UserFolders userFolders = repository.findByDisplayNameAndEmail(displayName, email);
                if (userFolders == null) userFolders = new UserFolders();
                userFolders.setFolderId(id);
                userFolders.setEmail(email);
                userFolders.setDisplayName(displayName);
                userFolders.setActive(true);
                repository.save(userFolders);
            }
        }
    }


    /**
     * Gets folders group by email.
     *
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @param filterMap the filter map
     * @return the folders group by email
     */
    public List<UserFolderDto> getFoldersGroupByEmail(Integer llimit, Integer ulimit, Map<String, Object> filterMap) {
        try {
            log.info("Processing filter map: {}", filterMap);
            
            List<Object[]> failureMap = daoImpl.getFoldersGroupByEmail(filterMap, llimit, ulimit);
            log.info("failureMap==============={}", failureMap);
            List<UserFolderDto> dtos = new ArrayList<>(); // Initialize the list
    
            for (Object[] obj : failureMap) {
                UserFolderDto dto = new UserFolderDto();
                dto.setEmail((String) obj[0]);
    
                // Handle null check for obj[1] (active folders)
                if (obj[1] != null) {
                    List<String> activeFolders = Arrays.asList(((String) obj[1]).split(","));
                    dto.setActive(activeFolders);
                } else {
                    dto.setActive(Collections.emptyList()); // Set empty list if null
                }
    
                // Handle null check for obj[2] (inactive folders)
                if (obj[2] != null) {
                    List<String> inActiveFolders = Arrays.asList(((String) obj[2]).split(","));
                    dto.setInActive(inActiveFolders);
                } else {
                    dto.setInActive(Collections.emptyList()); // Set empty list if null
                }
    
                dtos.add(dto);
            }
    
            return dtos;
        } catch (Exception e) {
            log.error("An error occurred while getting folders grouped by email", e);
        }
        return null;
    }


    /**
     * Get folders count group by email long.
     *
     * @param filterMap the filter map
     * @return the long
     */
    public long getFoldersCountGroupByEmail(Map<String, Object> filterMap){
        List<Object[]> failureMap = daoImpl.getFoldersCountGroupByEmail(filterMap);
        return failureMap.size();

    }
    
}
