package com.enttribe.emailagent.service;

import java.util.List;
import java.util.Map;

import com.enttribe.emailagent.dto.EmailUserDto;
import com.enttribe.emailagent.entity.Organisation;

/**
 * The interface Organistation service.
 *  <AUTHOR>
 */
public interface OrganistationService {

    Organisation create(Organisation organisation);

    Organisation update(Integer id, Organisation organisation);

    Organisation updateStatus(Integer id, boolean active);

    List<Organisation> organisationByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap);
    
    long organisationCountByFilter(Map<String, Object> filterMap);
    
}
