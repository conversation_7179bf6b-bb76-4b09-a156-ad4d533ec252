package com.enttribe.emailagent.service;

import com.enttribe.emailagent.repository.FailureLogsServiceDao;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * The type Error logging service.
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ErrorLoggingService {

    private final FailureLogsServiceDao failureLogsServiceDao;

    @Async
    public void logError(String errorMessage, String errorType, String userId) {
        // Your async code here
    }

}
