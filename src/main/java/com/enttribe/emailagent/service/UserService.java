package com.enttribe.emailagent.service;

import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

import static com.enttribe.emailagent.utils.ExceptionUtils.getStackTraceAsString;

/**
 * The type User service.
 *  <AUTHOR> Dangi
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserService {

    private final RestTemplate restTemplate;

    @Value("${userCreateUrl}")
    private String url;

    private static final Logger auditLog = EmailAgentLogger.getLogger(UserService.class);

    /**
     * Create user in platform .
     *
     * @param requestMap the request map
     * @param token      the token
     * @return the boolean
     */
    public boolean createUserInPlatform(Map<String, String> requestMap, String token) {
        log.debug("Inside @method createUserInPlatform. @param : requestMap -> {}", requestMap);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, String>> request = new HttpEntity<>(requestMap, headers);

            ResponseEntity<Void> response = restTemplate.postForEntity(url, request, Void.class);
            log.debug("Response code : {}", response.getStatusCode());
            return true;
        } catch (RestClientException e) {
            log.error("Error while creating user in PLATFORM. Exception message : {}", e.getMessage());
            auditLog.error("Error while creating user in PLATFORM", e.getMessage(), getStackTraceAsString(e), null, null, "CREATE_USER", null, null, null, null, null, AIUtils.convertToJSON(requestMap, true), null);
            return false;
        }
    }

}
