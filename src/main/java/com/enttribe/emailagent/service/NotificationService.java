package com.enttribe.emailagent.service;

import com.enttribe.emailagent.ai.dto.mail_summary.MailSummaryAIResponse;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.config.SpringContext;
import com.enttribe.emailagent.dto.Attachments;
import com.enttribe.emailagent.dto.NotificationDto;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.PollTimeInfoRepository;
import com.enttribe.emailagent.utils.FCMTokenUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * The type Notification service.
 *  <AUTHOR> Sonsale
 */
@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnExpression("#{environment.getProperty('kafka.enabled', 'false') == 'false' and environment.getProperty('notification.enable', 'false') == 'true'}")
public class NotificationService {

    private final IMailSummaryDao mailSummaryDao;
    private final EmailUserDao emailUserDao;
    private final PollTimeInfoRepository pollTimeInfoRepository;
    private final PollTimeInfoService pollTimeInfoService;
    private final IEmailPreferencesDao preferencesDao;
    private final FCMTokenUtils fcmTokenUtils;
    @Autowired
    private ImailSummaryService imailSummaryService;

    @Value("${notification.url}")
    private String url;

    /**
     * Send notifications.
     */
    @Scheduled(fixedDelay = 5 * 60 * 1000)
    public void sendNotifications() {
        log.debug("Inside @method sendNotifications");

        List<String> activeUsers = emailUserDao.findAllActiveUserIds();

        for (String userId : activeUsers) {

            List<MailSummary> mailsForNotification = mailSummaryDao.getMailsForNotification(userId, LocalDateTime.now().minusHours(1));
            imailSummaryService.addAttachmentDetails(mailsForNotification);
            log.debug("Sending notifications for total : {} mails of user : {}", mailsForNotification.size(), userId);
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
            if (preferences == null || preferences.getDeviceId() == null) continue;

            for (MailSummary mailSummary : mailsForNotification) {
                if (mailSummary.getFolderName() == null || mailSummary.getFolderName().equalsIgnoreCase("Sent Items")) continue;
                String deviceId = preferences.getDeviceId();
                NotificationDto notificationDto = convertMailSummaryToNotificationDto(mailSummary);
                String title = String.format("%s\n%s", mailSummary.getFromUser(), notificationDto.getSubject());
                String data = AIUtils.convertToJSON(notificationDto, true);
                String response = sendFCMNotification(deviceId, title, notificationDto.getContent(), data);
                if (response.equals("success")) {
                    mailSummaryDao.updateNotificationStatus(MailSummary.NotificationStatus.SENT, mailSummary.getId());
                } else {
                    mailSummaryDao.updateNotificationStatus(MailSummary.NotificationStatus.FAILED, mailSummary.getId());
                }
            }
        }
    }

    private NotificationDto convertMailSummaryToNotificationDto(MailSummary mailSummary) {
        String messageSummary = mailSummary.getMessageSummary();
        String content;
        if (mailSummary.getType().equalsIgnoreCase("Email")) {
            content = getContentOfMailSummary(messageSummary);
        } else {
            content = mailSummary.getMeetingPreview();
        }

        List<Attachments> attachmentsList = mailSummary.getAttachmentsList();
        List<String> graphDocIdList = new ArrayList<>();
        String graphDocIds = "";
        if (attachmentsList != null && !attachmentsList.isEmpty()) {
//            graphDocIdList = attachmentsList.stream().map(Attachments::getGraphDocId).toList();
            graphDocIdList = List.of("abc", "def", "123");
            graphDocIds = String.join(",", graphDocIdList);
        }

        return NotificationDto.builder()
                .conversationId(mailSummary.getConversationId())
                .messageId(mailSummary.getMessageId())
                .category(mailSummary.getCategory())
                .priority(mailSummary.getPriority())
                .subject(mailSummary.getSubject())
                .graphDocIds(graphDocIds)
                .type(mailSummary.getType())
                .content(content)
                .fromUser(mailSummary.getFromUser())
                .internetMessageId(mailSummary.getInternetMessageId())
                .folderName(mailSummary.getFolderName())
                .build();
    }

    /**
     * Send fcm notification .
     *
     * @param deviceId the device id
     * @param title    the title
     * @param body     the body
     * @param data     the data
     * @return the string
     */
    public String sendFCMNotification(String deviceId, String title, String body, String data) {
        // Create FCM payload using JSONObject
        JSONObject payload = new JSONObject();
        JSONObject message = new JSONObject();
        JSONObject notification = new JSONObject();
        JSONObject android = new JSONObject();
        JSONObject androidNotification = new JSONObject();
        JSONObject apns = new JSONObject();
        JSONObject apnsPayload = new JSONObject();
        JSONObject aps = new JSONObject();

        // Build notification part
        notification.put("title", title);
        notification.put("body", body);
        message.put("token", deviceId);
        message.put("notification", notification);

        // Build android part
        androidNotification.put("click_action", "OPEN_ACTIVITY");
        android.put("notification", androidNotification);
        message.put("android", android);
        // Convert data from string to JSONObject
        JSONObject dataObject = new JSONObject(data);
        message.put("data", dataObject);

        // Build iOS (APNS) part
        aps.put("badge", 1);
        apnsPayload.put("aps", aps);
        apns.put("payload", apnsPayload);
        message.put("apns", apns);

        // Complete payload
        payload.put("message", message);


        // Make the HTTP request
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Authorization", "Bearer " + fcmTokenUtils.getAccessToken());
            httpPost.setHeader("Content-Type", "application/json");

            // Set the JSON payload
            StringEntity entity = new StringEntity(payload.toString());
            httpPost.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity());

                // Log status and response
                log.debug("Response Status Code: {}", statusCode);
                log.debug("Response Body: {}", responseBody);

                // Check if the request was successful
                if (statusCode == 200) {
                    return "success";
                } else {
                    return "failed";
                }
            }
        } catch (Exception e) {
            log.debug("Error inside @method sendFCMMessage", e);
            return "failed";
        }
    }

    public String sendFCMNotification(String deviceId, MailSummary mailSummary) {
        NotificationDto notificationDto = convertMailSummaryToNotificationDto(mailSummary);
        String title = String.format("%s\n%s", mailSummary.getFromUser(), notificationDto.getSubject());
        String data = AIUtils.convertToJSON(notificationDto, true);
        return sendFCMNotification(deviceId, title, notificationDto.getContent(), data);
    }


    /**
     * Gets bearer token.
     *
     * @param serviceAccountKeyPath the service account key path
     * @return the bearer token
     */
    public static String getBearerToken(String serviceAccountKeyPath) {
        try {
            // Load the service account JSON key
            GoogleCredentials credentials = GoogleCredentials
                    .fromStream(new FileInputStream(serviceAccountKeyPath))
                    .createScoped("https://www.googleapis.com/auth/firebase.messaging");

            // Refresh the token if expired, to get a new token
            credentials.refreshIfExpired();

            // Return the Bearer token string
            return credentials.getAccessToken().getTokenValue();
        } catch (IOException e) {
            log.error("Error while getting notification token", e);
            return null;
        }
    }

    private String getContentOfMailSummary(String mailSummary) {
        if (mailSummary == null || mailSummary.equals("{}")) return "";
        ObjectMapper objectMapper = SpringContext.getBean(ObjectMapper.class);
        try {
            MailSummaryAIResponse mailSummaryAIResponse = objectMapper.readValue(mailSummary, MailSummaryAIResponse.class);
            if (mailSummaryAIResponse.getSummaryObject() != null) {
                return mailSummaryAIResponse.getSummaryObject().getContent();
            }
        } catch (Exception e) {
            log.error("Error while extracting summaryContent", e);
        }
        return "";
    }


}
