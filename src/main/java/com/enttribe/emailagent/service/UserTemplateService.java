package com.enttribe.emailagent.service;

import com.enttribe.emailagent.entity.UserTemplate;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.repository.UserTemplateRepository;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.EmailConstants;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * The type User template service.
 *  <AUTHOR> <PERSON>ak
 */
@Service
@Slf4j
public class UserTemplateService {

    @Autowired
    private UserTemplateRepository templateRepository;

    @Autowired
    private UserContextHolder userContextHolder;

    /**
     * Add map.
     *
     * @param requestBody the request body
     * @return the map
     */
    public Map<String, String> add(Map<String, String> requestBody) {
        log.debug("Inside @method add. @param : requestBody -> {}", requestBody);
        String email = userContextHolder.getCurrentUser().getEmail();
        UserTemplate userTemplate = templateRepository.findByEmail(email);
        Map<String, String> responseMap = new HashMap<>();
        if (userTemplate == null) {
            userTemplate = new UserTemplate();
            userTemplate.setEmail(email);
            userTemplate.setDataMap(requestBody);
            templateRepository.save(userTemplate);
            responseMap.put(EmailConstants.RESULT, "Template created successfully");
        } else {
            Map<String, String> dataMap = getDataMap(userTemplate);

            if (dataMap == null) dataMap = new HashMap<>();
            Set<Map.Entry<String, String>> entries = requestBody.entrySet();
            for (Map.Entry<String, String> entry : entries) {
                if (dataMap.containsKey(entry.getKey())) {
                    responseMap.put(EmailConstants.RESULT, "Template name already exist");
                    return responseMap;
                } else {
//                    String escapedString = entry.getValue().replace("\"", "\\\"");
                    dataMap.put(entry.getKey(), entry.getValue());
                }

            }
            userTemplate.setDataMap(dataMap);
            templateRepository.save(userTemplate);
            responseMap.put(EmailConstants.RESULT, "Template created successfully");
        }


        return responseMap;
    }

    /**
     * Update map.
     *
     * @param requestBody the request body
     * @return the map
     */
    public Map<String, String> update(Map<String, String> requestBody) {
        log.debug("Inside @method add. @param : requestBody -> {}", requestBody);
        String email = userContextHolder.getCurrentUser().getEmail();
        UserTemplate userTemplate = templateRepository.findByEmail(email);
        Map<String, String> responseMap = new HashMap<>();
        if (userTemplate == null) {
            responseMap.put(EmailConstants.RESULT, "Template does not exist");
            return responseMap;
        } else {
            Map<String, String> dataMap = getDataMap(userTemplate);

            if (dataMap == null) dataMap = new HashMap<>();
            Set<Map.Entry<String, String>> entries = requestBody.entrySet();
            for (Map.Entry<String, String> entry : entries) {
                dataMap.put(entry.getKey(), entry.getValue());
            }
            userTemplate.setDataMap(dataMap);
            templateRepository.save(userTemplate);
            responseMap.put(EmailConstants.RESULT, "Template updated successfully");
        }


        return responseMap;
    }

    private Map<String, String> getDataMap(UserTemplate userTemplate) {
        Map<String, String> dataMap;
        try {
            dataMap = userTemplate.getDataMap();
        } catch (Exception e) {
            log.error("Error inside @method getDataMap.", e);
            throw new BusinessException(e.getMessage());
        }
        return dataMap;
    }

    /**
     * Delete map.
     *
     * @param key the key
     * @return the map
     */
    public Map<String, String> delete(String key) {
        log.debug("Inside @method delete. @param : key -> {}", key);
        String email = userContextHolder.getCurrentUser().getEmail();
        UserTemplate userTemplate = templateRepository.findByEmail(email);
        if (userTemplate == null) throw new ResourceNotFoundException("No template is available for the user.");

        Map<String, String> dataMap = getDataMap(userTemplate);
        String removed = dataMap.remove(key);
        if (removed == null) throw new ResourceNotFoundException("No entry found in the template for the given key");
        userTemplate.setDataMap(dataMap);
        templateRepository.save(userTemplate);
        Map<String, String> responseMap = new HashMap<>();
        responseMap.put(EmailConstants.RESULT, "success");
        return responseMap;
    }

    /**
     * Get map.
     *
     * @param key the key
     * @return the map
     */
    public Map<String, Object> get(String key) {
        log.debug("Inside @method get. @param : key -> {}", key);

        String email = userContextHolder.getCurrentUser().getEmail();
        log.debug("Fetched email: {}", email);

        UserTemplate userTemplate = templateRepository.findByEmail(email);
        if (userTemplate == null) throw new ResourceNotFoundException("No template is available for the user.");
        Map<String, String> dataMap = getDataMap(userTemplate);
        log.debug("Fetched dataMap: {}", dataMap);

        if (key == null || key.isBlank()) {
            Object stringMap = searchKeysContaining("", dataMap);
            log.debug("Search result for empty key: {}", stringMap);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put(EmailConstants.RESULT, stringMap);
            return responseMap;
        }

        Object stringMap = searchKeysContaining(key, dataMap);
        log.debug("Search result for key '{}': {}", key, stringMap);

        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put(EmailConstants.RESULT, stringMap);
        return responseMap;
    }

    private Map<String, String> searchKeysContaining(String substring, Map<String, String> map) {
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getKey().toLowerCase().contains(substring.toLowerCase())) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        return result;
    }

}
