package com.enttribe.emailagent.service;

import com.enttribe.emailagent.ai.dto.meeting.AvailableTimeSlots;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.dao.UserMailAttachmentRepository;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.dto.OutOfOfficeDto;
import com.enttribe.emailagent.dto.UnReadEmail;
import com.enttribe.emailagent.dto.UserEmailDto;
import com.enttribe.emailagent.dto.UserEmailResponseDto;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.UserFolders;
import com.enttribe.emailagent.entity.UserMailAttachment;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.enttribe.emailagent.utils.*;
import com.enttribe.emailagent.wrapper.AvailableSlots;
import com.enttribe.emailagent.wrapper.MessageWrapper;
import com.enttribe.emailagent.wrapper.OutOfOffice;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.PropertySet;
import microsoft.exchange.webservices.data.core.enumeration.property.*;
import microsoft.exchange.webservices.data.core.enumeration.property.time.DayOfTheWeek;
import microsoft.exchange.webservices.data.core.enumeration.property.time.Month;
import microsoft.exchange.webservices.data.core.enumeration.search.FolderTraversal;
import microsoft.exchange.webservices.data.core.enumeration.search.LogicalOperator;
import microsoft.exchange.webservices.data.core.enumeration.search.SortDirection;
import microsoft.exchange.webservices.data.core.enumeration.service.ConflictResolutionMode;
import microsoft.exchange.webservices.data.core.enumeration.service.DeleteMode;
import microsoft.exchange.webservices.data.core.enumeration.service.SendInvitationsMode;
import microsoft.exchange.webservices.data.core.enumeration.service.SendInvitationsOrCancellationsMode;
import microsoft.exchange.webservices.data.core.enumeration.service.error.ServiceErrorHandling;
import microsoft.exchange.webservices.data.core.exception.service.local.ServiceLocalException;
import microsoft.exchange.webservices.data.core.exception.service.remote.ServiceResponseException;
import microsoft.exchange.webservices.data.core.response.FindItemResponse;
import microsoft.exchange.webservices.data.core.response.ServiceResponseCollection;
import microsoft.exchange.webservices.data.core.service.folder.CalendarFolder;
import microsoft.exchange.webservices.data.core.service.folder.Folder;
import microsoft.exchange.webservices.data.core.service.item.Appointment;
import microsoft.exchange.webservices.data.core.service.item.EmailMessage;
import microsoft.exchange.webservices.data.core.service.item.Item;
import microsoft.exchange.webservices.data.core.service.item.MeetingRequest;
import microsoft.exchange.webservices.data.core.service.response.ResponseMessage;
import microsoft.exchange.webservices.data.core.service.schema.EmailMessageSchema;
import microsoft.exchange.webservices.data.core.service.schema.ItemSchema;
import microsoft.exchange.webservices.data.misc.availability.OofReply;
import microsoft.exchange.webservices.data.misc.availability.TimeWindow;
import microsoft.exchange.webservices.data.property.complex.*;
import microsoft.exchange.webservices.data.property.complex.availability.OofSettings;
import microsoft.exchange.webservices.data.property.complex.recurrence.pattern.Recurrence;
import microsoft.exchange.webservices.data.property.definition.ExtendedPropertyDefinition;
import microsoft.exchange.webservices.data.search.*;
import microsoft.exchange.webservices.data.search.filter.SearchFilter;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.enttribe.emailagent.utils.CommonUtils.*;
import static com.enttribe.emailagent.utils.EWSUtils.convertToMessageWrapper;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.*;
import static com.google.common.base.Throwables.getStackTraceAsString;

/**
 * The type Ews service.
 *  <AUTHOR> Dangi
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EwsService {

    private final EmailUserDao emailUserDao;
    private final IEmailPreferencesDao preferencesDao;
    private final UserContextHolder userContextHolder;
    private final IMailSummaryDao mailSummaryDao;
    private final GraphIntegrationService graphIntegrationService;
    private final UserMailAttachmentRepository userMailAttachmentRepository;
    private final UserFoldersRepository foldersRepository;

    @Value("${app.supported.formats}")
    private List<String> supportedFormats;

    @Value("${email.body.content.token.limit}")
    private Integer bodyTokenLimit;

    @Value("${org.domain.name}")
    private List<String> orgDomainName;

    @Value("${outsiders.events.lookup.allow}")
    private Boolean allowOutsiders;

    @Value("${s3.bucketName}")
    private String s3BucketName;

    private static String uploadUrl;
    private static String attachmentFilePath;
    private static String answerUrl;

    private static final Logger auditLog = EmailAgentLogger.getLogger(EwsService.class);

    @Value("${generatedBy}")
    private String generatedBy;

    @Value("${documentUploadUrl}")
    public void setUploadUrl(String uploadUrl) {
        this.uploadUrl = uploadUrl;
    }

    @Value("${attachmentFilePath}")
    public void setAttachmentFilePath(String attachmentFilePath) {
        this.attachmentFilePath = attachmentFilePath;
    }

    @Value("${answerUrl}")
    public void setAnswerUrl(String answerUrl) {
        this.answerUrl = answerUrl;
    }

    @Autowired
    private S3Service s3Service;


    /**
     * Gets auto reply settings for user.
     *
     * @param userEmail the user email
     * @return the auto reply settings for user
     * @throws Exception the exception
     */
    public Map<String, Object> getAutoReplySettingsForUser(String userEmail) throws Exception {
        if (!EmailUtils.checkDomain(userEmail, orgDomainName)) return null;
        log.debug("Inside @method getAutoReplySettingsForUser. @param: userEmail -> {}", userEmail);

        // Create the ExchangeService object
        ExchangeService service = EWSUtils.getServiceObjectForUser(userEmail);

        // Fetch the OutOfOfficeSettings for the user
        OofSettings oofSettings = service.getUserOofSettings(userEmail);

        // Prepare the date format for time zone processing
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS");
        dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

        // Initialize a map to store the response
        Map<String, Object> responseMap = new HashMap<>();

        // Set the external audience (None, Known, All)
        OofExternalAudience externalAudience = oofSettings.getExternalAudience();
        responseMap.put("externalAudience", externalAudience != null ? externalAudience.toString().toLowerCase() : "none");

        // Set the OOF status (Disabled or Scheduled)
        OofState oofState = oofSettings.getState();
        String status = oofState == OofState.Disabled ? "disabled" : "scheduled";
        responseMap.put("status", status);

        // Set the internal and external reply messages
        String internalReply = oofSettings.getInternalReply().getMessage();
        String externalReply = oofSettings.getExternalReply().getMessage();
        responseMap.put("internalReplyMessage", internalReply != null ? internalReply.toString() : "");
        responseMap.put("externalReplyMessage", externalReply != null ? externalReply.toString() : "");

        // Set the scheduled start and end times
        if (oofSettings.getDuration() != null) {
            responseMap.put("scheduledStartDateTime", dateFormat.format(oofSettings.getDuration().getStartTime()));
            responseMap.put("scheduledEndDateTime", dateFormat.format(oofSettings.getDuration().getEndTime()));
            responseMap.put("scheduledStartTimeZone", "UTC");
            responseMap.put("scheduledEndTimeZone", "UTC");
        } else {
            responseMap.put("scheduledStartDateTime", "");
            responseMap.put("scheduledEndDateTime", "");
            responseMap.put("scheduledStartTimeZone", "");
            responseMap.put("scheduledEndTimeZone", "");
        }

        return responseMap;
    }

    /**
     * Cancel auto reply.
     *
     * @param userEmail the user email
     * @throws Exception the exception
     */
    public void cancelAutoReply(String userEmail) throws Exception {
        log.debug("Inside @method cancelAutoReply. @param: userEmail -> {}", userEmail);

        // Create the ExchangeService object
        ExchangeService service = EWSUtils.getServiceObjectForUser(userEmail);

        // Fetch the current OutOfOfficeSettings for the user
        OofSettings oofSettings = service.getUserOofSettings(userEmail);

        // Set the OutOfOffice state to Disabled
        oofSettings.setState(OofState.Disabled);

        // Update the settings for the user
        service.setUserOofSettings(userEmail, oofSettings);

        log.info("Auto-reply has been successfully cancelled for user: {}", userEmail);
    }

    /**
     * Sets auto reply for user.
     *
     * @param userEmail              the user email
     * @param internalReplyMessage   the internal reply message
     * @param externalReplyMessage   the external reply message
     * @param scheduledStartDateTime the scheduled start date time
     * @param scheduledEndDateTime   the scheduled end date time
     * @param timeZone               the time zone
     * @throws Exception the exception
     */
    public void setAutoReplyForUser(String userEmail, String internalReplyMessage, String externalReplyMessage,
                                    String scheduledStartDateTime, String scheduledEndDateTime, String timeZone) throws Exception {
        // Create the ExchangeService object
        ExchangeService service = EWSUtils.getServiceObjectForUser(userEmail);

        // Set timezone and parse the start and end times
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        dateFormat.setTimeZone(TimeZone.getTimeZone(timeZone));

        // Parse the provided date strings
        java.util.Date startDate = dateFormat.parse(scheduledStartDateTime);
        java.util.Date endDate = dateFormat.parse(scheduledEndDateTime);

        // Create TimeWindow object for the specified start and end times
        TimeWindow timeWindow = new TimeWindow(startDate, endDate);

        // Set up OutOfOfficeSettings
        OofSettings oofSettings = new OofSettings();
        oofSettings.setState(OofState.Scheduled);
        oofSettings.setDuration(timeWindow);

        // Set internal and external reply messages
        oofSettings.setInternalReply(new OofReply(internalReplyMessage));
        oofSettings.setExternalReply(new OofReply(externalReplyMessage));

        // Update the OutOfOffice settings for the user
        service.setUserOofSettings(userEmail, oofSettings);

        log.debug("Auto-reply has been successfully set for user: {}", userEmail);
    }

    /**
     * Gets emails of user.
     *
     * @param userId     the user id
     * @param email      the email
     * @param timeFilter the time filter
     * @param folder     the folder
     * @return the emails of user
     * @throws Exception the exception
     */
    public UserEmailResponseDto getEmailsOfUser(String userId, String email, String timeFilter, String folder) throws Exception {
        ExchangeService service = EWSUtils.getServiceObjectForUser(email);
        FolderId folderId = new FolderId(folder);
        Folder mailFolder = Folder.bind(service, folderId);

        ItemView view = new ItemView(100);
//        view.getOrderBy().add(ItemSchema.DateTimeReceived, SortDirection.Descending);
//        view.setTraversal(ItemTraversal.Shallow);
//        Date date = new Date(new Date().getTime() - 86400000);
        Date date = DateUtils.parseDate(timeFilter);
        SearchFilter filter = new SearchFilter.IsGreaterThan(ItemSchema.DateTimeReceived, date);
        FindItemsResults<Item> findResults = service.findItems(mailFolder.getId(), filter, view);
        PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
        propertySet.setRequestedBodyType(BodyType.Text);
        UserEmailResponseDto responseDto = new UserEmailResponseDto();

        if (findResults != null && !findResults.getItems().isEmpty()) {
            service.loadPropertiesForItems(findResults, propertySet);

            List<UserEmailDto> emailDtos = new ArrayList<>();

            for (Item item : findResults) {
                if (item instanceof EmailMessage emailMessage) {
                    UserEmailDto userEmailDto = getUserEmailDto(email, emailMessage, true);
                    emailDtos.add(userEmailDto);
                }
            }

            List<List<UserEmailDto>> messages = graphIntegrationService.groupByEmailConversation(emailDtos);
            responseDto.setMailBoxUserEmail(email);
            responseDto.setMailBoxUserId(email);
            responseDto.setMessages(messages);
        }
        return responseDto;
    }

    /**
     * Gets unread emails.
     *
     * @param email    the email
     * @param folderId the folder id
     * @return the unread emails
     */
    public List<UnReadEmail> getUnreadEmails(String email, String folderId) {
            try {
                ExchangeService service = EWSUtils.getServiceObjectForUser(email);
                FolderId folder = new FolderId(folderId);
                Folder mailFolder = Folder.bind(service, folder);

                ItemView view = new ItemView(100);

                SearchFilter filter = new SearchFilter.IsEqualTo(EmailMessageSchema.IsRead, false);
                FindItemsResults<Item> findResults = service.findItems(mailFolder.getId(), filter, view);
                PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
                propertySet.setRequestedBodyType(BodyType.Text);

                if (findResults != null && !findResults.getItems().isEmpty()) {
                    service.loadPropertiesForItems(findResults, propertySet);

                    List<UnReadEmail> unReadEmails = new ArrayList<>();

                    for (Item item : findResults) {
                        if (item instanceof EmailMessage emailMessage) {
                            String subject = emailMessage.getSubject();
                            Boolean isRead = !emailMessage.getIsRead();
                            String internetMessageId = emailMessage.getInternetMessageId();
                            UnReadEmail unReadEmail = new UnReadEmail();
                            unReadEmail.setSubject(subject);
                            unReadEmail.setIsUnread(isRead);
                            unReadEmail.setInternetMessageId(internetMessageId);
                            unReadEmails.add(unReadEmail);
                        }
                    }
                    return unReadEmails;
                }
            } catch (Exception e) {
                Map<String, String> auditMap = Map.of("email", email, "folderId", folderId);
                auditLog.error("Error while getting unread emails from EWS", e.getMessage(), getStackTraceAsString(e), null, null, "DELETE_EMAILS_SCHEDULER", email, null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            }
            return new ArrayList<>();
        }

    private UserEmailDto getUserEmailDto(String email, EmailMessage emailMessage, boolean forPolling) throws ServiceLocalException {
        EmailAddress messageFrom = emailMessage.getFrom();
        String from = "";
        if (messageFrom != null) {
            from = emailMessage.getFrom().getAddress();
        }
        String type = emailMessage instanceof MeetingRequest ? "Meeting" : "Email";
        List<String> toRecipients = getEmailList(emailMessage.getToRecipients().getItems());
        List<String> ccRecipients = getEmailList(emailMessage.getCcRecipients().getItems());
        List<String> bccRecipients = getEmailList(emailMessage.getBccRecipients().getItems());
        String subject = emailMessage.getSubject();
        String id = emailMessage.getId().getUniqueId();
        String internetMessageId = emailMessage.getInternetMessageId();
        String conversationId = emailMessage.getConversationId().getUniqueId();

        String bodyPreview = emailMessage.getBody().toString();
        String body = emailMessage.getBody().toString();

        if (forPolling) {
            String firstMessageId = getMessageIdOfFirstEmailInConversation(email, conversationId);
            int indexOfMailEnd = body.indexOf("From: ");
            if (indexOfMailEnd != -1 && firstMessageId != null && !firstMessageId.equals(id)) {
                body = body.substring(0, indexOfMailEnd);
            }
            body = AIUtils.reduceTextToMaxTokens(body, bodyTokenLimit);
        }

        Date dateTimeCreated = emailMessage.getDateTimeCreated();
        boolean hasAttachments = emailMessage.getHasAttachments();

        UserEmailDto userEmailDto = new UserEmailDto();
        userEmailDto.setId(id);
        userEmailDto.setFrom(from);
        userEmailDto.setSubject(subject);
        userEmailDto.setBodyPreview(bodyPreview);
        userEmailDto.setBody(body);
        userEmailDto.setType(type);
        userEmailDto.setToRecipients(toRecipients);
        userEmailDto.setCcRecipients(ccRecipients);
        userEmailDto.setBccRecipients(bccRecipients);
        userEmailDto.setConversationId(conversationId);
        userEmailDto.setCreatedTime(dateTimeCreated);
        userEmailDto.setMeetingEndTime(null);
        userEmailDto.setHasAttachments(hasAttachments);
        userEmailDto.setInternetMessageId(internetMessageId);
        userEmailDto.setImportance(emailMessage.getImportance().toString());
        return userEmailDto;
    }

    /**
     * Gets deleted emails of user.
     *
     * @param email      the email
     * @param timeFilter the time filter
     * @return the deleted emails of user
     */
    public List<UserEmailDto> getDeletedEmailsOfUser(String email, String timeFilter) {
        List<UserEmailDto> emailDtos = new ArrayList<>();

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            Folder mailFolder = Folder.bind(service, WellKnownFolderName.DeletedItems);

            ItemView view = new ItemView(100);
            Date date = DateUtils.parseDate(timeFilter);
            SearchFilter filter = new SearchFilter.IsGreaterThan(ItemSchema.LastModifiedTime, date);
            FindItemsResults<Item> findResults = service.findItems(mailFolder.getId(), filter, view);
            PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
            service.loadPropertiesForItems(findResults, propertySet);


            for (Item item : findResults) {
                if (item instanceof EmailMessage emailMessage) {
                    String subject = emailMessage.getSubject();
                    String id = emailMessage.getId().getUniqueId();
                    String internetMessageId = emailMessage.getInternetMessageId();

                    UserEmailDto userEmailDto = new UserEmailDto();
                    userEmailDto.setId(id);
                    userEmailDto.setSubject(subject);
                    userEmailDto.setInternetMessageId(internetMessageId);

                    emailDtos.add(userEmailDto);
                }
            }
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("timeFilter", timeFilter);
            auditLog.error("Error while getting deleted emails from EWS", e.getMessage(), getStackTraceAsString(e), null, null, "DELETE_EMAILS_SCHEDULER", email, null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
        }
        return emailDtos;
    }

    private List<String> getEmailList(List<EmailAddress> emailAddresses) {
        return emailAddresses.stream().map(EmailAddress::getAddress).toList();
    }


    /**
     * Summarize mail attachment.
     *
     * @param conversationId    the conversation id
     * @param internetMessageId the internet message id
     * @param email             the email
     * @param messageId         the message id
     * @param userId            the user id
     * @param subject           the subject
     * @throws Exception the exception
     */
    public void summarizeMailAttachment(String conversationId, String internetMessageId, String email, String messageId, String userId, String subject) throws Exception {
        log.debug("Inside @method summarizeMailAttachment. @param : email -> {} messageId -> {}", email, messageId);
        EmailUser user = emailUserDao.findByEmail(email);
        EmailMessage emailMessage = getEmailByMessageId(email, messageId);
        if (emailMessage == null) {
            log.error("No attachment is found for email : {} and messageId : {}", email, messageId);
            return;
        }
        List<Attachment> attachments = emailMessage.getAttachments().getItems();
        log.debug("Attachment size is : {}", attachments.size());
        for (Attachment attachment1 : attachments) {
            if (attachment1 instanceof FileAttachment fileAttachment) {
                String uniqueName = internetMessageId + "_" + fileAttachment.getName();
                log.debug("Going to search document {}", uniqueName);
                String status = userMailAttachmentRepository.processingStatusForDocument(uniqueName);
                if (status != null && ((status.equalsIgnoreCase("NEW")) || (status.equalsIgnoreCase("IN_PROGRESS")) ||
                        (status.equalsIgnoreCase("COMPLETED")) || (status.equalsIgnoreCase("ERROR")))) {
                    log.debug("Record exists {} ", status);
                    continue;
                }
                log.debug("FileAttachment name : {}", fileAttachment.getFileName());
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                fileAttachment.load(outputStream);
                byte[] fileBytes = outputStream.toByteArray();
                String attachmentName = fileAttachment.getName();

                int indexOfDot = attachmentName.lastIndexOf(".");
                String format = "";
                if (indexOfDot != -1) format = attachmentName.substring(indexOfDot + 1);
                if (!supportedFormats.contains(format)) {
                    log.debug("Format : {} is not supported for processing", format);
                    continue;
                }

                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

                Path tempFile = Files.createTempFile(Paths.get(attachmentFilePath), "attachment_", "_" + timestamp + "_" + attachmentName);

                try (FileOutputStream fos = new FileOutputStream(tempFile.toFile())) {
                    fos.write(fileBytes);

                    // Populate the UserMailAttachment entity
                    UserMailAttachment attachment = new UserMailAttachment();
                    attachment.setUserId(email);
                    attachment.setMessageId(messageId);
                    attachment.setUniqueName(uniqueName);
                    attachment.setName(attachmentName);
                    attachment.setType(fileAttachment.getContentType());
                    attachment.setAttachmentId(fileAttachment.getId());
                    attachment.setConversationId(conversationId);
                    attachment.setLongSummary(null);
                    attachment.setShortSummary(null);
                    attachment.setRagDocumentId(null);
                    attachment.setCreationTime(new Date());
                    attachment.setModifiedTime(new Date());
                    attachment.setSubject(subject);
                    attachment.setBatchId(user.getBatchId());
                    // Upload the file to S3 and get the file URL
                    //String s3BucketName = "emailAttachments";
                    String s3Key = userId + "/attachments/" + timestamp + "_" + attachmentName;
                    String s3Url = "";
                    try {
                        s3Url = s3Service.uploadFile(s3BucketName, s3Key, tempFile.toString());
                    } catch (Exception e) {
                        auditLog.error("Error inside @method upload file to S3", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, UPLOAD_S3, userContextHolder.getCurrentUser().getId(), subject, conversationId, true, null, null, null);

                    }
                    attachment.setDocPath(s3Key);
                    attachment.setProcessingStatus("NEW");
                    attachment.setInternetMessageId(internetMessageId);
                    userMailAttachmentRepository.save(attachment);
                    log.debug("File uploaded on s3 {}", s3Key);

                } catch (IOException e) {
                    auditLog.error("Error inside @method summarizeMailAttachment", e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, PROCESS_ATTACHMENT, email, null, conversationId, true, null, null, null);
                    log.error("An error occurred while writing the file or saving the attachment for user {} for subject {}", e, userId, subject);
                } finally {
                    try {
                        Files.deleteIfExists(tempFile);
                        log.debug("Temporary file deleted successfully");
                    } catch (IOException e) {
                        log.error("Failed to delete temporary file  for user {} for subject {}", e, userId, subject);
                    }
                }

            }
        }
    }

    /**
     * Gets email by message id.
     *
     * @param email     the email
     * @param messageId the message id
     * @return the email by message id
     */
    public EmailMessage getEmailByMessageId(String email, String messageId) {
        log.debug("Inside @method getEmailByMessageId. @param: email -> {}, messageId -> {}", email, messageId);

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            // Bind to the existing email message by its ID
            ItemId itemId = new ItemId(messageId);
            EmailMessage emailMessage = EmailMessage.bind(service, itemId);
            PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
            propertySet.setRequestedBodyType(BodyType.Text);
            emailMessage.load(propertySet);
            return emailMessage;
        } catch (Exception e) {
            auditLog.error("Error inside @method getEmailByMessageId", e.getMessage(), getStackTraceAsString(e), messageId, null, EWS_NETWORK_CALL, email, null, null, null, null, null, null);
//            log.error("Error inside @method getEmailByMessageId", e);
            return null;
        }
    }


    /**
     * Retrieves the details of an email by its message ID from the Exchange server.
     *
     * @param email     The email address of the user whose mailbox is being accessed.
     * @param messageId The ID of the email message to be retrieved.
     * @return A {@code MessageWrapper} object containing the details of the email message, or an empty {@code MessageWrapper} if an error occurs.
     */
    public MessageWrapper getMailDetails(String email, String messageId) {
        try {
            ExchangeService service = getExchangeService(email);
            EmailMessage emailMessage = getEmailMessage(service, messageId);
            loadEmailMessage(emailMessage);
            return convertToMessageWrapper(emailMessage);
        } catch (Exception e) {
            log.error("Error inside getMailDetails for email: {}", email, e);
            return new MessageWrapper();
        }
    }

    /**
     * Delete mail by id boolean.
     *
     * @param email      the email
     * @param messageId  the message id
     * @param softDelete the soft delete
     * @return the boolean
     */
    public Boolean deleteMailById(String email, String messageId, Boolean softDelete) {
        try {
            ExchangeService service = getExchangeService(email);
            EmailMessage emailMessage = getEmailMessage(service, messageId);
            if (emailMessage == null) {
                log.warn("Email message with ID {} not found for email: {}", messageId, email);
                return false;
            }
            if (Boolean.TRUE.equals(softDelete)) {
                emailMessage.delete(DeleteMode.MoveToDeletedItems);
                MailSummary message = mailSummaryDao.findByMessageId(messageId, email);
                message.setDeleted(true);
                mailSummaryDao.save(message);
            } else {
                emailMessage.delete(DeleteMode.HardDelete);
            }
            return true;
        } catch (ServiceResponseException e) {
            log.error("Service response error while deleting email for {}: {}", email, e.getMessage());
        } catch (Exception e) {
            log.error("Error inside deleteMailById for email: {}", email, e);
        }

        return false;
    }


    /**
     * Retrieves the {@link ExchangeService} object for the specified user email.
     *
     * @param email The email address of the user.
     * @return The {@link ExchangeService} object for the user.
     * @throws Exception If there is an issue accessing the service for the user.
     */
    private ExchangeService getExchangeService(String email) throws Exception {
        return EWSUtils.getServiceObjectForUser(email);
    }

    /**
     * Binds and retrieves an {@link EmailMessage} using the message ID from the Exchange server.
     *
     * @param service   The {@link ExchangeService} object to interact with the server.
     * @param messageId The ID of the email message.
     * @return The bound {@link EmailMessage} object.
     * @throws Exception If there is an issue retrieving the email message.
     */
    private EmailMessage getEmailMessage(ExchangeService service, String messageId) throws Exception {
        ItemId itemId = new ItemId(messageId);
        return EmailMessage.bind(service, itemId);
    }

    /**
     * Loads the necessary properties of the {@link EmailMessage} to retrieve the email body in HTML format.
     *
     * @param emailMessage The {@link EmailMessage} object to load.
     * @throws Exception If there is an issue loading the email message.
     */
    private void loadEmailMessage(EmailMessage emailMessage) throws Exception {
        PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
        propertySet.setRequestedBodyType(BodyType.HTML);
        emailMessage.load(propertySet);
    }

    /**
     * Gets email by internet message id.
     *
     * @param email             the email
     * @param internetMessageId the internet message id
     * @param folderName        the folder name
     * @return the email by internet message id
     */
    public UserEmailDto getEmailByInternetMessageId(String email, String internetMessageId, String folderName) {
        log.debug("Inside @method getEmailByInternetMessageId. @param: email -> {}, internetMessageId -> {} folderName -> {}", email, internetMessageId, folderName);

        try {
            // Get the Exchange service object for the user
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            UserFolders folder = foldersRepository.findByDisplayNameAndEmail(folderName, email);
            if (folder == null) {
                throw new ResourceNotFoundException(String.format("Folder %s is not found for the user", folderName));
            }

            FolderId folderId = new FolderId(folder.getFolderId());
            Folder mailFolder = Folder.bind(service, folderId);
            // Create a search filter to find the email by InternetMessageId
            SearchFilter searchFilter = new SearchFilter.IsEqualTo(EmailMessageSchema.InternetMessageId, internetMessageId);

            // Set the view to return a single result
            ItemView view = new ItemView(1);

            // Find items in the user's mailbox using the search filter
            FindItemsResults<Item> findResults = service.findItems(mailFolder.getId(), searchFilter, view);

            // Check if any items were found
            if (!findResults.getItems().isEmpty()) {
                // Bind to the found email message
                EmailMessage emailMessage = (EmailMessage) findResults.getItems().getFirst();

                // Load additional properties if necessary
                PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
                propertySet.setRequestedBodyType(BodyType.Text);
                emailMessage.load(propertySet);

                return getUserEmailDto(email, emailMessage, true);
            }
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("folderName", folderName);
            auditLog.error("Error inside @method getEmailByInternetMessageId", e.getMessage(), getStackTraceAsString(e), internetMessageId, null, "GET_EMAIL_BY_INTERNET_MESSAGE_ID", email, null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            // log.error("Error inside @method getEmailByInternetMessageId", e);
            return null;
        }

        // Return null if no message was found
        return null;
    }

    /**
     * Gets email by internet message id.
     *
     * @param email             the email
     * @param internetMessageId the internet message id
     * @return the email by internet message id
     */
    public UserEmailDto getEmailByInternetMessageId(String email, String internetMessageId) {
        log.debug("Inside @method getEmailByInternetMessageId. @param: email -> {}, internetMessageId -> {}", email, internetMessageId);

        try {
            // Get the Exchange service object for the user
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            List<UserFolders> folders = foldersRepository.getAllMailFoldersByEmail(email);
            if (folders == null || folders.isEmpty()) {
                throw new ResourceNotFoundException(String.format("No folder is found for the user : %s", email));
            }

            List<FolderId> folderIds = folders.stream().map(folder -> {
                try {
                    return new FolderId(folder.getFolderId());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }).toList();

            SearchFilter searchFilter = new SearchFilter.IsEqualTo(EmailMessageSchema.InternetMessageId, internetMessageId);

            // Set the view to return a single result
            ItemView view = new ItemView(1);

            // Find items in the user's mailbox using the search filter
            ServiceResponseCollection<FindItemResponse<Item>> items = service.findItems(
                    folderIds,            // Folder IDs to search
                    searchFilter,         // Search filter
                    null,                 // No query string, can be null
                    view,                 // ItemView with 1 item limit
                    null,                 // No grouping
                    ServiceErrorHandling.ReturnErrors // Error handling mode
            );

            FindItemsResults<Item> findResults = items.getEnumerator().nextElement().getResults();
//             Check if any items were found
            if (!findResults.getItems().isEmpty()) {
                // Bind to the found email message
                EmailMessage emailMessage = (EmailMessage) findResults.getItems().getFirst();

                // Load additional properties if necessary
                PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
                propertySet.setRequestedBodyType(BodyType.Text);
                emailMessage.load(propertySet);

                UserEmailDto userEmailDto = getUserEmailDto(email, emailMessage, true);
                FolderId parentFolderId = emailMessage.getParentFolderId();
                String folderDisplayName = foldersRepository.findDisplayNameByFolderIdAndEmail(parentFolderId.getUniqueId(), email);
                userEmailDto.setFolderName(folderDisplayName);
                return userEmailDto;
            }
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("internetMessageId", internetMessageId);
            auditLog.error("Error inside @method getEmailByInternetMessageId", e.getMessage(), getStackTraceAsString(e), internetMessageId, null, "GET_EMAIL_BY_INTERNET_MESSAGE_ID", email, null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            // log.error("Error inside @method getEmailByInternetMessageId", e);
            return null;
        }

        // Return null if no message was found
        return null;
    }


    /**
     * Gets message id of first email in conversation.
     *
     * @param email          the email
     * @param conversationId the conversation id
     * @return the message id of first email in conversation
     */
    public String getMessageIdOfFirstEmailInConversation(String email, String conversationId) {
        log.debug("Inside @method getMessageIdOfFirstEmailInConversation. @param: email -> {}, conversationId -> {}", email, conversationId);

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Define a search filter to find emails belonging to the conversationId
            SearchFilter searchFilter = new SearchFilter.IsEqualTo(EmailMessageSchema.ConversationId, conversationId);

            // Define the view that will determine the number of results returned and the properties loaded
            ItemView view = new ItemView(1); // We only want the first email
            view.getOrderBy().add(ItemSchema.DateTimeReceived, SortDirection.Ascending); // Sort by date received, ascending

            // Perform the search to find emails matching the conversationId
            FindItemsResults<Item> findResults = service.findItems(WellKnownFolderName.Inbox, searchFilter, view);

            if (findResults.getTotalCount() > 0) {
                // Get the first email message
                Item item = findResults.getItems().getFirst();
                EmailMessage emailMessage = EmailMessage.bind(service, item.getId());
                return emailMessage.getId().getUniqueId(); // Return the messageId
            } else {
                log.debug("No emails found in the conversation with id: {}", conversationId);
                return null;
            }
        } catch (Exception e) {
            auditLog.error("Error inside @method getMessageIdOfFirstEmailInConversation", e.getMessage(), getStackTraceAsString(e), null, null, EWS_NETWORK_CALL, email, null, conversationId, null, null, null, null);
//            log.error("Error inside @method getMessageIdOfFirstEmailInConversation", e);
            return null;
        }
    }

    /**
     * Gets calendar events.
     *
     * @param email           the email
     * @param startDateTime   the start date time
     * @param endDateTime     the end date time
     * @param forAvailability the for availability
     * @return the calendar events
     */
    public List<EventDto> getCalendarEvents(String email, String startDateTime, String endDateTime, boolean forAvailability) {
        log.debug("Inside @method getCalendarEvents. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", email, startDateTime, endDateTime);
        if (!EmailUtils.checkDomain(email, orgDomainName)) return new ArrayList<>();

        if (!allowOutsiders) {
            boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
            if (!userExists) return new ArrayList<>();
        }

        List<EventDto> eventDtos = new ArrayList<>();
        EmailUser user = emailUserDao.findByEmail(email);
        if (user == null) {
            log.warn("No user found for email: {}", email);
            return eventDtos; // Return an empty list
        }
        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
        String timeZone = preferences.getTimeZone();

        Date startDate;
        Date endDate;
        if (startDateTime == null || endDateTime == null) {
            startDate = EwsDateUtils.getFormattedDateTime(false, timeZone);
            endDate = EwsDateUtils.getFormattedDateTime(true, timeZone);
        } else {
            if (startDateTime.endsWith("Z")) {
                startDate = DateUtils.parseDate(startDateTime);
                endDate = DateUtils.parseDate(endDateTime);
            } else {
                startDate = EwsDateUtils.parseDateWithTimeZone(startDateTime, timeZone);
                endDate = EwsDateUtils.parseDateWithTimeZone(endDateTime, timeZone);
            }
        }

        List<EventDto> events = new ArrayList<>();

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            CalendarFolder cf = CalendarFolder.bind(service, WellKnownFolderName.Calendar);
            CalendarView view = new CalendarView(startDate, endDate);
            view.setMaxItemsReturned(100);
            FindItemsResults<Appointment> response = cf.findAppointments(view);
            for (Appointment appointment : response.getItems()) {
                appointment.load(PropertySet.FirstClassProperties);
                if (!appointment.getIsCancelled() &&
                        !appointment.getRequiredAttendees().getItems().stream()
                                .allMatch(res -> res.getResponseType().toString().equals("Decline"))) {

                    EventDto eventDto = ConverterUtils.convertToEventDto(appointment);
                    events.add(eventDto);
                }
            }
            return events;
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("startDateTime", startDateTime, "endDateTime", endDateTime);
            auditLog.error("Error inside @method getCalendarEvents", e.getMessage(), getStackTraceAsString(e), null, null, GET_CALENDER_EVENT_EWS, email, null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
//            log.error("Error inside @method getCalendarEvents", e);
        }
        return new ArrayList<>();
    }

    /**
     * Update priority .
     *
     * @param messageId the message id
     * @param priority  the priority
     * @return the string
     */
    public String updatePriority(String messageId, String priority) {
        UserInfo currentUser = userContextHolder.getCurrentUser();

        MailSummary summary = mailSummaryDao.findByInternetMessageId(messageId, currentUser.getId());
        if (summary == null) throw new ResourceNotFoundException("Summary is not found for the given messageId");

        summary.setPriority(priority);
        summary.setPriorityReason("Priority was updated manually");
        String category = summary.getCategory();
        mailSummaryDao.save(summary);

        return "success";
//        return setCategory(currentUser.getEmail(), messageId, List.of(priority, category));
    }

    /**
     * Sets category.
     *
     * @param email     the email
     * @param messageId the message id
     * @param category  the category
     * @return the category
     */
    public String setCategory(String email, String messageId, List<String> category) {
        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            EmailMessage emailMessage = EWSUtils.findEmailById(service, messageId);
            StringList categoryList = new StringList(category);
            emailMessage.setCategories(categoryList);
            emailMessage.update(ConflictResolutionMode.AutoResolve);
            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            log.error("Error inside @method setCategory", e);
            auditLog.error("Error while getFlagStatus from graph", e.getMessage(), e, messageId, null, SET_CATEGORY_EWS, email, null, null, null, null, category, null);

            return EmailConstants.FAILED;
        }
    }

    /**
     * Schedule event .
     *
     * @param email       the email
     * @param requestBody the request body
     * @return the string
     */
    public String scheduleEvent(String email, Map<String, Object> requestBody) {
        try {
            String subject = (String) requestBody.get("subject");
            String body = (String) requestBody.get("body");
            String meetingStartTime = (String) requestBody.get("meetingStartTime");
            String meetingEndTime = (String) requestBody.get("meetingEndTime");
            List<String> attendees = (List<String>) requestBody.get("requiredAttendees");
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            Appointment appointment = new Appointment(service);

            // Set appointment properties
            appointment.setSubject(subject);
            appointment.setBody(MessageBody.getMessageBodyFromText(body));

            Date startDate, endDate;
            if (meetingStartTime.endsWith("Z")) {
                startDate = DateUtils.parseDate(meetingStartTime);
                endDate = DateUtils.parseDate(meetingEndTime);
            } else {
                String timeZone = (String) requestBody.get(EmailConstants.TIME_ZONE);
                startDate = EwsDateUtils.parseDateWithTimeZone(meetingStartTime, timeZone);
                endDate = EwsDateUtils.parseDateWithTimeZone(meetingEndTime, timeZone);
            }
            appointment.setStart(startDate);
            appointment.setEnd(endDate);
            String location = Optional.ofNullable((String) requestBody.get(EmailConstants.LOCATION)).orElse(null);
            String locationUrl = Optional.ofNullable((String) requestBody.get(EmailConstants.LOCATION_URL)).orElse(null);
            log.info("location is {} and locationUrl is {}", location, locationUrl);
            //            appointment.setLocation("Conference Room");

            // Add required attendees
            attendees.forEach(attendee -> {
                try {
                    appointment.getRequiredAttendees().add(new Attendee(attendee));
                } catch (Exception e) {
                    log.error("Error while adding attendees for scheduleMeeting", e);
                }
            });
            String meetingType = (String) requestBody.get("meetingType");
            String timeZone = (String) requestBody.get(EmailConstants.TIME_ZONE);

            if (meetingType != null && meetingType.equalsIgnoreCase("Zoom")) {
                try {
                    Map<String, String> zoomResponse = ZoomClient.scheduleMeeting(email, requestBody.get(EmailConstants.MEETING_START_TIME).toString(), requestBody.get(EmailConstants.MEETING_END_TIME).toString(), requestBody.get("subject").toString(), requestBody.get("body").toString(), timeZone);

                    if (zoomResponse != null) {
                        String zoomBody = requestBody.get("body").toString();
                        body += "<div>Meeting URL : <a style=\"color: blue\" href=" + zoomResponse.get("join_url") + ">" + zoomResponse.get("join_url") + "</a></div><br>Meeting ID : " + zoomResponse.get("meeting_id") + "<br>Passcode : " + zoomResponse.get("passcode");
                        log.debug("body is {}", body);
                        appointment.setBody(MessageBody.getMessageBodyFromText(body));
                    }
                } catch (Exception e) {
                    auditLog.error("Error setting Zoom meeting url", e.getMessage(), getStackTraceAsString(e), null, null, "SCHEDULE_MEETING_EWS", email, null, null, null, null, AIUtils.convertToJSON(requestBody, true), null);
                }
            }
            setOptionalAttendees(appointment, requestBody);
            appointment.save(WellKnownFolderName.Calendar, SendInvitationsMode.SendToAllAndSaveCopy);
            log.debug("Meeting scheduled successfully.");
            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            auditLog.error("Error inside @method scheduleEvent", e.getMessage(), getStackTraceAsString(e), null, null, "SCHEDULE_MEETING_EWS", email, null, null, null, null, AIUtils.convertToJSON(requestBody, true), null);
            return EmailConstants.FAILED;
        }
    }

    /**
     * Schedule event recurring .
     *
     * @param email       the email
     * @param requestBody the request body
     * @return the string
     */
    public String scheduleEventRecurring(String email, Map<String, Object> requestBody) {
        try {
            String subject = (String) requestBody.get("subject");
            String body = (String) requestBody.get("body");
            String meetingStartTime = (String) requestBody.get("meetingStartTime");
            String meetingEndTime = (String) requestBody.get("meetingEndTime");
            String timeZone = (String) requestBody.get("timeZone");

            List<String> attendees = (List<String>) requestBody.get("requiredAttendees");

            // Recurrence information
            Boolean isRecurring = (Boolean) requestBody.get("isRecurring");
            String recurrenceType = (String) requestBody.get("recurrenceType"); // Daily, Weekly, AbsoluteMonthly
            String rangeType = (String) requestBody.get("rangeType"); // "EndDate", "NoEnd"
            Integer interval = (Integer) requestBody.get("interval"); // Interval for recurrence
            String recurrenceStartDate = (String) requestBody.get("recurrenceStartDate");
            String recurrenceEndDate = (String) requestBody.get("recurrenceEndDate");
            Integer dayOfMonth = (Integer) requestBody.get("dayOfMonth");
            Integer monthOfYear = (Integer) requestBody.get("monthOfYear");
            List<String> daysOfWeek = (List<String>) requestBody.get("daysOfWeek"); // e.g., ["Monday", "Wednesday"]

            // Get service object
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            Appointment appointment = new Appointment(service);

            // Set appointment properties
            appointment.setSubject(subject);
            appointment.setBody(MessageBody.getMessageBodyFromText(body));

            Date startDate;
            Date endDate;
            if (meetingStartTime.endsWith("Z")) {
                startDate = DateUtils.parseDate(meetingStartTime);
                endDate = DateUtils.parseDate(meetingEndTime);
            } else {
                startDate = EwsDateUtils.parseDateWithTimeZone(meetingStartTime, timeZone);
                endDate = EwsDateUtils.parseDateWithTimeZone(meetingEndTime, timeZone);
            }

            appointment.setStart(startDate);
            appointment.setEnd(endDate);

            // Add required attendees
            attendees.forEach(attendee -> {
                try {
                    appointment.getRequiredAttendees().add(new Attendee(attendee));
                } catch (Exception e) {
                    log.error("Error while adding attendees for scheduleMeeting", e);
                }
            });

            String meetingType = (String) requestBody.get("meetingType");
            if (meetingType != null && meetingType.equalsIgnoreCase("Zoom")) {
                try {
                    Map<String, String> zoomResponse = ZoomClient.scheduleMeeting(email, requestBody.get(EmailConstants.MEETING_START_TIME).toString(), requestBody.get(EmailConstants.MEETING_END_TIME).toString(), requestBody.get("subject").toString(), requestBody.get("body").toString(), timeZone);

                    if (zoomResponse != null) {
//                        String zoomBody = requestBody.get("body").toString();
                        body += "<div>Meeting URL : <a style=\"color: blue\" href=" + zoomResponse.get("join_url") + ">" + zoomResponse.get("join_url") + "</a></div><br>Meeting ID : " + zoomResponse.get("meeting_id") + "<br>Passcode : " + zoomResponse.get("passcode");
                        body += generatedBy;
                        log.debug("body is {}", body);
                        appointment.setBody(MessageBody.getMessageBodyFromText(body));
                    }
                } catch (Exception e) {
                    auditLog.error("Error setting Zoom meeting url", e.getMessage(), getStackTraceAsString(e), null, null, "SCHEDULE_MEETING_EWS", email, null, null, null, null, AIUtils.convertToJSON(requestBody, true), null);
                }
            }

            // Set recurrence if it's a recurring meeting
            if (isRecurring != null && isRecurring) {
                // Parse recurrence start date
                SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
                Date recStartDate = dateFormatter.parse(recurrenceStartDate);

                // Define recurrence pattern based on the recurrenceType
                if ("Weekly".equalsIgnoreCase(recurrenceType)) {
                    DayOfTheWeek[] dayOfTheWeekArray = daysOfWeek.stream()
                            .map(DayOfTheWeek::valueOf)
                            .toArray(DayOfTheWeek[]::new);

                    Recurrence.WeeklyPattern weeklyPattern = new Recurrence.WeeklyPattern(recStartDate, interval, dayOfTheWeekArray);

                    if ("EndDate".equalsIgnoreCase(rangeType)) {
                        Date recEndDate = dateFormatter.parse(recurrenceEndDate);
                        weeklyPattern.setEndDate(recEndDate);
                    } else if ("NoEnd".equalsIgnoreCase(rangeType)) {
                        weeklyPattern.neverEnds();
                    }

                    appointment.setRecurrence(weeklyPattern);

                } else if ("Daily".equalsIgnoreCase(recurrenceType)) {
                    Recurrence.DailyPattern dailyPattern = new Recurrence.DailyPattern(recStartDate, interval);

                    if ("EndDate".equalsIgnoreCase(rangeType)) {
                        Date recEndDate = dateFormatter.parse(recurrenceEndDate);
                        dailyPattern.setEndDate(recEndDate);
                    } else if ("NoEnd".equalsIgnoreCase(rangeType)) {
                        dailyPattern.neverEnds();
                    }

                    appointment.setRecurrence(dailyPattern);

                } else if ("AbsoluteMonthly".equalsIgnoreCase(recurrenceType)) {
                    Recurrence.MonthlyPattern monthlyPattern = new Recurrence.MonthlyPattern(recStartDate, interval, dayOfMonth);

                    if ("EndDate".equalsIgnoreCase(rangeType)) {
                        Date recEndDate = dateFormatter.parse(recurrenceEndDate);
                        monthlyPattern.setEndDate(recEndDate);
                    } else if ("NoEnd".equalsIgnoreCase(rangeType)) {
                        monthlyPattern.neverEnds();
                    }

                    appointment.setRecurrence(monthlyPattern);
                } else if ("absoluteYearly".equalsIgnoreCase(recurrenceType)) {
                    // Handle yearly recurrence
                    Month monthEnum = Month.values()[monthOfYear - 1];
                    Recurrence.YearlyPattern yearlyPattern = new Recurrence.YearlyPattern(recStartDate, monthEnum, dayOfMonth);

                    if ("EndDate".equalsIgnoreCase(rangeType)) {
                        Date recEndDate = dateFormatter.parse(recurrenceEndDate);
                        yearlyPattern.setEndDate(recEndDate);
                    } else if ("NoEnd".equalsIgnoreCase(rangeType)) {
                        yearlyPattern.neverEnds();
                    }

                    appointment.setRecurrence(yearlyPattern);
                }
                // Additional recurrence types (e.g., yearly) can be added similarly.
            }

            // Save the appointment and send invitations
            appointment.save(WellKnownFolderName.Calendar, SendInvitationsMode.SendToAllAndSaveCopy);
            log.debug("Recurring meeting scheduled successfully.");
            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            auditLog.error("Error inside @method scheduleEventRecurring", e.getMessage(), getStackTraceAsString(e), null, null, SCHEDULE_MEETING_EWS, email, null, null, null, null, AIUtils.convertToJSON(requestBody, true), null);
            return EmailConstants.FAILED;
        }
    }

    /**
     * Reschedule event map.
     *
     * @param requestMap the request map
     * @return the map
     */
    public Map<String, String> rescheduleEvent(Map<String, String> requestMap) {
        log.debug("Inside @method rescheduleEvent. @param : requestMap -> {}", requestMap);
        String email = userContextHolder.getCurrentUser().getEmail();
        try {
            // Get the ExchangeService object for the user
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            String eventId = requestMap.get("eventId");
            // Bind to the appointment using the appointmentId
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);

            // Update the appointment's start and end time
            String startTime = requestMap.get("startTime");
            String endTime = requestMap.get("endTime");
            String timeZone = requestMap.get("timeZone");
            if (timeZone == null) {
                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
                timeZone = preferences.getTimeZone();
            }

            Date startDate = DateUtils.parseDateWithTimeZone(startTime, timeZone);
            Date endDate = DateUtils.parseDateWithTimeZone(endTime, timeZone);
            appointment.setStart(startDate);
            appointment.setEnd(endDate);

            // Add description or reason for rescheduling
            String description = requestMap.get("rescheduleReason");
            if (description != null && !description.isEmpty()) {
                appointment.setBody(MessageBody.getMessageBodyFromText(description));
            }

            // Save the changes to the appointment
//            appointment.update(ConflictResolutionMode.AutoResolve);
            appointment.update(ConflictResolutionMode.AutoResolve, SendInvitationsOrCancellationsMode.SendToAllAndSaveCopy);

            return Map.of("result", EmailConstants.SUCCESS);
        } catch (Exception e) {
            auditLog.error("Error while rescheduling event", e.getMessage(), getStackTraceAsString(e), null, null, "RESCHEDULE_EVENT_EWS", email, null, null, null, null, AIUtils.convertToJSON(requestMap, true), null);
            return Map.of("result", EmailConstants.FAILED);
        }
    }

    private void setOptionalAttendees(Appointment appointment, Map<String, Object> requestBody) {
        if (requestBody != null && requestBody.containsKey("optionalAttendees")) {
            List<String> optionals = (List<String>) requestBody.get("optionalAttendees");
            optionals.forEach(attendee -> {
                try {
                    appointment.getOptionalAttendees().add(new Attendee(attendee));
                } catch (Exception e) {
                    log.error("Error while adding attendees for scheduleMeeting", e);
                }
            });
        }
    }

    /**
     * Accept meeting map.
     *
     * @param meetingRequestId the meeting request id
     * @return the map
     */
    public Map<String, String> acceptMeeting(String meetingRequestId) {
        try {
            log.debug("Inside @method acceptMeeting. @param : meetingRequestId -> {}", meetingRequestId);
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the meeting request by its ID
            ItemId itemId = new ItemId(meetingRequestId);
            Appointment appointment = Appointment.bind(service, itemId);
            // Load appointment properties
            appointment.load();
            // Accept the meeting request
            appointment.accept(true);
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("meetingRequestId", meetingRequestId);
            auditLog.error("Error inside @method acceptMeeting", e.getMessage(), getStackTraceAsString(e), null, null, ACCEPT_MEETING_EWS, userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
//            log.error("An error occurred while accepting the meeting", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    /**
     * Decline meeting map.
     *
     * @param meetingRequestId the meeting request id
     * @return the map
     */
    public Map<String, String> declineMeeting(String meetingRequestId) {
        try {
            log.debug("Inside @method declineMeeting. @param : meetingRequestId -> {}", meetingRequestId);
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the meeting request by its ID
            ItemId itemId = new ItemId(meetingRequestId);
            Appointment appointment = Appointment.bind(service, itemId);
            // Load appointment properties
            appointment.load();
            // Accept the meeting request
            appointment.decline(true); // true for sending response to all attendees, false for not sending

            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("meetingRequestId", meetingRequestId);
            auditLog.error("Error inside @method declineMeeting", e.getMessage(), getStackTraceAsString(e), null, null, DECLINE_MEETING_EWS, userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
//            log.error("An error occurred while accepting the meeting", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    /**
     * Tentatively accept meeting map.
     *
     * @param meetingRequestId the meeting request id
     * @return the map
     */
    public Map<String, String> tentativelyAcceptMeeting(String meetingRequestId) {
        try {
            log.debug("Inside @method tentativelyAcceptMeeting. @param : meetingRequestId -> {}", meetingRequestId);
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the meeting request by its ID
            ItemId itemId = new ItemId(meetingRequestId);
            Appointment appointment = Appointment.bind(service, itemId);
            // Load appointment properties
            appointment.load();
            // Tentatively accept the meeting request
            appointment.acceptTentatively(true); // true for sending response to all attendees, false for not sending

            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("meetingRequestId", meetingRequestId);
            auditLog.error("Error inside @method tentativelyAcceptMeeting", e.getMessage(), getStackTraceAsString(e), null, null, TENTATIVE_MEETING_ACCEPT_EWS, userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
//        log.error("An error occurred while tentatively accepting the meeting", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }


    /**
     * Gets users mail folders.
     *
     * @param email the email
     * @return the users mail folders
     */
    public List<Map<String, String>> getUsersMailFolders(String email) {
        List<Map<String, String>> result = new ArrayList<>();
        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            FolderView view = new FolderView(Integer.MAX_VALUE);

            // Set the traversal property to include all folders in the search
            view.setTraversal(FolderTraversal.Deep);

            // Find all folders under the root folder
            FindFoldersResults findFolderResults = service.findFolders(WellKnownFolderName.MsgFolderRoot, view);


            for (Folder folder : findFolderResults.getFolders()) {
                String folderName = folder.getDisplayName();
                log.debug("Folder {} Class is {}", folderName, folder.getFolderClass());

                // Exclude "Deleted Items" folder and its subfolders
                if (!isDeletedItemsOrSubfolder(folder)) {
                    Map<String, String> mailFolderMap = new HashMap<>();
                    mailFolderMap.put("id", folder.getId().getUniqueId());
                    mailFolderMap.put("displayName", folderName);
                    result.add(mailFolderMap);
                }
            }
        } catch (Exception e) {
            auditLog.error("Error inside @method get user folders", e.getMessage(), getStackTraceAsString(e), null, null, USER_FOLDER_EWS, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
        }
        return result;
    }

    private boolean isDeletedItemsOrSubfolder(Folder folder) throws Exception {
        // Check if this is the "Deleted Items" folder
        if ("Deleted Items".equalsIgnoreCase(folder.getDisplayName())) {
            return true;
        }

        // Traverse up the folder hierarchy to see if the "Deleted Items" is an ancestor
        Folder parentFolder = folder;
        while (parentFolder.getParentFolderId() != null && parentFolder.getDisplayName() != null) {
            parentFolder = Folder.bind(folder.getService(), parentFolder.getParentFolderId());
            if ("Deleted Items".equalsIgnoreCase(parentFolder.getDisplayName())) {
                return true;
            }
        }
        return false;
    }


    /**
     * Create draft.
     *
     * @param emailOfUser the email of user
     * @param subject     the subject
     * @param content     the content
     * @param toEmail     the to email
     * @param ccEmail     the cc email
     * @param bccEmail    the bcc email
     * @param sendDraft   the send draft
     * @return the string
     */
    public String createDraft(String emailOfUser, String subject, String content, String toEmail, String ccEmail, String bccEmail, Boolean sendDraft) {
        log.debug("Inside @method createDraft. @param: emailOfUser -> {}, subject -> {}, content -> {}, toEmail -> {}, ccEmail -> {}, bccEmail -> {}",
                emailOfUser, subject, content, toEmail, ccEmail, bccEmail);

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(emailOfUser);
            // Define the folder to save the draft email (e.g., Drafts folder)
            FolderId draftsFolderId = new FolderId(WellKnownFolderName.Drafts);

            // Create a new draft email message
            EmailMessage emailMessage = new EmailMessage(service);

            // Set email properties
            emailMessage.setSubject(subject);
            emailMessage.setBody(new MessageBody(content));

            // Add recipients
            List<String> toRecipientsList = CommonUtils.getListFromCommaSeparatedString(toEmail);
            EmailAddressCollection toRecipients = emailMessage.getToRecipients();
            for (String toRecipient : toRecipientsList) {
                toRecipients.add(new EmailAddress(toRecipient));
            }

            List<String> ccEmailList = CommonUtils.getListFromCommaSeparatedString(ccEmail);
            EmailAddressCollection ccRecipients = emailMessage.getCcRecipients();
            for (String ccRecipient : ccEmailList) {
                ccRecipients.add(new EmailAddress(ccRecipient));
            }

            List<String> bccEmailList = CommonUtils.getListFromCommaSeparatedString(bccEmail);
            EmailAddressCollection bccRecipients = emailMessage.getBccRecipients();
            for (String bccRecipient : bccEmailList) {
                bccRecipients.add(new EmailAddress(bccRecipient));
            }

            // Save the email as a draft
            if (!sendDraft) emailMessage.save(draftsFolderId);
            else emailMessage.sendAndSaveCopy();
            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            auditLog.error("Error inside @method createDraft", e.getMessage(), getStackTraceAsString(e), null, null, CREATE_DRAFT_EWS, emailOfUser, subject, null, null, null, content, null);
//            log.error("Error inside @method createDraft", e);
            return EmailConstants.FAILED;
        }

    }

    /**
     * Create draft with attachment .
     *
     * @param emailOfUser the email of user
     * @param subject     the subject
     * @param content     the content
     * @param toEmail     the to email
     * @param ccEmail     the cc email
     * @param bccEmail    the bcc email
     * @param isDraft     the is draft
     * @param attachments the attachments
     * @return the string
     */
    public String createDraftWithAttachment(String emailOfUser, String subject, String content, String toEmail, String ccEmail, String bccEmail, Boolean isDraft,String attachments) {
        log.debug("Inside @method createDraft. @param: emailOfUser -> {}, subject -> {}, content -> {}, toEmail -> {}, ccEmail -> {}, bccEmail -> {}",
                emailOfUser, subject, content, toEmail, ccEmail, bccEmail);
        List<String> attachmentPathsToDelete = new ArrayList<>();
        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(emailOfUser);
         //   String s3BucketName = "emailAttachments";
            // Define the folder to save the draft email (e.g., Drafts folder)
            FolderId draftsFolderId = new FolderId(WellKnownFolderName.Drafts);
            // Create a new draft email message
            EmailMessage emailMessage = new EmailMessage(service);
            // Set email properties
            emailMessage.setSubject(subject);
            emailMessage.setBody(new MessageBody(content));
            String prefix = "attachments/";
            // Add recipients
            List<String> toRecipientsList = CommonUtils.getListFromCommaSeparatedString(toEmail);
            EmailAddressCollection toRecipients = emailMessage.getToRecipients();
            for (String toRecipient : toRecipientsList) {
                toRecipients.add(new EmailAddress(toRecipient));
            }
            List<String> attachmentList = CommonUtils.getListFromCommaSeparatedString(attachments);
            for(String attachmentPath:attachmentList){
                int prefixIndex = attachmentPath.indexOf(prefix);
                String fileName;
                if (prefixIndex != -1) {
                    fileName = attachmentPath.substring(prefixIndex + prefix.length());
                } else {
                    fileName = attachmentPath.substring(attachmentPath.lastIndexOf("/") + 1);
                }
                fileName = removeTimestamp(fileName);
                byte[] in = s3Service.downloadFileAsBytes(s3BucketName,attachmentPath);
                emailMessage.getAttachments().addFileAttachment(fileName,in);
                attachmentPathsToDelete.add(attachmentPath);
            }
            List<String> ccEmailList = CommonUtils.getListFromCommaSeparatedString(ccEmail);
            EmailAddressCollection ccRecipients = emailMessage.getCcRecipients();
            for (String ccRecipient : ccEmailList) {
                ccRecipients.add(new EmailAddress(ccRecipient));
            }

            List<String> bccEmailList = CommonUtils.getListFromCommaSeparatedString(bccEmail);
            EmailAddressCollection bccRecipients = emailMessage.getBccRecipients();
            for (String bccRecipient : bccEmailList) {
                bccRecipients.add(new EmailAddress(bccRecipient));
            }
            if (isDraft) {
                emailMessage.save(draftsFolderId);
            } else {
                emailMessage.sendAndSaveCopy();
            }
            for (String attachmentPath : attachmentPathsToDelete) {
                s3Service.deleteFileFromS3(s3BucketName, attachmentPath);
            }
            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            auditLog.error("Error inside @method createDraft", e.getMessage(), getStackTraceAsString(e), null, null, CREATE_DRAFT_EWS, emailOfUser, subject, null, null, null, content, null);
            return EmailConstants.FAILED;
        }

    }

    /**
     * Create draft reply .
     *
     * @param email       the email
     * @param messageId   the message id
     * @param content     the content
     * @param sendDraft   the send draft
     * @param type        the type
     * @param contentType the content type
     * @return the string
     */
    public String createDraftReply(String email, String messageId, String content, boolean sendDraft, String type, String contentType) {
        log.debug("Inside @method createDraftReply. @param: email -> {}, messageId -> {}, content -> {}, sendDraft -> {} type -> {}", email, messageId, content, sendDraft, type);

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            // Bind to the existing email message by its ID
            ItemId itemId = new ItemId(messageId);
            EmailMessage originalMessage = EmailMessage.bind(service, itemId);
            // Create a reply-all message
            ResponseMessage replyAll;
            if (type != null && type.equals("reply")) {
                replyAll = originalMessage.createReply(false);   // false for Reply
            } else {
                replyAll = originalMessage.createReply(true);   // true for Reply All
            }
            // Set the body of the reply
            replyAll.setBody(MessageBody.getMessageBodyFromText(content));

            if (sendDraft) {
                // Send the reply if sendDraft is true
                replyAll.sendAndSaveCopy();
                log.debug("Reply-all message sent successfully for email: {}, messageId: {}", email, messageId);
            } else {
                // Save the reply as a draft in the Drafts folder if sendDraft is false
                replyAll.save(WellKnownFolderName.Drafts);
                log.debug("Reply-all message saved as draft for email: {}, messageId: {}", email, messageId);
            }
            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            auditLog.error("Error inside @method createDraftReply", e.getMessage(), getStackTraceAsString(e), messageId, null, CREATE_REPLY_EWS, email, null, null, null, null, content, null);
            return EmailConstants.FAILED;
        }
    }

    /**
     * Create draft forward.
     *
     * @param email        the email
     * @param messageId    the message id
     * @param comment      the comment
     * @param toRecipients the to recipients
     * @param ccRecipients the cc recipients
     * @param sendDraft    the send draft
     * @return the string
     */
// send forward ews
    public String createDraftForward(String email, String messageId, String comment, String toRecipients, String ccRecipients, boolean sendDraft) {
        log.debug("Inside @method createDraftForward. @param: email -> {}, messageId -> {}, comment -> {}, toRecipients -> {}, ccRecipients -> {}, sendDraft -> {}",
                email, messageId, comment, toRecipients, ccRecipients, sendDraft);

        try {

            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            ItemId itemId = new ItemId(messageId);

            EmailMessage originalMessage = EmailMessage.bind(service, itemId);

            ResponseMessage forwardMessage = originalMessage.createForward();

            forwardMessage.setBodyPrefix(MessageBody.getMessageBodyFromText(comment));

            if (toRecipients != null && !toRecipients.isEmpty()) {
                for (String recipient : toRecipients.split(",")) {
                    forwardMessage.getToRecipients().add(recipient.trim());
                }
            }

            if (ccRecipients != null && !ccRecipients.isEmpty()) {
                for (String ccRecipient : ccRecipients.split(",")) {
                    forwardMessage.getCcRecipients().add(ccRecipient.trim());
                }
            }

            if (sendDraft) {
                forwardMessage.sendAndSaveCopy();
                log.debug("Forward message sent successfully for email: {}, messageId: {}", email, messageId);
            } else {
                forwardMessage.save(WellKnownFolderName.Drafts);
                log.debug("Forward message saved as draft for email: {}, messageId: {}", email, messageId);
            }

            return EmailConstants.SUCCESS;

        } catch (Exception e) {
            log.error("Error inside @method createDraftForward", e);
            auditLog.error("Error inside @method createDraftForward", e.getMessage(), getStackTraceAsString(e), messageId, null, FORWARD_EWS, email, null, null, null, null, comment, null);
            return EmailConstants.FAILED;
        }
    }

    /**
     * Gets available slots and conflict.
     *
     * @param emails        the emails
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @param slotDuration  the slot duration
     * @return the available slots and conflict
     */
    public AvailableSlots getAvailableSlotsAndConflict(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        log.debug("Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
        String[] dateTimeRange = setupDateTimeRange(startDateTime, endDateTime);
        startDateTime = dateTimeRange[0];
        endDateTime = dateTimeRange[1];
        Map<String, List<Meeting>> result = fetchAndConvertCalendarEvents(emails, startDateTime, endDateTime);
        Date[] parsedDates = parseStartAndEndDates(startDateTime, endDateTime);
        Date start = parsedDates[0];
        Date end = parsedDates[1];
        long durationMillis = (long) slotDuration * 60 * 1000;
        AvailableSlots availableSlots = new AvailableSlots();
        availableSlots.setAvailableSlots(graphIntegrationService.findAvailableTimeSlots(result, start, end, durationMillis));
        availableSlots.setConflictMeeting(findUnavailableTimeSlots(result, start, end));
        availableSlots.setOutOfOffice(getOutOfOfficeDetails(result, start, end));
        return availableSlots;
    }

    private List<OutOfOffice> getOutOfOfficeDetails(Map<String, List<Meeting>> result, Date start, Date end) {
        List<OutOfOffice> outOfOffice = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"); // Assuming ISO 8601 format

        try {
            for (Map.Entry<String, List<Meeting>> entry : result.entrySet()) {
                String key = entry.getKey();
                //  List<Meeting> meetings = entry.getValue();
                Map<String, Object> userSettings = graphIntegrationService.getAutoReplySettingsForUser(key);
                if (userSettings != null) {
                    log.info("userSettings not null");
                    OutOfOffice office = convertToOutOfOffice(key, userSettings);
                    Date oooStart = null;
                    Date oooEnd = null;
                    if (office.getStartDateTime() != null && office.getEndDateTime() != null) {
                        oooStart = dateFormat.parse(office.getStartDateTime());
                        oooEnd = dateFormat.parse(office.getEndDateTime());
                    } else {
                        EmailPreferences emailPreferences = preferencesDao.getEmailPreferencesByUserId(key);
                        oooStart = convertLocalTimeToDate(emailPreferences.getCheckin());
                        oooEnd = convertLocalTimeToDate(emailPreferences.getCheckout());
                    }
                    if (oooStart != null && oooEnd != null && (oooStart.before(end) || oooStart.equals(end)) && (oooEnd.after(start) || oooEnd.equals(start)))
                        outOfOffice.add(office);
                }
            }
        } catch (Exception e) {
            log.error("error inside getAutoReplySettingsForUser", e);
        }
        return outOfOffice;
    }

    /**
     * Create draft reply with attachment string.
     *
     * @param email       the email
     * @param messageId   the message id
     * @param content     the content
     * @param sendDraft   the send draft
     * @param attachments the attachments
     * @return the string
     */
    public String createDraftReplyWithAttachment(String email, String messageId, String content, boolean sendDraft,String attachments) {
        log.debug("Inside @method createDraftReply. @param: email -> {}, messageId -> {}, content -> {}, sendDraft -> {}", email, messageId, content, sendDraft);
        List<String> attachmentPathsToDelete = new ArrayList<>();
        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            ItemId itemId = new ItemId(messageId);
      //      String s3BucketName = "emailAttachments";
            String prefix = "attachments/";
            EmailMessage originalMessage = EmailMessage.bind(service, itemId);
            ResponseMessage replyAll = originalMessage.createReply(true);
            replyAll.setBody(MessageBody.getMessageBodyFromText(content));
            EmailMessage savedDraft = replyAll.save(WellKnownFolderName.Drafts);
            List<String> attachmentList = CommonUtils.getListFromCommaSeparatedString(attachments);
            for(String attachmentPath:attachmentList){
                int prefixIndex = attachmentPath.indexOf(prefix);
                String fileName;
                if (prefixIndex != -1) {
                    fileName = attachmentPath.substring(prefixIndex + prefix.length());
                } else {
                    fileName = attachmentPath.substring(attachmentPath.lastIndexOf("/") + 1);
                }
                fileName = removeTimestamp(fileName);
                byte[] in = s3Service.downloadFileAsBytes(s3BucketName,attachmentPath);
                savedDraft.getAttachments().addFileAttachment(fileName,in);
                attachmentPathsToDelete.add(attachmentPath);
            }
            if (sendDraft) {
                savedDraft.send();
                log.debug("Reply-all message sent successfully for email: {}, messageId: {}", email, messageId);
            }
            for (String attachmentPath : attachmentPathsToDelete) {
                s3Service.deleteFileFromS3(s3BucketName, attachmentPath);
            }

            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            auditLog.error("Error inside @method createDraftReply", e.getMessage(), getStackTraceAsString(e), messageId, null, CREATE_REPLY_EWS, email, null, null, null, null, content, null);
            return EmailConstants.FAILED;
        }
    }

    private String[] setupDateTimeRange(String startDateTime, String endDateTime) {
        if (startDateTime == null || endDateTime == null) {
            String email = userContextHolder.getCurrentUser().getEmail();
            EmailUser user = emailUserDao.findByEmail(email);
            String userId = user.getUserId();
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
            startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
            endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
        }
        return new String[]{startDateTime, endDateTime};
    }

    private Map<String, List<Meeting>> fetchAndConvertCalendarEvents(List<String> emails, String startDateTime, String endDateTime) {
        Map<String, List<Meeting>> result = new HashMap<>();
        for (String email : emails) {
            List<EventDto> calendarEvents = getCalendarEvents(email, startDateTime, endDateTime, true);
            if (calendarEvents == null || calendarEvents.isEmpty()) {
                log.debug("No calendar events found for email: {}", email);
                result.put(email, new ArrayList<>());
                continue;
            }
            List<Meeting> list = convertEventDtosToMeetings(calendarEvents);
            result.put(email, list);
        }
        return result;
    }

    /**
     * Gets available meeting slots.
     *
     * @param emails        the emails
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @param slotDuration  the slot duration
     * @return the available meeting slots
     */
    public List<Meeting> getAvailableMeetingSlots(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        try {
            log.debug("Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
            if (startDateTime == null || endDateTime == null) {
                String email = userContextHolder.getCurrentUser().getEmail();
                EmailUser user = emailUserDao.findByEmail(email);
                String userId = user.getUserId();
                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
                startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
                endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
            }
            Map<String, List<Meeting>> result = new HashMap<>();
            for (String email : emails) {
                List<EventDto> calendarEvents = getCalendarEvents(email, startDateTime, endDateTime, true);
                if (calendarEvents == null || calendarEvents.isEmpty()) {
                    log.debug("No calendar events found for email: {}", email);
                    result.put(email, new ArrayList<>());  // Add empty list for this email
                    continue;
                }
                List<Meeting> list = new ArrayList<>();
                for (EventDto eventDto : calendarEvents) {
                    Meeting meeting = new Meeting();
                    meeting.setStartTime(eventDto.getMeetingStartTime());
                    meeting.setEndTime(eventDto.getMeetingEndTime());
                    list.add(meeting);
                }
                result.put(email, list);
            }
            Date start, end;
            if (startDateTime.endsWith("Z")) {
                start = convertStringToDate(startDateTime);
                end = convertStringToDate(endDateTime);
            } else {
                start = DateUtils.parseDateWithoutTZ(startDateTime);
                end = DateUtils.parseDateWithoutTZ(endDateTime);
            }

            long durationMillis = slotDuration * 60 * 1000;
            return graphIntegrationService.findAvailableTimeSlots(result, start, end, durationMillis);
        } catch (Exception e) {
            auditLog.error("Error inside @method getAvailableSlot", e.getMessage(), getStackTraceAsString(e), null, null, AVAILABLE_SLOT_EWS, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
        }
        return null;
    }

    /**
     * Gets available meeting slots oof.
     *
     * @param emails        the emails
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @param slotDuration  the slot duration
     * @return the available meeting slots oof
     */
    public List<Meeting> getAvailableMeetingSlotsOOF(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        try {
            log.debug("Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
            if (startDateTime == null || endDateTime == null) {
                String email = userContextHolder.getCurrentUser().getEmail();
                EmailUser user = emailUserDao.findByEmail(email);
                String userId = user.getUserId();
                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
                startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
                endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
            }
            Map<String, List<Meeting>> result = new HashMap<>();
            for (String email : emails) {
                OutOfOfficeDto outOfOfficeHours = getOutOfOfficeHours(email);
                List<EventDto> calendarEvents = getCalendarEvents(email, startDateTime, endDateTime, true);
                if (calendarEvents == null || calendarEvents.isEmpty()) {
                    log.debug("No calendar events found for email: {}", email);
                    result.put(email, addOutOfOfficeToMeeting(new ArrayList<>(), outOfOfficeHours));   // Add empty list for this email
                    continue;
                }
                List<Meeting> list = new ArrayList<>();
                for (EventDto eventDto : calendarEvents) {
                    Meeting meeting = new Meeting();
                    meeting.setStartTime(eventDto.getMeetingStartTime());
                    meeting.setEndTime(eventDto.getMeetingEndTime());
                    list.add(meeting);
                }

                list = addOutOfOfficeToMeeting(list, outOfOfficeHours);

                result.put(email, list);
            }
            Date start, end;
            if (startDateTime.endsWith("Z")) {
                start = convertStringToDate(startDateTime);
                end = convertStringToDate(endDateTime);
            } else {
                start = DateUtils.parseDateWithoutTZ(startDateTime);
                end = DateUtils.parseDateWithoutTZ(endDateTime);
            }

            long durationMillis = slotDuration * 60 * 1000;
            return graphIntegrationService.findAvailableTimeSlots(result, start, end, durationMillis);
        } catch (Exception e) {
            auditLog.error("Error inside @method getAvailableSlot", e.getMessage(), getStackTraceAsString(e), null, null, AVAILABLE_SLOT_EWS, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
        }
        return null;
    }

    /**
     * Flag email.
     *
     * @param email      the email
     * @param messageId  the message id
     * @param flagStatus the flag status
     * @return the string
     */
    public String flagEmail(String email, String messageId, String flagStatus) {
        log.debug("Inside @method flagEmail. @param: email -> {}, messageId -> {}, flag -> {}", email, messageId, flagStatus);

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            // Bind to the existing email message by its ID
            ItemId itemId = new ItemId(messageId);
            EmailMessage originalMessage = EmailMessage.bind(service, itemId);
            ExtendedPropertyDefinition flagStatusProp = new ExtendedPropertyDefinition(0x1090, MapiPropertyType.Integer);
            MailSummary message = mailSummaryDao.findByMessageId(messageId, email);
            if (flagStatus.equals("flagged")) {
                originalMessage.setExtendedProperty(flagStatusProp, 2);
                message.setFlagStatus("Flagged");
            } else {
                originalMessage.setExtendedProperty(flagStatusProp, 0);
                message.setFlagStatus("notFlagged");
            }
            mailSummaryDao.save(message);
            originalMessage.update(ConflictResolutionMode.AutoResolve);
            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            auditLog.error("Error inside @method flagEmail", e.getMessage(), getStackTraceAsString(e), messageId, null, FLAG_EWS, email, null, null, null, null, flagStatus, null);
//            log.error("Error inside @method flagEmail", e);
            return EmailConstants.FAILED;
        }
    }

    /**
     * Flag email.
     *
     * @param email      the email
     * @param databaseId  the database record id
     * @param flagStatus the flag status
     * @return the string
     */
    public String flagEmail(String email, Integer databaseId, String flagStatus) {
        log.debug("Inside @method flagEmail. @param: email -> {}, databaseId -> {}, flag -> {}", email, databaseId, flagStatus);

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            // Bind to the existing email message by its ID
            MailSummary message = mailSummaryDao.findById(databaseId)
                    .orElseThrow(() -> new ResourceNotFoundException("No database record found for the given id."));
            ItemId itemId = new ItemId(message.getMessageId());
            EmailMessage originalMessage = EmailMessage.bind(service, itemId);
            ExtendedPropertyDefinition flagStatusProp = new ExtendedPropertyDefinition(0x1090, MapiPropertyType.Integer);

            if (flagStatus.equals("flagged")) {
                originalMessage.setExtendedProperty(flagStatusProp, 2);
                message.setFlagStatus("Flagged");
            } else {
                originalMessage.setExtendedProperty(flagStatusProp, 0);
                message.setFlagStatus("notFlagged");
            }
            mailSummaryDao.save(message);
            originalMessage.update(ConflictResolutionMode.AutoResolve);
            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            auditLog.error("Error inside @method flagEmail", e.getMessage(), getStackTraceAsString(e), databaseId, null, FLAG_EWS, email, null, null, null, null, flagStatus, null);
//            log.error("Error inside @method flagEmail", e);
            return EmailConstants.FAILED;
        }
    }


    private Date convertStringToDate(String dateTimeString) {
        Instant instant = Instant.parse(dateTimeString);
        return Date.from(instant);
    }


    /**
     * Gets flag status.
     *
     * @param email     the email
     * @param messageId the message id
     * @return the flag status
     */
    public String getFlagStatus(String email, String messageId) {
        log.debug("Inside @method getFlagStatus. @param: email -> {}, messageId -> {}", email, messageId);

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            // Bind to the existing email message by its ID
            ItemId itemId = new ItemId(messageId);
            EmailMessage originalMessage = EmailMessage.bind(service, itemId);
            ExtendedPropertyDefinition flagStatusProp = new ExtendedPropertyDefinition(0x1090, MapiPropertyType.Integer);

            PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
            propertySet.setRequestedBodyType(BodyType.Text);
            propertySet.add(flagStatusProp);
            originalMessage.load(propertySet);

            ExtendedPropertyCollection extendedProperties = originalMessage.getExtendedProperties();
            List<ExtendedProperty> properties = extendedProperties.getItems();
            if (!properties.isEmpty() &&
                    properties.getFirst().getPropertyDefinition().getTag().equals(4240) &&
                    properties.getFirst().getValue().equals(2)) {

                return "flagged";
            } else {
                return "notFlagged";
            }
        } catch (Exception e) {
            auditLog.error("Error inside @method getFlagStatus", e.getMessage(), getStackTraceAsString(e), messageId, null, FLAG_STATUS_EWS, email, null, null, null, null, null, null);
//            log.error("Error occurred while getting flag status: ", e);
            return EmailConstants.FAILED;
        }
    }

    /**
     * Gets out of office hours.
     *
     * @param userEmail the user email
     * @return the out of office hours
     */
    public OutOfOfficeDto getOutOfOfficeHours(String userEmail) {

        Map<String, Object> autoReplySettings = null;
        try {
            autoReplySettings = getAutoReplySettingsForUser(userEmail);
        } catch (Exception e) {
            log.error("Error inside @method getOutOfOfficeHours", e);
        }

        if (autoReplySettings == null || autoReplySettings.get("status").equals("disabled")) return null;

        String start = (String) autoReplySettings.get("scheduledStartDateTime");
        String end = (String) autoReplySettings.get("scheduledEndDateTime");
        String timeZone = (String) autoReplySettings.get("scheduledEndTimeZone");

        Date startTime = DateUtils.parseDateFromString(start, timeZone);
        Date endTime = DateUtils.parseDateFromString(end, timeZone);
        OutOfOfficeDto outOfOfficeDto = new OutOfOfficeDto();
        outOfOfficeDto.setOutOfOfficeStart(startTime);
        outOfOfficeDto.setOutOfOfficeEnd(endTime);
        return outOfOfficeDto;
    }


    /**
     * Gets email by subject.
     *
     * @param requestBody the request body
     * @return the email by subject
     * @throws Exception the exception
     */
    public List<String> getEmailBySubject(Map<String, Object> requestBody) throws Exception {
        String email = (String) requestBody.get("email");
        String subject = (String) requestBody.get("subject");
        String type = (String) requestBody.get("type");
        log.debug("Inside @method getEmailBySubject. @param : email -> {} subject -> {} type -> {}",
                email, subject, type);
        ExchangeService service = EWSUtils.getServiceObjectForUser(email);
        ItemView view = new ItemView(10); // Change 10 to the number of items you want to fetch
        view.getOrderBy().add(ItemSchema.DateTimeReceived, SortDirection.Descending);
        SearchFilter filter = new SearchFilter.ContainsSubstring(EmailMessageSchema.Subject, subject);

        Folder folder = Folder.bind(service, WellKnownFolderName.valueOf("Inbox"));
        FindItemsResults<Item> findResults = service.findItems(folder.getId(), filter, view);

        PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
        propertySet.setRequestedBodyType(BodyType.Text);
        service.loadPropertiesForItems(findResults, propertySet);

        List<String> result = new ArrayList<>();
        for (Item item : findResults) {
            if (item instanceof EmailMessage) {
                EmailMessage emailMessage = (EmailMessage) item;
                String emailContent = JsonUtil.convertEmailMessageToJson(emailMessage, type).toString();
                result.add(emailContent);
            }
        }
        return result;
    }

    /**
     * Add custom tags boolean.
     *
     * @param email     the email
     * @param messageId the message id
     * @param tags      the tags
     * @return the boolean
     */
    public boolean addCustomTags(String email, String messageId, String... tags) {
        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            ItemId itemId = new ItemId(messageId);
            EmailMessage originalMessage = EmailMessage.bind(service, itemId);

            // Define custom property for tags
            UUID customTagPropertySetId = UUID.fromString("85C0E4E4-4DB9-4B42-BA72-94C16D414A77");
            ExtendedPropertyDefinition customTagsProperty = new ExtendedPropertyDefinition(customTagPropertySetId, "CustomTags", MapiPropertyType.String);

            // Join tags into a single string
            String tagsString = String.join(",", tags);

            // Set custom tags property
            originalMessage.setExtendedProperty(customTagsProperty, tagsString);

            // Update the email item on the server
            originalMessage.update(ConflictResolutionMode.AutoResolve);

            return true;
        } catch (Exception e) {
            auditLog.error("Error inside @method addCustomTags", e.getMessage(), getStackTraceAsString(e), messageId, null, TAG_EMAIL_EWS, email, null, null, null, null, tags, null);
//            log.error("Error while tagging", e);
            return false;
        }
    }

    /**
     * Gets message ids by tag.
     *
     * @param email the email
     * @param tag   the tag
     * @return the message ids by tag
     */
    public List<String> getMessageIdsByTag(String email, String tag) {
        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Define the custom property for tags
            UUID customTagPropertySetId = UUID.fromString("85C0E4E4-4DB9-4B42-BA72-94C16D414A77");
            ExtendedPropertyDefinition customTagsProperty = new ExtendedPropertyDefinition(customTagPropertySetId, "CustomTags", MapiPropertyType.String);

            Date startDate = DateUtils.getDateNDaysBefore(15);
            // Define the search filters for date and tag
            SearchFilter dateFilter = new SearchFilter.IsGreaterThanOrEqualTo(ItemSchema.DateTimeReceived, startDate);
            SearchFilter tagFilter = new SearchFilter.ContainsSubstring(customTagsProperty, tag);


            // Combine the filters using a search filter collection
            SearchFilter.SearchFilterCollection searchFilters = new SearchFilter.SearchFilterCollection(LogicalOperator.And);
            searchFilters.add(dateFilter);
            searchFilters.add(tagFilter);

            // Define the search filter for the custom tag
//            SearchFilter searchFilter = new SearchFilter.ContainsSubstring(customTagsProperty, tag);

            // Define the view for the items to be returned
            ItemView view = new ItemView(10); // Change 10 to the number of items you want to fetch
            view.getOrderBy().add(ItemSchema.DateTimeReceived, SortDirection.Descending);

            // Find items in the Inbox that match the search filter
            Folder folder = Folder.bind(service, WellKnownFolderName.Inbox);
            FindItemsResults<Item> findResults = service.findItems(folder.getId(), searchFilters, view);

            PropertySet propertySet = new PropertySet(PropertySet.FirstClassProperties.getBasePropertySet());
            propertySet.setRequestedBodyType(BodyType.Text);
            service.loadPropertiesForItems(findResults, propertySet);

            List<String> result = new ArrayList<>();
            for (Item item : findResults) {
                if (item instanceof EmailMessage emailMessage) {
                    String uniqueId = emailMessage.getId().getUniqueId();
                    result.add(uniqueId);
                }
            }
            return result;
        } catch (Exception e) {
            auditLog.error("Error inside @method getMessageIdsByTag", e.getMessage(), getStackTraceAsString(e), null, null, EWS_NETWORK_CALL, email, null, null, null, null, tag, null);
//            log.error("An error occurred while searching emails by tag", e);
            return new ArrayList<>();
        }
    }

    /**
     * Gets calendar event by id.
     *
     * @param email the email
     * @param id    the id
     * @return the calendar event by id
     */
    public List<EventDto> getCalendarEventById(String email, String id) {
        // Fetch the email message using the provided email and message id
        EmailMessage emailMessage = getEmailByMessageId(email, id);

        // Check if the email message is a MeetingRequest
        if (emailMessage instanceof MeetingRequest meetingRequest) {
            try {
                // Get the associated appointment's ItemId
                ItemId appointmentId = meetingRequest.getAssociatedAppointmentId();

                if (appointmentId != null) {
                    // Use the service to bind the appointment using its ItemId
                    ExchangeService service = meetingRequest.getService();
                    Appointment appointment = Appointment.bind(service, appointmentId);

                    // Load all necessary properties of the appointment
                    appointment.load(PropertySet.FirstClassProperties);

                    // Convert the appointment to EventDto using the provided utility method
                    EventDto eventDto = ConverterUtils.convertToEventDto(appointment);
                    return List.of(eventDto);
                }
            } catch (Exception e) {
                log.error("Error occurred while fetching the calendar event by id", e);
            }
        }
        return new ArrayList<>();
    }

    /**
     * Gets available meeting slots v 2.
     *
     * @param emails          the emails
     * @param startDateTime   the start date time
     * @param endDateTime     the end date time
     * @param meetingDuration the meeting duration
     * @return the available meeting slots v 2
     */
    public List<AvailableTimeSlots> getAvailableMeetingSlotsV2(List<String> emails, String startDateTime, String endDateTime, int meetingDuration) {
        try {
            log.debug("Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
            if (startDateTime == null || endDateTime == null) {
                String email = userContextHolder.getCurrentUser().getEmail();
                EmailUser user = emailUserDao.findByEmail(email);
                String userId = user.getUserId();
                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
                startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
                endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
            }
            Map<String, List<Meeting>> result = new HashMap<>();
            for (String email : emails) {
                OutOfOfficeDto outOfOfficeHours = getOutOfOfficeHours(email);
                List<EventDto> calendarEvents = getCalendarEvents(email, startDateTime, endDateTime, true);
                if (calendarEvents == null || calendarEvents.isEmpty()) {
                    log.debug("No calendar events found for email: {}", email);
                    result.put(email, addOutOfOfficeToMeeting(new ArrayList<>(), outOfOfficeHours));  // Add empty list for this email
                    continue;
                }
                List<Meeting> list = new ArrayList<>();
                for (EventDto eventDto : calendarEvents) {
                    Meeting meeting = new Meeting();
                    meeting.setStartTime(eventDto.getMeetingStartTime());
                    meeting.setEndTime(eventDto.getMeetingEndTime());
                    list.add(meeting);
                }
                list = addOutOfOfficeToMeeting(list, outOfOfficeHours);
                result.put(email, list);
            }
            Date start, end;
            if (startDateTime.endsWith("Z")) {
                start = convertStringToDate(startDateTime);
                end = convertStringToDate(endDateTime);
            } else {
                start = DateUtils.parseDateWithoutTZ(startDateTime);
                end = DateUtils.parseDateWithoutTZ(endDateTime);
            }

            return graphIntegrationService.getAvailableSlots(result, emails, start, end, meetingDuration);
        } catch (Exception e) {
            auditLog.error("Error inside @method getAvailableSlot", e.getMessage(), getStackTraceAsString(e), null, null, AVAILABLE_SLOT_EWS, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
        }
        return null;
    }


    /**
     * Mark message read unread .
     *
     * @param email             the email
     * @param internetMessageId the internet message id
     * @param markAsRead        the mark as read
     * @return the string
     */
    public String markMessageReadUnread(String email, String internetMessageId, boolean markAsRead) {
        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            SearchFilter filter = new SearchFilter.IsEqualTo(EmailMessageSchema.InternetMessageId, internetMessageId);
            FindItemsResults<Item> findResults = service.findItems(WellKnownFolderName.Inbox, filter, new ItemView(1));

            if (findResults.getItems().size() > 0) {
                EmailMessage message = EmailMessage.bind(service, findResults.getItems().get(0).getId());
                message.setIsRead(markAsRead);
                message.update(ConflictResolutionMode.AutoResolve);
                log.info("Message with InternetMessageID {} marked as {}", internetMessageId, markAsRead ? "read" : "unread");
                return EmailConstants.SUCCESS;
            } else {
                log.warn("No message found with internetMessageId {}", internetMessageId);
                return EmailConstants.FAILED;
            }
        } catch (Exception e) {
            log.error("Error marking EWS message as {}: {}", markAsRead ? "read" : "unread", e.getMessage());
            auditLog.error("Error marking EWS message", e.getMessage(), getStackTraceAsString(e), null, null, "ERROR_MARKING_EWS_MESSAGE", email, null, null, null, null, null, null);
            return EmailConstants.FAILED;
        }
    }

    /**
     * Create draft reply with attachment.
     *
     * @param email       the email
     * @param messageId   the message id
     * @param content     the content
     * @param sendDraft   the send draft
     * @param attachments the attachments
     * @param isReplyAll  the is reply all
     * @return the string
     */
    public String createDraftReplyWithAttachment(String email, String messageId, String content, boolean sendDraft, String attachments, boolean isReplyAll) {
        log.debug("Inside @method createDraftReply. @param: email -> {}, messageId -> {}, content -> {}, sendDraft -> {}, isReplyAll -> {}", email, messageId, content, sendDraft, isReplyAll);
        List<String> attachmentPathsToDelete = new ArrayList<>();
        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            ItemId itemId = new ItemId(messageId);
        //    String s3BucketName = "emailAttachments";
            String prefix = "attachments/";
            EmailMessage originalMessage = EmailMessage.bind(service, itemId);
            ResponseMessage reply;

            if (isReplyAll) {
                reply = originalMessage.createReply(true);
            } else {
                reply = originalMessage.createReply(false);
            }

            reply.setBody(MessageBody.getMessageBodyFromText(content));
            EmailMessage savedDraft = reply.save(WellKnownFolderName.Drafts);
            List<String> attachmentList = CommonUtils.getListFromCommaSeparatedString(attachments);

            for(String attachmentPath : attachmentList) {
                int prefixIndex = attachmentPath.indexOf(prefix);
                String fileName;
                if (prefixIndex != -1) {
                    fileName = attachmentPath.substring(prefixIndex + prefix.length());
                } else {
                    fileName = attachmentPath.substring(attachmentPath.lastIndexOf("/") + 1);
                }
                fileName = removeTimestamp(fileName);
                byte[] in = s3Service.downloadFileAsBytes(s3BucketName, attachmentPath);
                savedDraft.getAttachments().addFileAttachment(fileName, in);
                attachmentPathsToDelete.add(attachmentPath);
            }

            if (sendDraft) {
                savedDraft.send();
                log.debug("{} message sent successfully for email: {}, messageId: {}",
                        isReplyAll ? "Reply-all" : "Reply", email, messageId);
            }

            for (String attachmentPath : attachmentPathsToDelete) {
                s3Service.deleteFileFromS3(s3BucketName, attachmentPath);
            }

            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            auditLog.error("Error inside @method createDraftReply", e.getMessage(), getStackTraceAsString(e),
                    messageId, null, CREATE_REPLY_EWS, email, null, null, null, null,
                    Map.of("content", content, "isReplyAll", isReplyAll), null);
            return EmailConstants.FAILED;
        }
    }

    /**
     * Forward email.
     *
     * @param email         the email
     * @param messageId     the message id
     * @param toRecipients  the to recipients
     * @param ccRecipients  the cc recipients
     * @param bccRecipients the bcc recipients
     * @param comment       the comment
     * @return the string
     */
    public String forwardEmail(String email, String messageId, String toRecipients, String ccRecipients, String bccRecipients, String comment) {
        log.debug("Forwarding email using EWS. @param: email -> {}, messageId -> {}, toRecipients -> {}, ccRecipients -> {}, bccRecipients -> {}", email, messageId, toRecipients, ccRecipients, bccRecipients);
        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            ItemId itemId = new ItemId(messageId);

            EmailMessage message = EmailMessage.bind(service, itemId);

            ResponseMessage forward = message.createForward();

            addRecipientsToMessage(forward.getToRecipients(), toRecipients);
            addRecipientsToMessage(forward.getCcRecipients(), ccRecipients);
            addRecipientsToMessage(forward.getBccRecipients(), bccRecipients);

            forward.setBody(MessageBody.getMessageBodyFromText(comment));

            forward.send();

            log.debug("Email forwarded successfully using EWS");
            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            log.error("Exception while forwarding email using EWS", e);
            return EmailConstants.FAILED;
        }
    }

    private void addRecipientsToMessage(EmailAddressCollection addressCollection, String recipients) throws Exception {
        if (recipients != null && !recipients.trim().isEmpty()) {
            List<String> recipientList = CommonUtils.getListFromCommaSeparatedString(recipients);
            for (String recipient : recipientList) {
                addressCollection.add(recipient);
            }
        }
    }

    /**
     * Gets flagged emails.
     *
     * @param email    the email
     * @param folderId the folder id
     * @return the flagged emails
     */
    public List<String> getFlaggedEmails(String email, String folderId) {
        log.debug("Fetching flagged emails for userId {} and folderId {}", email, folderId);
        List<String> flaggedEmails = new ArrayList<>();

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Create the folder ID for the specified folder
            FolderId folder = new FolderId(folderId);

            // Define the extended property for flag status
            ExtendedPropertyDefinition flagStatusProp = new ExtendedPropertyDefinition(0x1090, MapiPropertyType.Integer);

            // Set the view to retrieve up to 1000 items at once
            ItemView view = new ItemView(1000);

            // Define search filter to find flagged emails (FlagStatus == 2 means 'Flagged')
            SearchFilter searchFilter = new SearchFilter.IsEqualTo(flagStatusProp, 2);

            // Perform the search for flagged emails in the folder
            FindItemsResults<Item> results = service.findItems(folder, searchFilter, view);
            for (Item item : results) {
                if (item instanceof EmailMessage emailMessage) {
                    emailMessage.load(new PropertySet(EmailMessageSchema.InternetMessageId));
                    flaggedEmails.add(emailMessage.getInternetMessageId());
                }
            }

        } catch (Exception e) {
            log.error("Error while getting flagged emails from EWS for user: {}, folder: {}", email, folderId, e);
        }

        return flaggedEmails;
    }

    /**
     * Gets emails of user.
     *
     * @param email      the email
     * @param mailFolder the mail folder
     * @param lowerLimit the lower limit
     * @param upperLimit the upper limit
     * @return the emails of user
     */
    public List<UserEmailDto> getEmailsOfUser(String email, String mailFolder, Integer lowerLimit, Integer upperLimit) {
        log.debug("Inside @method getEmailsOfUserFromEws. @param: email -> {}, lowerLimit -> {}, upperLimit -> {}, folder -> {}", email, lowerLimit, upperLimit, mailFolder);

        List<UserEmailDto> userEmailDtos = new ArrayList<>();
        try {
            // Create the ExchangeService object
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Default folder is Drafts if 'Drafts' is passed, else use the provided folder
            FolderId folderId;
            if ("Drafts".equalsIgnoreCase(mailFolder)) {
                folderId = new FolderId(WellKnownFolderName.Drafts);
            } else {
                folderId = new FolderId(WellKnownFolderName.valueOf(mailFolder));
            }

            // Log the folder being used
            log.debug("Using folder: {}", folderId);

            // Define the view for the items to be retrieved
            ItemView view = new ItemView(upperLimit - lowerLimit + 1);
            view.setOffset(lowerLimit);

            // Find items in the specified folder
            FindItemsResults<Item> findResults = service.findItems(folderId, view);

            // Log the result of the findItems call
            log.debug("Found {} items in folder {}", findResults.getTotalCount(), mailFolder);

            service.loadPropertiesForItems(findResults, PropertySet.FirstClassProperties);

            // Convert results to DTO objects
            for (Item item : findResults) {
                if (item instanceof EmailMessage) {
                    EmailMessage emailMessage = (EmailMessage) item;

                    // Use the existing getUserEmailDto method to populate the DTO
                    UserEmailDto dto = getUserEmailDto(email, emailMessage, false);
                    userEmailDtos.add(dto);
                }
            }

            log.debug("Successfully fetched emails from EWS for user: {}", email);
        } catch (Exception e) {
            log.error("Error while fetching emails from EWS for user: {}. Exception: {}", email, e.getMessage());
        }

        return userEmailDtos;
    }

    /**
     * Deletes an email by ID using EWS.
     *
     * @param userEmail the email address of the user
     * @param messageId the ID of the email to be deleted
     * @return true if deletion (move to Deleted Items) is successful, false otherwise
     */
    public boolean deleteEmailById(String userEmail, String messageId) {
        log.debug("Attempting to soft delete email with ID: {} for user: {}", messageId, userEmail);

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(userEmail);

            ItemId itemId = new ItemId(messageId);

            Item emailToDelete = Item.bind(service, itemId);

            emailToDelete.delete(DeleteMode.MoveToDeletedItems);

            log.info("Successfully deleted email with ID: {} for user: {}", messageId, userEmail);
            return true;
        } catch (ServiceResponseException e) {
            auditLog.error("ServiceResponseException occurred while trying to delete email with ID: {}", e.getMessage(),
                    getStackTraceAsString(e), null, null, "DELETE_EMAIL_EWS", userEmail, messageId, null, null, null, null, null);
        } catch (Exception e) {
            auditLog.error("Unexpected error occurred while  deleting email with ID: {}", e.getMessage(),
                    getStackTraceAsString(e), null, null, "DELETE_EMAIL_EWS", userEmail, messageId, null, null, null, null, null);
        }

        return false;
    }

    /**
     * Send draft string.
     *
     * @param email     the email
     * @param messageId the message id
     * @return the string
     */
    public String sendDraft(String email, String messageId) {
        log.debug("Inside @method sendDraft. @param: email -> {}, messageId -> {}", email, messageId);

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            // Bind to the draft email message by its ID
            ItemId itemId = new ItemId(messageId);
            EmailMessage draftMessage = EmailMessage.bind(service, itemId);

            // Send the draft email
            draftMessage.send();
            log.debug("Draft email sent successfully for email: {}, messageId: {}", email, messageId);

            return EmailConstants.SUCCESS;
        } catch (Exception e) {
            auditLog.error("Error inside @method sendDraft", e.getMessage(), getStackTraceAsString(e), messageId, null, "SEND_DRAFT_EWS", email, null, null, null, null, null, null);
            return EmailConstants.FAILED;
        }
    }

    public Map<String, Map<String, String>> getOutOfOffice(List<String> emails) throws Exception {
        log.debug("Inside @method getAutoReplySettingsForUser. @param: emails -> {}", emails);
        Map<String, Map<String, String>> result = new HashMap<>();
        for (String email : emails) {
            Map<String, Object> settings = null;
            try {
                settings = getAutoReplySettingsForUser(email);
            } catch (Exception e) {
                settings = new HashMap<>();
                settings.put("status", "error");
            }

            if (settings == null) {
                result.put(email, Map.of("error", "something went wrong"));
                continue;
            }

            String status = (String) settings.get("status");
            switch (status) {
                case "scheduled" -> {
                    String start = (String) settings.get("scheduledStartDateTime");
                    String end = (String) settings.get("scheduledEndDateTime");
                    Map<String, String> map = Map.of("start", start, "end", end);
                    result.put(email, map);
                }

                case "disabled" -> result.put(email, Map.of());

                case "error" -> result.put(email, Map.of("error", "something went wrong"));
            }
        }
        return result;
    }


}
