package com.enttribe.emailagent.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.enttribe.emailagent.dto.EmailUserDto;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.entity.Organisation;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.repository.OrganisationRepository;
import com.enttribe.emailagent.repository.impl.OrganistationRepositoryImpl;
import com.enttribe.emailagent.service.OrganistationService;

/**
 * The type Organisation service.
 *  <AUTHOR> Sonsale
 */
@Service
public class OrganisationServiceImpl implements OrganistationService{

    private static final Logger log = EmailAgentLogger.getLogger(OrganisationServiceImpl.class);

    @Autowired
    private OrganisationRepository repository;

    @Autowired
    private OrganistationRepositoryImpl daoImpl;


    @Override
    public Organisation create(Organisation organisation) {
        organisation.setCreatedTime(new Date());
        organisation.setModifiedTime(new Date());
        log.info("came to create organistation");
        return repository.save(organisation);
    }


    @Override
    public Organisation update(Integer id, Organisation organisation) {
        Organisation org = repository.findById(id).get();
        // org.setActive(organisation.isActive());
        // org.setBlacklisted(organisation.getBlacklisted());
        // org.setCheckin(organisation.getCheckin());
        // org.setCheckout(organisation.getCheckout());
        // org.setCreatedTime(organisation.getCreatedTime());
        // org.setMeetingType(organisation.getMeetingType());
        // org.setOrganisationName(organisation.getOrganisationName());
        // org.setTimeZone(organisation.getTimeZone());
        organisation.setModifiedTime(new Date());
        return repository.save(organisation);
    }

    @Override
    public Organisation updateStatus(Integer id, boolean active) {
            Organisation organisation= repository.findById(id).get();
            organisation.setModifiedTime(new Date());
            organisation.setActive(active);
            return repository.save(organisation); 
    }


    @Override
    public List<Organisation> organisationByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap) {
        List<Organisation> orgs = null;
        orgs = daoImpl.organisationByFilter(filterMap, llimit, ulimit);
        return orgs;
    }


    @Override
    public long organisationCountByFilter(Map<String, Object> filterMap) {
        List<Organisation> orgs = null;
        orgs = daoImpl.organisationCountByFilter(filterMap);
        return orgs.size();
    }
}
