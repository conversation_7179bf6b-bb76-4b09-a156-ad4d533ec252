package com.enttribe.emailagent.service.impl;

import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.repository.FailureLogsServiceDao;
import com.enttribe.emailagent.repository.impl.FailureLogsServiceDaoImpl;
import com.enttribe.emailagent.service.FailureLogsService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.EmailConstants;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Failure logs service.
 *  <AUTHOR> Pathak
 */
@Service
public class FailureLogsServiceImpl implements FailureLogsService {
    private static final Logger log = EmailAgentLogger.getLogger(FailureLogsServiceImpl.class);

    @Autowired
    private FailureLogsServiceDao failureLogsServiceDao;
    @Autowired
    private FailureLogsServiceDaoImpl daoImpl;
    @Autowired
    private UserContextHolder userContextHolder;


    /**
     * Saves a new FailureLog entity to the database.
     *
     * @param failureLog the FailureLog entity to save
     * @return the saved FailureLog entity
     */
    @Override
    public FailureLogs save(FailureLogs failureLog) {
        try {
            return failureLogsServiceDao.save(failureLog);
        }
        catch (IllegalArgumentException e){
            log.error("Invalid argument provided for FailureLogs creation", e);
            throw new RuntimeException("Invalid argument provided", e);
        }
        catch (DataAccessException e) {
            log.error("Database access error while saving FailureLogs", e);
            throw new RuntimeException("Database access error", e); // Custom exception or appropriate response
        }
        catch (Exception e) {
            log.error("Unexpected error occurred while processing FailureLogs", e);
            throw new RuntimeException("An unexpected error occurred", e); // Custom exception or appropriate response
        }
    }

    @Override
    public FailureLogs ingestFailureLog(FailureLogs failureLog) {
        try {
            log.info("Saving new FailureLog: {}");
            FailureLogs failureLogsIngest=failureLogsServiceDao.save(failureLog);
            log.info("successfully ingest errors");
            return failureLogsIngest;
        }
        catch (IllegalArgumentException e){
            log.error("Invalid argument provided for FailureLogs creation", e);
            return null;
        }
        catch (DataAccessException e) {
            log.error("Database access error while saving FailureLogs", e);
            return null;
        }
        catch (Exception e) {
            log.error("Unexpected error occurred while processing FailureLogs", e);
            return null;
        }
    }

    /**
     * Creates a FailureLogs entity with the provided values.
     *
     * @param internetMessageId the unique identifier for the email message
     * @param email the email address associated with the failure
     * @param emailSubject the subject of the email
     * @param messageId the unique identifier for the message within the email
     * @param conversationId the unique identifier for the email conversation
     * @param type the type of failure or error encountered
     * @param exceptionMessage a message describing the exception or error
     * @param customExceptionMessage any custom exception message to provide additional context
     * @param exceptionTrace the stack trace of the exception for debugging purposes
     * @param attachment a boolean indicating if there was an attachment involved in the failure
     * @param userMailAttachment an optional attachment associated with the failure
     * @param intent the intent or purpose of the email if relevant
     * @param attendees a list of attendees related to the email or failure
     * @param errorDate the date and time when the error occurred
     * @return the created FailureLogs entity
     */
    @Override
    public FailureLogs ingestFailureLog(String internetMessageId, String email, String emailSubject, String messageId, String conversationId, String type, String exceptionMessage, String customExceptionMessage, String exceptionTrace, Boolean attachment, String userMailAttachment, String intent, String attendees, Date errorDate) {
            try {
                return save(new FailureLogs(internetMessageId,email,emailSubject,messageId,conversationId,type,exceptionMessage,customExceptionMessage,exceptionTrace,attachment,userMailAttachment,intent,attendees,errorDate));
            }
            catch (IllegalArgumentException e){
                log.error(EmailConstants.INVALID_ARGUMENT_PROVIDED_FOR_FAILURELOGS_CREATION, e);
                throw new RuntimeException(EmailConstants.INVALID_ARGUMENT_PROVIDED, e);
            }
            catch (DataAccessException e) {
                log.error(EmailConstants.DATABASE_ACCESS_ERROR_WHILE_SAVING_FAILURELOGS, e);
                throw new RuntimeException(EmailConstants.DATABASE_ACCESS_ERROR, e); // Custom exception or appropriate response
            }
            catch (Exception e) {
                log.error(EmailConstants.UNEXPECTED_ERROR_OCCURRED_WHILE_PROCESSING_FAILURELOGS, e);
                throw new RuntimeException(EmailConstants.AN_UNEXPECTED_ERROR_OCCURRED, e); // Custom exception or appropriate response
            }
    }

    /**
     * Retrieves all FailureLogs from the database.
     *
     * @return a list of all FailureLogs
     */
    @Override
    public List<FailureLogs> findAll() {
        log.info("Retrieving all FailureLogs");
        return failureLogsServiceDao.findAll();
    }

    /**
     * Retrieves a specific FailureLog by its ID.
     *
     * @param id the ID of the FailureLog to retrieve
     * @return the FailureLog entity with the specified ID
     * @throws ResourceNotFoundException if the FailureLog is not found
     */
    @Override
    public FailureLogs findById(Integer id) {
        log.info("Finding FailureLog by ID: {}", id);
        return failureLogsServiceDao.findById(id).orElseThrow(() -> new ResourceNotFoundException(EmailConstants.FAILURELOGS_NOT_FOUND_FOR_THIS_ID + id));
    }

    /**
     * Updates an existing FailureLog by its ID.
     *
     * @param id the ID of the FailureLog to update
     * @param failureLogDetails the FailureLog entity containing updated details
     * @return the updated FailureLog entity
     * @throws ResourceNotFoundException if the FailureLog is not found
     */
    @Override
    public FailureLogs update(Integer id, FailureLogs failureLogDetails) {
        log.info("Updating FailureLog with ID: {}", id);
        FailureLogs failureLog = failureLogsServiceDao.findById(id).orElseThrow(() -> new ResourceNotFoundException(EmailConstants.FAILURELOGS_NOT_FOUND_FOR_THIS_ID + id));
        return failureLogsServiceDao.save(failureLog);
    }

    /**
     * Deletes a FailureLog by its ID.
     *
     * @param id the ID of the FailureLog to delete
     * @return true if the FailureLog was successfully deleted, false otherwise
     * @throws ResourceNotFoundException if the FailureLog is not found
     */
    @Override
    public Boolean deleteById(Integer id) {
        log.info("Deleting FailureLog with ID: {}", id);
        FailureLogs failureLog = failureLogsServiceDao.findById(id).orElseThrow(() -> new ResourceNotFoundException(EmailConstants.FAILURELOGS_NOT_FOUND_FOR_THIS_ID + id));
        failureLogsServiceDao.delete(failureLog);
        return true;
    }


    @Override
    public List<FailureLogs> search(Integer id, String internetMessageId, String email, String emailSubject, String messageId, String conversationId, String type, String exceptionMessage, String customExceptionMessage, String exceptionTrace, String errorDate
    ) {
        try {
            log.debug("Searching for FailureLogs with parameters: id={}, internetMessageId={}, email={}, emailSubject={}, messageId={}, conversationId={}, type={}, exceptionMessage={}, customExceptionMessage={}, exceptionTrace={}, errorDate={}", id, internetMessageId, email, emailSubject, messageId, conversationId, type, exceptionMessage, customExceptionMessage, exceptionTrace, errorDate);
            return failureLogsServiceDao.search(id, internetMessageId, email, emailSubject, messageId, conversationId, type, exceptionMessage, customExceptionMessage, exceptionTrace, errorDate);
        } catch (Exception e) {
            log.error("Error occurred while searching for FailureLogs", e);
            throw new RuntimeException("Search failed", e); // or handle as appropriate
        }
    }

    @Override
    public List<FailureLogs> getFailureLogByEmail(String email) {
        return failureLogsServiceDao.findByEmail(email);
    }

        @Override
public Long count(Integer id, String internetMessageId, String email, String emailSubject, String messageId, String conversationId, String type, String exceptionMessage, String customExceptionMessage, String exceptionTrace, String errorDate) {
    try {
        log.debug("Counting FailureLogs with parameters: id={}, internetMessageId={}, email={}, emailSubject={}, messageId={}, conversationId={}, type={}, exceptionMessage={}, customExceptionMessage={}, exceptionTrace={}, errorDate={}",id, internetMessageId, email, emailSubject, messageId, conversationId, type, exceptionMessage, customExceptionMessage, exceptionTrace, errorDate);
        Pageable pageable = PageRequest.of(0, 25);
        return failureLogsServiceDao.countLimited(id, internetMessageId, email, emailSubject, messageId, conversationId, type, exceptionMessage, customExceptionMessage, exceptionTrace, errorDate);
    } catch (Exception e) {
        log.error("Error occurred while counting FailureLogs", e);
        throw new RuntimeException("Count failed", e); // or handle as appropriate
    }
}



    @Override
    public List<FailureLogs> failureLogByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap) {
        List<FailureLogs> failureMap = null;
        failureMap = daoImpl.getFilteredFailureLogs(filterMap, llimit, ulimit);
        return failureMap;
    }

    @Override
    public long failureLogCountByFilter(Map<String, Object> filterMap) {
        List<FailureLogs> failureMap = null;
        failureMap = daoImpl.getFilteredFailureLogsCount(filterMap);
        return failureMap.size();
    }

    @Override
    public List<MailSummary> mailSummaryByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap) {
        filterMap.put("userId",userContextHolder.getCurrentUser().getId());
        List<MailSummary> failureMap = null;
        failureMap = daoImpl.getFilteredMailSummary(filterMap, llimit, ulimit);
        return failureMap;

    }

    @Override
    public long mailSummaryCountByFilter(Map<String, Object> filterMap) {
        filterMap.put("userId",userContextHolder.getCurrentUser().getId());
        return daoImpl.getMailSummaryCountByFilter(filterMap);
        }

    @Override
    public List<String> typesOfFailureLogs() {
        return failureLogsServiceDao.typesOfFailureLogs();
    }

    @Override
    public String updateStatus(Integer id, String status) {
        try {
            FailureLogs logs = findById(id); // Assuming findById is a method that fetches the FailureLogs record by id
            logs.setStatus(status); // Update the status field
            failureLogsServiceDao.save(logs); // Save the updated entity
            return "{\"result\":\"Success\"}";
        } catch (Exception e) {
            return "{\"result\":\"Failed\"}";
        }
    }

    //         @Override
    // public EmailUser updateStatus(Integer id, boolean deleted) {
    //         EmailUser emailUser=findById(id);
    //         log.info("emailUser id is {}",emailUser.getId());
    //         emailUser.setModifiedTime(new Date());
    //         emailUser.setDeleted(deleted);
    //         return emailUserDao.save(emailUser); 
    // }

}
