package com.enttribe.emailagent.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.entity.UserMailAttachment;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.repository.UserMailAttachmentDao;
import com.enttribe.emailagent.repository.impl.UserMailAttachmentDaoImpl;
import com.enttribe.emailagent.service.UserMailAttachmentService;
import com.enttribe.emailagent.utils.EmailConstants;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * The type User mail attachment service.
 *  <AUTHOR> Dangi
 */
@Service
public class UserMailAttachmentServiceImpl implements UserMailAttachmentService{

    private static final Logger log = EmailAgentLogger.getLogger(FailureLogsServiceImpl.class);

    @Autowired
    private UserMailAttachmentDao userMailAttachmentDao;

    @Autowired
    private UserMailAttachmentDaoImpl daoImpl;

    @Override
    public UserMailAttachment save(UserMailAttachment userMailAttachment) {
        userMailAttachment.setCreationTime(new Date());
        userMailAttachment.setModifiedTime(new Date());
        userMailAttachmentDao.save(userMailAttachment);
        return userMailAttachment;
    }

        @Override
    public UserMailAttachment findById(Integer id) {
        try {
            return userMailAttachmentDao.findById(id).get();
        }
        catch (DataAccessException e) {
            throw new RuntimeException("Error occurred while get EmailUser",e);
        }
    }


        @Override
    public UserMailAttachment update(Integer id, UserMailAttachment userMailAttachment) {
        UserMailAttachment userMail = userMailAttachmentDao.findById(id).orElseThrow(() -> new ResourceNotFoundException(EmailConstants.FAILURELOGS_NOT_FOUND_FOR_THIS_ID + id));
        return userMailAttachmentDao.save(userMail);
    }

    @Override
    public String updateStatus(Integer id, boolean deleted) {
        try {
            UserMailAttachment userMail = findById(id);
            userMail.setModifiedTime(new Date());
            // userMail.setDeleted(deleted);
            userMailAttachmentDao.save(userMail);
            return "Success";
        } catch (Exception e) {
            return "Failure: " + e.getMessage();
        }
    }
    

    @Override
    public List<UserMailAttachment> search(Integer id,String userId,String name,String uniqueName, String type,
    String messageId,String attachmentId,String ragDocumentId,
    String conversationId, String internetMessageId,String processingStatus,String processingError,Date creationTime, Date modifiedTime
    ) {
        try {
            return userMailAttachmentDao.search(id,userId,name,uniqueName,type,messageId,attachmentId,ragDocumentId,conversationId,internetMessageId,processingStatus,processingError,creationTime,modifiedTime);
        } catch (Exception e) {
            log.error("Error occurred while searching for FailureLogs", e);
            throw new RuntimeException("Search failed", e); // or handle as appropriate
        }
    }

    @Override
    public long count(Integer id,String userId,String name,String uniqueName, String type,
    String messageId,String attachmentId,String ragDocumentId,
    String conversationId, String internetMessageId,String processingStatus,String processingError,Date creationTime, Date modifiedTime
    ) {
        try {
            return userMailAttachmentDao.count(id,userId,name,uniqueName,type,messageId,attachmentId,ragDocumentId,conversationId,internetMessageId,processingStatus,processingError,creationTime,modifiedTime);
        } catch (Exception e) {
            log.error("Error occurred while searching for FailureLogs", e);
            throw new RuntimeException("Search failed", e); // or handle as appropriate
        }
    }

    @Override
    public UserMailAttachment userMailAttachmentByAttachmentId(String attachmentId) {
        return userMailAttachmentDao.findByAttachmentId(attachmentId);
    }

    @Override
    public List<UserMailAttachment> userMailAttachmentByFilter(Integer llimit, Integer ulimit,
            Map<String, Object> filterMap) {

                List<UserMailAttachment> userMailAttachments = null;
                userMailAttachments = daoImpl.userMailAttachmentByFilter(filterMap, llimit, ulimit);
                return userMailAttachments;

    }

    @Override
    public long userMailAttachmentCountByFilter( Map<String, Object> filterMap) {
        List<UserMailAttachment> userMailAttachments = null;
        userMailAttachments = daoImpl.userMailAttachmentByFilterCount(filterMap);
        return userMailAttachments.size();  
    }

    @Override
public String reset(Integer id) {
    UserMailAttachment userMail = findById(id);
    userMail.setProcessingStatus("NEW");
    userMail.setProcessingError("");
    userMailAttachmentDao.save(userMail);

    Map<String, String> response = new HashMap<>();
    response.put("message", "Success");

    // Convert map to JSON
    ObjectMapper objectMapper = new ObjectMapper();
    try {
        return objectMapper.writeValueAsString(response);
    } catch (Exception e) {
        return "{\"message\":\"Failed\"}";
    }
}





}
