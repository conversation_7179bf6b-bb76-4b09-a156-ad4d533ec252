package com.enttribe.emailagent.service.impl;

import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.time.Instant;

import com.enttribe.emailagent.entity.*;
import com.enttribe.emailagent.repository.ContactBookRepository;
import com.enttribe.emailagent.service.EmailPreferencesService;
import com.enttribe.emailagent.utils.EmailConstants;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;

import com.enttribe.emailagent.dao.BatchRepository;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.enttribe.emailagent.dto.EmailUserDto;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.PollTimeInfoRepository;
import com.enttribe.emailagent.repository.impl.EmailUserDaoImpl;
import com.enttribe.emailagent.service.EmailUserService;
import com.enttribe.emailagent.service.UserFoldersService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import org.springframework.web.multipart.MultipartFile;

/**
 * The type Email user service.
 *  <AUTHOR> Barfa
 */
@Service
public class EmailUserServiceImpl implements EmailUserService {
    private static final Logger log = EmailAgentLogger.getLogger(FailureLogsServiceImpl.class);

    @Autowired
    private EmailUserDao emailUserDao;

    @Autowired
    private EmailUserDaoImpl daoImpl;

        @Autowired
    private UserContextHolder userContextHolder;

        @Autowired
    private PollTimeInfoRepository pollTimeInfoRepository;

    @Autowired
    private UserFoldersService userFoldersService;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private ContactBookRepository contactBookRepository;

    @Autowired
    private EmailPreferencesService emailPreferencesService;



    @Override
    public List<EmailUser> findAll() {
        return emailUserDao.findAll();
    }

    @Override
    public EmailUser findById(Integer id) {
        try {
            return emailUserDao.findById(id).get();
        }
        catch (DataAccessException e) {
            throw new RuntimeException("Error occurred while get EmailUser",e);
        }
    }

    @Override
    public EmailUser save(EmailUser emailUser) {
        log.info("came to create user");
        emailUser.setModifiedTime(new Date());
        emailUser.setCreatedTime(new Date());
        userFoldersService.refreshUsersFolder(emailUser.getEmail());
        Batch batch= batchRepository.findByBatchId(emailUser.getBatchId());
        emailUser.setQueueName(batch.getQueueName());
        return emailUserDao.save(emailUser);
    }

    @Override
    public EmailUser saveEmailUser(EmailUserDto emailUser) {
        log.info("Going to create user");
        EmailUser emailUserEntity = mapDtoToEntity(emailUser);
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setUserId(emailUser.getEmail());
        emailPreferences.setContactNumber(emailUser.getPhoneNumber());
        emailPreferences.setTimeZone(emailUser.getTimeZone());
        emailPreferences.setCheckin(LocalTime.of(10, 00));
        emailPreferences.setCheckout(LocalTime.of(20, 00));
        userFoldersService.refreshUsersFolder(emailUser.getEmail());
        emailPreferencesService.saveEmailPreferences(emailPreferences);
        log.info("Successful save user");
        return emailUserDao.save(emailUserEntity);
    }

    private EmailUser mapDtoToEntity(EmailUserDto dto) {
        EmailUser entity = new EmailUser();
        entity.setEmail(dto.getEmail());
        entity.setUserId(dto.getEmail());
        entity.setType(dto.getType());
        entity.setName(dto.getName());
        entity.setPhoneNumber(dto.getPhoneNumber());
        entity.setCreatedTime(new Date());
        entity.setModifiedTime(new Date());
        entity.setDeleted(false);
        return entity;
    }


    @Override
    public List<Map<String, String>> createUsersFromCsv(MultipartFile file) throws IOException {
        List<Map<String, String>> responseList = new ArrayList<>();

        try (InputStreamReader reader = new InputStreamReader(file.getInputStream());
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withHeader("name", "email", "phoneNumber", "batchId", "type").withFirstRecordAsHeader())) {

            for (CSVRecord csvRecord : csvParser) {
                Map<String, String> userStatus = new HashMap<>();
                try {
                    String name = csvRecord.get("name");
                    String email = csvRecord.get("email");
                    String phoneNumber = csvRecord.get("phoneNumber");
                    String batchId = csvRecord.get("batchId");
                    String type = csvRecord.get("type");

                    if (name == null || name.trim().isEmpty() ||
                            email == null || email.trim().isEmpty() ||
                            batchId == null || batchId.trim().isEmpty() ||
                            type == null || type.trim().isEmpty()) {

                        String errorMessage = String.format("Skipping record due to missing mandatory fields: name=%s, email=%s, batchId=%s, type=%s", name, email, batchId, type);
                        log.info(errorMessage);

                        userStatus.put("name", name);
                        userStatus.put("email", email);
                        userStatus.put("status", "failed");
                        responseList.add(userStatus);
                        continue;
                    }

                    EmailUser emailUser = new EmailUser();
                    emailUser.setName(name);
                    emailUser.setEmail(email);
                    emailUser.setUserId(email);
                    emailUser.setPhoneNumber(phoneNumber);
                    emailUser.setBatchId(batchId);
                    emailUser.setType(type);
                    emailUser.setDeleted(false);
                    Batch batch= batchRepository.findByBatchId(emailUser.getBatchId());
                    emailUser.setQueueName(batch.getQueueName());
                    EmailUser createdUser = this.save(emailUser);

                    userStatus.put("name", name);
                    userStatus.put("email", email);
                    userStatus.put("status", "success");
                    responseList.add(userStatus);

                } catch (Exception e) {
                    String errorMessage = String.format("Error processing record: name=%s, email=%s, batchId=%s, type=%s",
                            csvRecord.get("name"), csvRecord.get("email"), csvRecord.get("batchId"), csvRecord.get("type"));
                    log.error(errorMessage, e);

                    userStatus.put("name", csvRecord.get("name"));
                    userStatus.put("email", csvRecord.get("email"));
                    userStatus.put("status", "failed");
                    responseList.add(userStatus);
                }
            }
        }

        return responseList;
    }



    @Override
    public EmailUserDto update(Integer id, EmailUserDto emailUserDetails) {
        try {
            EmailUser emailUser = findById(id);

            emailUser.setBatchId(emailUserDetails.getBatchId());
            emailUser.setUserId(emailUserDetails.getUserId());
            emailUser.setName(emailUserDetails.getName());
            emailUser.setPhoneNumber(emailUserDetails.getPhoneNumber());
            emailUser.setType(emailUserDetails.getType());
            emailUser.setBatchId(emailUserDetails.getBatchId());
            emailUser.setModifiedTime(new Date());
            Batch batch= batchRepository.findByBatchId(emailUser.getBatchId());
            emailUser.setQueueName(batch.getQueueName());
            emailUserDao.save(emailUser);

            log.info("refreshing user's email folders");
            boolean refreshed = userFoldersService.refreshUsersFolder(emailUser.getEmail());
            log.info("refreshed folder : {}", refreshed);

            Optional<PollTimeInfo> pollTime = pollTimeInfoRepository.findFirstByEmailOrderByStartTimeDesc(emailUserDetails.getEmail());
            if (pollTime.isPresent()) {
                PollTimeInfo pollTimeInfo = pollTime.get();
                log.info("poll time {}", pollTimeInfo);
                log.info("poll time start time {}", pollTimeInfo.getStartTime());
               long lastPollTimeInMillis = emailUserDetails.getLastPollTime();
LocalDateTime startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(lastPollTimeInMillis), ZoneId.systemDefault());
                pollTimeInfoRepository.updatePollTimeInfo(emailUserDetails.getEmail(), startTime);
            }

            return emailUserDetails;

        }catch (Exception e){
            log.error("Error occurred while update for emailUserDetails", e);
            throw new RuntimeException("update failed", e);
        }
    }


        @Override
    public EmailUser updateStatus(Integer id, boolean deleted) {
            EmailUser emailUser=findById(id);
            log.info("emailUser id is {}",emailUser.getId());
            emailUser.setModifiedTime(new Date());
            emailUser.setDeleted(deleted);
            return emailUserDao.save(emailUser); 
    }

    @Override
    public EmailUser updateBatch(Integer id, String batch) {
        try {
            EmailUser emailUser=findById(id);
            log.info("emailUser id is {}",emailUser.getId());
            emailUser.setModifiedTime(new Date());
            emailUser.setBatchId(batch);
            return  emailUserDao.save(emailUser);
        }
        catch (Exception e){
            log.error("Error occurred while updateEmailUser for emailUserDetails", e);
            throw new RuntimeException("updateEmailUser failed", e); // or handle as appropriate
        }
    }

    @Override
    public Boolean deleteById(Integer id) {
        try {
            EmailUser emailUser=findById(id);
            emailUser.setModifiedTime(new Date());
            emailUser.setDeleted(true);
            emailUserDao.save(emailUser);
            return true;
        } catch (Exception e) {
            log.error("Error occurred while searching for FailureLogs", e);
           return false;
        }

    }

    @Override
    public List<EmailUser> search(Integer id, String userId, String email, String type, Boolean deleted, String name,int lLimit,int uLimit) {
        Pageable pageable = PageRequest.of(0, 25);
        return emailUserDao.search(id,userId,email,type,deleted,name);
    }

    @Override
    public Long count(Integer id, String userId, String email, String type, Boolean deleted, String name,int lLimit,int uLimit) {
        Pageable pageable = PageRequest.of(0, 25);
        return emailUserDao.countLimited(id,userId,email,type,deleted,name);
    }


     public List<EmailUserDto> emailUserByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap) {
                try{
        List<EmailUser> emailUsers = null;
        emailUsers = daoImpl.filteredEmailUser(filterMap, llimit, ulimit);
        List<EmailUserDto> userDtos = new ArrayList<>();
        // String userId = userContextHolder.getCurrentUser().getId();
        for(EmailUser user: emailUsers){
            EmailUserDto userDto = new EmailUserDto();
            userDto.setId(user.getId());
            userDto.setBatchId(user.getBatchId());
            userDto.setDeleted(user.getDeleted());
            userDto.setEmail(user.getEmail());
            userDto.setName(user.getName());
            userDto.setPhoneNumber(user.getPhoneNumber());
            userDto.setType(user.getType());
            userDto.setUserId(user.getUserId());
            userDto.setCreatedTime(user.getCreatedTime());
            userDto.setModifiedTime(user.getModifiedTime());

             Optional<PollTimeInfo> pollTime = pollTimeInfoRepository.findFirstByEmailOrderByStartTimeDesc(user.getEmail());
             if (pollTime.isPresent() && pollTime.get().getStartTime() != null) {
                long startTimeInMillis = pollTime.get().getStartTime().toInstant(ZoneOffset.UTC).toEpochMilli();
                userDto.setLastPollTime(startTimeInMillis);
                
                long nextPollTimeInMillis = pollTime.get().getStartTime().plusMinutes(5).toInstant(ZoneOffset.UTC).toEpochMilli();
                userDto.setNextPollTime(nextPollTimeInMillis);
            }
            userDtos.add(userDto);

        }
        return userDtos;
    }catch(Exception e){
        System.out.println(e.getStackTrace());
        return null;
    }
    }


    public long emailUserCountByFilter(Map<String, Object> filterMap) {
        List<EmailUser> failureMap = null;
        failureMap = daoImpl.filteredEmailUserCount(filterMap);
        return failureMap.size();
    }

    @Override
    public List<String> getDistinctBatchId(String batchId) {
        return emailUserDao.getDistinctBatchId(batchId);
    }

    @Override
    public List<String> getBatchId() {
       return batchRepository.getBatchList();
    }

    @Override
    public void saveContactsForUsers() {

        log.info("Initiating contact saving process for active users.");

        try {

            List<EmailUser> activeUsers = emailUserDao.findAllActiveUsers();

            if (activeUsers.size() > 1) {
                EmailUser firstUser = activeUsers.get(0);

                for (int i = 1; i < activeUsers.size(); i++) {
                    EmailUser contactUser = activeUsers.get(i);

                    ContactBook contact = new ContactBook();
                    contact.setUserId(firstUser.getUserId());
                    contact.setContactName(contactUser.getName());
                    contact.setContacts(contactUser.getEmail());

                    contactBookRepository.save(contact);
                }

                log.info("Contact saving process completed for {} active users.", activeUsers.size());
            } else {
                log.warn("Not enough active users to save contacts. Active users found: {}", activeUsers.size());
            }
        } catch (Exception e) {

            log.error("An error occurred while saving contacts: {}", e.getMessage());
        }
    }

    @Override
    public Map<String, String> updateUserStatus(String email, String status) {
        log.info("Inside updateUserStatus method email: {} and status: {}", email, status);
        EmailUser user = emailUserDao.findByEmail(email);
        if (user == null) {
            throw new NoSuchElementException("User with email " + email + " not found.");
        }
        switch (status.toLowerCase()) {
            case "enable" -> user.setDeleted(false);
            case "disable" -> user.setDeleted(true);
            default -> {
                log.warn("Invalid status: {} use enable or disable valid  status", status);
                return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
            }
        }
        log.info("User status updated Successfully for email: {} and status: {}", email, status);
        emailUserDao.save(user);
        return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
    }





}
