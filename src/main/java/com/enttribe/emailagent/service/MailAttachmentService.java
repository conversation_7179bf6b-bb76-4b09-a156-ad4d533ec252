package com.enttribe.emailagent.service;

import com.enttribe.emailagent.dto.EmailAttachment;
import com.enttribe.emailagent.utils.EwsTokenUtils;
import com.enttribe.emailagent.utils.TokenUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * The type Mail attachment service.
 *  <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MailAttachmentService {

    private final TokenUtils tokenUtils;

    /**
     * Fetch attachments graph list.
     *
     * @param emailId   the email id
     * @param messageId the message id
     * @return the list
     * @throws Exception the exception
     */
    public List<EmailAttachment> fetchAttachmentsGraph(String emailId, String messageId) throws Exception {
        log.debug("Inside @method fetchAttachmentsGraph. @param : emailId -> {} messageId -> {}", emailId, messageId);
        String accessToken = tokenUtils.getAccessToken();

        String apiUrl = "https://graph.microsoft.com/v1.0/users/" + emailId + "/messages/" + messageId + "/attachments?$select=name,contentType";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(apiUrl);
            request.addHeader("Authorization", "Bearer " + accessToken);

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String jsonResponse = EntityUtils.toString(response.getEntity());
                ObjectMapper mapper = new ObjectMapper();
                JsonNode attachmentsNode = mapper.readTree(jsonResponse).get("value");
                log.debug("Attachment size : {}", attachmentsNode.size());

                List<EmailAttachment> attachments = new ArrayList<>();

                for (JsonNode attachmentNode : attachmentsNode) {
                    String attachmentId = attachmentNode.get("id").asText();
                    String ragDocumentId = UUID.randomUUID().toString();
                    String name = attachmentNode.get("name").asText();
                    log.debug("Attachment name : {}", name);
                    String contentType = attachmentNode.get("contentType").asText();
                    byte[] content = downloadAttachment(emailId, messageId, attachmentId, accessToken);

                    EmailAttachment attachment = new EmailAttachment(name, attachmentId, ragDocumentId, contentType, content);
                    attachments.add(attachment);
                }
                return attachments;
            }
        }
    }

    private byte[] downloadAttachment(String emailId, String messageId, String attachmentId, String accessToken) throws IOException {
        String apiUrl = "https://graph.microsoft.com/v1.0/users/" + emailId + "/messages/" + messageId + "/attachments/" + attachmentId + "/$value";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(apiUrl);
            request.addHeader("Authorization", "Bearer " + accessToken);

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                return EntityUtils.toByteArray(response.getEntity());
            }
        }
    }
}
