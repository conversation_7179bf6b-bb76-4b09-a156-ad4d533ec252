package com.enttribe.emailagent.service;

import com.enttribe.emailagent.AIClient;
import com.enttribe.emailagent.dao.IBuildEmailDao;
import com.enttribe.emailagent.dto.UserEmailDto;
import com.enttribe.emailagent.dto.UserEmailResponseDto;
import com.enttribe.emailagent.entity.BuildEmails;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.EmailConstants;
import com.enttribe.emailagent.utils.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * The type Deployment service.
 * <AUTHOR> Pathak
 */
@Service
@Slf4j
public class DeploymentService {

    @Value("${build.approval.list}")
    private String buildUserApproval;

    @Autowired
    private TokenUtils tokenUtils;

    private final String BACKEND_JENKINS_URL = "https://dev.visionwaves.com/jenkins/job/Backend_Microservice_Demo/buildWithParameters";
    private final String BACKEND_JOB_NAME = "Backend_Microservice_Demo";
    private final String UI_JENKINS_URL = "https://dev.visionwaves.com/jenkins/job/MicroFrontend_Demo/buildWithParameters";
    private final String UI_JOB_NAME = "MicroFrontend_Demo";


    private final String JENKINS_URL = "https://dev.visionwaves.com/jenkins/job/Backend_Microservice_Demo/buildWithParameters";
    private final String JOB_NAME = "Backend_Microservice_Demo";
    private final String USER = EmailConstants.ADMIN;
    private final String API_TOKEN = EmailConstants.API_TOKEN;
    private final String JENKINS_CRUMB = "8ac5f1bb477d01a30bef78669c690989d2d9b6f403586b8cf2ead6a6e9048dfc";


    @Autowired
    private IBuildEmailDao iBuildEmailDao;

    @Autowired
    private GraphIntegrationService graphIntegrationService;


    /**
     * Gets emails of bntv user.
     *
     * @param userId           the user id
     * @param email            the email
     * @param receivedDateTime the received date time
     * @param categories       the categories
     * @param isRead           the is read
     * @param limit            the limit
     * @param offset           the offset
     * @return the emails of bntv user
     * @throws IOException          the io exception
     * @throws InterruptedException the interrupted exception
     * @throws ExecutionException   the execution exception
     */
    public UserEmailResponseDto getEmailsOfBNTVUser(String userId, String email, String receivedDateTime, String categories, Boolean isRead, Integer limit, Integer offset) throws IOException, InterruptedException, ExecutionException {
        log.debug("Inside @method getEmailsOfBNTVUser. @param: email -> {}, receivedDateTime -> {}, categories -> {}, isRead -> {}, limit -> {}, offset -> {}",
                email, receivedDateTime, categories, isRead, limit, offset);

        String url = graphIntegrationService.buildUrlWithFilterAndPagination(email, receivedDateTime, categories, isRead, "inbox", limit, offset);
        log.debug("URL to get emails: {}", url);
        // Create an HttpClient instance
        CloseableHttpClient httpClient = HttpClients.createDefault();

        // Create a GET request
        HttpGet request = new HttpGet(url);
//        request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + MSUtils.getAccessTokenBNTV());
        request.setHeader("Prefer", "outlook.body-content-type=\"text\"");

        // Execute the request
        try (CloseableHttpResponse response = httpClient.execute(request)) {

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                // Return the response body as a String
                String result = EntityUtils.toString(entity);
                List<UserEmailDto> response1 = graphIntegrationService.convertJsonToUserEmailDto(result, email, true);
                List<List<UserEmailDto>> messages = graphIntegrationService.groupByEmailConversation(response1);
                UserEmailResponseDto responseDto = new UserEmailResponseDto();
                responseDto.setMailBoxUserEmail(email);
                responseDto.setMailBoxUserId(userId);
                responseDto.setMessages(messages);
                return responseDto;
            }
        }

        return null;
    }

    /**
     * Poll build email.
     *
     * @param emailId    the email id
     * @param userId     the user id
     * @param timeFilter the time filter
     * @throws IOException          the io exception
     * @throws InterruptedException the interrupted exception
     * @throws ExecutionException   the execution exception
     */
    public void pollBuildEmail(String emailId, String userId, String timeFilter) throws IOException, InterruptedException, ExecutionException {
        log.debug("Entry inside @method pollBuildEmail emailId {} userId {}", emailId, userId);

        JSONObject apiResponse = CommonUtils.convertToJSONObject(getEmailsOfBNTVUser(userId, emailId, timeFilter, null, null, 100, 0));
        log.debug("API response is {}", apiResponse.toString());
        JSONArray message = apiResponse.getJSONArray("messages");
        log.debug("Email array from API response is {}", message.toString());
        for (int i = 0; i < message.length(); i++) {
            String conversationId = message.getJSONArray(i).getJSONObject(i).getString("conversationId");
            String subject = message.getJSONArray(i).getJSONObject(i).getString("subject");
            String body = message.getJSONArray(i).getJSONObject(i).getString("body");
            String fromUser = message.getJSONArray(i).getJSONObject(i).getString("from");
            if (subject.toLowerCase().contains("demo") && subject.toLowerCase().contains("microservice")) {
                log.debug("Backend build deployment " + subject);
                BuildEmails emailByConversationId = iBuildEmailDao.getEmailByConversationId(conversationId);
                if (emailByConversationId == null) {
                    emailByConversationId = new BuildEmails();
                    emailByConversationId.setConversationId(conversationId);
                    emailByConversationId.setContent(body);
                    String categoryResponse = null;
                    String buildJson = null;
                    try {
                        categoryResponse = AIClient.generateAiResponse(body, "", null, null, "BackendBuild", null, null);
                        buildJson = AIClient.parseResponse(categoryResponse);
                        emailByConversationId.setBuildParameters(buildJson);
                        emailByConversationId.setEmailFrom(fromUser);
                        emailByConversationId.setCreatedTime(new Date());
                        iBuildEmailDao.save(emailByConversationId);

                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }

                } else {
                    if (!true) {
                        log.debug("Build is not deployed for {}", emailByConversationId.getId());
                        String buildJson = emailByConversationId.getBuildParameters();
                        String approvalJson = null;
                        String approvalResponse = null;
                        JSONArray threadMessages = message.getJSONArray(i);
                        //JSONArray mailSummaryList=new JSONArray();
                        // String mailSummaryList = "";
                        log.debug("Thread from email are {}", threadMessages.toString());
                        for (int j = 0; j < threadMessages.length(); j++) {
                            String messageId = threadMessages.getJSONObject(j).getString("id");
                            String fromUserApproval = threadMessages.getJSONObject(j).getString("from");
                            if (buildUserApproval.contains(fromUserApproval)) {
                                String threadBody = threadMessages.getJSONObject(j).getString("body");
                                if (buildJson != null) {
                                    log.debug("Going to deploy build with parameters " + buildJson.toString());
                                    log.debug("Checking approval email ");
                                    try {
                                        approvalResponse = AIClient.generateAiResponse(threadBody, "", null, null, "Approval", null, null);
                                        approvalJson = AIClient.parseResponse(approvalResponse);
                                        log.debug("Approval status " + approvalJson);
                                        JSONObject approval = new JSONObject(approvalJson);
                                        String approvalMessage = (String) approval.get("approval_status");
                                        if (approvalMessage.equalsIgnoreCase("approved")) {
                                            JSONObject jsonObject = new JSONObject(buildJson);
                                            log.debug("Found Approval Email going to deploy build " + emailByConversationId.getId());
                                            String responseUrl = callJenkinsPipeline(jsonObject, "Backend");
                                            String outputJson = checkBlueOceanBuildStatus(responseUrl);
                                            JSONObject outPutJsonObj = new JSONObject(outputJson);
                                            String outputStatus = (String) outPutJsonObj.get(EmailConstants.STATUS);
                                            log.debug("outputStatus is " + outputStatus);
                                            if (outputStatus.equalsIgnoreCase(EmailConstants.SUCCESS)) {
                                                emailByConversationId.setStatus(EmailConstants.SUCCESS);
                                                replyBuildEmail(EmailConstants.USER_ID, messageId, "Dear Team,\n\n AI has successfully deployed your build on Demo environment\n\n Please check.\n\n Thanks,\nAI Devops(visionwaves) Where automation comes first!!!");

                                            } else {
                                                emailByConversationId.setStatus(EmailConstants.FAILURE);
                                                BufferedInputStream attachmentStream = new BufferedInputStream
                                                        (new ByteArrayInputStream(outputStatus.getBytes(StandardCharsets.UTF_8)));
                                                replyWithAttachment(EmailConstants.USER_ID, messageId, "Dear Team,\n\n AI has successfully attempted to deployed your build on Demo environment\n\nBut the build has not been deployed due to issue comes in pipeline stages. Please check the attached logs for your reference\n\nThanks,\nAI Devops(visionwaves) - Where automation comes first!!!", attachmentStream, "pipeline_error.log");

                                            }
//                                            emailByConversationId.setIsDeployed(true);
                                            emailByConversationId.setBlueOceanUrl(responseUrl);
                                            emailByConversationId.setApprovedBy(fromUserApproval);
                                            emailByConversationId.setDeployedDate(new Date());
                                            iBuildEmailDao.save(emailByConversationId);

                                        } else {
                                            log.debug("Waiting for approval for " + emailByConversationId.getId());
                                        }


                                    } catch (Exception e) {
                                        throw new RuntimeException(e);
                                    }


                                } else {
                                    log.debug("Not parsable build " + buildJson);
                                }
//                        }else{
//                            log.debug("Approval Must be from given list");
//                        }

                            } else {
                                log.debug("Approver Not in list");
                            }
                        }

                    } else {
                        log.debug("Ignore email as deployed " + emailByConversationId.getId());
                    }
                }
            } else if ((subject.toLowerCase().contains("demo") && subject.toLowerCase().contains("microfrontend"))) {
                log.debug("Build for MicroFrontEnd " + subject);
                BuildEmails emailByConversationId = iBuildEmailDao.getEmailByConversationId(conversationId);
                if (emailByConversationId == null) {
                    emailByConversationId = new BuildEmails();
                    emailByConversationId.setConversationId(conversationId);
                    emailByConversationId.setContent(body);
                    String categoryResponse = null;
                    String buildJson = null;
                    try {
                        categoryResponse = AIClient.generateAiResponse(body, "", null, null, "BackendBuild", null, null);
                        buildJson = AIClient.parseResponse(categoryResponse);
                        emailByConversationId.setBuildParameters(buildJson);
                        emailByConversationId.setEmailFrom(fromUser);
                        emailByConversationId.setCreatedTime(new Date());
                        iBuildEmailDao.save(emailByConversationId);

                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }

                } else {
                    if (!true) {
                        log.debug("Build is not deployed for " + emailByConversationId.getId());
                        String buildJson = emailByConversationId.getBuildParameters();
                        String approvalJson = null;
                        String approvalResponse = null;
                        JSONArray threadMessages = message.getJSONArray(i);
                        //JSONArray mailSummaryList=new JSONArray();
                        // String mailSummaryList = "";
                        log.debug("Thread from email are {}", threadMessages.toString());
                        for (int j = 0; j < threadMessages.length(); j++) {
                            String messageId = threadMessages.getJSONObject(j).getString("id");
                            String fromUserApproval = threadMessages.getJSONObject(j).getString("from");
                            if (buildUserApproval.contains(fromUserApproval)) {
                                String threadBody = threadMessages.getJSONObject(j).getString("body");
                                if (buildJson != null) {
                                    log.debug("Going to deploy build with parameters " + buildJson.toString());
                                    log.debug("Checking approval email ");
                                    try {
                                        approvalResponse = AIClient.generateAiResponse(threadBody, "", null, null, "Approval", null, null);
                                        approvalJson = AIClient.parseResponse(approvalResponse);
                                        log.debug("Approval status " + approvalJson);
                                        JSONObject approval = new JSONObject(approvalJson);
                                        String approvalMessage = (String) approval.get("approval_status");
                                        if (approvalMessage.equalsIgnoreCase("approved")) {
                                            JSONObject jsonObject = new JSONObject(buildJson);
                                            log.debug("Found Approval Email going to deploy build " + emailByConversationId.getId());
                                            String responseUrl = callJenkinsPipeline(jsonObject, "UI");
                                            String outputJson = checkBlueOceanBuildStatus(responseUrl);
                                            JSONObject outPutJsonObj = new JSONObject(outputJson);
                                            String outputStatus = (String) outPutJsonObj.get(EmailConstants.STATUS);
                                            log.debug("outputStatus is " + outputStatus);
                                            if (outputStatus.equalsIgnoreCase(EmailConstants.SUCCESS)) {
                                                emailByConversationId.setStatus(EmailConstants.SUCCESS);
                                                replyBuildEmail(EmailConstants.USER_ID, messageId, "Dear Team,\n\n AI has successfully deployed your build on Demo environment\n\n Please check.\n\n Thanks,\nAI Devops(visionwaves) Where automation comes first!!!");

                                            } else {
                                                emailByConversationId.setStatus(EmailConstants.FAILURE);
                                                BufferedInputStream attachmentStream = new BufferedInputStream
                                                        (new ByteArrayInputStream(outputStatus.getBytes(StandardCharsets.UTF_8)));
                                                replyWithAttachment(EmailConstants.USER_ID, messageId, "Dear Team,\n\n AI has successfully attempted to deployed your build on Demo environment\n\nBut the build has not been deployed due to issue comes in pipeline stages. Please check the attached logs for your reference\n\nThanks,\nAI Devops(visionwaves) - Where automation comes first!!!", attachmentStream, "pipeline_error.log");

                                            }
//                                            emailByConversationId.setIsDeployed(true);
                                            emailByConversationId.setBlueOceanUrl(responseUrl);
                                            emailByConversationId.setApprovedBy(fromUserApproval);
                                            emailByConversationId.setDeployedDate(new Date());
                                            iBuildEmailDao.save(emailByConversationId);

                                        } else {
                                            log.debug("Waiting for approval for " + emailByConversationId.getId());
                                        }


                                    } catch (Exception e) {
                                        throw new RuntimeException(e);
                                    }


                                } else {
                                    log.debug("Not parsable build " + buildJson);
                                }
//                        }else{
//                            log.debug("Approval Must be from given list");
//                        }

                            } else {
                                log.debug("Approver user are not authorized");
                            }
                        }

                    } else {
                        log.debug("Ignore email as deployed " + emailByConversationId.getId());
                    }
                }


            } else if ((subject.toLowerCase().contains("demo") && subject.toLowerCase().contains("database"))) {
                log.debug("Build for MicroFrontEnd " + subject);

            }
        }


    }

    /**
     * Reply build email .
     *
     * @param userid    the userid
     * @param messageId the message id
     * @param content   the content
     * @return the string
     * @throws Exception the exception
     */
    public String replyBuildEmail(String userid, String messageId, String content) throws Exception {
        log.debug("Inside @method reply. @param: userid -> {}, messageId -> {}, content -> {}", userid, messageId, content);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/replyAll", userid, messageId);

        JSONObject replyObject = createReplyObject(content);

        String valueAsString = replyObject.toString();

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader("Content-Type", "application/json");

            StringEntity entity = new StringEntity(valueAsString);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 202) {
                    return EmailConstants.SUCCESS;
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                log.error("Error inside @method reply. Exception message : {}", errorMessage);
                return "failed";
            }
        }
    }

    private JSONObject createReplyObject(String content) {

        JSONObject body = new JSONObject();
        body.put("contentType", "text");
        body.put("content", content);

        JSONObject message = new JSONObject();
        message.put("body", body);

        JSONObject mainObject = new JSONObject();
        mainObject.put(EmailConstants.MESSAGE, message);

        return mainObject;
    }

    private String callJenkinsPipeline(JSONObject buildJson, String type) {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost postRequest = null;
            if (type.equalsIgnoreCase("Backend"))
                postRequest = new HttpPost(BACKEND_JENKINS_URL);
            else {
                postRequest = new HttpPost(UI_JENKINS_URL);
            }
            // Set the necessary headers
            postRequest.setHeader("Jenkins-Crumb", JENKINS_CRUMB);
            postRequest.setHeader("Content-type", "application/x-www-form-urlencoded");

            // Basic auth header
            String auth = USER + ":" + API_TOKEN;
            byte[] encodedAuth = org.apache.commons.codec.binary.Base64.encodeBase64(auth.getBytes());
            String authHeader = EmailConstants.BASIC + new String(encodedAuth);
            postRequest.setHeader(EmailConstants.AUTHORIZATION, authHeader);

            // Prepare the data
            StringBuilder data = new StringBuilder();
            for (String key : buildJson.keySet()) {
                try {
                    data.append(URLEncoder.encode(key, EmailConstants.UTF_8)).append("=")
                            .append(URLEncoder.encode(buildJson.getString(key), EmailConstants.UTF_8)).append("&");
                } catch (Exception e) {
                    e.printStackTrace();
                    return "Error encoding buildJson";
                }
            }
            try {
                data.append(URLEncoder.encode(buildJson.toString(), EmailConstants.UTF_8));
            } catch (Exception e) {
                e.printStackTrace();
                return "Error encoding buildJson";
            }

            postRequest.setEntity(new StringEntity(data.toString()));

            try (CloseableHttpResponse response = client.execute(postRequest)) {
                System.out.println("Status Code :  " + response.getStatusLine().getStatusCode());

                Header[] responseHeaders = response.getAllHeaders();

                String locationHeaderValue = null;
                for (Header header : responseHeaders) {
                    if (header.getName().equalsIgnoreCase("Location")) {
                        locationHeaderValue = header.getValue();
                        break;
                    }
                }

                if (locationHeaderValue != null) {
                    System.out.println("Queue URL: " + locationHeaderValue);

                    while (true) {
                        HttpGet buildStatusRequest = new HttpGet(URI.create(locationHeaderValue + "api/json"));
                        buildStatusRequest.setHeader(EmailConstants.AUTHORIZATION, authHeader);

                        try (CloseableHttpResponse buildStatusResponse = client.execute(buildStatusRequest)) {
                            String responseBody = EntityUtils.toString(buildStatusResponse.getEntity());

                            JSONObject json = new JSONObject(responseBody);
                            JSONObject executable = json.optJSONObject("executable");

                            if (executable != null) {
                                String buildUrl = executable.getString("url");
                                System.out.println("Build URL: " + buildUrl);

                                String[] buildUrlParts = buildUrl.split("/");
                                String buildId = buildUrlParts[buildUrlParts.length - 1];
                                String jobName = buildUrlParts[buildUrlParts.length - 2];

                                String blueOceanUrl = String.format("https://dev.visionwaves.com/jenkins/blue/rest/organizations/jenkins/pipelines/%s/runs/%s/", jobName, buildId);
                                return blueOceanUrl; // Return the Blue Ocean URL
                            } else {
                                System.out.println("Waiting for the build..!!");
                                Thread.sleep(1000);
                            }
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                            return EmailConstants.ERROR_WAITING_FOR_BUILD;
                        }
                    }
                } else {
                    System.out.println("Location header not found in the response.");
                    return "Location header not found in the response";
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            return "Error executing Jenkins pipeline";
        }
    }


    /**
     * Check blue ocean build status .
     *
     * @param blueOceanUrl the blue ocean url
     * @return the string
     */
    public String checkBlueOceanBuildStatus(String blueOceanUrl) {
        String JENKINS_USER = EmailConstants.ADMIN;
        String JENKINS_TOKEN = EmailConstants.API_TOKEN;

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            String auth = JENKINS_USER + ":" + JENKINS_TOKEN;
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
            String authHeader = EmailConstants.BASIC + new String(encodedAuth);

            while (true) {
                HttpGet buildStatusRequest = new HttpGet(blueOceanUrl);
                buildStatusRequest.setHeader(EmailConstants.AUTHORIZATION, authHeader);

                try (CloseableHttpResponse buildStatusResponse = client.execute(buildStatusRequest)) {
                    String responseBody = EntityUtils.toString(buildStatusResponse.getEntity());
                    JSONObject json = new JSONObject(responseBody);
                    String state = json.optString("state");
                    String result = json.optString("result");

                    if ("FINISHED".equalsIgnoreCase(state)) {
                        if (EmailConstants.FAILURE.equalsIgnoreCase(result)) {
                            String logUrl = json.getJSONObject("_links").getJSONObject("log").getString("href");
                            HttpGet logRequest = new HttpGet("https://dev.visionwaves.com/jenkins" + logUrl);
                            logRequest.setHeader(EmailConstants.AUTHORIZATION, authHeader);

                            try (CloseableHttpResponse logResponse = client.execute(logRequest)) {
                                String logOutput = EntityUtils.toString(logResponse.getEntity());
                                JSONObject outputJson = new JSONObject();
                                outputJson.put(EmailConstants.STATUS, logOutput);
                                return outputJson.toString();
                            }
                        } else {
                            JSONObject outputJson = new JSONObject();
                            outputJson.put(EmailConstants.STATUS, EmailConstants.SUCCESS);
                            return outputJson.toString();
                        }
                    } else {
                        System.out.println("Waiting for the build to complete...");
                        Thread.sleep(5000); // Poll every 5 seconds
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    return EmailConstants.ERROR_WAITING_FOR_BUILD;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            return "Error checking build status";
        }
    }


    /**
     * Check build status .
     *
     * @param buildUrl the build url
     * @return the string
     */
    public String checkBuildStatus(String buildUrl) {
        String JENKINS_USER = EmailConstants.ADMIN;
        String JENKINS_TOKEN = EmailConstants.API_TOKEN;

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            String auth = JENKINS_USER + ":" + JENKINS_TOKEN;
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
            String authHeader = EmailConstants.BASIC + new String(encodedAuth);

            while (true) {
                HttpGet buildStatusRequest = new HttpGet(buildUrl + "api/json");
                buildStatusRequest.setHeader(EmailConstants.AUTHORIZATION, authHeader);

                try (CloseableHttpResponse buildStatusResponse = client.execute(buildStatusRequest)) {
                    String responseBody = EntityUtils.toString(buildStatusResponse.getEntity());
                    JSONObject json = new JSONObject(responseBody);
                    String result = json.optString("result");

                    if (!result.isEmpty()) {
                        System.out.println("Build Result: " + result);
                        return result; // Return the final build result
                    } else {
                        System.out.println("Waiting for the build to complete...");
                        Thread.sleep(5000); // Poll every 5 seconds
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    return EmailConstants.ERROR_WAITING_FOR_BUILD;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            return "Error checking build status";
        }
    }

    /**
     * Reply with attachment .
     *
     * @param userId             the user id
     * @param messageId          the message id
     * @param content            the content
     * @param attachment         the attachment
     * @param attachmentFileName the attachment file name
     * @return the string
     * @throws Exception the exception
     */
    public String replyWithAttachment(String userId, String messageId, String content, BufferedInputStream attachment, String attachmentFileName) throws Exception {
        log.debug("Inside @method replyWithAttachment. @param: userId -> {}, messageId -> {}, content -> {}, attachmentFileName -> {}", userId, messageId, content, attachmentFileName);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/replyAll", userId, messageId);
        log.debug("URL to reply with attachment: {}", url);

        JSONObject replyObject = createReplyObjectWithAttachment(content, attachment, attachmentFileName);

        String valueAsString = replyObject.toString();

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader("Content-Type", "application/json");

            StringEntity entity = new StringEntity(valueAsString);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 202) {
                    return EmailConstants.SUCCESS;
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                log.error("Error inside @method replyWithAttachment. Exception message: {}", errorMessage);
                return "failed";
            }
        }
    }

    private JSONObject createReplyObjectWithAttachment(String content, BufferedInputStream attachment, String attachmentFileName) throws IOException {
        JSONObject messageBody = new JSONObject();
        messageBody.put("contentType", "Text");
        messageBody.put("content", content);

        JSONObject message = new JSONObject();
        message.put(EmailConstants.MESSAGE, new JSONObject().put("body", messageBody));

        // Encode the attachment file content in base64
        byte[] attachmentBytes = attachment.readAllBytes();
        String encodedAttachment = Base64.getEncoder().encodeToString(attachmentBytes);

        JSONArray attachments = new JSONArray();
        JSONObject attachmentObject = new JSONObject();
        attachmentObject.put("@odata.type", "#microsoft.graph.fileAttachment");
        attachmentObject.put("name", attachmentFileName);
        attachmentObject.put("contentBytes", encodedAttachment);
        attachments.put(attachmentObject);

        message.getJSONObject(EmailConstants.MESSAGE).put("attachments", attachments);

        return message;
    }


    /**
     * Deploy build.
     */
    public void deployBuild() {
        try {
            pollBuildEmail("<EMAIL>", EmailConstants.USER_ID, getDateTimeFilter());
        } catch (IOException | InterruptedException | ExecutionException e) {
            log.error("Error inside @method deployBuild", e);
            throw new BusinessException(e.getMessage());
        }
    }

    private String getDateTimeFilter() {
        LocalDateTime latestTime = LocalDateTime.now().minusDays(3);
        ZonedDateTime zonedDateTime = latestTime.atZone(ZoneId.systemDefault());
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        String dateString = utcDateTime.format(formatter);
        return "receivedDateTime ge " + dateString;
    }

}
