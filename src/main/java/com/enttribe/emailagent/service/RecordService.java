package com.enttribe.emailagent.service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.enttribe.emailagent.ai.polling.OutlookPollingAI;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.entity.*;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IEmailThreadDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.impl.FailureLogsServiceDaoImpl;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.EmailConstants;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.security.PublicKey;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPublicKey;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static com.enttribe.emailagent.utils.ExceptionUtils.getStackTraceAsString;

/**
 * The type Record service.
 *  <AUTHOR> Sonsale
 */
@Service
@Slf4j
public class RecordService {

    @Autowired
    private FailureLogsServiceDaoImpl daoImpl;

    @Autowired
    private IMailSummaryDao mailSummaryDao;

    @Autowired
    private UserContextHolder userContextHolder;

    @Autowired
    private IEmailThreadDao emailThreadDao;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private EmailUserDao emailUserDao;

    @Autowired
    private EwsService ewsService;

    @Autowired
    private GraphIntegrationService graphIntegrationService;

    @Value("${pollingMode}")
    private String pollingMode;

    @Value("${auth.secret.publicKey}")
    private String secretKey;

    @Value("${insightQuestionUrl}")
    private String insightQuestionUrl;

    @Value("${getChartJsonUrl}")
    private String getChartJsonUrl;

    private static final Logger auditLog = EmailAgentLogger.getLogger(OutlookPollingAI.class);

    /**
     * Validate user by email and phone map.
     *
     * @param email the email
     * @param phone the phone
     * @return the map
     */
    public Map<String, Object> validateUserByEmailAndPhone(String email, String phone) {
        EmailUser user = emailUserDao.findByEmail(email);
        if (user == null) return Map.of(EmailConstants.RESULT, "User does not exist.");
        String phoneNumber = user.getPhoneNumber();
        boolean validated = phoneNumber != null && phoneNumber.equals(phone);
        if (validated) {
            return Map.of(EmailConstants.RESULT, true);
        } else {
            return Map.of(EmailConstants.RESULT, "Wrong phone number.");
        }
    }

    /**
     * Retain latest records.
     */
    @Transactional
    public void retainLatestRecords() {
        // Step 1: Count users
        long userCount = emailUserDao.getActiveUsersCount();
        log.debug("Total active users count : {}", userCount);

        userCount = (userCount * 3) + 800;

        // Step 2: Fetch the IDs of the records to retain
        List<Integer> idsToRetain = entityManager.createQuery(
                        "SELECT p.id FROM PollTimeInfo p ORDER BY p.id DESC", Integer.class)
                .setMaxResults((int) userCount) // Retain only the latest n records
                .getResultList();

        log.debug("idsToRetain : {}", idsToRetain);

        // If there are more records than user count, delete the older ones
        if (!idsToRetain.isEmpty()) {
            // Step 3: Delete all records NOT in the retained list
            Query deleteQuery = entityManager.createQuery(
                    "DELETE FROM PollTimeInfo p WHERE p.id NOT IN :ids");
            deleteQuery.setParameter("ids", idsToRetain);
            int deletedCount = deleteQuery.executeUpdate();
            log.debug("Total {} records deleted.", deletedCount);
        }
    }

    /**
     * Gets emails by tag.
     *
     * @param tags   the tags
     * @param userId the user id
     * @return the emails by tag
     */
    public List<MailSummary> getEmailsByTag(List<String> tags, String userId) {
        log.debug("Inside @method getEmailsByTag. @param: tags -> {}, userId -> {}", tags, userId);
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<MailSummary> cq = cb.createQuery(MailSummary.class);
        Root<MailSummary> root = cq.from(MailSummary.class);

        if (tags == null || tags.isEmpty()) {
            return new ArrayList<>();
        }

        List<Predicate> tagPredicates = new ArrayList<>();

        for (String tag : tags) {
            tagPredicates.add(cb.like(root.get("tag"), "%" + tag + "%"));
        }

        Predicate tagPredicate = cb.or(tagPredicates.toArray(new Predicate[0]));

        Predicate userIdPredicate = cb.equal(root.get("userId"), userId);

        cq.where(cb.and(userIdPredicate, tagPredicate));
        return entityManager.createQuery(cq).getResultList();
    }

    /**
     * Gets emails by tag v1.
     *
     * @param tags  the tags
     * @param email the email
     * @return the emails by tag v 1
     * @throws Exception the exception
     */
    public List<MailSummary> getEmailsByTagV1(List<String> tags, String email) throws Exception {
        String tag = "";
        if (tags != null && !tags.isEmpty()) tag = tags.getFirst();
        List<String> messageIdsByTag;
        if (pollingMode.equalsIgnoreCase("EWS")) {
            messageIdsByTag = ewsService.getMessageIdsByTag(email, tag);
        } else {
            messageIdsByTag = graphIntegrationService.searchEmailsByTag(email, tag);
        }
        return mailSummaryDao.findByMessageIds(email, messageIdsByTag);
    }

    /**
     * Introspect ews token boolean.
     *
     * @param token the token
     * @return the boolean
     */
    public boolean introspectEwsToken(String token) {
//        log.debug("Inside @method introspectEwsToken . @param : token -> {}", token);
        return verifyToken(secretKey, token);
    }

    private boolean verifyToken(String secretKey, String token) {
        try {
            PublicKey publicKey = getPublicKeyFromCertificateString(secretKey);
            Algorithm algorithm = Algorithm.RSA256((RSAPublicKey) publicKey, null);
            JWTVerifier verifier = JWT.require(algorithm)
                    .acceptLeeway(60)
                    .build();
            DecodedJWT jwt = verifier.verify(token);
            return true;
        } catch (Exception exception) {
            log.error("Token validation failed", exception);
            return false;
        }
    }

    private PublicKey getPublicKeyFromCertificateString(String certificateString) throws Exception {
        byte[] decoded = Base64.getDecoder().decode(certificateString);
        CertificateFactory factory = CertificateFactory.getInstance("X.509");
        X509Certificate certificate = (X509Certificate) factory.generateCertificate(new ByteArrayInputStream(decoded));
        return certificate.getPublicKey();
    }

    /**
     * Gets questions by intent name.
     *
     * @param intentName the intent name
     * @param token      the token
     * @return the questions by intent name
     */
    public String getQuestionsByIntentName(String intentName, String token) {
        log.debug("Inside @method getQuestionsByIntentName. @param: intentName -> {}", intentName);

        String url = String.format(insightQuestionUrl + "/knowledgecache/getQuestionsByIntentName?intentName=%s", intentName);
        log.debug("URL to getQuestionsByIntentName : {}", url);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader("Authorization", token);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();

                if (entity != null && statusCode == 200) {
                    log.debug("success");
                    String result = EntityUtils.toString(entity);
//                    JSONObject jsonObject = new JSONObject(result);
                    return result;
                } else {
                    String errorMessage = EntityUtils.toString(response.getEntity());
                    Map<String, String> auditMap = Map.of("intentName", intentName);
                    auditLog.error("Exception inside @method getQuestionsByIntentName", errorMessage, errorMessage, null, null, "INSIGHT_QUESTIONS", userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    log.error("Error inside @method getQuestionsByIntentName. Exception message -> {}", errorMessage);
                    return null;
                }

            }
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("intentName", intentName);
            auditLog.error("Exception inside @method getQuestionsByIntentName", e.getMessage(), getStackTraceAsString(e), null, null, "INSIGHT_QUESTIONS", userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            //log.error("Exception inside @method getQuestionsByIntentName: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Gets chart json.
     *
     * @param question the question
     * @param token    the token
     * @return the chart json
     */
    public Object getChartJson(String question, String token) {
        log.debug("Inside @method getChartJson. @param: question -> {}", question);

        String url = String.format(getChartJsonUrl + "/knowledgecache/getChartJson?question=%s", question);
        url = url.replace(" ", "%20");
        log.debug("URL to getChartJson : {}", url);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader("Authorization", token);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();

                if (entity != null && statusCode == 200) {
                    log.debug("success ");
                    String result = EntityUtils.toString(entity);
                    return result;
                } else {
                    String errorMessage = EntityUtils.toString(response.getEntity());
                    Map<String, String> auditMap = Map.of("question", question);
                    auditLog.error("Exception inside @method getChartJson", errorMessage, errorMessage, null, null, "INSIGHT_QUESTIONS", userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
                    log.error("Error inside @method getChartJson. Exception message -> {}", errorMessage);
                    return null;
                }
            }
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("question", question);
            auditLog.error("Exception inside @method getChartJson", e.getMessage(), getStackTraceAsString(e), null, null, "INSIGHT_QUESTIONS", userContextHolder.getCurrentUser().getEmail(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            log.error("Exception inside @method getChartJson: {}", e.getMessage());
            return null;
        }
    }

    /**
     * User actions by filter list.
     *
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @param filterMap the filter map
     * @return the list
     */
    public List<UserActions> userActionsByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap) {
        return daoImpl.getFilteredUserActions(filterMap, llimit, ulimit);
    }

    /**
     * User actions count by filter long.
     *
     * @param filterMap the filter map
     * @return the long
     */
    public Long userActionsCountByFilter(Map<String, Object> filterMap) {
        return daoImpl.getUserActionsCountByFilter(filterMap);
    }

    /**
     * Filter emails list.
     *
     * @param filters    the filters
     * @param userId     the user id
     * @param lowerLimit the lower limit
     * @param upperLimit the upper limit
     * @return the list
     */
    public List<MailSummary> filterEmails(Map<String, Object> filters, String userId, Integer lowerLimit, Integer upperLimit) {
        log.debug("Inside @method filterEmails. @param: filters -> {}, userId -> {}, lowerLimit -> {}, upperLimit -> {}",
                filters, userId, lowerLimit, upperLimit);

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<MailSummary> cq = cb.createQuery(MailSummary.class);
        Root<MailSummary> root = cq.from(MailSummary.class);

        List<Predicate> predicates = new ArrayList<>();

        predicates.add(cb.equal(root.get("userId"), userId));

        if (filters != null && !filters.isEmpty()) {
            String search = (String) filters.get("search");
            String type = (String) filters.get("type");

            if (search != null && !search.isEmpty()) {
                if ("to".equalsIgnoreCase(type)) {
                    predicates.add(cb.like(root.get("toUser"), "%" + search + "%"));
                } else if ("from".equalsIgnoreCase(type)) {
                    predicates.add(cb.like(root.get("fromUser"), "%" + search + "%"));
                } else if ("subject".equalsIgnoreCase(type)) {
                    predicates.add(cb.like(root.get("subject"), "%" + search + "%"));
                } else if ("tag".equalsIgnoreCase(type)) {
                    predicates.add(cb.like(root.get("tag"), "%" + search + "%"));
                } else if ("starred".equalsIgnoreCase(type)) {
                    String starredValue = (String) filters.get("starred");
                    boolean isStarred = (starredValue != null) ? Boolean.parseBoolean(starredValue) : true;
                    predicates.add(cb.equal(root.get("starMarked"), isStarred));
                } else if ("attachment".equalsIgnoreCase(type)) {
                    String attachmentValue = (String) filters.get("attachment");
                    boolean includeAttachments = (attachmentValue != null) ? Boolean.parseBoolean(attachmentValue) : true;
                    Subquery<String> attachmentSubquery = cq.subquery(String.class);
                    Root<UserMailAttachment> attachmentRoot = attachmentSubquery.from(UserMailAttachment.class);
                    attachmentSubquery.select(attachmentRoot.get("messageId"))
                            .where(cb.equal(attachmentRoot.get("userId"), userId));

                    if (includeAttachments) {
                        predicates.add(root.get("messageId").in(attachmentSubquery));
                    } else {
                        predicates.add(cb.not(root.get("messageId").in(attachmentSubquery)));
                    }
                } else {
                    // Generic search across fields if type is not specified
                    Predicate toPredicate = cb.like(root.get("toUser"), "%" + search + "%");
                    Predicate fromPredicate = cb.like(root.get("fromUser"), "%" + search + "%");
                    Predicate subjectPredicate = cb.like(root.get("subject"), "%" + search + "%");
                    Predicate tagPredicate = cb.like(root.get("tag"), "%" + search + "%");

                    predicates.add(cb.or(toPredicate, fromPredicate, subjectPredicate, tagPredicate));
                }
            }
        }

        cq.where(predicates.toArray(new Predicate[0]));

        cq.orderBy(cb.desc(root.get("mailReceivedTime")));

        TypedQuery<MailSummary> query = entityManager.createQuery(cq);

        if (lowerLimit != null && upperLimit != null) {
            int maxResults = upperLimit - lowerLimit + 1;
            query.setFirstResult(lowerLimit);
            query.setMaxResults(maxResults);
        }

        return query.getResultList();
    }

}
