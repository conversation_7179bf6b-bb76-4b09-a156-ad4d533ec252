package com.enttribe.emailagent.service;

import com.enttribe.emailagent.entity.PollTimeInfo;
import com.enttribe.emailagent.repository.PollTimeInfoRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * The type Poll time info service.
 *  <AUTHOR> Sonsale
 */
@Service
@Slf4j
public class PollTimeInfoService {

    @Autowired
    private PollTimeInfoRepository repository;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Gets second last poll time info.
     *
     * @param email the email
     * @return the second last poll time info
     */
// Clean method to get second last record by email
    public Optional<PollTimeInfo> getSecondLastPollTimeInfo(String email) {
        String jpql = "SELECT p FROM PollTimeInfo p WHERE p.email = :email ORDER BY p.startTime DESC";
        TypedQuery<PollTimeInfo> query = entityManager.createQuery(jpql, PollTimeInfo.class);
        query.setParameter("email", email);
        query.setFirstResult(1); // Skip the first result (i.e., the latest record)
        query.setMaxResults(1);  // Fetch only one record (i.e., the second last)

        List<PollTimeInfo> results = query.getResultList();
        return results.stream().findFirst(); // Return Optional to handle the absence of records
    }

    /**
     * Sets start time.
     *
     * @param email         the email
     * @param localDateTime the local date time
     * @param status        the status
     * @return the start time
     */
    public boolean setStartTime(String email, LocalDateTime localDateTime, PollTimeInfo.Status status) {
        log.debug("Inside @method setStartTime. @param :email -> {} localDateTime -> {} status -> {}",
                                                         email, localDateTime, status);
        PollTimeInfo pollTimeInfo = new PollTimeInfo();
        pollTimeInfo.setStartTime(localDateTime);
        pollTimeInfo.setStatus(status);
        pollTimeInfo.setEmail(email);
        repository.save(pollTimeInfo);
        return true;
    }

    /**
     * Sets end time.
     *
     * @param email         the email
     * @param localDateTime the local date time
     * @param status        the status
     * @return the end time
     */
    public boolean setEndTime(String email, LocalDateTime localDateTime, PollTimeInfo.Status status) {
        log.debug("Inside @method setEndTime. @param :email -> {} localDateTime -> {} status -> {}",
                                                         email, localDateTime, status);
        PollTimeInfo pollTimeInfo = new PollTimeInfo();
        pollTimeInfo.setEndTime(localDateTime);
        pollTimeInfo.setStatus(status);
        pollTimeInfo.setEmail(email);
        repository.save(pollTimeInfo);
        return true;
    }

    /**
     * Fetch time to poll local date time.
     *
     * @param email the email
     * @return the local date time
     */
    public LocalDateTime fetchTimeToPoll(String email) {
        log.debug("Inside @method fetchTimeToPoll. @param :email -> {}", email);
        Optional<PollTimeInfo> endTimeInfo = repository.findFirstByEmailAndStatusOrderByEndTimeDesc(email,PollTimeInfo.Status.SUCCESS);

        if (endTimeInfo.isEmpty()) {
            PollTimeInfo pollTimeInfo = new PollTimeInfo();
            pollTimeInfo.setStartTime(LocalDateTime.now());
            pollTimeInfo.setEndTime(LocalDateTime.now());
            pollTimeInfo.setStatus(PollTimeInfo.Status.PROCESSING);
            pollTimeInfo.setEmail(email);
            PollTimeInfo saved = repository.save(pollTimeInfo);
            return saved.getStartTime().withHour(0).withMinute(0);
        }

        PollTimeInfo.Status status = endTimeInfo.get().getStatus();
        if (status != null && status.equals(PollTimeInfo.Status.SUCCESS)) {
            Optional<PollTimeInfo> startTimeInfo = repository.findFirstByEmailOrderByStartTimeDesc(email);
            return startTimeInfo.isEmpty() ? null : startTimeInfo.get().getStartTime();
        }
        return null;
    }


}
