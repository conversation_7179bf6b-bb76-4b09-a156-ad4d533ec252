package com.enttribe.emailagent.service;

import com.enttribe.emailagent.dao.MeetingSummaryDao;
import com.enttribe.emailagent.dto.UnReadEmail;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.MeetingSummary;
import com.enttribe.emailagent.entity.UserFolders;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;

import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * The type Email service.
 *  <AUTHOR> Pathak
 */
@Service
@Slf4j
public class EmailService {

    @Autowired
    private MeetingSummaryDao meetingSummaryDao;

    @Autowired
    private PollTimeInfoService pollTimeInfoService;

    @Autowired
    private EwsService ewsService;

    @Autowired
    private GraphIntegrationService graphIntegrationService;

    @Autowired
    private IMailSummaryDao mailSummaryDao;

    @Autowired
    private UserContextHolder userContextHolder;

    @Autowired
    private GmailIntegration gmailIntegration;

    @Autowired
    private UserFoldersRepository userFoldersRepository;


    @Value("${pollingMode}")
    private String pollingMode;


    /**
     * Gets received date time filter.
     *
     * @param email the email
     * @return the received date time filter
     */
    public String getReceivedDateTimeFilter(String email) {
        LocalDateTime timeToPoll = pollTimeInfoService.fetchTimeToPoll(email);
        if (timeToPoll == null) {
            timeToPoll = LocalDateTime.now().withHour(0).withMinute(0);
        }
        ZonedDateTime zonedDateTime = timeToPoll.atZone(ZoneId.systemDefault());
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        String dateString = utcDateTime.format(formatter);
        if (pollingMode.equalsIgnoreCase("EWS") || pollingMode.equalsIgnoreCase("Gmail")) {
            return dateString;
        } else {
            return "receivedDateTime ge " + dateString;
        }
    }

    /**
     * Update priority.
     *
     * @param messageId the message id
     * @param priority  the priority
     * @return the string
     */
    public String updatePriority(String messageId, String priority) {
        UserInfo currentUser = userContextHolder.getCurrentUser();

        MailSummary summary = mailSummaryDao.findByInternetMessageId(messageId, currentUser.getId());
        if(summary == null){
            summary = mailSummaryDao.findByMessageId(messageId,currentUser.getId());
        }
        if (summary == null) throw new ResourceNotFoundException("Summary is not found for the given messageId");

        summary.setPriority(priority);
        summary.setPriorityReason("Priority was updated manually");
        mailSummaryDao.save(summary);
        return "success";
    }

    /**
     * Gets meeting summary by internet message id.
     *
     * @param internetMessageId the internet message id
     * @return the meeting summary by internet message id
     */
    public MeetingSummary getMeetingSummaryByInternetMessageId(String internetMessageId) {
        return meetingSummaryDao.findMeetingSummaryByInternetMessageId(internetMessageId);
    }


    /**
     * Get unread mail list.
     *
     * @param userMail       the user mail
     * @param userFolderName the user folder name
     * @return the list
     */
    public List<UnReadEmail> getUnreadMail(String userMail,String userFolderName){
        List<UnReadEmail> unreadEmails = new ArrayList<>();
        List<UserFolders> userFoldersList = new ArrayList<>();
                if(userFolderName == null || userFolderName.trim().isEmpty()){
                    userFoldersList =  userFoldersRepository.getActiveMailFoldersByEmail(userMail);
                }
                else {
                    userFoldersList.add(userFoldersRepository.findByDisplayNameAndEmail(userFolderName, userMail));
                }
                for(UserFolders userFolder : userFoldersList) {
                    if (!userFolder.getDisplayName().equalsIgnoreCase("Sent Items") && !userFolder.getDisplayName().equalsIgnoreCase("SENT")
                            && !userFolder.getDisplayName().equalsIgnoreCase("Deleted Items")) {
                        if (pollingMode.equalsIgnoreCase("EWS")) {
                            unreadEmails.addAll(ewsService.getUnreadEmails(userMail, userFolder.getFolderId()));
                        } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                            unreadEmails.addAll(gmailIntegration.getUnreadEmails(userMail, userFolder.getFolderId()));
                        } else {
                            unreadEmails.addAll(graphIntegrationService.getUnreadEmails(userMail, userFolder.getFolderId()));
                        }
                    }
                }
        return unreadEmails;
    }


    /**
     * Gets flag mail.
     *
     * @param userMail       the user mail
     * @param userFolderName the user folder name
     * @return the flag mail
     */
    public List<String> getFlagMail(String userMail,String userFolderName) {
        List<String> flaggedEmails = new ArrayList<>();
        List<UserFolders> userFoldersList = new ArrayList<>();
        if(userFolderName == null || userFolderName.trim().isEmpty()){
            userFoldersList =  userFoldersRepository.getActiveMailFoldersByEmail(userMail);
        }
        else {
            userFoldersList.add(userFoldersRepository.findByDisplayNameAndEmail(userFolderName, userMail));
        }
        for(UserFolders userFolder : userFoldersList) {
            if (!userFolder.getDisplayName().equalsIgnoreCase("Sent Items")
                    && !userFolder.getDisplayName().equalsIgnoreCase("SENT")
                    && !userFolder.getDisplayName().equalsIgnoreCase("Deleted Items")) {
                if (pollingMode.equalsIgnoreCase("EWS")) {
                    flaggedEmails.addAll(ewsService.getFlaggedEmails(userMail, userFolder.getFolderId()));
                } else if (pollingMode.equalsIgnoreCase("GMAIL")) {
                    return Collections.emptyList();
                } else {
                    flaggedEmails.addAll(graphIntegrationService.getFlaggedEmails(userMail, userFolder.getFolderId()));
                }
            }
        }
        return flaggedEmails;
    }


}
