package com.enttribe.emailagent.service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.enttribe.emailagent.dto.*;
import com.enttribe.emailagent.repository.UserActionsDao;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.enttribe.emailagent.dao.UserMailAttachmentRepository;
import com.enttribe.emailagent.entity.ContactBook;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailThread;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.UserMailAttachment;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.repository.ContactBookRepository;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IEmailThreadDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.impl.BatchRepositoryImpl;
import com.enttribe.emailagent.repository.impl.MailSummaryDaoImpl;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.DateUtils;
import com.enttribe.emailagent.utils.EmailConstants;

import lombok.extern.slf4j.Slf4j;

/**
 * The type Mail summary service.
 *  <AUTHOR> Dangi
 */
@Service
@Slf4j
public class MailSummaryServiceImpl implements ImailSummaryService {

    @Autowired
    private IMailSummaryDao mailSummaryDao;

    @Autowired
    private UserActionsDao userActionsDao;

    @Autowired
    private MailSummaryDaoImpl mailSummaryDaoImpl;

    @Autowired
    private IEmailThreadDao emailThreadDao;

    @Autowired
    private IEmailPreferencesDao preferencesDao;

    @Autowired
    private UserContextHolder userContextHolder;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private UserMailAttachmentRepository userMailAttachmentRepository;

    @Autowired
    private ContactBookRepository contactBookRepository;

    @Autowired
    private EmailUserDao emailUserDao;

    @Autowired
    private BatchRepositoryImpl batchRepo;

    @Autowired
    private EmailService emailService;


    @Override
    public List<MailSummary> getAllMailSummary(Integer llimit, Integer ulimit,String folderName) {
        Map<String, Object> map=new HashMap<>();
        String userMail = userContextHolder.getCurrentUser().getId();
//        String userMail = "<EMAIL>";
        if (folderName != null && !folderName.trim().isEmpty()) {
            map.put("folderName", folderName);
        }
        List<MailSummary> mailSummaryList =  mailSummaryDaoImpl.filteredMailSummary(map,llimit,ulimit);
        addAllAttachmentDetails(mailSummaryList);
        List<UnReadEmail> unreadEmails = emailService.getUnreadMail(userMail,folderName);
        List<String> flaggedEmails = emailService.getFlagMail(userMail,folderName);
        log.info("flaggedEmails is {}",flaggedEmails);
        if(mailSummaryList != null && !mailSummaryList.isEmpty()) {
            mailSummaryList.stream()
                    .forEach(mailSummary -> {

                        boolean isUnread = unreadEmails.stream()
                                .anyMatch(unreadEmail -> {
                                    boolean matches = mailSummary.getInternetMessageId().equals(unreadEmail.getInternetMessageId());
                                    if (matches) {
                                        log.debug("Match found for internetMessageId: {}", unreadEmail.getInternetMessageId());
                                    }
                                    return matches;
                                });

                        boolean isFlagged = flaggedEmails.stream()
                                .anyMatch(flaggedEmail -> mailSummary.getInternetMessageId().equals(flaggedEmail));
                        if (isFlagged) {
                            mailSummary.setFlagStatus("Flagged");
                            log.debug("Marked email as flagged. internetMessageId: {}", mailSummary.getInternetMessageId());
                        }else{
                            log.debug("Marked email as not flagged. internetMessageId: {}", mailSummary.getInternetMessageId());

                            mailSummary.setFlagStatus("notFlagged");
                        }

                        if (isUnread) {
                            mailSummary.setIsUnread(true);
                            log.debug("Marked email as unread. internetMessageId: {}", mailSummary.getInternetMessageId());
                        } else {
                            log.debug("No match found for mail summary with internetMessageId: {}", mailSummary.getInternetMessageId());
                        }
                    });
        }
        return mailSummaryList;
    }

    @Override
    public List<DaySummary> getDaySummary(String userId, String emailId, String dateString) {
        Date date = getDate(dateString);

        List<MailSummary> mailSummaryList = mailSummaryDao.getMailByUserId(date, userId, EmailConstants.EMAIL);

//        List<MailSummary> mailSummaryList = mailSummaryDao.findMailSummariesByUserIdAndDateRange(emailId, startDate, endDate);;
        return getDaySummaryList(mailSummaryList, emailId, userId);

    }

    @Override
    public List<DaySummary> getDaySummaryBetweenDates(String userId, String emailId, String startDate1, String endDate1) {

        Date startDate = getDate(startDate1);
        Date endDate = getDate(endDate1);
        // Check the difference between startDate and endDate in days
        long diffInMillis = Math.abs(endDate.getTime() - startDate.getTime());
        long diffInDays = TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);

        // If the difference is greater than 7 days, throw an exception
        if (diffInDays > 7) {
            throw new IllegalArgumentException("The date range cannot exceed 7 days.");
        }

        List<MailSummary> mailSummaryList = mailSummaryDao.getMailByUserIdBetweenDate(startDate, endDate, userId, EmailConstants.EMAIL);
        return getDaySummaryList(mailSummaryList, emailId, userId);

    }

    private List<DaySummary> getDaySummaryList(List<MailSummary> mailSummaryList, String emailId, String userId) {
        List<DaySummary> daySummaries = new ArrayList<DaySummary>();
        addAttachmentDetails(mailSummaryList);
        DaySummary prioritySummary = priorityWiseSummary(mailSummaryList);
        if (prioritySummary != null)
            daySummaries.add(prioritySummary);
        DaySummary actionOwnerSummary = actionOwnerSummary(mailSummaryList, emailId);
        if (actionOwnerSummary != null)
            daySummaries.add(actionOwnerSummary);
        DaySummary requiredAttentionSummary = requiredAttentionSummary(mailSummaryList);
        if (requiredAttentionSummary != null)
            daySummaries.add(requiredAttentionSummary);
        DaySummary eventSummary = eventSummary(mailSummaryList);
        if (eventSummary != null)
            daySummaries.add(eventSummary);
        EmailPreferences preference = preferencesDao.getEmailPreferencesByUserId(userId);

        if (preference != null && preference.getEmailSender() != null) {
            DaySummary senderSummaries = senderSummary(mailSummaryList);
            daySummaries.add(senderSummaries);
        }
        return daySummaries;
    }


    @Override
    public List<DaySummary> getDaySummaryV2(String userId, String emailId, String type, String date) {
        LocalDateTime startDate = null;
        LocalDateTime endDate = null;
        if (type == null || type.equalsIgnoreCase(EmailConstants.TODAY)) {
            startDate = DateUtils.convertToLocalDateTime(date);
            endDate = startDate.plusDays(1);
        } else if (type.equalsIgnoreCase(EmailConstants.YESTERDAY)) {
            startDate = DateUtils.convertToLocalDateTime(date).minusDays(1);
            endDate = startDate.plusDays(1);
        } else if (type.equalsIgnoreCase("week")) {
            startDate = DateUtils.convertToLocalDateTime(date).minusDays(7);
            endDate = DateUtils.convertToLocalDateTime(date);
        }
        log.info("Inside @method getDaySummaryV2 userId : {} , startDate :{} , endDate : {} ",userId , startDate , endDate);
        List<DaySummary> result = getSummaryList(userId, startDate, endDate);
        return result;
    }

    @Override
    public List<DaySummary> getDateRangeSummary(String email, LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("Inside @method getDateRangeSummary. @param : email -> {} startDate -> {} endDate -> {}", email, startDate, endDate);
        return getSummaryList(email, startDate, endDate);
    }

    @Override
    public List<MailSummary> getDateRangeSummaryByAction(String userId, String startDateTime, String endDateTime, String action) {
        log.info("Getting date range summary by action. userId: {}, action: {}, startDateTime: {}, endDateTime: {}",
                userId, action, startDateTime, endDateTime);

        try {
            // Convert string dates to LocalDateTime
            LocalDateTime startDate = DateUtils.convertToLocalDateTime(startDateTime);
            LocalDateTime endDate = DateUtils.convertToLocalDateTime(endDateTime);

            log.debug("Converted dates - startDate: {}, endDate: {}", startDate, endDate);

            // Return appropriate data based on action type
            return switch (action.toLowerCase()) {
                case "priority" -> {
                    log.debug("Fetching high priority mail for userId: {}", userId);
                    yield mailSummaryDao.highPriorityMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);
                }
                case "star" -> {
                    log.debug("Fetching star marked mail for userId: {}", userId);
                    yield mailSummaryDao.starMarkedMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);
                }
                case "attention" -> {
                    log.debug("Fetching attention mail for userId: {}", userId);
                    yield mailSummaryDao.attentionMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);
                }
                case "action" -> {
                    log.debug("Fetching actionable mail for userId: {}", userId);
                    yield userActionsDao.actionableMailByUserId(endDate, userId);
                }
                case "meeting" -> {
                    log.debug("Fetching meeting events for userId: {}", userId);
                    yield mailSummaryDao.eventByUserId(startDate, endDate, userId, "Meeting");
                }
                default -> {
                    log.warn("Unknown action type: {}", action);
                    yield List.of();
                }
            };
        } catch (IllegalArgumentException e) {
            log.error("Invalid date format or date range: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error getting date range summary by action: {}", e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public Map<String, Object> encryptExistingMailSummary(Integer startId, Integer endId) {
        log.info("Encrypting existing mail summary. startId: {}, endId: {}", startId, endId);
        List<MailSummary> emails = mailSummaryDao.findByIdBetween(startId, endId);

        // Extract found IDs
        Set<Integer> foundIds = emails.stream()
                .map(MailSummary::getId)
                .map(Integer::intValue) // assuming getId() returns Long
                .collect(Collectors.toSet());

        // Calculate not found IDs
        List<Integer> notFoundIds = IntStream.rangeClosed(startId, endId)
                .filter(id -> !foundIds.contains(id))
                .boxed()
                .collect(Collectors.toList());

        List<Integer> successIds = new ArrayList<>();
        List<Integer> failedIds = new ArrayList<>();
        for (MailSummary ms : emails) {
            try {
                ms.setMessageSummary(ms.getMessageSummary() + " ");
                ms.setSubject(ms.getSubject() + " ");
                successIds.add(ms.getId());
            } catch (Exception e) {
                failedIds.add(ms.getId());
                log.error("Error encrypting MailSummary ID {}: {}", ms.getId(), e.getMessage());
            }
        }
        mailSummaryDao.saveAll(emails);
        log.info("Saving encrypted mail summary");
        return Map.of(
                "successIds", successIds,
                "failedIds", failedIds,
                "notFoundIds", notFoundIds
        );
    }


    @Override
    public Map<String, Object> encryptExistingThreadSummary(Integer startId, Integer endId) {
        log.info("Encrypting existing thread summary. startId: {}, endId: {}", startId, endId);

        List<EmailThread> threads = emailThreadDao.findByIdBetween(startId, endId);

        // Extract found IDs
        Set<Integer> foundIds = threads.stream()
                .map(EmailThread::getId)
                .map(Integer::intValue) // assuming getId() returns Long
                .collect(Collectors.toSet());

        // Calculate not found IDs
        List<Integer> notFoundIds = IntStream.rangeClosed(startId, endId)
                .filter(id -> !foundIds.contains(id))
                .boxed()
                .collect(Collectors.toList());

        List<Integer> successIds = new ArrayList<>();
        List<Integer> failedIds = new ArrayList<>();
        for (EmailThread et : threads) {
            try {
                et.setThreadSummary(et.getThreadSummary() + " ");
                et.setSubject(et.getSubject() + " ");
                et.setShortSummary(et.getShortSummary() + " ");
                successIds.add(et.getId());
            } catch (Exception e) {
                failedIds.add(et.getId());
                log.error("Error encrypting EmailThread ID {}: {}", et.getId(), e.getMessage());
            }
        }
        emailThreadDao.saveAll(threads);
        log.info("Saving encrypted thread summary");
        return Map.of(
                "successIds", successIds,
                "failedIds", failedIds,
                "notFoundIds", notFoundIds
        );
    }

    private List<DaySummary> getSummaryList(String userId, LocalDateTime startDate, LocalDateTime endDate) {
       log.debug("Inside @method getSummaryList. @param : userId -> {} startDate -> {} endDate -> {}", userId, startDate, endDate);
        try {
            long highPriorityMailCount = mailSummaryDao.countHighPriorityMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);
            long attentionMailCount = mailSummaryDao.countAttentionMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);
            long starMarkedMailCount = mailSummaryDao.countStarMarkedMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);
            long actionableMailCount = userActionsDao.countActionableMailByUserId(endDate, userId);

            List<DaySummary> result = new ArrayList<>();
            DaySummary attentionSummary = getAttentionSummary(attentionMailCount);
            DaySummary highPrioritySummary = getHighPrioritySummary(highPriorityMailCount);
            DaySummary starMarkedSummary = getStarMarkedSummary(starMarkedMailCount);
            DaySummary actionableSummary = getActionableSummary(actionableMailCount);

            result.add(attentionSummary);
            result.add(highPrioritySummary);
            result.add(starMarkedSummary);
            result.add(actionableSummary);
            log.debug("Get successfully summary list");
            return result;
        } catch (Exception e) {
            throw new BusinessException("Error while fetching summary list: " + e.getMessage());
        }
    }



    public List<MailSummary> getDaySummaryV3(String userId, String type, String date) {
        LocalDateTime startDate = null;
        LocalDateTime endDate = null;
        if (type == null || type.equalsIgnoreCase(EmailConstants.TODAY)) {
            startDate = DateUtils.convertToLocalDateTime(date);
            endDate = startDate.plusDays(1);
        } else if (type.equalsIgnoreCase(EmailConstants.YESTERDAY)) {
            startDate = DateUtils.convertToLocalDateTime(date).minusDays(1);
            endDate = startDate.plusDays(1);
        } else if (type.equalsIgnoreCase("week")) {
            startDate = DateUtils.convertToLocalDateTime(date).minusDays(7);
            endDate = DateUtils.convertToLocalDateTime(date);
        }
        List<MailSummary> highPriorityMailSummary = mailSummaryDao.highPriorityMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);

        List<MailSummary> attentionMailSummary = mailSummaryDao.attentionMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);

        List<MailSummary> starMarkedMailSummary = mailSummaryDao.starMarkedMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);

        List<MailSummary> actionableMailSummary = userActionsDao.actionableMailByUserId(endDate, userId);

        Map<String, MailSummary> mailSummaryMap = new HashMap<>();


        updateMailSummaryList(mailSummaryMap, highPriorityMailSummary, "isHighPriority");
        updateMailSummaryList(mailSummaryMap, starMarkedMailSummary, "isStarMarked");
        updateMailSummaryList(mailSummaryMap, attentionMailSummary, "isAttention");
        updateMailSummaryList(mailSummaryMap, actionableMailSummary, "isActionable");

        return new ArrayList<>(mailSummaryMap.values());
    }


    private void updateMailSummaryList(Map<String, MailSummary> mailSummaryMap, List<MailSummary> mailSummaries, String field) {
        for (MailSummary mail : mailSummaries) {
            MailSummary existingMail = mailSummaryMap.get(mail.getInternetMessageId());
            if (existingMail == null) {
                mailSummaryMap.put(mail.getInternetMessageId(), mail);
                existingMail = mail;
            }
            switch (field) {
                case "isHighPriority":
                    existingMail.setIsHighPriority(true);
                    break;
                case "isStarMarked":
                    existingMail.setIsStarMarked(true);
                    break;
                case "isAttention":
                    existingMail.setIsAttention(true);
                    break;
                case "isActionable":
                    existingMail.setIsActionable(true);
                    break;
            }
        }
    }


    @Override
    public List<MailSummary> getMailSummaries(String emailId, String startDateStr, String endDateStr, String timeZone) {

        LocalDateTime startDate, endDate;
        if (startDateStr == null) {
            startDate = DateUtils.getDayStartInUtc(timeZone);
            endDate = startDate.plusDays(1);
        } else {
            startDate = DateUtils.convertToLocalDateTime(startDateStr);
            endDate = DateUtils.convertToLocalDateTime(endDateStr);
        }

        long dayRange = ChronoUnit.DAYS.between(startDate, endDate);
        if (dayRange < 0) throw new BusinessException("Start date can not be less than End date.");
        if (dayRange > 7) throw new BusinessException("Range between start date and end date can not be greater than 7 days");
        return mailSummaryDao.findMailSummariesByUserIdAndDateRange(emailId, startDate, endDate);
    }

    private DaySummary getHighPrioritySummary(long highPriorityMailCount) {
        DaySummary prioritySummary = new DaySummary();
        if (highPriorityMailCount == 1) {
//            prioritySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + highPriorityMailCount + " email </span> of high priority");
            prioritySummary.setContent(highPriorityMailCount + " " + EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + "high priority </span> email");
            prioritySummary.setContentKey("HIGH_PRIORITY_EMAIL");
            prioritySummary.setMobileContent("High priority");
            prioritySummary.setValue((int) highPriorityMailCount);
            prioritySummary.setAction("priority");
            return prioritySummary;
        } else if (highPriorityMailCount > 1) {
//            prioritySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + highPriorityMailCount + " emails </span> of high priority");
            prioritySummary.setContent(highPriorityMailCount + " " + EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + "high priority </span> emails");
            prioritySummary.setContentKey("HIGH_PRIORITY_EMAILS");
            prioritySummary.setMobileContent("High priority");
            prioritySummary.setAction("priority");
            prioritySummary.setValue((int) highPriorityMailCount);
            return prioritySummary;
        } else {
            return null;
        }
    }

    private DaySummary getAttentionSummary(long attentionMailCount) {
        DaySummary attentionSummary = new DaySummary();
        if (attentionMailCount == 1) {
//            attentionSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + attentionMailCount + " email</span> requiring attention");
            attentionSummary.setContent(attentionMailCount + " email requiring " + EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + "attention</span>");
            attentionSummary.setContentKey("EMAIL_REQUIRING_ATTENTION");
            attentionSummary.setMobileContent("Email requiring attention");
            attentionSummary.setAction("Attention");
            attentionSummary.setValue((int) attentionMailCount);
            return attentionSummary;
        } else if (attentionMailCount > 1) {
//            attentionSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + attentionMailCount + " emails</span> requiring attention");
            attentionSummary.setContent(attentionMailCount + " emails requiring " + EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + "attention</span>");
            attentionSummary.setContentKey("EMAILS_REQUIRING_ATTENTION");
            attentionSummary.setMobileContent("Email requiring attention");
            attentionSummary.setAction("Attention");
            attentionSummary.setValue((int) attentionMailCount);
            return attentionSummary;
        } else {
            return null;
        }
    }

    private DaySummary getStarMarkedSummary(long starMarkedMailCount) {
        DaySummary starMarkedSummary = new DaySummary();
        if (starMarkedMailCount == 1) {
//            starMarkedSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + starMarkedMailCount + " starred</span> conversation");
            starMarkedSummary.setContent(starMarkedMailCount + " " + EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + "starred</span> email");
            starMarkedSummary.setContentKey("STARRED_EMAIL");
            starMarkedSummary.setMobileContent("Starred email");
            starMarkedSummary.setAction("Star");
            starMarkedSummary.setValue((int) starMarkedMailCount);
            return starMarkedSummary;
        } else if (starMarkedMailCount > 1) {
//            starMarkedSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + starMarkedMailCount + " starred</span> conversations");
            starMarkedSummary.setContent(starMarkedMailCount + " " + EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + "starred</span> emails");
            starMarkedSummary.setContentKey("STARRED_EMAILS");
            starMarkedSummary.setMobileContent("Starred email");
            starMarkedSummary.setAction("Star");
            starMarkedSummary.setValue((int) starMarkedMailCount);
            return starMarkedSummary;
        } else {
            return null;
        }
    }

    private DaySummary getActionableSummary(long actionableMailCount) {
        DaySummary actionableSummary = new DaySummary();
        if (actionableMailCount == 1) {
//            actionableSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + actionableMailCount + " email</span> for your actions till today");
            actionableSummary.setContent(actionableMailCount + " email with " + EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + "pending actions");
            actionableSummary.setContentKey("EMAIL_WITH_PENDING_ACTIONS");
            actionableSummary.setMobileContent("Email with pending actions");
            actionableSummary.setValue((int) actionableMailCount);
            actionableSummary.setAction("Action");
            return actionableSummary;
        } else if (actionableMailCount > 1) {
//            actionableSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + actionableMailCount + " emails</span> for your actions till today");
            actionableSummary.setContent(actionableMailCount + " emails with " + EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + "pending actions");
            actionableSummary.setContentKey("EMAILS_WITH_PENDING_ACTIONS");
            actionableSummary.setMobileContent("Email with pending actions");
            actionableSummary.setValue((int) actionableMailCount);
            actionableSummary.setAction("Action");
            return actionableSummary;
        } else {
            actionableSummary.setContent(EmailConstants.getActionItemMessage());
            actionableSummary.setMobileContent(EmailConstants.getActionItemMessage());
            return actionableSummary;
        }
    }

    @Override
    public List<MailSummary> getSummaryByTypeV1(String type, String action, String date) {
        log.debug("Inside @method getSummaryByTypeV1. @param : type -> {} action -> {} date -> {}", type, action, date);
        String userId = userContextHolder.getCurrentUser().getId();
        String emailId = userContextHolder.getCurrentUser().getEmail();

        LocalDateTime startDate = null;
        LocalDateTime endDate = null;
        if (type == null || type.equalsIgnoreCase(EmailConstants.TODAY)) {
            startDate = DateUtils.convertToLocalDateTime(date);
            endDate = startDate.plusDays(1);
        } else if (type.equalsIgnoreCase(EmailConstants.YESTERDAY)) {
            startDate = DateUtils.convertToLocalDateTime(date).minusDays(1);
            endDate = startDate.plusDays(1);
        } else if (type.equalsIgnoreCase("week")) {
            startDate = DateUtils.convertToLocalDateTime(date).minusDays(7);
            endDate = DateUtils.convertToLocalDateTime(date);
        }

        return switch (action.toLowerCase()) {
            case "priority" ->
                    mailSummaryDao.highPriorityMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);
            case "star" -> mailSummaryDao.starMarkedMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);
            case "attention" -> mailSummaryDao.attentionMailByUserId(startDate, endDate, userId, EmailConstants.EMAIL);
            case "action" -> userActionsDao.actionableMailByUserId(endDate, userId);
            case "meeting" -> mailSummaryDao.eventByUserId(startDate, endDate, userId, "Meeting");
            default -> null;
        };
    }

    @Override
    public long countAllMailOfUser(String userId, String type, String date) {
        LocalDateTime startDate = null;
        LocalDateTime endDate = null;
        if (type == null || type.equalsIgnoreCase(EmailConstants.TODAY)) {
            startDate = DateUtils.convertToLocalDateTime(date);
            endDate = startDate.plusDays(1);
        } else if (type.equalsIgnoreCase(EmailConstants.YESTERDAY)) {
            startDate = DateUtils.convertToLocalDateTime(date).minusDays(1);
            endDate = startDate.plusDays(1);
        } else if (type.equalsIgnoreCase("week")) {
            startDate = DateUtils.convertToLocalDateTime(date).minusDays(7);
            endDate = DateUtils.convertToLocalDateTime(date);
        }

        return mailSummaryDao.countAllMailOfUser(startDate, endDate, userId, "Sent Items");
    }

    @Override
    public void addAttachmentDetails(List<MailSummary> mailSummaryList) {
        try {
            for (MailSummary mailSummary : mailSummaryList) {
                log.debug("checking attachment for messageID : {}", mailSummary.getMessageId());
                List<UserMailAttachment> attachemntList = userMailAttachmentRepository.getAttachmentsByInternetMessageId(mailSummary.getInternetMessageId());
                if (attachemntList.isEmpty()) {
                    mailSummary.setHasAttachment(false);
                } else {
                    log.debug("Attachment size is : {}", attachemntList.size());
                    List<Attachments> attachments = new ArrayList<>();
                    for (UserMailAttachment attachment : attachemntList) {
                        log.debug("Attachment name : {}", attachment.getName());
                        Attachments attachments1 = new Attachments();
                        attachments1.setId(attachment.getId());
                        attachments1.setDocType(attachment.getType());
                        attachments1.setGraphDocId(attachment.getAttachmentId());
                        attachments1.setDocName(attachment.getName());
                        attachments.add(attachments1);
                    }
                    mailSummary.setHasAttachment(true);
                    mailSummary.setAttachmentsList(attachments);
                }

            }
        } catch (Exception e) {
            log.error("Error inside @method addAttachmentDetails. Exception message : {}", e.getMessage());
        }
    }


    private void addAllAttachmentDetails(List<MailSummary> mailSummaryList) {
        try {
            for (MailSummary mailSummary : mailSummaryList) {
                log.debug("checking addAllAttachmentDetails for messageID : {}", mailSummary.getMessageId());
                List<UserMailAttachment> attachemntList = userMailAttachmentRepository.getAttachmentsByInternetMessageId(mailSummary.getInternetMessageId());
                log.info("attachment for email wih IM_ID - {} , {}",mailSummary.getInternetMessageId(),attachemntList);
                if (attachemntList.isEmpty()) {
                    mailSummary.setHasAttachment(false);
                } else {
                    log.debug("Attachment size is : {}", attachemntList.size());
                    List<Attachments> attachments = new ArrayList<>();
                    for (UserMailAttachment attachment : attachemntList) {
                        log.debug("Attachment name : {}", attachment.getName());
                        Attachments attachments1 = new Attachments();
                        attachments1.setId(attachment.getId());
                        attachments1.setDocType(attachment.getType());
                        attachments1.setGraphDocId(attachment.getAttachmentId());
                        attachments1.setDocName(attachment.getName());
                        attachments.add(attachments1);
                    }
                    mailSummary.setHasAttachment(true);
                    mailSummary.setAttachmentsList(attachments);
                }

            }
        } catch (Exception e) {
            log.error("Error inside @method addAttachmentDetails. Exception message : {}", e.getMessage());
        }
    }

    private Date getDate(String dateString) {
        log.debug("Date string : {}", dateString);

        Calendar cal = Calendar.getInstance();

        if (dateString == null || dateString.isEmpty()) {
            cal.setTime(new Date());
        } else {
            // Parse the provided date and time string
            SimpleDateFormat sdf;
            if (dateString.length() > 10) {
                sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            } else {
                sdf = new SimpleDateFormat("yyyy-MM-dd");
            }
            try {
                Date date = sdf.parse(dateString);
                cal.setTime(date);
            } catch (ParseException e) {
                log.error("Error while parsing date", e);
                throw new BusinessException("Error while parsing date.");
            }
        }

        return cal.getTime();
    }


    /**
     * Priority wise summary day summary.
     *
     * @param mailSummaryList the mail summary list
     * @return the day summary
     */
    public DaySummary priorityWiseSummary(List<MailSummary> mailSummaryList) {
        List<MailSummary> highPriorityMails = mailSummaryList.stream()
                .filter(mail -> "High".equals(mail.getPriority()))
                .filter(mail -> !"{}".equals(mail.getMessageSummary()))
                .collect(Collectors.toList());

        DaySummary prioritySummary = new DaySummary();
        if (highPriorityMails.size() == 1) {
            prioritySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + highPriorityMails.size() + " email </span> of high priority");
            prioritySummary.setMailObject(highPriorityMails);
            return prioritySummary;
        } else if (highPriorityMails.size() > 0) {
            prioritySummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + highPriorityMails.size() + " emails </span> of high priority");
            prioritySummary.setMailObject(highPriorityMails);
            return prioritySummary;
        } else {
            return null;
        }

    }

    /**
     * Action owner summary day summary.
     *
     * @param mailSummaryList the mail summary list
     * @param emailId         the email id
     * @return the day summary
     */
    public DaySummary actionOwnerSummary(List<MailSummary> mailSummaryList, String emailId) {

//        List<MailSummary> actionOwnerMails = mailSummaryDao.actionableMailByUserId(LocalDateTime.now().plusDays(1), emailId, EmailConstants.EMAIL, emailId);
        List<MailSummary> actionOwnerMails = userActionsDao.actionableMailByUserId(LocalDateTime.now().plusDays(1), emailId);


        DaySummary actionOwners = new DaySummary();
        if (actionOwnerMails.size() == 1) {
            actionOwners.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + actionOwnerMails.size() + " email</span> for your actions");
            actionOwners.setMailObject(actionOwnerMails);
            return actionOwners;
        } else if (actionOwnerMails.size() > 0) {
            actionOwners.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + actionOwnerMails.size() + " emails</span> for your actions");
            actionOwners.setMailObject(actionOwnerMails);
            return actionOwners;
        } else {
            actionOwners.setContent("No email for your actions");
            actionOwners.setMailObject(actionOwnerMails);
            return null;
        }

    }


    /**
     * Required attention summary day summary.
     *
     * @param mailSummaryList the mail summary list
     * @return the day summary
     */
    public DaySummary requiredAttentionSummary(List<MailSummary> mailSummaryList) {
        List<MailSummary> categoryMails = mailSummaryList.stream()
                .filter(mail -> "Attention".equals(mail.getCategory()))
                .collect(Collectors.toList());

        DaySummary categoryMailsSummary = new DaySummary();
        if (categoryMails.size() == 1) {
            categoryMailsSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + categoryMails.size() + " email</span> requiring attention");
            categoryMailsSummary.setMailObject(categoryMails);
            return categoryMailsSummary;
        } else if (categoryMails.size() > 0) {
            categoryMailsSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + categoryMails.size() + " emails</span> requiring attention");
            categoryMailsSummary.setMailObject(categoryMails);
            return categoryMailsSummary;
        } else {
            categoryMailsSummary.setContent("No email requiring attention");
            categoryMailsSummary.setMailObject(categoryMails);
            return null;
        }

    }

    /**
     * Event summary day summary.
     *
     * @param mailSummaryList the mail summary list
     * @return the day summary
     */
    public DaySummary eventSummary(List<MailSummary> mailSummaryList) {
        List<MailSummary> categoryMails = mailSummaryList.stream()
                .filter(mail -> "Attention".equals(mail.getCategory()))
                .filter(mail -> "High".equals(mail.getPriority()))
//                .filter(mail -> mail.getMeetingEndTime() != null && mail.getMeetingEndTime().isAfter(LocalDateTime.now()))
                .collect(Collectors.toList());

        DaySummary categoryMailsSummary = new DaySummary();
        if (categoryMails.size() == 1) {
            categoryMailsSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + categoryMails.size() + "</span> important event today");
            categoryMailsSummary.setMailObject(categoryMails);
            return categoryMailsSummary;
        } else if (categoryMails.size() > 0) {
            categoryMailsSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + categoryMails.size() + "</span> important events today");
            categoryMailsSummary.setMailObject(categoryMails);
            return categoryMailsSummary;
        } else {
            categoryMailsSummary.setContent("No important event today");
            categoryMailsSummary.setMailObject(categoryMails);
            return null;
        }

    }

    /**
     * Sender summary day summary.
     *
     * @param mailSummaryList the mail summary list
     * @return the day summary
     */
    public DaySummary senderSummary(List<MailSummary> mailSummaryList) {
        List<MailSummary> senderMails = mailSummaryList.stream()
                .filter(mail -> mail.getStarMarked() != null && mail.getStarMarked())
                .collect(Collectors.toList());

        DaySummary categoryMailsSummary = new DaySummary();
        if (senderMails.size() == 1) {
            categoryMailsSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + senderMails.size() + " starred</span> conversation");
            categoryMailsSummary.setMailObject(senderMails);
            return categoryMailsSummary;
        } else if (senderMails.size() > 0) {
            categoryMailsSummary.setContent(EmailConstants.SPAN_CLASS_TITLE_TEXT_HIGHLIGHTED + senderMails.size() + " starred</span> conversations");
            categoryMailsSummary.setMailObject(senderMails);
            return categoryMailsSummary;
        } else {
            categoryMailsSummary.setContent("No starred conversation");
            categoryMailsSummary.setMailObject(senderMails);
            return null;
        }

    }

    @Override
    public Map<String, Object> getMailSummaryAndThreadSummary(String userId, String internetMessageId) {
        Map<String, Object> obj = new HashMap<>();
        MailSummary mailSummary = mailSummaryDao.findByUserIdAndInternetMessageId(userId, internetMessageId);
        obj.put("mailSummary", mailSummary);
        String conversationId = mailSummary.getConversationId();
        EmailThread emailThread = emailThreadDao.findByConversationId(conversationId, userId);
        obj.put("emailThread", emailThread);
        return obj;
    }

    @Override
    public void saveContactSummary() {
        try {
            List<String> activeUserIds = emailUserDao.findAllActiveUserIds();
            List<ContactBook> contactBooks = new ArrayList<>();

            log.info("Active User IDs: {}", activeUserIds);

            for (String userId : activeUserIds) {
                List<MailSummary> todayMails = mailSummaryDao.getTodayMailsByUserId(userId);

                Set<String> uniqueContacts = new HashSet<>();

                for (MailSummary mail : todayMails) {
                    if (mail.getFromUser() != null) uniqueContacts.add(mail.getFromUser());

                    if (mail.getToUser() != null) {
                        String[] toUsers = Arrays.stream(mail.getToUser().split(","))
                                .filter(s -> !s.isEmpty())  // Filter out empty strings
                                .toArray(String[]::new);
                        for (String toUser : toUsers) {
                            String trimmedToUser = toUser.trim();
                            if (!userId.equals(trimmedToUser)) {
                                uniqueContacts.add(trimmedToUser);
                            }
                        }
                    }

                    if (mail.getCcUser() != null) {
                        String[] ccUsers = Arrays.stream(mail.getCcUser().split(","))
                                .filter(s -> !s.isEmpty())  // Filter out empty strings
                                .toArray(String[]::new);
                        for (String ccUser : ccUsers) {
                            uniqueContacts.add(ccUser.trim());
                        }
                    }
                }

                log.info("Unique contacts for user ID {}: {}", userId, uniqueContacts);

                for (String contact : uniqueContacts) {
                    String contactName = preferencesDao.findDisplayNameByEmail(contact);
                    if (contactName == null || contactName.trim().isEmpty()) {
                        String[] emailParts = contact.split("@");
                        if (emailParts.length > 1) {
                            String localPart = emailParts[0];
                            String[] nameParts = localPart.split("\\.");
                            if (nameParts.length > 1) {
                                contactName = String.join(" ", Arrays.copyOf(nameParts, nameParts.length - 1)) + " " + nameParts[nameParts.length - 1];
                            } else {
                                contactName = localPart;
                            }
                        } else {
                            contactName = contact;
                        }
                    }

                    log.info("Contact: {}, Display Name: {}", contact, contactName);

                    ContactBook contactBook = new ContactBook();
                    contactBook.setUserId(userId);
                    contactBook.setContacts(contact);
                    contactBook.setContactName(contactName);

                    if (!contactBookRepository.existsByUserIdAndContacts(userId, contact)) {
                        contactBooks.add(contactBook);
                    } else {
                        log.info("Duplicate contact for user ID {}: {}, skipping save", userId, contact);
                    }
                }

                if (!contactBooks.isEmpty()) {
                    contactBookRepository.saveAll(contactBooks);
                    log.info("Successfully saved contacts for user ID: {}", userId);
                }

                log.info("Total contacts processed for user ID {}: {}", userId, contactBooks.size());

                contactBooks.clear();
            }
        } catch (Exception e) {
            log.error("An error occurred while saving contact summaries: {}", e.getMessage(), e);
        }
    }


    public static void main(String[] args) {
        String a = ",<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>";
        String[] ccUsers = Arrays.stream(a.split(","))
                .filter(s -> !s.isEmpty())  // Filter out empty strings
                .toArray(String[]::new);
        for (int i = 0; i < ccUsers.length; i++)
            System.out.println(ccUsers[i]);
    }

    public void updateBatchByAvgTime() {

        log.info("Going to update batch for users");
        try {
            List<Object[]> userList = batchRepo.avgMailTime();
            List<String> emptyUserList = new ArrayList<String>();
            List<String> avgUserList = new ArrayList<String>();
            for (Object[] result : userList) {

                if (result[1] == null) {
                    emptyUserList.add((String) result[0]);
                } else {
                    avgUserList.add((String) result[0]);
                }
            }

            log.info("userList with empty avg time {}", emptyUserList.toString());

            log.info("userList with non empty avg time {}", avgUserList.toString());

            batchRepo.updateBatch("Batch-4", emptyUserList);

            int totalRecords = avgUserList.size();
            int subBatchSize = totalRecords / 3; // Divide total records by 3

            // Adjust for any remaining records if the size is not perfectly divisible by 3
            int remainingRecords = totalRecords % 3;
            int batchIndex = 2;
            // Run three loops for subbatches
            for (int i = 0; i < 3; i++) {
                int start = i * subBatchSize;
                int end = start + subBatchSize + (i < remainingRecords ? 1 : 0); // Add one extra record to the first few batches if there are remaining records
                List<String> sublist = new ArrayList<>();
                String newBatchId = "";
                if (start < totalRecords) {
                    sublist = avgUserList.subList(start, Math.min(end, totalRecords));


                    newBatchId = "Batch-" + batchIndex;


                }
                log.debug("going to update new batch id {} for {}", newBatchId, sublist.toString());
                batchRepo.updateBatch(newBatchId, sublist);
                batchIndex++;
            }
        } catch (Exception e) {
            log.error("Error while updating batch of users", e);
        }

    }


    @Override
    public List<ActionItemDto> getActionItemDetails(String userId, String conversationId, String subject) {
        log.debug("Inside @method getActionItemDetails. @param : userId -> {} conversationId -> {}", userId, conversationId);

        List<ActionItemDto> actionItemDetails = userActionsDao.findByActionOwnerAndConversationId(userId, conversationId);

        log.debug("actionItemDetails coming from DB is : {}", actionItemDetails);

        return actionItemDetails;
    }

    @Override
    public List<ConversationEmailsDTO> getConversations(String userId, Integer lowerLimit, Integer upperLimit) {
        log.debug("Inside @method getConversations. @param : userId -> {} lowerLimit -> {} upperLimit -> {}", userId, lowerLimit, upperLimit);

        List<String> conversationIdsOfUser = getConversationIdsOfUser(userId, lowerLimit, upperLimit);
        List<MailSummary> topConversationsOfUser = mailSummaryDao.findTopConversationsOfUser(userId, conversationIdsOfUser, 10);
        addAttachmentDetails(topConversationsOfUser);

        Map<String, List<MailSummary>> summaryMap = topConversationsOfUser.stream()
                .collect(Collectors.groupingBy(MailSummary::getConversationId));

        List<ConversationEmailsDTO> conversationEmailGroups = new ArrayList<>();
        for (String conversationId: conversationIdsOfUser) {
            List<MailSummary> mailSummaries = summaryMap.get(conversationId);
            mailSummaries.sort(Comparator.comparing(MailSummary::getMailReceivedTime).reversed());
            ConversationEmailsDTO dto = new ConversationEmailsDTO();
            dto.setLatestMail(mailSummaries.getFirst());
            dto.setConversation(mailSummaries);
            conversationEmailGroups.add(dto);
        }
        return conversationEmailGroups;
    }

    @Override
    public List<String> getConversationIdsOfUser(String userId, Integer lowerLimit, Integer upperLimit) {
        log.debug("Inside @method getConversationIdsOfUser. @param : userId -> {} lowerLimit -> {} upperLimit -> {}", userId, lowerLimit, upperLimit);
        if (lowerLimit < 0) throw new BusinessException("Lower limit can not be less than 0");
        if (lowerLimit > upperLimit) throw new BusinessException("Lower limit can not be greater than upper limit");

        String jpql = "SELECT m.conversationId FROM MailSummary m WHERE m.userId = :userId AND m.deleted = false GROUP BY m.conversationId ORDER BY MAX(m.mailReceivedTime) DESC";
        TypedQuery<String> query = entityManager.createQuery(jpql, String.class);
        query.setParameter("userId", userId);
        query.setFirstResult(lowerLimit);
        query.setMaxResults(upperLimit - lowerLimit);
        return query.getResultList();
    }

    @Override
    public List<MailSummary> getMailsInConversation(String userId, String conversationId, Integer lowerLimit, Integer upperLimit) {
        log.debug("Inside @method getMailsInConversation. @param : userId -> {} lowerLimit -> {} upperLimit -> {}", userId, lowerLimit, upperLimit);
        if (lowerLimit < 0) throw new BusinessException("Lower limit can not be less than 0");
        if (lowerLimit > upperLimit) throw new BusinessException("Lower limit can not be greater than upper limit");

        String jpql = "SELECT m FROM MailSummary m WHERE m.userId = :userId AND m.conversationId = :conversationId AND m.deleted = false ORDER BY m.mailReceivedTime DESC";
        TypedQuery<MailSummary> query = entityManager.createQuery(jpql, MailSummary.class);
        query.setParameter("userId", userId);
        query.setParameter("conversationId", conversationId);
        query.setFirstResult(lowerLimit);
        query.setMaxResults(upperLimit - lowerLimit);
        List<MailSummary> mailSummaries = query.getResultList();
        addAttachmentDetails(mailSummaries);
        return mailSummaries;
    }

    @Override
    public Map<String, String> saveMailSummary(MailSummary mailSummary) {
        Map<String, String> response = new HashMap<>();
        
        try {
            // MailSummary existingMail = mailSummaryDao.findByUserIdAndInternetMessageId(
            //     mailSummary.getUserId(), 
            //     mailSummary.getInternetMessageId()
            // );
            
            // if (existingMail != null) {
            //     response.put("result", "failed");
            //     response.put("message", "Mail summary already exists");
            //     return response;
            // }
            
            // Save the mail summary
            mailSummaryDao.save(mailSummary);
            
            response.put("result", "success");
            response.put("message", "Mail summary saved successfully");
            
        } catch (Exception e) {
            log.error("Error saving mail summary: ", e);
            response.put("result", "failed");
            response.put("message", "Error saving mail summary");
        }
        
        return response;
    }

}
