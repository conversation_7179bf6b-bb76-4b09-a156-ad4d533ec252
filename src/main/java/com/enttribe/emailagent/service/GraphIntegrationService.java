package com.enttribe.emailagent.service;


import com.enttribe.emailagent.ai.dto.meeting.AvailableTimeSlots;
import com.enttribe.emailagent.ai.dto.meeting_summary.MeetingSummaryAiResponse;
import com.enttribe.emailagent.ai.service.AIService;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.dao.IEmailStatsDao;
import com.enttribe.emailagent.dao.MeetingSummaryDao;
import com.enttribe.emailagent.dao.UserMailAttachmentRepository;
import com.enttribe.emailagent.dto.*;
import com.enttribe.emailagent.entity.*;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.masking.HttpClientUtlis;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IEmailThreadDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.repository.impl.EmailStatsDaomImpl;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.enttribe.emailagent.utils.*;
import com.enttribe.emailagent.wrapper.AvailableSlots;
import com.enttribe.emailagent.wrapper.MessageWrapper;
import com.enttribe.emailagent.wrapper.OutOfOffice;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.mail.MessagingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.enttribe.emailagent.utils.CommonUtils.*;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.*;
import static com.enttribe.emailagent.utils.WorkingHoursUtil.GRAPH_API_TO_IANA_TIMEZONE;
import static com.google.common.base.Throwables.getStackTraceAsString;

/**
 * The type Graph integration service.
 *  <AUTHOR> Dangi
 */
@Service
@Slf4j
public class GraphIntegrationService {

    @Autowired
    private PollTimeInfoService pollTimeInfoService;

    @Autowired
    private TokenUtils tokenUtils;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private EmailUserDao emailUserDao;

    @Autowired
    private IEmailStatsDao iEmailStatsDao;

    @Autowired
    private IMailSummaryDao mailSummaryDao;

    @Autowired
    private UserContextHolder userContextHolder;

    @Autowired
    private IEmailPreferencesDao preferencesDao;

    @Autowired
    private AIService aiService;

    @Autowired
    private UserMailAttachmentRepository userMailAttachmentRepository;

    @Autowired
    MeetingSummaryDao meetingDao;

    @Autowired
    private UserFoldersRepository foldersRepository;

    @Autowired
    private IEmailThreadDao threadDao;

    @Autowired
    private EmailStatsDaomImpl emailStatsDao;

    @Value("${pollingMode}")
    private String pollingMode;

    @Value("${s3.bucketName}")
    private String s3BucketName;

    @Value("${total.meeting.limit}")
    private String meetingLimit;

    @Value("${app.supported.formats}")
    private List<String> supportedFormats;

    @Value("${email.body.content.token.limit}")
    private Integer bodyTokenLimit;

    @Value("${org.domain.name}")
    private List<String> orgDomainName;

    @Value("${outsiders.events.lookup.allow}")
    private Boolean allowOutsiders;

    private static String uploadUrl;

    private static String attachmentFilePath;

    private static String answerUrl;

    private static String summaryAnswerUrl;

    private static String summaryUploadUrl;

    private static final Logger auditLog = EmailAgentLogger.getLogger(GraphIntegrationService.class);

    private static final String GRAPH_API_MAILBOX_SETTINGS = "https://graph.microsoft.com/v1.0/users/%s/mailboxSettings";
    private static final String GRAPH_API_CANCEL_EVENT = "https://graph.microsoft.com/v1.0/users/%s/events/%s/cancel";
    private static final String GRAPH_API_FORWARD_EVENT = "https://graph.microsoft.com/v1.0/users/%s/events/%s/forward";
    private static final String GRAPH_API_GET_EVENT = "https://graph.microsoft.com/v1.0/users/%s/events/%s";

    @Autowired
    private MeetingSummaryDao meetingSummaryDao;


    @Value("${answerUrl}")
    public void setAnswerUrlUrl(String answerUrl) {
        GraphIntegrationService.answerUrl = answerUrl;
    }

    @Value("${summaryAnswerUrl}")
    public void setSummaryAnswerUrl(String summaryAnswerUrl) {
        GraphIntegrationService.summaryAnswerUrl = summaryAnswerUrl;
    }

    @Value("${summaryUploadUrl}")
    public void setSummaryUploadUrl(String summaryUploadUrl) {
        GraphIntegrationService.summaryUploadUrl = summaryUploadUrl;
    }


    @Value("${documentUploadUrl}")
    public void setDocumentUploadUrl(String documentUploadUrl) {
        GraphIntegrationService.uploadUrl = documentUploadUrl;
    }

    @Value("${attachmentFilePath}")
    public void setAttachmentFilePath(String attachmentFilePath) {
        GraphIntegrationService.attachmentFilePath = attachmentFilePath;
    }

    /**
     * Upload file upload response.
     *
     * @param filePath    the file path
     * @param fileName    the file name
     * @param contentType the content type
     * @return the upload response
     * @throws IOException the io exception
     */
    public UploadResponse uploadFile(Path filePath, String fileName, String contentType) throws IOException {
        log.debug("Inside @method uploadFile. @param : filePath -> {}", filePath);

        RestTemplate restTemplate = new RestTemplate();

        FileSystemResource fileResource = new FileSystemResource(filePath.toFile());

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", fileResource);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        org.springframework.http.HttpEntity<MultiValueMap<String, Object>> requestEntity = new org.springframework.http.HttpEntity<>(body, headers);

        ResponseEntity<UploadResponse> response = restTemplate.exchange(
                uploadUrl,
                HttpMethod.POST,
                requestEntity,
                UploadResponse.class
        );
        log.debug("Response coming is for upload response {}", response.getBody());
        return response.getBody();
    }


    private static final int CHUNK_SIZE = 1000; // Example chunk size

    /**
     * Chunk document list.
     *
     * @param content the content
     * @return the list
     */
    public List<String> chunkDocument(String content) {
        List<String> chunks = new ArrayList<>();
        int length = content.length();
        for (int i = 0; i < length; i += CHUNK_SIZE) {
            chunks.add(content.substring(i, Math.min(length, i + CHUNK_SIZE)));
        }
        return chunks;
    }


    private String createBasicAuthHeader(String username, String password) {
        String auth = username + ":" + password;
        byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
        return "Basic " + new String(encodedAuth);
    }

    private String extractValue(String jsonResponse, int startIndex) {
        int colonIndex = jsonResponse.indexOf(":", startIndex);
        int startQuote = jsonResponse.indexOf("\"", colonIndex + 1);
        int endQuote = jsonResponse.indexOf("\"", startQuote + 1);
        return jsonResponse.substring(startQuote + 1, endQuote);
    }


    /**
     * Summarize mail attachment.
     *
     * @param conversationId    the conversation id
     * @param internetMessageId the internet message id
     * @param emailId           the email id
     * @param messageId         the message id
     * @param userId            the user id
     * @param subject           the subject
     */
    public void summarizeMailAttachment(String conversationId, String internetMessageId, String emailId, String messageId, String userId, String subject) {
        log.debug("Inside @method summarizeMailAttachment. @param : emailId -> {}, messageId -> {}", emailId, messageId);
        String apiUrl = "https://graph.microsoft.com/v1.0/users/" + emailId + "/messages/" + messageId + "/attachments";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(apiUrl);
            request.addHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String jsonResponse = EntityUtils.toString(response.getEntity());
                processAttachments(jsonResponse, conversationId, internetMessageId, emailId, messageId, userId, subject);
            }
        } catch (IOException e) {
            logError("IO error in summarizeMailAttachment", e, messageId, internetMessageId, emailId, conversationId, userId);
        } catch (Exception e) {
            logError("Unexpected error in summarizeMailAttachment", e, messageId, internetMessageId, emailId, conversationId, userId);
        }
    }

    private void processAttachments(String jsonResponse, String conversationId, String internetMessageId, String emailId, String messageId, String userId, String subject) {
        int currentIndex = 0;
        while (currentIndex < jsonResponse.length()) {
            try {
                AttachmentInfo attachmentInfo = extractAttachmentInfo(jsonResponse, currentIndex);
                if (attachmentInfo == null) break;

                if (isValidAttachment(attachmentInfo)) {
                    processValidAttachment(attachmentInfo, conversationId, internetMessageId, emailId, messageId, userId, subject);
                }

                currentIndex = attachmentInfo.getNextIndex();
            } catch (Exception e) {
                log.error("Error processing attachment at index {}", currentIndex, e);
                // Move to next potential attachment
                currentIndex = jsonResponse.indexOf("\"contentType\":", currentIndex + 1);
                if (currentIndex == -1) break;
            }
        }
    }

    private AttachmentInfo extractAttachmentInfo(String jsonResponse, int startIndex) {
        int contentTypeStart = jsonResponse.indexOf("\"contentType\":", startIndex);
        int idStart = jsonResponse.indexOf("\"id\":", startIndex);
        int nameStart = jsonResponse.indexOf("\"name\":", startIndex);
        int contentBytesStart = jsonResponse.indexOf("\"contentBytes\":", startIndex);

        if (contentTypeStart == -1 || idStart == -1 || nameStart == -1 || contentBytesStart == -1) {
            return null;
        }

        String contentType = extractValue(jsonResponse, contentTypeStart);
        String attachmentId = extractValue(jsonResponse, idStart);
        String name = extractValue(jsonResponse, nameStart);
        String contentBytes = extractValue(jsonResponse, contentBytesStart);

        return new AttachmentInfo(contentType, attachmentId, name, contentBytes, contentBytesStart + contentBytes.length());
    }

    private boolean isValidAttachment(AttachmentInfo info) {
        String format = getFileFormat(info.getName());
        return supportedFormats.contains(format);
    }

    private void processValidAttachment(AttachmentInfo info, String conversationId, String internetMessageId, String emailId, String messageId, String userId, String subject) {
        String attachmentName = internetMessageId + "_" + info.getName();

        if (isAttachmentAlreadyProcessed(attachmentName, userId)) {
            log.debug("Attachment already processed: {}", attachmentName);
            return;
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Path tempFile = null;

        try {
            tempFile = createTempFile(info, timestamp);
            String s3Key = uploadToS3(tempFile, userId, timestamp, info.getName());
            saveAttachmentInfo(info, conversationId, internetMessageId, emailId, messageId, userId, subject, s3Key, attachmentName);
        } catch (IOException e) {
            logError("IO error processing attachment", e, messageId, internetMessageId, emailId, conversationId, userId);
        } catch (Exception e) {
            logError("Error processing attachment", e, messageId, internetMessageId, emailId, conversationId, userId);
        } finally {
            deleteTempFile(tempFile);
        }
    }

    private boolean isAttachmentAlreadyProcessed(String attachmentName, String userId) {
        String status = userMailAttachmentRepository.processingStatusForDocument(attachmentName);
        return status != null && (status.equalsIgnoreCase("NEW") || status.equalsIgnoreCase("IN_PROGRESS") ||
                status.equalsIgnoreCase("COMPLETED") || status.equalsIgnoreCase("ERROR"));
    }

    private Path createTempFile(AttachmentInfo info, String timestamp) throws IOException {
        byte[] fileBytes = Base64.getDecoder().decode(info.getContentBytes());
        Path tempFile = Files.createTempFile(Paths.get(attachmentFilePath), "attachment_", "_" + timestamp + "_" + info.getName());
        Files.write(tempFile, fileBytes);
        return tempFile;
    }

    private String uploadToS3(Path tempFile, String userId, String timestamp, String fileName) {
        //    String s3BucketName = "emailAttachments";
        String s3Key = userId + "/attachments/" + timestamp + "_" + fileName;
        try {
            s3Service.uploadFile(s3BucketName, s3Key, tempFile.toString());
            return s3Key;
        } catch (Exception e) {
            log.error("Error uploading file to S3", e);
            throw new RuntimeException("Failed to upload file to S3", e);
        }
    }

    private void saveAttachmentInfo(AttachmentInfo info, String conversationId, String internetMessageId, String emailId, String messageId, String userId, String subject, String s3Key, String attachmentName) {
        EmailUser user = emailUserDao.findByEmail(emailId);
        UserMailAttachment attachment = new UserMailAttachment();
        attachment.setUserId(emailId);
        attachment.setMessageId(messageId);
        attachment.setName(info.getName());
        attachment.setUniqueName(attachmentName);
        attachment.setType(info.getContentType());
        attachment.setAttachmentId(info.getAttachmentId());
        attachment.setConversationId(conversationId);
        attachment.setShortSummary(null);
        attachment.setLongSummary(null);
        attachment.setRagDocumentId(null);
        attachment.setCreationTime(new Date());
        attachment.setModifiedTime(new Date());
        attachment.setSubject(subject);
        attachment.setInternetMessageId(internetMessageId);
        attachment.setDocPath(s3Key);
        attachment.setProcessingStatus("NEW");
        attachment.setBatchId(user.getBatchId());
        userMailAttachmentRepository.save(attachment);
        log.debug("File uploaded to S3: {}", s3Key);
    }

    private void deleteTempFile(Path tempFile) {
        if (tempFile != null) {
            try {
                Files.deleteIfExists(tempFile);
                log.debug("Temporary file deleted successfully");
            } catch (IOException e) {
                log.error("Failed to delete temporary file", e);
            }
        }
    }

    private void logError(String message, Exception e, String messageId, String internetMessageId, String emailId, String conversationId, String userId) {
        Map<String, String> auditMap = Map.of("userId", userId, "internetMessageId", internetMessageId);
        auditLog.error(message, e.getMessage(), getStackTraceAsString(e), messageId, internetMessageId, PROCESS_ATTACHMENT, emailId, null, conversationId, true, null, AIUtils.convertToJSON(auditMap, true), null);
        log.error(message, e);
    }

    /**
     * Search contact list.
     *
     * @param contactName the contact name
     * @return the list
     */
    public List<String> searchContact(String contactName) {
        log.debug("Searching contact for name {}", contactName);
        List<String> contacts = new ArrayList<>();

        // Trim leading and trailing spaces from contactName
        contactName = contactName.trim();

        // Split the contactName by spaces (to handle first name and last name)
        String[] nameParts = contactName.split("\\s+");

        if (nameParts.length == 1) {
            if (nameParts[0].contains("@")) {
                // If there's only one part, search by email when it matches regex for email
                String[] parts = nameParts[0].split("@");
                String emailPartBeforeAt = parts[0];
                String[] namePart = emailPartBeforeAt.split("\\.");
                String firstName, lastName;
                if (namePart.length > 1) {
                    firstName = namePart[0];
                    lastName = namePart[1];
                } else {
                    firstName = namePart[0];
                    lastName = "";
                }
                String emailPartAfterAt = parts.length > 1 ? parts[1] : "";
                contacts = emailUserDao.searchByEmailParts(firstName, lastName, emailPartAfterAt);
            } else {
                // If there's only one part, search by first name
                contacts = emailUserDao.searchBySingleName(nameParts[0]);
            }
        } else if (nameParts.length > 1) {
            // If there are multiple parts, search by both first name and last name using SOUNDEX
            contacts = emailUserDao.searchByFirstAndLastName(nameParts[0], nameParts[nameParts.length - 1]);
        }

        return contacts;
    }

    public Map<String, List<String>> searchMultipleContacts(List<String> emails) {
        Map<String, List<String>> response = new HashMap<>();
        for (String email: emails) {
            try {
                List<String> contacts = searchContact(email);
                response.put(email, contacts);
            } catch (Exception e) {
                log.error("Error while searching contact", e);
            }
        }
        return response;
    }

    public List<UserEmailDto> getEmailsWithFilter(String email, String filter, String folder) {
        log.debug("Inside @method getEmailsOfUser. @param: email -> {}, filter -> {}, folder -> {}", email, filter, folder);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String url = buildUrlWithFilter(email, folder, filter);
            log.debug("URL to get emails : {}", url);

            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                    String result = EntityUtils.toString(entity);
                    return convertJsonToUserEmailDto(result, email, false);
                } else if (entity != null) {
                    String errorMessage = EntityUtils.toString(entity);
                    auditLog.error("Error while getting emails from graph", errorMessage, errorMessage + " Response Code " + response.getStatusLine().getStatusCode(), null, null, "GET_EMAIL_FROM_FOLDER", email, null, null, null, null, null, null);
                    return new ArrayList<>();
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while getting emails of a folder from graph", e.getMessage(), getStackTraceAsString(e), null, null, "GET_EMAIL_FROM_FOLDER", email, null, null, null, null, null, null);
        }
        return new ArrayList<>();
    }

    private String buildUrlWithFilter(String email, String mailFolder, String filter) throws URISyntaxException {
        // Base URL
        URIBuilder uriBuilder = new URIBuilder(
                String.format("https://graph.microsoft.com/v1.0/users/%s/mailFolders/%s/messages", email, mailFolder)
        );

        // Add the filter parameter if provided
        if (filter != null && !filter.isEmpty()) {
            uriBuilder.addParameter("$filter", filter);
        }

        return uriBuilder.build().toString();
    }

    public Object getEmailByMessageId(String email, String messageId) {
        log.debug("Inside @method getEmailByMessageId. @param email : {} messageId : {}", email, messageId);
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s", email, messageId);
            log.debug("URL to get emails : {}", url);

            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                    String result = EntityUtils.toString(entity);
                    return result;
                } else if (entity != null) {
                    String errorMessage = EntityUtils.toString(entity);
                    auditLog.error("Error while getting emails from graph", errorMessage, errorMessage + " Response Code " + response.getStatusLine().getStatusCode(), null, null, "GET_EMAIL_FROM_FOLDER", email, null, null, null, null, null, null);
                    return new ArrayList<>();
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while getting emails of a folder from graph", e.getMessage(), getStackTraceAsString(e), null, null, "GET_EMAIL_FROM_FOLDER", email, null, null, null, null, null, null);
        }
        return null;
    }

    public Map<String, Object> updateEvent(String eventId , String email, String meetingRequestJson) {
        log.debug("Inside @method updateEvent. @param: email -> {}, eventId -> {}, jsonBody -> {}", email, eventId, meetingRequestJson);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/events/%s", email, eventId);
        log.debug("URL to update event: {}", url);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

            // Step 1: Check if the event exists
            HttpGet getRequest = new HttpGet(url);
            getRequest.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());

            try (CloseableHttpResponse getResponse = httpClient.execute(getRequest)) {
                int getStatusCode = getResponse.getStatusLine().getStatusCode();

                if (getStatusCode != 200) {
                    // Event does not exist
                    log.warn("Event with ID {} not found for user {}", eventId, email);
                    return Map.of(EmailConstants.RESULT, EmailConstants.FAILED, "reason", "Event not found");
                }
            }

            // Step 2: If exists, update the event
            HttpPatch patchRequest = new HttpPatch(url);
            patchRequest.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            patchRequest.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(meetingRequestJson, ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8));
            patchRequest.setEntity(entity);

            try (CloseableHttpResponse patchResponse = httpClient.execute(patchRequest)) {
                int patchStatusCode = patchResponse.getStatusLine().getStatusCode();
                String responseBody = patchResponse.getEntity() != null ? EntityUtils.toString(patchResponse.getEntity()) : "";

                if (patchStatusCode == 200) {
                    JSONObject jsonObject = new JSONObject(responseBody);
                    EventDto eventDto = getEventDto(jsonObject);

                    Map<String, Object> map = new HashMap<>();
                    map.put(EmailConstants.RESULT, EmailConstants.SUCCESS);
                    map.put("updatedEventObject", new JSONObject(eventDto).toString());
                    return map;
                } else {
                    auditLog.error("Error while updating event", responseBody, responseBody, null, null, "UPDATE_MEETING_GRAPH", email, eventId, null, null, null, meetingRequestJson, null);
                    return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
                }
            }

        } catch (Exception e) {
            auditLog.error("Exception while updating event", e.getMessage(), getStackTraceAsString(e), null, null, "UPDATE_MEETING_GRAPH", email, eventId, null, null, null, meetingRequestJson, null);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }


    private static class AttachmentInfo {
        private final String contentType;
        private final String attachmentId;
        private final String name;
        private final String contentBytes;
        private final int nextIndex;

        AttachmentInfo(String contentType, String attachmentId, String name, String contentBytes, int nextIndex) {
            this.contentType = contentType;
            this.attachmentId = attachmentId;
            this.name = name;
            this.contentBytes = contentBytes;
            this.nextIndex = nextIndex;
        }

        // Getters
        public String getContentType() {
            return contentType;
        }

        public String getAttachmentId() {
            return attachmentId;
        }

        public String getName() {
            return name;
        }

        public String getContentBytes() {
            return contentBytes;
        }

        public int getNextIndex() {
            return nextIndex;
        }
    }

    private String getFileFormat(String name) {
        int indexOfDot = name.lastIndexOf(".");
        return (indexOfDot != -1) ? name.substring(indexOfDot + 1) : "";
    }


    private URI buildUrlToFindGroupByAlias(String alias) throws URISyntaxException {
        return new URIBuilder("https://graph.microsoft.com/v1.0/groups")
                .setParameter("$filter", "mail eq '" + alias + "'")
                .build();
    }

    private URI buildUrlToGetGroupMembers(String groupId) throws URISyntaxException {
        return new URIBuilder("https://graph.microsoft.com/v1.0/groups/" + groupId + "/members").build();
    }

    private List<String> getAllGroupMembers(String groupId) throws IOException {
        List<String> emailAddresses = new ArrayList<>();
        try {
            emailAddresses = new ArrayList<>();
            URI url = buildUrlToGetGroupMembers(groupId);
            while (url != null) {
                String response = executeGetRequest(url);
                url = extractMembersAndNextLink(response, emailAddresses);
            }
        } catch (Exception e) {
            log.error("Error getting members ", e);
        }
        return emailAddresses;
    }

    private URI extractMembersAndNextLink(String response, List<String> emailAddresses) throws IOException, URISyntaxException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        JsonNode valueNode = rootNode.path(EmailConstants.VALUE);
        if (valueNode.isArray()) {
            for (JsonNode memberNode : valueNode) {
                if (memberNode.has("mail")) {
                    emailAddresses.add(memberNode.path("mail").asText());
                }
            }
        }
        String nextLink = rootNode.path("@odata.nextLink").asText(null);
        return nextLink != null ? new URI(nextLink) : null;
    }


    private String getGroupIdByAlias(String alias) throws IOException {
        try {
            URI url = buildUrlToFindGroupByAlias(alias);
            String response = executeGetRequest(url);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(response);
            JsonNode valueNode = rootNode.path(EmailConstants.VALUE);
            if (valueNode.isArray() && !valueNode.isEmpty()) {
                return valueNode.get(0).path("id").asText();
            }
        } catch (Exception e) {
            log.error("Error getting GroupID ", e);
        }

        return "";
    }

    private String executeGetRequest(URI url) throws IOException, InterruptedException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                    return EntityUtils.toString(entity);
                } else if (entity != null) {
                    String errorMessage = EntityUtils.toString(entity);
                    log.error("Error while making GET request to Graph API. Exception message : {}", errorMessage);
                }
            }
        }
        return null;
    }

    /**
     * Gets emails of alias.
     *
     * @param alias the alias
     * @return the emails of alias
     * @throws IOException the io exception
     */
    public List<String> getEmailsOfAlias(String alias) throws IOException {
        String groupId = getGroupIdByAlias(alias);
        if (groupId != null) {
            return getAllGroupMembers(groupId);
        }
        return new ArrayList<>();
    }


    /**
     * Gets email by internet message id.
     *
     * @param email             the email
     * @param internetMessageId the internet message id
     * @param folderName        the folder name
     * @return the email by internet message id
     * @throws IOException          the io exception
     * @throws InterruptedException the interrupted exception
     * @throws URISyntaxException   the uri syntax exception
     */
    public UserEmailDto getEmailByInternetMessageId(String email, String internetMessageId, String folderName) throws IOException, InterruptedException, URISyntaxException {
        log.debug("Inside @method getEmailByInternetMessageId. @param: email -> {} internetMessageId -> {}", email, internetMessageId);

        URIBuilder uriBuilder = new URIBuilder(String.format("https://graph.microsoft.com/v1.0/users/%s/messages", email));
        String filter = "internetMessageId eq '" + internetMessageId + "'";
        uriBuilder.addParameter("$filter", filter);

        URI uri = uriBuilder.build();
        String url = uri.toString();
        log.debug("URL to get email by internetMessageId : {}", url);

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.PREFER, EmailConstants.OUTLOOK_BODY_CONTENT_TYPE_TEXT);
            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                    String result = EntityUtils.toString(entity);
                    List<UserEmailDto> response1 = convertJsonToUserEmailDto(result, email, true);
                    if (!response1.isEmpty()) return response1.getFirst();
                } else if (entity != null) {
                    String errorMessage = EntityUtils.toString(entity);
                    auditLog.error("Error while getting email by internetMessageId from graph", errorMessage, errorMessage, null, null, GRAPH_NETWORK_CALL, email, null, null, null, null, internetMessageId, null);
//                log.error("Error while getting emails from graph. Exception message : {}", errorMessage);
                }
            }
        }

        return null;
    }


    /**
     * Gets emails of user.
     *
     * @param userId           the user id
     * @param email            the email
     * @param receivedDateTime the received date time
     * @param categories       the categories
     * @param isRead           the is read
     * @param mailFolder       the mail folder
     * @param limit            the limit
     * @param offset           the offset
     * @return the emails of user
     * @throws IOException          the io exception
     * @throws InterruptedException the interrupted exception
     */
    public UserEmailResponseDto getEmailsOfUser(String userId, String email, String receivedDateTime, String categories, Boolean isRead, String mailFolder, Integer limit, Integer offset) throws IOException, InterruptedException {
        log.debug("Inside @method getEmailsOfUser. @param: email -> {}, receivedDateTime -> {}, categories -> {}, isRead -> {}, limit -> {}, offset -> {}",
                email, receivedDateTime, categories, isRead, limit, offset);
        String url = buildUrlWithFilterAndPagination(email, receivedDateTime, categories, isRead, mailFolder, limit, offset);
        log.debug("URL to get emails : {}", url);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.PREFER, EmailConstants.OUTLOOK_BODY_CONTENT_TYPE_TEXT);
            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                    String result = EntityUtils.toString(entity);
                    List<UserEmailDto> response1 = convertJsonToUserEmailDto(result, email, true);
                    List<List<UserEmailDto>> messages = groupByEmailConversation(response1);
                    UserEmailResponseDto responseDto = new UserEmailResponseDto();
                    responseDto.setMailBoxUserEmail(email);
                    responseDto.setMailBoxUserId(userId);
                    responseDto.setMessages(messages);
                    return responseDto;
                } else if (entity != null) {
                    String errorMessage = EntityUtils.toString(entity);
                    auditLog.error("Error while getting emails from graph", errorMessage, errorMessage + " Response Code " + response.getStatusLine().getStatusCode(), null, null, "GRAPH_EMAIL_POLL", email, null, null, null, null, receivedDateTime, null);
                    //                log.error("Error while getting emails from graph. Exception message : {}", errorMessage);
                    return null;
                }
            }
        }

        return null;
    }

    /**
     * Build url with filter and pagination .
     *
     * @param email            the email
     * @param receivedDateTime the received date time
     * @param categories       the categories
     * @param isRead           the is read
     * @param mailFolder       the mail folder
     * @param limit            the limit
     * @param offset           the offset
     * @return the string
     */
    String buildUrlWithFilterAndPagination(String email, String receivedDateTime, String categories, Boolean isRead, String mailFolder, Integer limit, Integer offset) {
        StringBuilder url = new StringBuilder("https://graph.microsoft.com/v1.0/users/")
                .append(email)
                .append("/mailFolders/")
                .append(mailFolder)
                .append("/messages?")
                .append("$filter=isDraft eq false");

//        receivedDateTime = getReceivedDateTimeFilter();

        if (isRead != null) {
            url.append(EmailConstants.AND).append("isRead eq ").append(isRead);
        }

        if (receivedDateTime != null) {
            url.append(EmailConstants.AND).append(receivedDateTime);
        }

        if (categories != null) {
            url.append(EmailConstants.AND);
            String[] categoryArray = categories.split(",");

            for (int i = 0; i < categoryArray.length; i++) {
                if (i == 0) {
                    url.append("(");
                    url.append("categories/any(c:c eq '").append(categoryArray[i].trim()).append("')");
                } else {
                    url.append(" or categories/any(c:c eq '").append(categoryArray[i].trim()).append("')");
                }
            }
            url.append(")");
        }

        if (limit != null && offset != null) {
            url.append("&$skip=").append(offset).append("&$top=").append(limit);
        }

        return url.toString().replace(" ", "%20");
    }

    /**
     * Flag email .
     *
     * @param email     the email
     * @param messageId the message id
     * @param flag      the flag
     * @return the string
     */
    public String flagEmail(String email, String messageId, String flag) {
        log.debug("Inside @method flagEmail. @param: messageId -> {}, flag -> {}", messageId, flag);

        String result = EmailConstants.SUCCESS;

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s", email, messageId);
        log.debug("URL to flag email : {}", url);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPatch request = new HttpPatch(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(flag);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                log.info("statusCode is {}", statusCode);
                if (statusCode == 200 || statusCode == 201) {
                    MailSummary message = mailSummaryDao.findByMessageId(messageId, email);
                    if (message != null && flag.equalsIgnoreCase("flagged")) {
                        message.setFlagStatus("Flagged");
                        mailSummaryDao.save(message);
                    } else if (message != null) {
                        message.setFlagStatus("notFlagged");
                        mailSummaryDao.save(message);
                    }
                } else {
                    result = EmailConstants.FAILED;
                    String errorMessage = EntityUtils.toString(response.getEntity());
                    auditLog.error("Error while setting flag from graph ", errorMessage, errorMessage, messageId, null, FLAG_GRAPH, email, null, null, null, null, flag, null);
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while setting flag from graph", e.getMessage(), getStackTraceAsString(e), messageId, null, FLAG_GRAPH, email, null, null, null, null, flag, null);
            result = EmailConstants.FAILED;
        }
        return result;
    }

    /**
     * Flag email using database ID.
     *
     * @param email     the email
     * @param databaseId the message id
     * @param flag      the flag
     * @return the string
     */
    public String flagEmail(String email, Integer databaseId, String flag) {
        log.debug("Inside @method flagEmail. @param: databaseId -> {}, flag -> {}", databaseId, flag);

        String result = EmailConstants.SUCCESS;
        MailSummary message = mailSummaryDao.findById(databaseId)
                .orElseThrow(() -> new ResourceNotFoundException("No database record found for the given id."));

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s", email, message.getMessageId());
        log.debug("URL to flag email : {}", url);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPatch request = new HttpPatch(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(flag);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                log.info("statusCode is {}", statusCode);
                if (statusCode == 200 || statusCode == 201) {
                    if (message != null && flag.equalsIgnoreCase("flagged")) {
                        message.setFlagStatus("Flagged");
                        mailSummaryDao.save(message);
                    } else if (message != null) {
                        message.setFlagStatus("notFlagged");
                        mailSummaryDao.save(message);
                    }
                } else {
                    result = EmailConstants.FAILED;
                    String errorMessage = EntityUtils.toString(response.getEntity());
                    auditLog.error("Error while setting flag from graph ", errorMessage, errorMessage, databaseId, null, FLAG_GRAPH, email, null, null, null, null, flag, null);
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while setting flag from graph", e.getMessage(), getStackTraceAsString(e), databaseId, null, FLAG_GRAPH, email, null, null, null, null, flag, null);
            result = EmailConstants.FAILED;
        }
        return result;
    }

    /**
     * Gets unread emails.
     *
     * @param email    the email
     * @param folderId the folder id
     * @return the unread emails
     */
    public List<UnReadEmail> getUnreadEmails(String email, String folderId) {
        log.debug("Inside get unread emails for userId {} and folderId {}", email, folderId);
        List<UnReadEmail> unReadEmails = new ArrayList<>();
        try {
            // Construct the API URL to fetch unread emails, including subject in the query
            String url = String.format("https://graph.microsoft.com/v1.0/users/%s/mailFolders/%s/messages?$filter=isRead eq false&$select=internetMessageId,isRead,subject",
                    email, folderId);
            url = url.replaceAll(" ", "%20");

            log.debug("URL to get unread emails: {}", url);

            // Create an HttpClient instance
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                // Create a GET request
                HttpGet request = new HttpGet(url);
                request.setHeader("Authorization", "Bearer " + tokenUtils.getAccessToken());
                request.setHeader("Prefer", "outlook.body-content-type='text'");

                // Execute the request
                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                        String result = EntityUtils.toString(entity);

                        // Parse the JSON response
                        JSONObject jsonObject = new JSONObject(result);
                        JSONArray messages = jsonObject.getJSONArray("value");

                        // Prepare list to store unread emails

                        // Loop through each message
                        for (int i = 0; i < messages.length(); i++) {
                            JSONObject message = messages.getJSONObject(i);

                            // Extract the internetMessageId, isRead status, and subject
                            String internetMessageId = message.getString("internetMessageId");
                            boolean isUnread = !message.getBoolean("isRead");
                            String subject = message.has("subject") ? message.getString("subject") : "";

                            // Create UnReadEmail object and set values
                            UnReadEmail unreadEmail = new UnReadEmail();
                            unreadEmail.setInternetMessageId(internetMessageId);
                            unreadEmail.setIsUnread(isUnread);
                            unreadEmail.setSubject(subject);

                            // Add it to the result list
                            unReadEmails.add(unreadEmail);
                        }

                        return unReadEmails;
                    } else if (entity != null) {
                        String errorMessage = EntityUtils.toString(entity);
                        log.error("Error while getting unread emails: {}", errorMessage);
                    }
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while getting deleted emails from graph", e.getMessage(), getStackTraceAsString(e), null, null, "ERROR_GETTING_UNREAD_EMAILS", email, null, null, null, null, null, null);

        }

        return unReadEmails;
    }

    /**
     * Gets deleted emails of user.
     *
     * @param email      the email
     * @param dateFilter the date filter
     * @return the deleted emails of user
     * @throws IOException          the io exception
     * @throws InterruptedException the interrupted exception
     */
    public List<UserEmailDto> getDeletedEmailsOfUser(String email, String dateFilter) throws IOException, InterruptedException {
        log.debug("Inside @method getDeletedEmailsOfUser. @param: email -> {}", email);
//        String url = buildUrlWithFilterAndPagination(email, receivedDateTime, categories, isRead, "DeletedItems", limit, offset);
        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/mailFolders/DeletedItems/messages?$filter=lastModifiedDateTime ge %s&$select=subject,internetMessageId&$top=100&$orderby=lastModifiedDateTime desc", email, dateFilter);
        url = url.replaceAll(" ", "%20");

        log.debug("URL to get deleted emails : {}", url);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.PREFER, EmailConstants.OUTLOOK_BODY_CONTENT_TYPE_TEXT);
            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                    String result = EntityUtils.toString(entity);

                    JSONObject jsonObject = new JSONObject(result);
                    JSONArray messages = jsonObject.getJSONArray(EmailConstants.VALUE);
                    List<UserEmailDto> resultList = new ArrayList<>();

                    for (int i = 0; i < messages.length(); i++) {
                        JSONObject message = messages.getJSONObject(i);

                        String id = message.getString("id");
                        String internetMessageId = message.getString("internetMessageId");

                        String subject;
                        try {
                            subject = message.getString(EmailConstants.SUBJECT);
                        } catch (JSONException e) {
                            subject = "";
                        }

                        UserEmailDto userEmailDto = new UserEmailDto();
                        userEmailDto.setId(id);
                        userEmailDto.setSubject(subject);
                        userEmailDto.setInternetMessageId(internetMessageId);

                        resultList.add(userEmailDto);
                    }
                    return resultList;
                } else if (entity != null) {
                    String errorMessage = EntityUtils.toString(entity);
                    auditLog.error("Error while getting deleted emails from graph", errorMessage, errorMessage, null, null, "DELETE_EMAILS_SCHEDULER", email, null, null, null, null, null, null);
                    //                log.error("Error while getting emails from graph. Exception message : {}", errorMessage);
                }
            }
        }

        return null;
    }

    /**
     * Gets flag status.
     *
     * @param messageId the message id
     * @return the flag status
     * @throws IOException          the io exception
     * @throws InterruptedException the interrupted exception
     */
    public String getFlagStatus(String messageId) throws IOException, InterruptedException {
        log.debug("Inside @method getFlagStatus. @param: messageId -> {}", messageId);

        UserInfo currentUser = userContextHolder.getCurrentUser();
        String email = currentUser.getEmail();

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s?$select=flag", email, messageId);
        log.debug("URL to getFlagStatus : {}", url);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.PREFER, EmailConstants.OUTLOOK_BODY_CONTENT_TYPE_TEXT);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();

                if (entity != null && statusCode == 200) {
                    String result = EntityUtils.toString(entity);
                    JSONObject jsonObject = new JSONObject(result);
                    return jsonObject.getJSONObject("flag").getString("flagStatus");
                } else {
                    String errorMessage = EntityUtils.toString(response.getEntity());
                    auditLog.error("Error while getFlagStatus from graph", errorMessage, errorMessage, messageId, null, FLAG_STATUS_GRAPH, email, null, null, null, null, null, null);
//                    log.error("Error inside @method getFlagStatus. Exception message -> {}", errorMessage);
                }
                return EmailConstants.FAILED;
            }
        }
    }

    /**
     * Sets category.
     *
     * @param email     the email
     * @param messageId the message id
     * @param category  the category
     * @return the category
     * @throws Exception the exception
     */
    public String setCategory(String email, String messageId, String category) throws Exception {
        log.debug("Inside @method setCategory. @param: email -> {}, messageId -> {}, category -> {}", email, messageId, category);

        EmailUser details = emailUserDao.findByEmail(email);
        if (details == null) throw new ResourceNotFoundException(EmailConstants.EMAIL_DOES_NOT_EXIST_IN_OUR_STRING);
        String userId = details.getUserId();

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s", userId, messageId);
        log.debug("URL to setCategory : {}", url);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPatch request = new HttpPatch(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(category);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 200) {
                    return EmailConstants.SUCCESS;
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                auditLog.error("Error while getFlagStatus from graph", errorMessage, errorMessage, messageId, null, SET_CATEGORY_GRAPH, email, null, null, null, null, category, null);
//                log.error("Error inside @method setCategory. Exception message -> {}", errorMessage);
                return EmailConstants.FAILED;
            }
        }
    }

    /**
     * Sets category v 1.
     *
     * @param email      the email
     * @param messageId  the message id
     * @param categories the categories
     * @return the category v 1
     * @throws Exception the exception
     */
    public String setCategoryV1(String email, String messageId, List<String> categories) throws Exception {
        String category1 = categories.get(0);
//        String category2 = categories.get(1);
        String categoryPayload = String.format("{\"categories\":[\"%s\"]}", category1);
        return setCategory(email, messageId, categoryPayload);
    }

    //getAvailbleSlotsAndConflict


    /**
     * Gets available meeting slots.
     *
     * @param emails        the emails
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @param slotDuration  the slot duration
     * @return the available meeting slots
     */
    public List<Meeting> getAvailableMeetingSlots(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        log.debug("Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
        try {
            if (startDateTime == null || endDateTime == null) {
                String email = userContextHolder.getCurrentUser().getEmail();
                EmailUser user = emailUserDao.findByEmail(email);
                String userId = user.getUserId();
                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
                startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
                endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
            }

            Map<String, List<Meeting>> result = new HashMap<>();
            for (String email : emails) {
                List<EventDto> calendarEvents = getCalendarEventsV1(email, startDateTime, endDateTime,null);
//                List<EventDto> calendarEvents = new ArrayList<>();
                // Handle case where calendarEvents is empty
                if (calendarEvents == null || calendarEvents.isEmpty()) {
                    log.debug("No calendar events found for email: {}", email);
                    result.put(email, new ArrayList<>());  // Add empty list for this email
                    continue;  // Skip to the next email
                }
                List<Meeting> list = new ArrayList<>();
                for (EventDto eventDto : calendarEvents) {
                    Meeting meeting = new Meeting();
                    meeting.setStartTime(eventDto.getMeetingStartTime());
                    meeting.setEndTime(eventDto.getMeetingEndTime());
                    list.add(meeting);
                }
                result.put(email, list);
            }
            Date start = convertStringToDate(startDateTime);
            Date end = convertStringToDate(endDateTime);
            long durationMillis = (long) slotDuration * 60 * 1000;
            return findAvailableTimeSlots(result, start, end, durationMillis);
        } catch (Exception e) {
            auditLog.error("Error inside @method getAvailableSlot", e.getMessage(), getStackTraceAsString(e), null, null, AVAILABLE_SLOT_GRAPH, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
        }
        return null;
    }

    /**
     * Gets available meeting slots oof.
     *
     * @param emails        the emails
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @param slotDuration  the slot duration
     * @return the available meeting slots oof
     */
    public List<Meeting> getAvailableMeetingSlotsOOF(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        log.debug("Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
        try {
            if (startDateTime == null || endDateTime == null) {
                String email = userContextHolder.getCurrentUser().getEmail();
                EmailUser user = emailUserDao.findByEmail(email);
                String userId = user.getUserId();
                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
                startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
                endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
            }

            Map<String, List<Meeting>> result = new HashMap<>();
            for (String email : emails) {
                OutOfOfficeDto outOfOfficeHours = getOutOfOfficeHours(email);
                List<EventDto> calendarEvents = getCalendarEventsV1(email, startDateTime, endDateTime,null);
//                List<EventDto> calendarEvents = new ArrayList<>();
                // Handle case where calendarEvents is empty
                if (calendarEvents == null || calendarEvents.isEmpty()) {
                    log.debug("No calendar events found for email: {}", email);
                    result.put(email, addOutOfOfficeToMeeting(new ArrayList<>(), outOfOfficeHours));  // Add empty list for this email
                    continue;  // Skip to the next email
                }
                List<Meeting> list = new ArrayList<>();
                for (EventDto eventDto : calendarEvents) {
                    Meeting meeting = new Meeting();
                    meeting.setStartTime(eventDto.getMeetingStartTime());
                    meeting.setEndTime(eventDto.getMeetingEndTime());
                    list.add(meeting);
                }

                list = addOutOfOfficeToMeeting(list, outOfOfficeHours);

                result.put(email, list);
            }
            Date start = convertStringToDate(startDateTime);
            Date end = convertStringToDate(endDateTime);
            long durationMillis = (long) slotDuration * 60 * 1000;
            return findAvailableTimeSlots(result, start, end, durationMillis);
        } catch (Exception e) {
            auditLog.error("Error inside @method getAvailableSlot", e.getMessage(), getStackTraceAsString(e), null, null, AVAILABLE_SLOT_GRAPH, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
        }
        return new ArrayList<>();
    }

    /**
     * Gets available slots and conflict.
     *
     * @param emails        the emails
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @param slotDuration  the slot duration
     * @return the available slots and conflict
     * @throws Exception the exception
     */
    public AvailableSlots getAvailableSlotsAndConflict(List<String> emails, String startDateTime, String endDateTime, int slotDuration) throws Exception {
        log.debug("Inside @method getAvailableSlotsAndConflict. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
        String[] dateTimeRange = setupDateTimeRange(startDateTime, endDateTime);
        startDateTime = dateTimeRange[0];
        endDateTime = dateTimeRange[1];
        Map<String, List<Meeting>> result = fetchCalendarEventsForEmails(emails, startDateTime, endDateTime);
        Date start = convertStringToDate(startDateTime);
        Date end = convertStringToDate(endDateTime);
        long durationMillis = (long) slotDuration * 60 * 1000;
        AvailableSlots availableSlots = new AvailableSlots();
        availableSlots.setAvailableSlots(findAvailableTimeSlots(result, start, end, durationMillis));
        availableSlots.setConflictMeeting(findUnavailableTimeSlots(result, start, end));
        availableSlots.setOutOfOffice(getOutOfOfficeDetails(result, start, end));
        return availableSlots;
    }

    private List<OutOfOffice> getOutOfOfficeDetails(Map<String, List<Meeting>> result, Date start, Date end) {
        List<OutOfOffice> outOfOffice = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"); // Assuming ISO 8601 format

        try {
            for (Map.Entry<String, List<Meeting>> entry : result.entrySet()) {
                String key = entry.getKey();
                //  List<Meeting> meetings = entry.getValue();
                Map<String, Object> userSettings = getAutoReplySettingsForUser(key);
                log.info("userSettings is {}", userSettings);
                if (userSettings != null) {
                    log.info("userSettings not null");
                    OutOfOffice office = convertToOutOfOffice(key, userSettings);
                    Date oooStart = null;
                    Date oooEnd = null;
                    if (office.getStatus() != null && office.getStatus().equalsIgnoreCase("Enabled") && office.getStartDateTime() != null && office.getEndDateTime() != null) {
                        oooStart = dateFormat.parse(office.getStartDateTime());
                        oooEnd = dateFormat.parse(office.getEndDateTime());
                    } else {
                        EmailPreferences emailPreferences = preferencesDao.getEmailPreferencesByUserId(key);
                        oooStart = convertLocalTimeToDate(emailPreferences.getCheckin());
                        oooEnd = convertLocalTimeToDate(emailPreferences.getCheckout());
                    }
                    if ((oooStart.before(end) || oooStart.equals(end)) && (oooEnd.after(start) || oooEnd.equals(start)))
                        outOfOffice.add(office);
                }
            }
        } catch (Exception e) {
            log.error("error inside getAutoReplySettingsForUser", e);
        }
        return outOfOffice;
    }

    private String[] setupDateTimeRange(String startDateTime, String endDateTime) throws Exception {
        if (startDateTime == null || endDateTime == null) {
            String email = userContextHolder.getCurrentUser().getEmail();
            EmailUser user = emailUserDao.findByEmail(email);
            String userId = user.getUserId();
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
            startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
            endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
        }
        return new String[]{startDateTime, endDateTime};
    }

    private Map<String, List<Meeting>> fetchCalendarEventsForEmails(List<String> emails, String startDateTime, String endDateTime) throws Exception {
        Map<String, List<Meeting>> result = new HashMap<>();
        for (String email : emails) {
            List<EventDto> calendarEvents = getCalendarEvents(email, startDateTime, endDateTime, true);
            if (calendarEvents == null || calendarEvents.isEmpty()) {
                log.debug("No calendar events found for email: {}", email);
                result.put(email, new ArrayList<>());
                continue;
            }
            List<Meeting> meetings = convertEventDtosToMeetings(calendarEvents);
            result.put(email, meetings);
        }
        return result;
    }

    private List<Meeting> convertEventDtosToMeetings(List<EventDto> eventDtos) {
        List<Meeting> meetings = new ArrayList<>();
        for (EventDto eventDto : eventDtos) {
            Meeting meeting = new Meeting();
            meeting.setStartTime(eventDto.getMeetingStartTime());
            meeting.setEndTime(eventDto.getMeetingEndTime());
            meetings.add(meeting);
        }
        return meetings;
    }

    /**
     * Gets available meeting slots v2.
     *
     * @param emails        the emails
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @param slotDuration  the slot duration
     * @return the available meeting slots v 2
     */
    public List<AvailableTimeSlots> getAvailableMeetingSlotsV2(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        log.debug("Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
        try {

            Map<String, List<Meeting>> result = new HashMap<>();
            for (String email : emails) {
                OutOfOfficeDto outOfOfficeHours = getOutOfOfficeHours(email);
                List<EventDto> calendarEvents = getCalendarEventsV1(email, startDateTime, endDateTime,null);
//                List<EventDto> calendarEvents = new ArrayList<>();
                // Handle case where calendarEvents is empty
                if (calendarEvents == null || calendarEvents.isEmpty()) {
                    log.debug("No calendar events found for email: {}", email);
                    result.put(email, addOutOfOfficeToMeeting(new ArrayList<>(), outOfOfficeHours));   // Add empty list for this email
                    continue;  // Skip to the next email
                }
                List<Meeting> list = new ArrayList<>();
                for (EventDto eventDto : calendarEvents) {
                    Meeting meeting = new Meeting();
                    meeting.setStartTime(eventDto.getMeetingStartTime());
                    meeting.setEndTime(eventDto.getMeetingEndTime());
                    list.add(meeting);
                }
                list = addOutOfOfficeToMeeting(list, outOfOfficeHours);
                result.put(email, list);
            }
            Date start = convertStringToDate(startDateTime);
            Date end = convertStringToDate(endDateTime);
            return getAvailableSlots(result, emails, start, end, slotDuration);
        } catch (Exception e) {
            auditLog.error("Error inside @method getAvailableSlot", e.getMessage(), getStackTraceAsString(e), null, null, AVAILABLE_SLOT_GRAPH, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
        }
        return null;
    }

    /**
     * Gets available slots.
     *
     * @param userMeetings      the user meetings
     * @param users             the users
     * @param startRange        the start range
     * @param endRange          the end range
     * @param durationInMinutes the duration in minutes
     * @return the available slots
     */
    public static List<AvailableTimeSlots> getAvailableSlots(
            Map<String, List<Meeting>> userMeetings,
            List<String> users, Date startRange, Date endRange, int durationInMinutes) {

        List<AvailableTimeSlots> availableSlotsList = new ArrayList<>();
        Calendar cal = Calendar.getInstance();

        // Start from the startRange time
        cal.setTime(startRange);

        while (cal.getTime().before(endRange)) {
            Date slotStartTime = cal.getTime();
            cal.add(Calendar.MINUTE, durationInMinutes);  // Add duration to start time
            Date slotEndTime = cal.getTime();

            List<String> available = new ArrayList<>();
            List<String> unavailable = new ArrayList<>();

            // Check each user for availability in this slot
            for (String user : users) {
                List<Meeting> meetings = userMeetings.getOrDefault(user, new ArrayList<>());

                if (isUserAvailable(meetings, slotStartTime, slotEndTime)) {
                    available.add(user);
                } else {
                    unavailable.add(user);
                }
            }

            // Create an AvailableSlots object for this time slot
            availableSlotsList.add(new AvailableTimeSlots(slotStartTime, slotEndTime, available, unavailable));
        }

        return availableSlotsList;
    }


    private static boolean isUserAvailable(List<Meeting> meetings, Date slotStartTime, Date slotEndTime) {
        for (Meeting meeting : meetings) {
            // Check if the meeting overlaps with the slot
            if (meeting.getStartTime().before(slotEndTime) && meeting.getEndTime().after(slotStartTime)) {
                // There is an overlap with an existing meeting
                return false;
            }
        }
        return true;  // No overlapping meetings
    }


    /**
     * Find available time slots list.
     *
     * @param meetings              the meetings
     * @param from                  the from
     * @param till                  the till
     * @param meetingDurationMillis the meeting duration millis
     * @return the list
     */
    List<Meeting> findAvailableTimeSlots(Map<String, List<Meeting>> meetings, Date from, Date till, long meetingDurationMillis) {
        List<Meeting> availableSlots = new ArrayList<>();
        // Merge all meetings into one list and sort by start time
        List<Meeting> allMeetings = new ArrayList<>();
        for (List<Meeting> userMeetings : meetings.values()) {
            allMeetings.addAll(userMeetings);
        }
        allMeetings.sort(Comparator.comparing(Meeting::getStartTime));
        // Initialize the currentStart to the 'from' date
        Date currentStart = from;
        for (Meeting meeting : allMeetings) {
            // Check if the gap between currentStart and the next meeting's startTime is enough
            if (meeting.getStartTime().after(currentStart)) {
                long gap = meeting.getStartTime().getTime() - currentStart.getTime();
                while (gap >= meetingDurationMillis) {
                    Date potentialEnd = new Date(currentStart.getTime() + meetingDurationMillis);

                    // Check if currentStart or potentialEnd goes beyond 'till'
                    if (currentStart.after(till) || potentialEnd.after(till)) {
                        break; // Exit if it exceeds 'till'
                    }
                    // Add the slot if valid
                    availableSlots.add(new Meeting(currentStart, potentialEnd));

                    // Move to the next slot
                    currentStart = potentialEnd;

                    // Recalculate the gap for the next iteration
                    gap = meeting.getStartTime().getTime() - currentStart.getTime();
                }
            }

            // Move currentStart to the end of the current meeting if it's later
            if (meeting.getEndTime().after(currentStart)) {
                currentStart = meeting.getEndTime();
            }
        }
        // Check the time slot after the last meeting until 'till' time
        while (currentStart.before(till) && (till.getTime() - currentStart.getTime() >= meetingDurationMillis)) {
            Date potentialEnd = new Date(currentStart.getTime() + meetingDurationMillis);
            if (potentialEnd.after(till)) {
                break;
            }
            availableSlots.add(new Meeting(currentStart, potentialEnd));
            currentStart = potentialEnd;
        }

        return availableSlots;
    }

    private Date convertStringToDate(String dateTimeString) {
        Instant instant = Instant.parse(dateTimeString);
        return Date.from(instant);
    }


    /**
     * Gets calendar events.
     *
     * @param email           the email
     * @param startDateTime   the start date time
     * @param endDateTime     the end date time
     * @param forAvailability the for availability
     * @return the calendar events
     * @throws Exception the exception
     */
    public List<EventDto> getCalendarEvents(String email, String startDateTime, String endDateTime, boolean forAvailability) throws Exception {
        log.debug("Inside @method getCalendarEvents. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", email, startDateTime, endDateTime);
        if (!EmailUtils.checkDomain(email, orgDomainName)) return new ArrayList<>();

        if (!allowOutsiders) {
            boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
            if (!userExists) return new ArrayList<>();
        }

        List<EventDto> eventDtos = new ArrayList<>();
        EmailUser user = emailUserDao.findByEmail(email);
        if (user == null) {
            log.warn("No user found for email: {}", email);
            return eventDtos; // Return an empty list
        }
        String userId = user.getUserId();
        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
        if (startDateTime == null || endDateTime == null) {
            startDateTime = DateUtils.getFormattedDateTime(false, forAvailability ? "UTC" : preferences.getTimeZone());
            endDateTime = DateUtils.getFormattedDateTime(true, forAvailability ? "UTC" : preferences.getTimeZone());
        }

        String url;
        url = String.format("https://graph.microsoft.com/v1.0/users/%s/calendarView?startdatetime=%s&enddatetime=%s&$filter=isCancelled eq false&$top=100",
                email, startDateTime, endDateTime);
        log.debug("URL to getCalendarEvents : {}", url);

        url = url.replace(" ", "%20").replace("'", "%27");

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
            if (!forAvailability)
                request.setHeader(EmailConstants.PREFER, "outlook.timezone=\"" + preferences.getTimeZone() + "\"");

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();

                if (statusCode == 404) {
                    throw new ResourceNotFoundException("User " + email + " is invalid.");
                }
                // Extract the response entity
                HttpEntity entity = response.getEntity();
                if (entity != null && statusCode == 200) {
                    // Convert the entity content to a String
                    String responseBody = EntityUtils.toString(entity);

                    return convertJsonToEventDto(responseBody);
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while getting calendar events from graph", e.getMessage(), getStackTraceAsString(e), null, null, GET_CALENDER_EVENT_GRAPH, email, null, null, null, null, Map.of("startDateTime", startDateTime, "endDateTime", endDateTime), null);
        }
        return new ArrayList<>();

    }

    /**
     * Gets calendar events v1.
     *
     * @param email         the email
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @return the calendar events v 1
     * @throws URISyntaxException the uri syntax exception
     */
    public List<EventDto> getCalendarEventsV1(String email, String startDateTime, String endDateTime,String subject) throws URISyntaxException {
        log.debug("Inside @method getCalendarEventsV1. @param: email -> {} startDateTime -> {}, endDateTime -> {}", email, startDateTime, endDateTime);
        if (!EmailUtils.checkDomain(email, orgDomainName)) return new ArrayList<>();

        if (!allowOutsiders) {
            boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
            if (!userExists) return new ArrayList<>();
        }

        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);

        if (startDateTime != null && endDateTime != null && !startDateTime.endsWith("Z")) {
            startDateTime = DateUtils.convertToUTCString(startDateTime, preferences.getTimeZone());
            endDateTime = DateUtils.convertToUTCString(endDateTime, preferences.getTimeZone());
        }

        if (startDateTime == null || endDateTime == null) {
            startDateTime = DateUtils.getUTCDateTime(false, preferences.getTimeZone());
            endDateTime = DateUtils.getUTCDateTime(true, preferences.getTimeZone());
        }

        URIBuilder uriBuilder = new URIBuilder(String.format("https://graph.microsoft.com/v1.0/users/%s/calendarView", email));
        uriBuilder.addParameter("$top", meetingLimit);
        uriBuilder.addParameter("startdatetime", startDateTime);
        uriBuilder.addParameter("enddatetime", endDateTime);
        if (subject != null && !subject.isEmpty()) {
            uriBuilder.addParameter(
                    "filter",
                    String.format("contains(subject,'%s')", subject.toLowerCase())
            );
        }

        URI uri = uriBuilder.build();
        String url = uri.toString();

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();

                // Extract the response entity
                HttpEntity entity = response.getEntity();
                if (entity != null && statusCode == 200) {
                    // Convert the entity content to a String
                    String responseBody = EntityUtils.toString(entity);

                    return convertJsonToEventDto(responseBody);
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while getting calendar events from graph", e.getMessage(), getStackTraceAsString(e), null, null, GET_CALENDER_EVENT_GRAPH, email, null, null, null, null, Map.of("startDateTime", startDateTime, "endDateTime", endDateTime), null);
        }
        return new ArrayList<>();
    }

    /**
     * Gets calendar event by id.
     *
     * @param email     the email
     * @param messageId the message id
     * @return the calendar event by id
     */
    public List<EventDto> getCalendarEventById(String email, String messageId) {
        log.debug("Inside @method getEventDetailsByMeetingRequest. @param: email -> {}, messageId -> {}", email, messageId);

        try {
            // Build the URI to fetch the eventMessage by its ID
            URIBuilder uriBuilder = new URIBuilder(String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s", email, messageId));
            URI uri = uriBuilder.build();
            String url = uri.toString();
            log.debug("URL to get event message: {}", url);

            // Create an HttpClient instance and send a GET request to fetch the email
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet request = new HttpGet(url);
                request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
                request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

                // Execute the request
                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                        String responseBody = EntityUtils.toString(entity);
                        JSONObject emailJson = new JSONObject(responseBody);

                        // Check if this is a meeting request
                        if (emailJson.has("meetingMessageType") || emailJson.has("meetingRequestType")) {
                            // Extract key event details
                            String subject = emailJson.getString("subject");
                            String startDateTime = emailJson.getJSONObject("startDateTime").getString("dateTime");
                            String endDateTime = emailJson.getJSONObject("endDateTime").getString("dateTime");
//                            String location = emailJson.getJSONObject("location").getString("displayName");

                            // Now use these details to search for the actual event
                            return getEventDetailsBySubjectAndTime(email, subject, startDateTime, endDateTime);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error while fetching calendar events", e);
            throw new BusinessException(String.format("Unable to fetch calendar event for email: %s with messageId: %s", email, messageId));
        }
        return new ArrayList<>();
    }

    /**
     * Gets event details by subject and time.
     *
     * @param email         the email
     * @param subject       the subject
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @return the event details by subject and time
     */
    public List<EventDto> getEventDetailsBySubjectAndTime(String email, String subject, String startDateTime, String endDateTime) {
        log.debug("Fetching event details for subject: {}, start: {}, end: {}", subject, startDateTime, endDateTime);

        try {
            // Build the URI to search for the event in the user's calendar using subject and time filter
            URIBuilder uriBuilder = new URIBuilder(String.format("https://graph.microsoft.com/v1.0/users/%s/calendar/events", email));

            // Add filters for time, subject, and location
            uriBuilder.addParameter("$filter", String.format("(start/dateTime ge '%s' and end/dateTime le '%s' and subject eq '%s')", startDateTime, endDateTime, subject));
            URI uri = uriBuilder.build();
            String url = uri.toString();
            log.debug("URL to fetch event details: {}", url);

            // Create an HttpClient instance and send a GET request
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet request = new HttpGet(url);
                request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
                request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

                // Execute the request
                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                        String responseBody = EntityUtils.toString(entity);
                        return convertJsonToEventDto(responseBody); // Convert response to EventDto list
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error while fetching event details", e);
            throw new BusinessException(String.format("Unable to fetch event details for subject: %s", subject));
        }
        return new ArrayList<>();
    }


    /**
     * Gets message details.
     *
     * @param email the email
     * @param id    the id
     * @return the message details
     * @throws Exception the exception
     */
    public String getMessageDetails(String email, String id) throws Exception {
        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s?$select=subject", email, id);
        log.debug("URL to getEmailSubjectById : {}", url);
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                    String responseBody = EntityUtils.toString(entity);
                    log.info("responseBody is {}", responseBody);
                    JSONObject jsonObject = new JSONObject(responseBody);
                    if (jsonObject.has(EmailConstants.SUBJECT)) {
                        return jsonObject.getString(EmailConstants.SUBJECT);
                    } else {
                        return null;
                    }
                } else {
                    String errorMessage = EntityUtils.toString(response.getEntity());
                    log.error("Failed to get email. Status code: {} Message : {}", response.getStatusLine().getStatusCode(), errorMessage);
                    throw new BusinessException(errorMessage);
                }
            }
        }
    }


    /**
     * Gets email subject by id.
     *
     * @param email the email
     * @param id    the id
     * @return the email subject by id
     * @throws Exception the exception
     */
    public String getEmailSubjectById(String email, String id) throws Exception {
        log.debug("Inside @method getEmailById. @param: email -> {}, id -> {}", email, id);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s?$select=subject", email, id);
        log.debug("URL to getEmailSubjectById : {}", url);

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                    String responseBody = EntityUtils.toString(entity);
                    JSONObject jsonObject = new JSONObject(responseBody);
                    if (jsonObject.has(EmailConstants.SUBJECT)) {
                        return jsonObject.getString(EmailConstants.SUBJECT);
                    } else {
                        return null;
                    }
                } else {
                    String errorMessage = EntityUtils.toString(response.getEntity());
                    log.error("Failed to get email. Status code: {} Message : {}", response.getStatusLine().getStatusCode(), errorMessage);
                    throw new BusinessException(errorMessage);
                }
            }
        }
    }

    /**
     * Accept meeting map.
     *
     * @param eventId the event id
     * @return the map
     * @throws Exception the exception
     */
    public Map<String, String> acceptMeeting(String eventId) throws Exception {
        Map<String, String> responseMap = new HashMap<>();
        try {
            log.debug("Inside @method acceptMeeting. @param : eventId -> {}", eventId);
            String email = userContextHolder.getCurrentUser().getEmail();
            String acceptUrl = String.format("https://graph.microsoft.com/v1.0/users/%s/events/%s/accept", email, eventId);
            log.debug("URL to acceptMeeting : {}", acceptUrl);
            sendResponse(acceptUrl, null);

            responseMap.put("result", EmailConstants.SUCCESS);

        } catch (Exception e) {
            auditLog.error("Error inside @method accept meeting", e.getMessage(), getStackTraceAsString(e), eventId, null, ACCEPT_MEETING_GRAPH, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
            responseMap.put("result", EmailConstants.FAILED);
        }
        return responseMap;
    }

    /**
     * Decline meeting map.
     *
     * @param eventId the event id
     * @return the map
     * @throws Exception the exception
     */
    public Map<String, String> declineMeeting(String eventId) throws Exception {
        Map<String, String> responseMap = new HashMap<>();
        try {
            log.debug("Inside @method declineMeeting. @param : eventId -> {}", eventId);
            String email = userContextHolder.getCurrentUser().getEmail();
            String declineUrl = String.format("https://graph.microsoft.com/v1.0/users/%s/events/%s/decline", email, eventId);
            log.debug("URL to declineMeeting : {}", declineUrl);
            sendResponse(declineUrl, null);
            responseMap.put("result", EmailConstants.SUCCESS);
        } catch (Exception e) {
            auditLog.error("Error inside @method decline meeting", e.getMessage(), getStackTraceAsString(e), eventId, null, DECLINE_MEETING_GRAPH, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
            responseMap.put("result", EmailConstants.FAILED);
        }
        return responseMap;
    }

    /**
     * Tentatively accept map.
     *
     * @param eventId the event id
     * @return the map
     * @throws Exception the exception
     */
    public Map<String, String> tentativelyAccept(String eventId) throws Exception {
        Map<String, String> responseMap = new HashMap<>();
        try {
            String email = userContextHolder.getCurrentUser().getEmail();
            String url = String.format("https://graph.microsoft.com/v1.0/users/%s/events/%s/tentativelyAccept", email, eventId);
            log.debug("URL to tentativelyAccept : {}", url);

            String proposedNewTime = "{\"comment\": \"Meeting is tentatively accepted.\",\"sendResponse\":true}";

            sendResponse(url, proposedNewTime);
            responseMap.put("result", EmailConstants.SUCCESS);
        } catch (Exception e) {
            auditLog.error("Error inside @method decline meeting", e.getMessage(), getStackTraceAsString(e), eventId, null, TENTATIVE_MEETING_ACCEPT_GRAPH, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
            responseMap.put("result", EmailConstants.FAILED);
        }
        return responseMap;
    }

    private void sendResponse(String url, String proposedNewTime) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            if (proposedNewTime != null) {
                StringEntity entity = new StringEntity(proposedNewTime);
                request.setEntity(entity);
            }

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 202) {
                    log.debug("Meeting response sent successfully.");
                } else {
                    log.error("Failed to send meeting response. Status code: {}", response.getStatusLine().getStatusCode());
                    throw new BusinessException(EntityUtils.toString(response.getEntity()));
                }
            }
        }
    }

    /**
     * Schedule event.
     *
     * @param email    the email
     * @param jsonBody the json body
     * @return the string
     */
    public Map<String,Object> scheduleEvent(String email, String jsonBody) {
        log.debug("Inside @method scheduleEvent. @param: email -> {}, jsonBody -> {}", email, jsonBody);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/events", email);
        log.debug("URL to scheduleEvent : {}", url);

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(jsonBody, ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8));

            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 201) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject jsonObject = new JSONObject(responseBody);
                    EventDto eventDto= getEventDto(jsonObject);
                    Map<String,Object> map=new HashMap<>();
                    map.put(EmailConstants.RESULT ,EmailConstants.SUCCESS);
                    map.put("scheduleEventObject",new JSONObject(eventDto).toString());
                    return map;
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                auditLog.error("Error while scheduling event", errorMessage, errorMessage, null, null, "SCHEDULE_MEETING_GRAPH", email, null, null, null, null, jsonBody, null);
//                log.error("Error inside @method scheduleEvent. Ex message: {}", errorMessage);
                return Map.of(EmailConstants.RESULT,EmailConstants.FAILED);
            }
        } catch (Exception e) {
            auditLog.error("Error while scheduling event ", e.getMessage(), getStackTraceAsString(e), null, null, "SCHEDULE_MEETING_GRAPH", email, null, null, null, null, jsonBody, null);
            return Map.of(EmailConstants.RESULT,EmailConstants.FAILED);
        }
    }

    /**
     * Create draft reply.
     *
     * @param email       the email
     * @param messageId   the message id
     * @param content     the content
     * @param sendDraft   the send draft
     * @param type        the type
     * @param contentType the content type
     * @return the string
     * @throws Exception the exception
     */
    public String createDraftReply(String email, String messageId, String content, boolean sendDraft, String type, String contentType) throws Exception {
        log.debug("Inside @method createDraftReply. @param: email -> {}, messageId -> {}, content -> {}, sendDraft -> {} type -> {}", email, messageId, content, sendDraft, type);

        // Choose the appropriate URL based on the type
        String url;
        if (type != null && type.equalsIgnoreCase("reply")) {
            url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/createReply", email, messageId);
        } else {
            url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/createReplyAll", email, messageId);
        }

        log.debug("URL to createDraftReply : {}", url);

        JSONObject replyObject = createReplyObject(content, contentType);

        String valueAsString = replyObject.toString();

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(valueAsString);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 201 || statusCode == 202) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject responseJson = new JSONObject(responseBody);
                    String draftMessageId = responseJson.getString("id");
                    // If sendDraft is true, send the draft immediately
                    if (sendDraft) {
                        return sendDraftEmail(httpClient, email, draftMessageId);
                    }
                    // Return the draft message ID if not sending immediately
                    return draftMessageId;
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                auditLog.error("Error while drafting reply", errorMessage, errorMessage, messageId, null, DRAFT_REPLY_GRAPH, email, null, null, null, null, Map.of("url", url, "content", content), null);
//                log.error("Error inside @method createDraftReply. Exception message -> {}", errorMessage);
                return EmailConstants.FAILED;
            } catch (Exception e) {
                auditLog.error("Error while drafting reply", e.getMessage(), getStackTraceAsString(e), messageId, null, DRAFT_REPLY_GRAPH, email, null, null, null, null, Map.of("url", url, "content", content), null);
                return EmailConstants.FAILED;
            }
        }
    }


    /**
     * Create draft forward.
     *
     * @param email        the email
     * @param messageId    the message id
     * @param comment      the comment
     * @param toRecipients the to recipients
     * @param ccRecipients the cc recipients
     * @param sendDraft    the send draft
     * @return the string
     * @throws Exception   the exception
     */
    public String createDraftForward(String email, String messageId, String comment, String toRecipients, String ccRecipients, boolean sendDraft) throws Exception {
        log.debug("Inside @method createDraftForward. @param: email -> {}, messageId -> {}, comment -> {}, toRecipients -> {}, ccRecipients -> {}, sendDraft -> {}",
                email, messageId, comment, toRecipients, ccRecipients, sendDraft);

        JSONObject forwardMessage = new JSONObject();
        forwardMessage.put("comment", comment);

        JSONArray toArray = new JSONArray();
        if (toRecipients != null && !toRecipients.isEmpty()) {
            for (String recipient : toRecipients.split(",")) {
                JSONObject toRecipient = new JSONObject();
                toRecipient.put("emailAddress", new JSONObject().put("address", recipient.trim()));
                toArray.put(toRecipient);
            }
        }
        forwardMessage.put("toRecipients", toArray);

        JSONArray ccArray = new JSONArray();
        if (ccRecipients != null && !ccRecipients.isEmpty()) {
            for (String ccRecipient : ccRecipients.split(",")) {
                JSONObject ccRecipientObj = new JSONObject();
                ccRecipientObj.put("emailAddress", new JSONObject().put("address", ccRecipient.trim()));
                ccArray.put(ccRecipientObj);
            }
        }
        forwardMessage.put("ccRecipients", ccArray);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/createForward", email, messageId);
        log.debug("URL to createDraftForward: {}", url);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(forwardMessage.toString());
            request.setEntity(entity);


            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 201) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject responseJson = new JSONObject(responseBody);
                    String draftMessageId = responseJson.getString("id");

                    if (sendDraft) {
                        return sendDraftEmail(httpClient, email, draftMessageId);
                    }

                    log.debug("Forward message saved as draft for email: {}, messageId: {}", email, messageId);
                    return EmailConstants.SUCCESS;
                } else {
                    String errorMessage = EntityUtils.toString(response.getEntity());
                    auditLog.error("Error while creating draft forward", errorMessage, errorMessage, messageId, null, FORWARD_GRAPH, email, null, null, null, null, Map.of("url", url, "comment", comment), null);
                    return EmailConstants.FAILED;
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while creating draft forward", e.getMessage(), getStackTraceAsString(e), messageId, null, FORWARD_GRAPH, email, null, null, null, null, Map.of("url", url, "comment", comment), null);
            return EmailConstants.FAILED;
        }
    }


    private String sendDraftEmail(CloseableHttpClient httpClient, String userId, String draftMessageId) throws Exception {
        String sendUrl = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/send", userId, draftMessageId);
        log.debug("URL to sendDraftEmail : {}", sendUrl);

        // Create a POST request to send the draft
        HttpPost sendRequest = new HttpPost(sendUrl);
        sendRequest.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
        sendRequest.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

        // Execute the request to send the email
        try (CloseableHttpResponse sendResponse = httpClient.execute(sendRequest)) {
            int sendStatusCode = sendResponse.getStatusLine().getStatusCode();
            if (sendStatusCode == 202) {
                return EmailConstants.SUCCESS;
            }
            String sendErrorMessage = EntityUtils.toString(sendResponse.getEntity());
            log.error("Error sending draft email. Status code -> {}, Error message -> {}", sendStatusCode, sendErrorMessage);
            return EmailConstants.FAILED;
        } catch (Exception e) {
            log.error("Error inside @method sendDraftEmail. Exception message -> {}", e.getMessage());
            return EmailConstants.FAILED;
        }
    }

    /**
     * Create draft.
     *
     * @param emailOfUser the email of user
     * @param subject     the subject
     * @param content     the content
     * @param toEmail     the to email
     * @param ccEmail     the cc email
     * @param bccEmail    the bcc email
     * @param sendDraft   the send draft
     * @return the string
     */
    public String createDraft(String emailOfUser, String subject, String content, String toEmail, String ccEmail, String bccEmail, Boolean sendDraft) {
        log.debug("Inside @method createDraft. @param: emailOfUser -> {}, subject -> {}, content -> {}, toEmail -> {}, ccEmail -> {}, bccEmail -> {}",
                emailOfUser, subject, content, toEmail, ccEmail, bccEmail);

        EmailUser details = emailUserDao.findByEmail(emailOfUser);
        if (details == null) throw new ResourceNotFoundException(EmailConstants.EMAIL_DOES_NOT_EXIST_IN_OUR_STRING);
        String userId = details.getUserId();

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages", userId);
        log.debug("URL to createDraft : {}", url);

        // Create JSON payload
        JSONObject jsonBody = new JSONObject();
        jsonBody.put(EmailConstants.SUBJECT, subject);

        JSONObject bodyContent = new JSONObject();
        bodyContent.put(EmailConstants.CONTENTTYPE, "HTML");
        bodyContent.put(EmailConstants.CONTENT, content);
        jsonBody.put("body", bodyContent);

        // Add toRecipients
        JSONArray toRecipients = new JSONArray();
        for (String email : toEmail.split(",")) {
            JSONObject emailObject = new JSONObject();
            emailObject.put(EmailConstants.ADDRESS, email.trim());
            JSONObject recipient = new JSONObject();
            recipient.put(EmailConstants.EMAIL_ADDRESS, emailObject);
            toRecipients.put(recipient);
        }
        jsonBody.put("toRecipients", toRecipients);

        // Add ccRecipients
        if (ccEmail != null && !ccEmail.isEmpty()) {
            JSONArray ccRecipients = new JSONArray();
            for (String email : ccEmail.split(",")) {
                JSONObject emailObject = new JSONObject();
                emailObject.put(EmailConstants.ADDRESS, email.trim());
                JSONObject recipient = new JSONObject();
                recipient.put(EmailConstants.EMAIL_ADDRESS, emailObject);
                ccRecipients.put(recipient);
            }
            jsonBody.put("ccRecipients", ccRecipients);
        }

        // Add bccRecipients
        if (bccEmail != null && !bccEmail.isEmpty()) {
            JSONArray bccRecipients = new JSONArray();
            for (String email : bccEmail.split(",")) {
                JSONObject emailObject = new JSONObject();
                emailObject.put(EmailConstants.ADDRESS, email.trim());
                JSONObject recipient = new JSONObject();
                recipient.put(EmailConstants.EMAIL_ADDRESS, emailObject);
                bccRecipients.put(recipient);
            }
            jsonBody.put("bccRecipients", bccRecipients);
        }
//        jsonBody.put("saveToSentItems", true);

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a POST request
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(jsonBody.toString());
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 201) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject responseJson = new JSONObject(responseBody);
                    String draftMessageId = responseJson.getString("id");
                    if (sendDraft) {
                        return sendDraftEmail(httpClient, emailOfUser, draftMessageId);
                    }
                    return EmailConstants.SUCCESS;
                } else {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    auditLog.error("Error while scheduling event", responseBody, responseBody, null, null, CREATE_DRAFT_GRAPH, emailOfUser, subject, null, null, null, jsonBody, null);
                    return EmailConstants.FAILED;
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while creating draft", e.getMessage(), getStackTraceAsString(e), null, null, CREATE_DRAFT_GRAPH, emailOfUser, subject, null, null, null, content, null);
            return EmailConstants.FAILED;
        }
    }


    /**
     * Create draft with attachment.
     *
     * @param emailOfUser     the email of user
     * @param subject         the subject
     * @param content         the content
     * @param toEmail         the to email
     * @param ccEmail         the cc email
     * @param bccEmail        the bcc email
     * @param isDraft         the is draft
     * @param attachmentPaths the attachment paths
     * @return the string
     */
    public String createDraftWithAttachment(String emailOfUser, String subject, String content, String toEmail, String ccEmail, String bccEmail, Boolean isDraft, String attachmentPaths) {
        log.debug("Inside @method createDraft. @param: emailOfUser -> {}, subject -> {}, content -> {}, toEmail -> {}, ccEmail -> {}, bccEmail -> {}, attachmentPaths -> {}",
                emailOfUser, subject, content, toEmail, ccEmail, bccEmail, attachmentPaths);

        EmailUser details = emailUserDao.findByEmail(emailOfUser);
        List<String> successfullyAttachedFiles = new ArrayList<>();
        if (details == null) throw new ResourceNotFoundException(EmailConstants.EMAIL_DOES_NOT_EXIST_IN_OUR_STRING);
        String userId = details.getUserId();

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages", userId);
        log.debug("URL to createDraft : {}", url);

        JSONObject jsonBody = new JSONObject();
        jsonBody.put(EmailConstants.SUBJECT, subject);

        JSONObject bodyContent = new JSONObject();
        bodyContent.put(EmailConstants.CONTENTTYPE, "HTML");
        bodyContent.put(EmailConstants.CONTENT, content);
        jsonBody.put("body", bodyContent);

        JSONArray toRecipients = createRecipientArray(toEmail);
        jsonBody.put("toRecipients", toRecipients);

        if (ccEmail != null && !ccEmail.isEmpty()) {
            JSONArray ccRecipients = createRecipientArray(ccEmail);
            jsonBody.put("ccRecipients", ccRecipients);
        }

        if (bccEmail != null && !bccEmail.isEmpty()) {
            JSONArray bccRecipients = createRecipientArray(bccEmail);
            jsonBody.put("bccRecipients", bccRecipients);
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(jsonBody.toString());
            request.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 201) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject responseJson = new JSONObject(responseBody);
                    String draftMessageId = responseJson.getString("id");
                    List<String> attachmentList = CommonUtils.getListFromCommaSeparatedString(attachmentPaths);
                    // Add attachments if any
                    if (attachmentList != null && !attachmentList.isEmpty()) {
                        addAttachmentsToDraftFromS3(draftMessageId, attachmentList, userId, httpClient, successfullyAttachedFiles);
                    }

                    if (!isDraft) {
                        deleteAttachedFiles(successfullyAttachedFiles);
                        return sendDraftEmail(httpClient, emailOfUser, draftMessageId);
                    }
                    deleteAttachedFiles(successfullyAttachedFiles);
                    return EmailConstants.SUCCESS;
                } else {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    auditLog.error("Error while scheduling event", responseBody, responseBody, null, null, CREATE_DRAFT_GRAPH, emailOfUser, subject, null, null, null, jsonBody, null);
                    return EmailConstants.FAILED;
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while creating draft", e.getMessage(), getStackTraceAsString(e), null, null, CREATE_DRAFT_GRAPH, emailOfUser, subject, null, null, null, content, null);
            return EmailConstants.FAILED;
        }
    }

    private JSONArray createRecipientArray(String emailList) {

        JSONArray recipientsArray = new JSONArray();
        try {
            for (String email : emailList.split(",")) {
                JSONObject emailObject = new JSONObject();
                emailObject.put(EmailConstants.ADDRESS, email.trim());
                JSONObject recipient = new JSONObject();
                recipient.put(EmailConstants.EMAIL_ADDRESS, emailObject);
                recipientsArray.put(recipient);
            }
        } catch (JSONException e) {
            log.error("Error creating JSON objects: {}", ExceptionUtils.getStackTraceAsString(e));
        } catch (Exception e) {
            log.error("An error occurred:  {}", ExceptionUtils.getStackTraceAsString(e));
        }
        return recipientsArray;
    }


    private void deleteAttachedFiles(List<String> attachmentPathsToDelete) {
        //   String s3BucketName = "emailAttachments";
        for (String attachmentPath : attachmentPathsToDelete) {
            try {
                s3Service.deleteFileFromS3(s3BucketName, attachmentPath);
                log.info("Successfully deleted attachment from S3: {}", attachmentPath);
            } catch (Exception e) {
                log.error("Failed to delete attachment from S3: {}", attachmentPath, e);
            }
        }
    }


    private void addAttachmentsToDraftFromS3(String draftMessageId, List<String> attachmentPaths, String userId, CloseableHttpClient httpClient, List<String> successfullyAttachedFiles) throws IOException, MessagingException {
        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/attachments", userId, draftMessageId);
        //    String s3BucketName = "emailAttachments";

        for (String s3FilePath : attachmentPaths) {
            byte[] fileContent = s3Service.downloadFileAsBytes(s3BucketName, s3FilePath);

            JSONObject attachmentJson = new JSONObject();
            attachmentJson.put("@odata.type", "#microsoft.graph.fileAttachment");

            String fileName = Paths.get(s3FilePath).getFileName().toString();
            attachmentJson.put("name", fileName);

            String base64Content = Base64.getEncoder().encodeToString(fileContent);
            attachmentJson.put("contentBytes", base64Content);
            attachmentJson.put("contentType", "application/octet-stream");

            HttpPost attachmentRequest = new HttpPost(url);
            try {
                attachmentRequest.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            attachmentRequest.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity attachmentEntity = new StringEntity(attachmentJson.toString());
            attachmentRequest.setEntity(attachmentEntity);

            try (CloseableHttpResponse attachmentResponse = httpClient.execute(attachmentRequest)) {
                if (attachmentResponse.getStatusLine().getStatusCode() == 201) {
                    successfullyAttachedFiles.add(s3FilePath);
                } else {
                    String responseBody = EntityUtils.toString(attachmentResponse.getEntity());
                    log.error("Failed to add attachment: {}", responseBody);
                }
            }
        }
    }


    /**
     * Create master category.
     *
     * @param email    the email
     * @param jsonBody the json body
     * @return the string
     * @throws Exception the exception
     */
    public String createMasterCategory(String email, String jsonBody) throws Exception {
        log.debug("Inside @method createMasterCategory. @param: email -> {}, jsonBody -> {}", email, jsonBody);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/outlook/masterCategories", email);
        log.debug("URL to createMasterCategory : {}", url);

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(jsonBody);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 201) {
                    return EmailConstants.SUCCESS;
                } else if (response.getStatusLine().getStatusCode() == 409) {
                    return "Category name already exists";
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                log.error("Error inside @method createMasterCategory. Exception message -> {}", errorMessage);
                return EmailConstants.FAILED;
            }
        }
    }

    /**
     * Create categories for all users.
     *
     * @return the string
     * @throws Exception the exception
     */
    public String createCategoriesForAllUsers() throws Exception {
        log.debug("Inside @method createCategoriesForAllUsers");

        List<EmailUser> list = emailUserDao.findAll();
        List<String> emailList = list.stream().map(EmailUser::getEmail).toList();
        String[] categories = {"{\"displayName\": \"Attention\",\"color\": \"preset0\"}",
                "{\"displayName\": \"Event\",\"color\": \"preset8\"}",
                "{\"displayName\": \"High\",\"color\": \"preset9\"}",
                "{\"displayName\": \"Medium\",\"color\": \"preset3\"}",
                "{\"displayName\": \"Low\",\"color\": \"preset2\"}",
                "{\"displayName\": \"FYI\",\"color\": \"preset5\"}\n"};
        for (String email : emailList) {

            for (String category : categories) {
                String response = createMasterCategory(email, category);
                if (!Objects.equals(response, EmailConstants.SUCCESS)) {
                    log.debug("Error inside @method createCategoriesForAllUsers for user {}. Exception message : {}", email, response);
                }
            }
        }
        return EmailConstants.SUCCESS;
    }


    private Date getReminderTime(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);
        return getDateFromJSONObject(jsonObject, "reminderDateTime");
    }

    /**
     * Reply .
     *
     * @param email     the email
     * @param messageId the message id
     * @param content   the content
     * @return the string
     * @throws Exception the exception
     */
    public String reply(String email, String messageId, String content) throws Exception {
        log.debug("Inside @method reply. @param: email -> {}, messageId -> {}, content -> {}", email, messageId, content);

        EmailUser details = emailUserDao.findByEmail(email);
        if (details == null) throw new ResourceNotFoundException(EmailConstants.EMAIL_DOES_NOT_EXIST_IN_OUR_STRING);
        String userId = details.getUserId();

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/replyAll", userId, messageId);
        log.debug("URL to reply : {}", url);

        JSONObject replyObject = createReplyObject(content, "HTML");

        String valueAsString = replyObject.toString();

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(valueAsString);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 202) {
                    return EmailConstants.SUCCESS;
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                auditLog.error("Error inside @method reply", errorMessage, errorMessage, messageId, null, CREATE_REPLY_GRAPH, email, null, null, null, null, content, null);
//                log.error("Error inside @method reply. Exception message: {}", errorMessage);
                return EmailConstants.FAILED;
            }
        } catch (Exception e) {
            auditLog.error("Error inside @method reply", e.getMessage(), getStackTraceAsString(e), messageId, null, CREATE_REPLY_GRAPH, email, null, null, null, null, content, null);
            return EmailConstants.FAILED;
        }
    }

    private JSONObject createReplyObject(String content, String contentType) {

        JSONObject body = new JSONObject();
        if (contentType == null || contentType.isEmpty()) contentType = "HTML";
        body.put(EmailConstants.CONTENTTYPE, contentType);
        body.put(EmailConstants.CONTENT, content);

        JSONObject message = new JSONObject();
        message.put("body", body);

        JSONObject mainObject = new JSONObject();
        mainObject.put("message", message);
        mainObject.put("saveToSentItems", true);

        return mainObject;
    }

    /**
     * Gets reminders.
     *
     * @param email          the email
     * @param todoTaskListId the todo task list id
     * @return the reminders
     * @throws Exception the exception
     */
    public List<TaskDto> getReminders(String email, String todoTaskListId) throws Exception {
        log.debug("Inside @method getTodoList. @param: email -> {}, todoTaskListId -> {}", email, todoTaskListId);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/todo/lists/%s/tasks", email, todoTaskListId);
        log.debug("URL to getReminders : {}", url);

        // Create an HttpClient instance
        CloseableHttpClient httpClient = HttpClients.createDefault();

        // Create a GET request
        HttpGet request = new HttpGet(url);
        request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());

        // Execute the request
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                // Return the response body as a String
                String jsonString = EntityUtils.toString(entity);
                return convertJsonToTaskDto(jsonString);
            }
        }
        return null;
    }

    /**
     * Gets message id of first email in conversation.
     *
     * @param email          the email
     * @param conversationId the conversation id
     * @return the message id of first email in conversation
     */
    public String getMessageIdOfFirstEmailInConversation(String email, String conversationId) {
        log.debug("Inside @method getMessageIdOfFirstEmailInConversation. @param: email -> {} conversationId -> {}", email, conversationId);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages?$filter=conversationId eq '%s'&$top=1&select=conversationId", email, conversationId);
        log.debug("URL to getMessageIdOfFirstEmailInConversation : {}", url);

        url = url.replace(" ", "%20").replace("'", "%27");

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                int statusCode = response.getStatusLine().getStatusCode();
                if (entity != null && statusCode == 200) {
                    String responseBody = EntityUtils.toString(entity);
                    JSONObject jsonObject = new JSONObject(responseBody);
                    JSONArray value = jsonObject.getJSONArray(EmailConstants.VALUE);
                    return value.getJSONObject(0).getString("id");
                } else {
                    String errorMessage = EntityUtils.toString(entity);
                    auditLog.error("Error inside @method getMessageIdOfFirstEmailInConversation", errorMessage, errorMessage, null, null, GRAPH_NETWORK_CALL, email, null, conversationId, null, null, null, null);
//                    log.error("Failed to get getMessageIdOfFirstEmailInConversation. ErrorMessage : {} Status code: {}", errorMessage, statusCode);
                    return null;
                }
            }
        } catch (Exception e) {
            auditLog.error("Error inside @method getMessageIdOfFirstEmailInConversation", e.getMessage(), getStackTraceAsString(e), null, null, GRAPH_NETWORK_CALL, email, null, conversationId, null, null, null, null);
            return EmailConstants.FAILED;
        }
    }

    /**
     * Gets email content by message id.
     *
     * @param email     the email
     * @param messageId the message id
     * @return the email content by message id
     * @throws Exception the exception
     */
    public String getEmailContentByMessageId(String email, String messageId) throws Exception {
        log.debug("Inside @method getEmailContentByMessageId. @param: email -> {} messageId -> {}", email, messageId);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s?select=body", email, messageId);
        log.debug("URL to getEmailContentByMessageId : {}", url);

        url = url.replace(" ", "%20").replace("'", "%27");

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
            request.setHeader(EmailConstants.PREFER, EmailConstants.OUTLOOK_BODY_CONTENT_TYPE_TEXT);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                int statusCode = response.getStatusLine().getStatusCode();
                if (entity != null && statusCode == 200) {
                    String responseBody = EntityUtils.toString(entity);
                    JSONObject jsonObject = new JSONObject(responseBody);
                    return jsonObject.getJSONObject("body").getString(EmailConstants.CONTENT);
                } else {
                    String errorMessage = EntityUtils.toString(entity);
                    log.error("Failed to get getEmailContentByMessageId. ErrorMessage : {} Status code: {}", errorMessage, statusCode);
                    return null;
                }
            }
        }
    }

    /**
     * Gets message wrapper by message id.
     *
     * @param email     the email
     * @param messageId the message id
     * @return the message wrapper by message id
     * @throws Exception the exception
     */
    public MessageWrapper getMessageWrapperByMessageId(String email, String messageId) throws Exception {
        log.debug("Inside @method getMessageWrapperByMessageId. @param: email -> {} messageId -> {}", email, messageId);
        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s", email, messageId);
        log.debug("URL to getMessageWrapperByMessageId : {}", url);
        url = url.replace(" ", "%20").replace("'", "%27");
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
            request.setHeader(EmailConstants.PREFER, "outlook.body-content-type=\"html\"");
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                int statusCode = response.getStatusLine().getStatusCode();
                if (entity != null && statusCode == 200) {
                    String responseBody = EntityUtils.toString(entity);
                    JSONObject jsonObject = new JSONObject(responseBody);
                    MessageWrapper messageWrapper = GraphUtils.parseEmailContent(jsonObject);
                    messageWrapper.setUserId(email);
                    return messageWrapper;
                } else {
                    String errorMessage = EntityUtils.toString(entity);
                    log.error("Failed to get getEmailContentByMessageId. ErrorMessage : {} Status code: {}", errorMessage, statusCode);
                    return null;
                }
            }
        }
    }


    /**
     * Gets todo categories.
     *
     * @param email the email
     * @return the todo categories
     * @throws Exception the exception
     */
    public List<Map<String, String>> getTodoCategories(String email) throws Exception {
        log.debug("Inside @method getTodoCategories. @param: email -> {}", email);

        EmailUser details = emailUserDao.findByEmail(email);
        if (details == null) throw new ResourceNotFoundException(EmailConstants.EMAIL_DOES_NOT_EXIST_IN_OUR_STRING);
        String userId = details.getUserId();

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/todo/lists", userId);
        log.debug("URL to getTodoCategories : {}", url);

        // Create an HttpClient instance
        CloseableHttpClient httpClient = HttpClients.createDefault();

        // Create a GET request
        HttpGet request = new HttpGet(url);
        request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());

        // Execute the request
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                // Return the response body as a String
                String jsonString = EntityUtils.toString(entity);
                return convertJsonToTodoTaskListMap(jsonString);
            }
        }
        return null;
    }

    private static final String DEFAULT_TIME_ZONE = "Indian Standard Time";

    /**
     * Cancel auto reply for user.
     *
     * @param userEmail the user email
     * @throws Exception the exception
     */
    public void cancelAutoReplyForUser(String userEmail) throws Exception {

        log.debug("Inside @method cancelAutoReplyForUser. @param: userEmail -> {}", userEmail);

        // Prepare the URL
        String url = String.format(GRAPH_API_MAILBOX_SETTINGS, userEmail);
        log.debug("URL for canceling auto-reply: {}", url);

        // Prepare the JSON request body to disable auto-reply
        String jsonRequest = prepareCancelAutoReplyJson();

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

            // Create a PATCH request
            HttpPatch request = new HttpPatch(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            // Set the request body
            StringEntity entity = new StringEntity(jsonRequest);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 200) {
                    log.info("Successfully disabled auto-reply for user: {}", userEmail);
                } else {
                    String errorResponse = EntityUtils.toString(response.getEntity());
                    log.error("Error disabling auto-reply for user: {}. Status code: {}. Response: {}", userEmail, statusCode, errorResponse);
                    throw new Exception("Failed to disable auto-reply for user: " + userEmail);
                }
            }
        }
    }

    private String prepareCancelAutoReplyJson() {
        return "{ \"automaticRepliesSetting\": { \"status\": \"disabled\" } }";
    }

    /**
     * Gets auto reply settings for user.
     *
     * @param userEmail the user email
     * @return the auto reply settings for user
     * @throws Exception the exception
     */
    public Map<String, Object> getAutoReplySettingsForUser(String userEmail) throws Exception {
        if (!EmailUtils.checkDomain(userEmail, orgDomainName)) return null;
        log.debug("Inside @method getAutoReplySettingsForUser. @param: userEmail -> {}", userEmail);
        // Prepare the URL
        String url = String.format(GRAPH_API_MAILBOX_SETTINGS, userEmail);
        log.debug("URL to get mailbox settings: {}", url);

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 200) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        String jsonString = EntityUtils.toString(entity);
                        return parseAutoReplySettings(jsonString);
                    }
                } else {
                    String errorResponse = EntityUtils.toString(response.getEntity());
                    log.error("Error fetching mailbox settings for user: {}. Status code: {}. Response: {}", userEmail, statusCode, errorResponse);
                    throw new Exception("Failed to fetch mailbox settings for user: " + userEmail);
                }
            }
        }

        return Collections.emptyMap();
    }

    public Map<String, Map<String, String>> getOutOfOffice(List<String> emails) throws Exception {
        log.debug("Inside @method getOutOfOffice. @param: emails -> {}", emails);

        Map<String, Map<String, String>> result = new HashMap<>();
        for (String email : emails) {
            Map<String, Object> settings = null;
            try {
                settings = getAutoReplySettingsForUser(email);
            } catch (Exception e) {
                settings = new HashMap<>();
                settings.put("status", "error");
            }

            if (settings == null) {
                result.put(email, Map.of("error", "something went wrong"));
                continue;
            }

            String status = (String) settings.get("status");
            switch (status) {
                case "scheduled" -> {
                    String start = (String) settings.get("scheduledStartDateTime");
                    String end = (String) settings.get("scheduledEndDateTime");
                    Map<String, String> map = Map.of("start", start, "end", end);
                    result.put(email, map);
                }
                case "disabled" -> result.put(email, Map.of());
                case "error" -> result.put(email, Map.of("error", "something went wrong"));
            }
        }
        return result;
    }
    /**
     * Gets out of office hours.
     *
     * @param userEmail the user email
     * @return the out of office hours
     */
    public OutOfOfficeDto getOutOfOfficeHours(String userEmail) {
        log.debug("inside @method getOutOfOfficeHours. @param : userEmail -> {}", userEmail);
        Map<String, Object> autoReplySettings = null;
        try {
            autoReplySettings = getAutoReplySettingsForUser(userEmail);
        } catch (Exception e) {
            log.error("Error inside @method getOutOfOfficeHours", e);
        }

        if (autoReplySettings == null || autoReplySettings.get("status").equals("disabled")) return null;

        String start = (String) autoReplySettings.get("scheduledStartDateTime");
        String end = (String) autoReplySettings.get("scheduledEndDateTime");
        String timeZone = (String) autoReplySettings.get("scheduledEndTimeZone");

        Date startTime = parseDateFromString(start, timeZone);
        Date endTime = parseDateFromString(end, timeZone);
        OutOfOfficeDto outOfOfficeDto = new OutOfOfficeDto();
        outOfOfficeDto.setOutOfOfficeStart(startTime);
        outOfOfficeDto.setOutOfOfficeEnd(endTime);
        return outOfOfficeDto;
    }

    private Map<String, Object> parseAutoReplySettings(String jsonString) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        // Using Jackson library to parse the JSON response
        Map<String, Object> responseMap = objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {
        });
        // Extract relevant fields from the JSON response
        Map<String, Object> autoReplySettings = new HashMap<>();
        // Get the automaticRepliesSetting block
        if (responseMap.containsKey("automaticRepliesSetting")) {
            Map<String, Object> automaticRepliesSetting = (Map<String, Object>) responseMap.get("automaticRepliesSetting");

            autoReplySettings.put(EmailConstants.STATUS, automaticRepliesSetting.get(EmailConstants.STATUS));
            autoReplySettings.put("internalReplyMessage", automaticRepliesSetting.get("internalReplyMessage"));
            autoReplySettings.put("externalReplyMessage", automaticRepliesSetting.get("externalReplyMessage"));
            autoReplySettings.put("externalAudience", automaticRepliesSetting.get("externalAudience"));

            // Get scheduledStartDateTime and scheduledEndDateTime if available
            if (automaticRepliesSetting.containsKey(EmailConstants.SCHEDULED_START_DATE_TIME)) {
                Map<String, String> startDateTime = (Map<String, String>) automaticRepliesSetting.get(EmailConstants.SCHEDULED_START_DATE_TIME);
                autoReplySettings.put(EmailConstants.SCHEDULED_START_DATE_TIME, startDateTime.get(EmailConstants.DATE_TIME));
                autoReplySettings.put("scheduledStartTimeZone", startDateTime.get("timeZone"));
            }

            if (automaticRepliesSetting.containsKey(EmailConstants.SCHEDULED_END_DATE_TIME)) {
                Map<String, String> endDateTime = (Map<String, String>) automaticRepliesSetting.get(EmailConstants.SCHEDULED_END_DATE_TIME);
                autoReplySettings.put(EmailConstants.SCHEDULED_END_DATE_TIME, endDateTime.get(EmailConstants.DATE_TIME));
                autoReplySettings.put("scheduledEndTimeZone", endDateTime.get("timeZone"));
            }
        }

        // Return the parsed data
        return autoReplySettings;
    }


    /**
     * Sets auto reply for user.
     *
     * @param userEmail              the user email
     * @param internalReplyMessage   the internal reply message
     * @param externalReplyMessage   the external reply message
     * @param scheduledStartDateTime the scheduled start date time
     * @param scheduledEndDateTime   the scheduled end date time
     * @param timeZone               the time zone
     * @throws Exception the exception
     */
    public void setAutoReplyForUser(String userEmail, String internalReplyMessage, String externalReplyMessage,
                                    String scheduledStartDateTime, String scheduledEndDateTime, String timeZone) throws Exception {

        log.debug("Inside @method setAutoReplyForUser. @param: userEmail -> {}", userEmail);

        // If timeZone is null, default to "Indian Standard Time"
        if (timeZone == null || timeZone.isEmpty()) {
            timeZone = DEFAULT_TIME_ZONE;
        }

        // Prepare the URL
        String url = String.format(GRAPH_API_MAILBOX_SETTINGS, userEmail);
        log.debug("URL for setting auto reply: {}", url);

        // Prepare the JSON request body
        String jsonRequest = prepareAutoReplyJson(internalReplyMessage, externalReplyMessage, scheduledStartDateTime, scheduledEndDateTime, timeZone);

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

            // Create a PATCH request
            HttpPatch request = new HttpPatch(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            // Set the request body
            StringEntity entity = new StringEntity(jsonRequest);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 200) {
                    log.info("Successfully set auto-reply settings for user: {}", userEmail);
                } else {
                    String errorResponse = EntityUtils.toString(response.getEntity());
                    log.error("Error setting auto-reply for user: {}. Status code: {}. Response: {}", userEmail, statusCode, errorResponse);
                    throw new Exception("Failed to set auto-reply settings for user: " + userEmail);
                }
            }
        }
    }

    /**
     * Prepare auto reply json.
     *
     * @param internalReplyMessage   the internal reply message
     * @param externalReplyMessage   the external reply message
     * @param scheduledStartDateTime the scheduled start date time
     * @param scheduledEndDateTime   the scheduled end date time
     * @param timeZone               the time zone
     * @return the string
     */
    public String prepareAutoReplyJson(String internalReplyMessage, String externalReplyMessage,
                                       String scheduledStartDateTime, String scheduledEndDateTime, String timeZone) {
        JSONObject json = new JSONObject();

        // Create the automaticRepliesSetting object
        JSONObject automaticRepliesSetting = new JSONObject();
        automaticRepliesSetting.put("status", "scheduled");  // You can change this value if needed
        automaticRepliesSetting.put("externalAudience", "all");
        automaticRepliesSetting.put("internalReplyMessage", internalReplyMessage);
        automaticRepliesSetting.put("externalReplyMessage", externalReplyMessage);

        // Handle scheduled start and end date/time
        if (scheduledStartDateTime != null && scheduledEndDateTime != null) {
            JSONObject startDateTime = new JSONObject();
            startDateTime.put("dateTime", scheduledStartDateTime);
            startDateTime.put("timeZone", timeZone);

            JSONObject endDateTime = new JSONObject();
            endDateTime.put("dateTime", scheduledEndDateTime);
            endDateTime.put("timeZone", timeZone);

            automaticRepliesSetting.put("scheduledStartDateTime", startDateTime);
            automaticRepliesSetting.put("scheduledEndDateTime", endDateTime);
        } else {
            automaticRepliesSetting.put("scheduledStartDateTime", JSONObject.NULL);
            automaticRepliesSetting.put("scheduledEndDateTime", JSONObject.NULL);
        }

        // Add automaticRepliesSetting to the main JSON object
        json.put("automaticRepliesSetting", automaticRepliesSetting);

        // Convert the JSON object to string and return
        return json.toString();
    }

    /**
     * Gets users mail folders.
     *
     * @param email the email
     * @return the users mail folders
     * @throws Exception the exception
     */
    public List<Map<String, String>> getUsersMailFolders(String email) throws Exception {
        log.debug("Inside @method getUsersMailFolders. @param: email -> {}", email);

        // Base URL for retrieving mail folders
        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/mailFolders?$top=100", email);
        log.debug("URL to getUsersMailFolders : {}", url);

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a list to hold all folders
            List<Map<String, String>> allFolders = new ArrayList<>();

            // Fetch top-level folders
            fetchFoldersRecursively(url, email, httpClient, allFolders);

            return allFolders;
        } catch (Exception e) {
            auditLog.error("Error inside @method get user folders", e.getMessage(), getStackTraceAsString(e), null, null, USER_FOLDER_GRAPH, userContextHolder.getCurrentUser().getId(), null, null, null, null, null, null);
        }
        return null;
    }

    private void fetchFoldersRecursively(String url, String email, CloseableHttpClient httpClient, List<Map<String, String>> allFolders) throws Exception {
        HttpGet request = new HttpGet(url);
        request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            HttpEntity entity = response.getEntity();
            int statusCode = response.getStatusLine().getStatusCode();
            if (entity != null && statusCode == 200) {
                // Parse the response
                String jsonString = EntityUtils.toString(entity);
                List<Map<String, String>> folders = parseMailFolderResponse(jsonString);
                allFolders.addAll(folders);

                // Fetch child folders for each folder
                for (Map<String, String> folder : folders) {
                    if (folder.get("displayName").equals("Deleted Items")) {
                        continue;
                    }
                    String folderId = folder.get("id");
                    String childFoldersUrl = String.format("https://graph.microsoft.com/v1.0/users/%s/mailFolders/%s/childFolders?$top=100", email, folderId);
                    fetchFoldersRecursively(childFoldersUrl, email, httpClient, allFolders);
                }
            } else {
                String errorResponse = EntityUtils.toString(entity);
                log.error("Error inside @method fetchFoldersRecursively. Exception message : {} statusCode : {}", errorResponse, statusCode);
            }
        }
    }


    /**
     * Delete message by id boolean.
     *
     * @param userEmail  the user email
     * @param messageId  the message id
     * @param softDelete the soft delete
     * @return the boolean
     */
    public boolean deleteMessageById(String userEmail, String messageId, boolean softDelete) {
        log.debug("Inside @method deleteMessage. @param: userEmail -> {} messageId -> {} softDelete -> {}", userEmail, messageId);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String token = tokenUtils.getAccessToken();
            UserFolders folder = foldersRepository.findByDisplayNameAndEmail("Inbox", userEmail);
            String url = String.format("https://graph.microsoft.com/v1.0/users/%s/mailFolders/%s/messages/%s", userEmail, folder.getFolderId(), messageId);
            log.info("URL for deleting message: {}", url);
            if (softDelete) {
                MailSummary message = mailSummaryDao.findByMessageId(messageId, userEmail);
                message.setDeleted(true);
                mailSummaryDao.save(message);
                return GraphUtils.moveMessageToFolder(messageId, token, httpClient, userEmail);
            } else {
                return hardDeleteMessage(url, httpClient);
            }
        } catch (Exception e) {
            log.error("Error while deleting email: {}", e.getMessage(), e);
            return false;
        }
    }

    private boolean hardDeleteMessage(String messageUrl, CloseableHttpClient httpClient) throws Exception {
        HttpDelete deleteRequest = new HttpDelete(messageUrl);
        deleteRequest.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
        deleteRequest.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

        try (CloseableHttpResponse response = httpClient.execute(deleteRequest)) {
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 204) {
                log.info("Successfully hard deleted email");
                return true;
            } else {
                String errorMessage = EntityUtils.toString(response.getEntity());
                log.error("Failed to hard delete email. ErrorMessage: {} Status code: {}", errorMessage, statusCode);
                return false;
            }
        }
    }


    private List<Map<String, String>> parseMailFolderResponse(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);

        JSONArray messages = jsonObject.getJSONArray(EmailConstants.VALUE);

        List<Map<String, String>> result = new ArrayList<>();

        for (int i = 0; i < messages.length(); i++) {
            JSONObject message = messages.getJSONObject(i);

            String id = message.getString("id");
            String displayName = message.getString(EmailConstants.DISPLAY_NAME);

            Map<String, String> mailFolderMap = new HashMap<>();
            mailFolderMap.put("id", id);
            mailFolderMap.put(EmailConstants.DISPLAY_NAME, displayName);

            result.add(mailFolderMap);
        }
        return result;
    }

    private List<Map<String, String>> convertJsonToTodoTaskListMap(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);
        JSONArray messages = jsonObject.getJSONArray(EmailConstants.VALUE);
        List<Map<String, String>> resultList = new ArrayList<>();

        for (int i = 0; i < messages.length(); i++) {
            JSONObject message = messages.getJSONObject(i);

            Map<String, String> categroryMap = new HashMap<>();
            String id = message.getString("id");
            String displayName = message.getString(EmailConstants.DISPLAY_NAME);

            categroryMap.put("id", id);
            categroryMap.put(EmailConstants.DISPLAY_NAME, displayName);

            resultList.add(categroryMap);
        }
        return resultList;
    }

    List<List<UserEmailDto>> groupByEmailConversation(List<UserEmailDto> userEmailDtos) {
        // Create a HashMap to group UserEmailDto objects by conversationId
        Map<String, List<UserEmailDto>> conversationMap = new HashMap<>();

        // Iterate through each UserEmailDto
        for (UserEmailDto email : userEmailDtos) {
            // Get the conversationId
            String conversationId = email.getConversationId();

            // Check if the conversationId already exists in the map
            if (conversationMap.containsKey(conversationId)) {
                // If yes, add the email to the existing list
                conversationMap.get(conversationId).add(email);
            } else {
                // If no, create a new list with this email and put it in the map
                List<UserEmailDto> newList = new ArrayList<>();
                newList.add(email);
                conversationMap.put(conversationId, newList);
            }
        }

        // Convert the values of the map (lists of emails) to a List<List<UserEmailDto>>
        List<List<UserEmailDto>> resultList = new ArrayList<>(conversationMap.values());

        // Sort the lists based on createdTime if they contain more than one item
        for (List<UserEmailDto> emailList : resultList) {
            if (emailList.size() > 1) {
                emailList.sort(Comparator.comparing(UserEmailDto::getCreatedTime));
            }
        }

        return resultList;
    }

    List<UserEmailDto> convertJsonToUserEmailDto(String jsonString, String emailId, boolean forPolling) {
        JSONObject jsonObject = new JSONObject(jsonString);

        JSONArray messages = jsonObject.getJSONArray(EmailConstants.VALUE);

        List<UserEmailDto> resultList = new ArrayList<>();

        for (int i = 0; i < messages.length(); i++) {
            JSONObject message = messages.getJSONObject(i);

            String id = message.getString("id");
            Boolean hasAttachments = message.getBoolean("hasAttachments");
            String internetMessageId = message.getString("internetMessageId");
            String importance = message.getString("importance");
            String subject = null;
            try {
                subject = message.getString(EmailConstants.SUBJECT);
            } catch (JSONException e) {
                subject = "";
            }
            String parentFolderId = message.getString("parentFolderId");
            String conversationId = message.getString("conversationId");
            String bodyPreview = message.getString("bodyPreview");
            String body = message.getJSONObject("body").getString(EmailConstants.CONTENT);
            String type = getType(message);
            String from = "";

            if (message.has("from")) {
                from = message.getJSONObject("from").getJSONObject(EmailConstants.EMAIL_ADDRESS).getString(EmailConstants.ADDRESS);
            }

            if (forPolling) {
                //generate stats only when it is polling
//                generateStatsForEmail(from, emailId, body, type);

                //Do body truncate only for polling
                String firstMessageId = getMessageIdOfFirstEmailInConversation(emailId, conversationId);
                int indexOfMailEnd = body.indexOf("From: ");
                if (indexOfMailEnd != -1 && firstMessageId != null && !firstMessageId.equals(id)) {
                    body = body.substring(0, indexOfMailEnd);
                    body = AIUtils.reduceTextToMaxTokens(body, bodyTokenLimit);
                }
            }

            List<String> toRecipients = getRecipients(message, "toRecipients");
            List<String> ccRecipients = getRecipients(message, "ccRecipients");
            List<String> bccRecipients = getRecipients(message, "bccRecipients");

            // Assuming 'createdDateTime' is in ISO 8601 format
            Date createdDateTime = java.util.Date.from(java.time.Instant.parse(message.getString("receivedDateTime")));
            Date endDateTime = getDateFromJSONObject(message, "endDateTime");

            UserEmailDto userEmailDto = new UserEmailDto();
            userEmailDto.setId(id);
            userEmailDto.setFrom(from);
            userEmailDto.setSubject(subject);
            userEmailDto.setBodyPreview(getMeetingPreview(bodyPreview));
            userEmailDto.setBody(body);
            userEmailDto.setType(type);
            userEmailDto.setToRecipients(toRecipients);
            userEmailDto.setCcRecipients(ccRecipients);
            userEmailDto.setBccRecipients(bccRecipients);
            userEmailDto.setConversationId(conversationId);
            userEmailDto.setCreatedTime(createdDateTime);
            userEmailDto.setMeetingEndTime(endDateTime);
            userEmailDto.setHasAttachments(hasAttachments);
            userEmailDto.setInternetMessageId(internetMessageId);
            userEmailDto.setImportance(importance);
            userEmailDto.setFolderName(parentFolderId);

            resultList.add(userEmailDto);
        }
        return resultList;
    }

    private String getMeetingPreview(String bodyPreview) {
        if (bodyPreview == null || bodyPreview.startsWith("_________________________________________")) {
            return "";
        }
        return bodyPreview;
    }

    public List<UserEmailDto> getEmailsOfUser(String email, String mailFolder, Integer lowerLimit, Integer upperLimit) {
        log.debug("Inside @method getEmailsOfUser. @param: email -> {}, lowerLimit -> {}, upperLimit -> {}", email, lowerLimit, upperLimit);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String url = buildUrlWithPagination(email, mailFolder, lowerLimit, upperLimit);
            log.debug("URL to get emails : {}", url);

            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
//            request.setHeader(EmailConstants.PREFER, EmailConstants.OUTLOOK_BODY_CONTENT_TYPE_TEXT);
            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                    String result = EntityUtils.toString(entity);
                    return convertJsonToUserEmailDto(result, email, false);
                } else if (entity != null) {
                    String errorMessage = EntityUtils.toString(entity);
                    auditLog.error("Error while getting emails from graph", errorMessage, errorMessage + " Response Code " + response.getStatusLine().getStatusCode(), null, null, "GET_EMAIL_FROM_FOLDER", email, null, null, null, null, null, null);
                    //                log.error("Error while getting emails from graph. Exception message : {}", errorMessage);
                    return new ArrayList<>();
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while getting emails of a folder from graph", e.getMessage(), getStackTraceAsString(e), null, null, "GET_EMAIL_FROM_FOLDER", email, null, null, null, null, null, null);
        }
        return new ArrayList<>();
    }

    public String buildUrlWithPagination(String email, String mailFolder, Integer lowerLimit, Integer upperLimit) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(String.format("https://graph.microsoft.com/v1.0/users/%s/mailFolders/%s/messages", email, mailFolder));

        // Add pagination parameters if provided
        if (lowerLimit != null && upperLimit != null) {
            uriBuilder.addParameter("$skip", lowerLimit.toString());
            int top = (upperLimit - lowerLimit);
            uriBuilder.addParameter("$top", Integer.toString(top));
        }

        return uriBuilder.build().toString();
    }

    /**
     * Generate stats for email.
     *
     * @param from        the from
     * @param emailId     the email id
     * @param bodyPreview the body preview
     * @param type        the type
     */
    public void generateStatsForEmail(String from, String emailId, String bodyPreview, String type) {
        log.debug("Generating stats for {}", emailId);
        if (from.equalsIgnoreCase(emailId)) {


            EmailStats currentStats = iEmailStatsDao.getStatsForUser(emailId, new Date(), type.toLowerCase());

            if (currentStats == null) {
                // If no stats exist for the day, create a new EmailStats object
                currentStats = new EmailStats();
                currentStats.setUserId(emailId);
                currentStats.setStatsDate(new Date());
                currentStats.setUsedCount(0);
                currentStats.setUnusedCount(0);
                currentStats.setType(type.toLowerCase());
            }
            // Check the bodyPreview content
            if (bodyPreview.contains("Generated by Email Assistant")) {
                currentStats.setUsedCount(currentStats.getUsedCount() + 1);
                log.debug("Increasing used count for {}", emailId);
            } else {
                // Increase the unused count
                currentStats.setUnusedCount(currentStats.getUnusedCount() + 1);
                log.debug("Increasing unused count for {}", emailId);
            }

            // Save the updated stats
            iEmailStatsDao.save(currentStats);

            log.debug("Stats updated for {}", emailId);
        }
    }


    private String getType(JSONObject message) {
        if (message.has("meetingMessageType") || message.has("meetingRequestType")) {
            return "Meeting";
        } else {
            return "Email";
        }
    }

    private List<TaskDto> convertJsonToTaskDto(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);
        JSONArray tasks = jsonObject.getJSONArray(EmailConstants.VALUE);
        List<TaskDto> resultList = new ArrayList<>();

        for (int i = 0; i < tasks.length(); i++) {
            JSONObject task = tasks.getJSONObject(i);

            String id = task.getString("id");
            String importance = task.getString("importance");
            String status = task.getString(EmailConstants.STATUS);
            String title = task.getString("title");
            String content = "";
            if (task.has("body")) {
                content = task.getJSONObject("body").getString(EmailConstants.CONTENT);
            }

            List<String> categories = new ArrayList<>();
            if (task.has("categories")) {
                JSONArray categoriesArray = task.getJSONArray("categories");
                for (int j = 0; j < categoriesArray.length(); j++) {
                    categories.add(categoriesArray.getString(j));
                }
            }

            Date reminderDateTime = getDateFromJSONObject(task, "reminderDateTime");
            Date dueDateTime = getDateFromJSONObject(task, "dueDateTime");

            TaskDto taskDto = new TaskDto();
            taskDto.setId(id);
            taskDto.setImportance(importance);
            taskDto.setStatus(status);
            taskDto.setTitle(title);
            taskDto.setContent(content);
            taskDto.setCategories(categories);
            taskDto.setReminderDateTime(reminderDateTime);
            taskDto.setDueDateTime(dueDateTime);

            resultList.add(taskDto);
        }
        return resultList;
    }

    private Date getDateFromJSONObject(JSONObject jsonObject, String key) {
        Date date = null;
        if (jsonObject.has(key)) {
            String dueDateTime = jsonObject.getJSONObject(key).getString(EmailConstants.DATE_TIME);
            String timeZone = jsonObject.getJSONObject(key).getString("timeZone");
            date = parseDateFromString(dueDateTime, timeZone);
        }
        return date;
    }

    private Date getDateFromJSONObject(JSONObject jsonObject, String key, String timeZone) {
        Date date = null;
        if (jsonObject.has(key)) {
            String dueDateTime = jsonObject.getJSONObject(key).getString(EmailConstants.DATE_TIME);
            date = parseDateFromString(dueDateTime, timeZone);
        }
        return date;
    }

    private List<EventDto> convertJsonToEventDto(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);

        JSONArray messages = jsonObject.getJSONArray(EmailConstants.VALUE);

        List<EventDto> resultList = new ArrayList<>();

        for (int i = 0; i < messages.length(); i++) {
            JSONObject message = messages.getJSONObject(i);

            EventDto eventDto = getEventDto(message);

            resultList.add(eventDto);
        }
        return resultList;
    }

    private EventDto getEventDto(JSONObject message) {
        String id = message.getString("id");
        String organizer = null;
        if (message.getJSONObject("organizer").getJSONObject(EmailConstants.EMAIL_ADDRESS).has(EmailConstants.ADDRESS)) {
            organizer = message.getJSONObject("organizer").getJSONObject(EmailConstants.EMAIL_ADDRESS).getString(EmailConstants.ADDRESS);
        }
        List<String> attendees = getRecipients(message, "attendees");
        List<AttendeeAndStatus> required = getAttendeesByType(message, "required");
        List<AttendeeAndStatus> optional = getAttendeesByType(message, "optional");
        String location = message.getJSONObject("location").getString("displayName");
        String subject = null;
        try {
            subject = message.getString(EmailConstants.SUBJECT);
        } catch (JSONException e) {
            subject = "";
        }
        JSONObject body = message.getJSONObject("body");
        String bodyPreview = body.getString(EmailConstants.CONTENT);
        String joinUrl = null;
        if (message.getBoolean("isOnlineMeeting")) {
            joinUrl = message.getJSONObject("onlineMeeting").getString("joinUrl");
        }
        Boolean hasAttachments = message.getBoolean("hasAttachments");

        Date meetingStartTime = getDateFromJSONObject(message, "start");
        Date meetingEndTime = getDateFromJSONObject(message, "end");
        // Assuming 'createdDateTime' is in ISO 8601 format
        Date createdDateTime = Date.from(Instant.parse(message.getString("createdDateTime")));
        Date lastModifiedDateTime = Date.from(Instant.parse(message.getString("lastModifiedDateTime")));
        String accepted = message.getJSONObject("responseStatus").getString("response");

        EventDto eventDto = new EventDto();
        eventDto.setId(id);
        eventDto.setOrganizer(organizer);
        eventDto.setAttendees(attendees);
        eventDto.setSubject(subject);
        eventDto.setAccepted(accepted);
        eventDto.setBodyPreview(bodyPreview);
        eventDto.setJoinUrl(joinUrl);
        eventDto.setHasAttachments(hasAttachments);
        eventDto.setMeetingStartTime(meetingStartTime);
        eventDto.setMeetingEndTime(meetingEndTime);
        eventDto.setCreatedDateTime(createdDateTime);
        eventDto.setLastModifiedDateTime(lastModifiedDateTime);
        eventDto.setLocation(location);
        eventDto.setRequiredAttendees(required);
        eventDto.setOptionalAttendees(optional);
        return eventDto;
    }

    private List<String> getAttendees(JSONObject message, String recipientType) {
        JSONArray toRecipientsArray = message.getJSONArray(recipientType);
        List<String> toRecipients = new ArrayList<>();
        for (int j = 0; j < toRecipientsArray.length(); j++) {
            String status = null;
            String recipientDetail = null;
            if (toRecipientsArray.getJSONObject(j).getJSONObject(EmailConstants.EMAIL_ADDRESS).has(EmailConstants.ADDRESS)) {
                JSONObject recipient = new JSONObject();
                String recipientAddress = null;
                recipientAddress = toRecipientsArray.getJSONObject(j).getJSONObject(EmailConstants.EMAIL_ADDRESS).getString(EmailConstants.ADDRESS);
                status = toRecipientsArray.getJSONObject(j).getJSONObject(EmailConstants.STATUS).getString("response");
                recipient.put("email", recipientAddress);
                recipient.put(EmailConstants.STATUS, status);
                recipientDetail = recipient.toString();
            }
            toRecipients.add(recipientDetail);
        }
        return toRecipients;
    }

    private List<AttendeeAndStatus> getAttendeesByType(JSONObject message, String attendeeType) {
        JSONArray attendeesArray = message.getJSONArray("attendees");
        List<AttendeeAndStatus> attendeesList = new ArrayList<>();

        // Loop through each attendee
        for (int i = 0; i < attendeesArray.length(); i++) {
            JSONObject attendeeObject = attendeesArray.getJSONObject(i);

            // Check if the attendee type matches the requested type (e.g., "required" or "optional")
            if (attendeeObject.getString(TYPE).equalsIgnoreCase(attendeeType)) {

                // Extract email address and response status
                String emailAddress = attendeeObject.getJSONObject("emailAddress").getString("address");
                String responseStatus = attendeeObject.getJSONObject("status").getString("response");

                // Create a new AttendeeAndStatus object and add it to the list
                attendeesList.add(new AttendeeAndStatus(emailAddress, responseStatus));
            }
        }

        return attendeesList;
    }

    private List<String> getRecipients(JSONObject message, String recipientType) {
        JSONArray toRecipientsArray = message.getJSONArray(recipientType);
        List<String> toRecipients = new ArrayList<>();
        for (int j = 0; j < toRecipientsArray.length(); j++) {
            String recipientAddress = null;
            if (toRecipientsArray.getJSONObject(j).getJSONObject(EmailConstants.EMAIL_ADDRESS).has(EmailConstants.ADDRESS)) {
                recipientAddress = toRecipientsArray.getJSONObject(j).getJSONObject(EmailConstants.EMAIL_ADDRESS).getString(EmailConstants.ADDRESS);
            }
            toRecipients.add(recipientAddress);
        }
        return toRecipients;
    }

    private Date parseDateFromString(String date, String timeZone) {
        // Define the formatter for the input date-time string
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS");

        // Parse the date-time string to a ZonedDateTime in the given time zone
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(date, formatter.withZone(ZoneId.of(timeZone)));

        // Convert the ZonedDateTime to an Instant and then to java.util.Date
        Instant instant = zonedDateTime.toInstant();
        return Date.from(instant);
    }

    /**
     * Gets summary of attachment.
     *
     * @param docId the doc id
     * @return the summary of attachment
     */
    public String getSummaryOfAttachment(String docId) {
        return userMailAttachmentRepository.getLongSummaryForDocument(docId);
    }

    /**
     * Gets long and short summary for document.
     *
     * @param docId the doc id
     * @return the long and short summary for document
     */
    public AttachmentsDto getLongAndShortSummaryForDocument(String docId) {
        try {
            return userMailAttachmentRepository.getLongAndShortSummaryForDocument(docId);
        } catch (Exception e) {
            auditLog.error("Error while getting summary for document", e.getMessage(), getStackTraceAsString(e), null, null, "VOICE_GET_DOC", userContextHolder.getCurrentUser().getEmail(), null, null, null, null, Map.of("docId", docId), null);
            return null;
        }
    }

    /**
     * Gets answer from attachment.
     *
     * @param docId    the doc id
     * @param question the question
     * @return the answer from attachment
     */
    public String getAnswerFromAttachment(String docId, String question) {
        System.out.println(" Get answer from Attachment " + docId + " Question " + question);
       /* String ragId = userMailAttachmentRepository.getRagIdFromDocId(docId);
        System.out.println("RagID coming is " + ragId);*/
        RestTemplate restTemplate = new RestTemplate();
        String url = String.format("%s", answerUrl);

        Map<String, Object> bodyParams = new HashMap<>();
        bodyParams.put("fileId", docId);
        bodyParams.put("question", question);


        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
        org.springframework.http.HttpEntity<Map<String, Object>> requestEntity = new org.springframework.http.HttpEntity<>(bodyParams, headers);
        log.debug("Url for Question {}", url);
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );

        JSONObject jsonResponse = new JSONObject(response.getBody());
        log.info("jsonResponse is {}", jsonResponse.toString());
        return aiService.formatAnswer(jsonResponse.getString("answer")).getAnswer();

    }

    private String parseAnswer(String jsonResponse) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode rootNode = mapper.readTree(jsonResponse);
            return rootNode.path("answer").asText();
        } catch (Exception e) {
            e.printStackTrace();
            return "Error parsing response";
        }
    }

    /**
     * Search emails by tag list.
     *
     * @param email the email
     * @param tag   the tag
     * @return the list
     * @throws InterruptedException the interrupted exception
     * @throws IOException          the io exception
     * @throws URISyntaxException   the uri syntax exception
     */
    public List<String> searchEmailsByTag(String email, String tag) throws InterruptedException, IOException, URISyntaxException {
        log.debug("Inside @method searchEmailsByTag. @param: tag -> {}", tag);

        String filter = String.format("singleValueExtendedProperties/Any(ep: ep/id eq 'String {00020329-0000-0000-C000-000000000046} Name %s' and ep/value eq '%s')", tag, tag);

        URIBuilder uriBuilder = new URIBuilder("https://graph.microsoft.com/v1.0/users/" + email + "/messages");
        uriBuilder.addParameter("$filter", filter);
        uriBuilder.addParameter("$select", "id");

        URI uri = uriBuilder.build();
        String url = uri.toString();

        log.debug("URL to searchEmailsByTag : {}", url);
        // Create an HttpClient instance
        CloseableHttpClient httpClient = HttpClients.createDefault();

        // Create a GET request
        HttpGet request = new HttpGet(url);
        request.setHeader("Authorization", "Bearer " + tokenUtils.getAccessToken());
        request.setHeader("Prefer", "outlook.body-content-type=\"text\"");

        // Execute the request
        try (CloseableHttpResponse response = httpClient.execute(request)) {

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity);
                return getTaggedMessageIds(result);
            }
        }
        return new ArrayList<>();
    }


    private List<String> getTaggedMessageIds(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);

        JSONArray messages = jsonObject.getJSONArray("value");

        List<String> resultList = new ArrayList<>();

        for (int i = 0; i < messages.length(); i++) {
            JSONObject message = messages.getJSONObject(i);
            String messageId = message.getString("id");
            resultList.add(messageId);
        }
        return resultList;
    }


    /**
     * Tag email boolean.
     *
     * @param email     the email
     * @param messageId the message id
     * @param tag       the tag
     * @param toAdd     the to add
     * @return the boolean
     * @throws Exception the exception
     */
    public boolean tagEmail(String email, String messageId, String tag, boolean toAdd) throws Exception {
        log.debug("Inside @method tagEmail. @param: messageId -> {}, tag -> {}", messageId, tag);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s", email, messageId);
        log.debug("URL to tagEmail : {}", url);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request

            String requestBody = toAdd ? String.format("{\"singleValueExtendedProperties\":[{\"id\":\"String {00020329-0000-0000-C000-000000000046} Name %s\",\"value\":\"%s\"}]}", tag, tag) :
                    String.format("{\"singleValueExtendedProperties\":[{\"id\":\"String {00020329-0000-0000-C000-000000000046} Name %s\",\"value\":null}]}", tag);

            HttpPatch request = getHttpPatch(url, requestBody);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();

                if (statusCode == 200) {
                    return true;
                } else if (statusCode == 404) {
                    return false;
                }

                String errorMessage = EntityUtils.toString(response.getEntity());
                auditLog.error("Error inside @method tagEmail", errorMessage, errorMessage, messageId, null, TAG_EMAIL_GRAPH, email, null, null, null, null, tag, null);
//                log.error("Error inside @method tagEmail. Exception message -> {}", errorMessage);
                return false;
            }
        } catch (Exception e) {
            auditLog.error("Error inside @method tagEmail", e.getMessage(), getStackTraceAsString(e), messageId, null, TAG_EMAIL_GRAPH, email, null, null, null, null, tag, null);
            return false;
        }
    }

    private HttpPatch getHttpPatch(String url, String requestBody) throws InterruptedException, UnsupportedEncodingException {
        HttpPatch request = new HttpPatch(url);
        request.setHeader("Authorization", "Bearer " + tokenUtils.getAccessToken());
        request.setHeader("Content-Type", "application/json");

        StringEntity entity = new StringEntity(requestBody);
        request.setEntity(entity);
        return request;
    }

    /**
     * Find attachment status list.
     *
     * @param internetMessageId the internet message id
     * @return the list
     */
    public List<Attachments> findAttachmentStatus(String internetMessageId) {
        List<Attachments> attachmentList = new ArrayList<Attachments>();
        try {
            List<UserMailAttachment> mailAttachment = userMailAttachmentRepository.getAttachmentsByInternetMessageId(internetMessageId);
            for (int i = 0; i < mailAttachment.size(); i++) {
                UserMailAttachment tempAttachment = mailAttachment.get(i);
                Attachments attachment = new Attachments();
                attachment.setId(tempAttachment.getId());
                attachment.setDocType(tempAttachment.getType());
                attachment.setGraphDocId(tempAttachment.getAttachmentId());
                attachment.setDocName(tempAttachment.getName());
                attachment.setStatus(tempAttachment.getProcessingStatus());
                attachmentList.add(attachment);
            }
        } catch (Exception e) {
            log.error("Error while getting attachment status for internetMessageId {} ", internetMessageId, e);
        }
        return attachmentList;
    }


    /**
     * Reschedule event map.
     *
     * @param requestBody the request body
     * @return the map
     */
    public Map<String, String> rescheduleEvent(Map<String, String> requestBody) {
        log.debug("Inside @method rescheduleEvent. @param : requestBody -> {}", requestBody);

        String email = userContextHolder.getCurrentUser().getEmail();
        String timeZone = requestBody.get("timeZone");
        if (timeZone == null) {
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
            timeZone = preferences.getTimeZone();
        }
        requestBody.put("timeZone", timeZone);

        String eventId = requestBody.get("eventId");
        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/events/%s", email, eventId);
        log.debug("URL to rescheduleEvent : {}", url);

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPatch request = new HttpPatch(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            String payload = CommonUtils.convertToRescheduleMeetingPayload(requestBody);
            StringEntity entity = new StringEntity(payload);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();

                if (statusCode == 200) {
                    return Map.of("result", EmailConstants.SUCCESS);
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                auditLog.error("Error while rescheduling event", errorMessage, errorMessage, null, null, "RESCHEDULE_EVENT_GRAPH", email, null, null, null, null, requestBody, null);
                return Map.of("result", EmailConstants.FAILED);
            }
        } catch (Exception e) {
            auditLog.error("Error while rescheduling event", e.getMessage(), getStackTraceAsString(e), null, null, "RESCHEDULE_EVENT_GRAPH", email, null, null, null, null, requestBody, null);
            return Map.of("result", EmailConstants.FAILED);
        }
    }


    /**
     * Save mail summary in vector.
     *
     * @param requestBody the request body
     */
    @Async
    public void saveMailSummaryInVector(String requestBody) {

        String response = HttpClientUtlis.sendPost(requestBody, summaryUploadUrl);
        log.info("jsonResponse is {}", response);

    }


    /**
     * Query mail summary in vector string.
     *
     * @param requestBody the request body
     * @return the string
     */
    public String queryMailSummaryInVector(Map<String, Object> requestBody) {

        RestTemplate restTemplate = new RestTemplate();
        String url = String.format("%s", summaryAnswerUrl);
        String email = userContextHolder.getCurrentUser().getEmail();
        requestBody.put(EmailConstants.EMAIL_KEY, email);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
        org.springframework.http.HttpEntity<Map<String, Object>> requestEntity = new org.springframework.http.HttpEntity<>(requestBody, headers);
        log.debug("Url for Question {} {}", url, requestBody.toString());
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );

        String jsonResponse = response.getBody();
        log.info("jsonResponse is {}", jsonResponse);
        return jsonResponse;

    }


    /**
     * Generate meeting summary.
     */
    public void generateMeetingSummary() {
        log.debug("Going to generate meeting summary");
        try {
            List<MeetingSummary> meetingList = meetingDao.findByStatus("NEW");
            for (int i = 0; i < meetingList.size(); i++) {
                MeetingSummary meetingSummary = meetingList.get(i);
                String meetingId = meetingSummary.getMeetingId();
                String transcript = ZoomClient.meetingTranscript(meetingId);
                if (transcript != null) {
                    meetingSummary.setStatus("IN_PROGRESS");
                    meetingSummaryDao.save(meetingSummary);
                    try {
                        MeetingSummaryAiResponse meetingResponse = aiService.getMeetingSummaryResponse(transcript);
                        log.debug("Meeting summary response for meeting id {} is {}", meetingId, meetingResponse.toString());
                        if (meetingResponse != null) {
                            meetingSummary.setLongSummary(meetingResponse.getLongSummary());
                            meetingSummary.setShortSummary(meetingResponse.getShortSummary());
                            meetingSummary.setActionItems(meetingResponse.getActionItems());
                            meetingSummary.setStatus("COMPLETED");
                            meetingSummaryDao.save(meetingSummary);
                        }
                    } catch (Exception e) {
                        log.error("Error while getting meeting summary ai response", e);
                        meetingSummary.setStatus("ERROR");
                        meetingSummary.setProcessingError(getStackTraceAsString(e));
                        meetingSummaryDao.save(meetingSummary);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error generating meeting summary", e);
        }
    }


    /**
     * Email stats by filter list.
     *
     * @param llimit    the llimit
     * @param ulimit    the ulimit
     * @param filterMap the filter map
     * @return the list
     */
    public List<EmailStats> emailStatsByFilter(Integer llimit, Integer ulimit, Map<String, Object> filterMap) {
        List<EmailStats> statusMap = null;
        statusMap = emailStatsDao.getFilteredEmailStats(filterMap, llimit, ulimit);
        return statusMap;
    }


    /**
     * Email stats count by filter.
     *
     * @param filterMap the filter map
     * @return the long
     */
    public long emailStatsCountByFilter(Map<String, Object> filterMap) {
        List<EmailStats> failureMap = null;
        failureMap = emailStatsDao.getFilteredEmailStatsCount(filterMap);
        return failureMap.size();
    }


    /**
     * Mark message read unread .
     *
     * @param email             the email
     * @param internetMessageId the internet message id
     * @param markAsRead        the mark as read
     * @param folderName        the folder name
     * @return the string
     */
    public String markMessageReadUnread(String email, String internetMessageId, boolean markAsRead, String folderName) {
        try {
            UserFolders folder = foldersRepository.findByDisplayNameAndEmail(folderName, email);
            String url = String.format("https://graph.microsoft.com/v1.0/users/%s/mailFolders/%s/messages", email, folder.getFolderId());
            String filter = String.format("internetMessageId eq '%s'", internetMessageId);

            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet getRequest = new HttpGet(url + "?$filter=" + URLEncoder.encode(filter, StandardCharsets.UTF_8));
                getRequest.setHeader("Authorization", "Bearer " + tokenUtils.getAccessToken());

                try (CloseableHttpResponse getResponse = httpClient.execute(getRequest)) {
                    if (getResponse.getStatusLine().getStatusCode() == 200) {
                        String responseBody = EntityUtils.toString(getResponse.getEntity());
                        JSONObject jsonResponse = new JSONObject(responseBody);
                        JSONArray messages = jsonResponse.getJSONArray("value");
                        if (messages.length() > 0) {
                            String messageId = messages.getJSONObject(0).getString("id");
                            String patchUrl = url + "/" + messageId;

                            HttpPatch patchRequest = new HttpPatch(patchUrl);
                            patchRequest.setHeader("Authorization", "Bearer " + tokenUtils.getAccessToken());
                            patchRequest.setHeader("Content-Type", "application/json");

                            String jsonBody = String.format("{\"isRead\": %b}", markAsRead);
                            patchRequest.setEntity(new StringEntity(jsonBody));

                            try (CloseableHttpResponse patchResponse = httpClient.execute(patchRequest)) {
                                if (patchResponse.getStatusLine().getStatusCode() == 200 || patchResponse.getStatusLine().getStatusCode() == 204) {
                                    log.info("Message with InternetMessageID {} marked as {} via Graph API", internetMessageId, markAsRead ? "read" : "unread");
                                    return EmailConstants.SUCCESS;
                                } else {
                                    log.error("Error marking message as {} via Graph API. Status code: {}", markAsRead ? "read" : "unread", patchResponse.getStatusLine().getStatusCode());
                                    return EmailConstants.FAILED;

                                }
                            }
                        } else {

                            log.warn("No message found with internetMessageId {} via Graph API", internetMessageId);
                            return EmailConstants.FAILED;

                        }
                    } else {
                        log.error("Error fetching message via Graph API. Status code: {}", getResponse.getStatusLine().getStatusCode());
                        return EmailConstants.FAILED;

                    }
                }
            }
        } catch (Exception e) {
            log.error("Error marking Graph API message as {}: {}", markAsRead ? "read" : "unread", e.getMessage());
            auditLog.error("Error marking Graph API message", e.getMessage(), getStackTraceAsString(e), null, null, "ERROR_MARKING_GRAPH_MESSAGE", email, null, null, null, null, null, null);
            return EmailConstants.FAILED;

        }
    }


    /**
     * Create draft reply with attachment .
     *
     * @param email           the email
     * @param messageId       the message id
     * @param content         the content
     * @param sendDraft       the send draft
     * @param attachmentPaths the attachment paths
     * @param isReplyAll      the is reply all
     * @return the string
     * @throws Exception the exception
     */
    public String createDraftReplyWithAttachment(String email, String messageId, String content, boolean sendDraft, String attachmentPaths, boolean isReplyAll) throws Exception {
        log.debug("Inside @method createDraftReply. @param: email -> {}, messageId -> {}, content -> {}, sendDraft -> {}, isReplyAll -> {}", email, messageId, content, sendDraft, isReplyAll);

        String endpoint = isReplyAll ? "createReplyAll" : "createReply";
        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/%s", email, messageId, endpoint);
        log.debug("URL to createDraftReply : {}", url);
        List<String> successfullyAttachedFiles = new ArrayList<>();

        JSONObject replyObject = createReplyObject(content, "HTML");

        String valueAsString = replyObject.toString();

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(valueAsString);
            request.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 201 || statusCode == 202) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject responseJson = new JSONObject(responseBody);
                    String draftMessageId = responseJson.getString("id");
                    List<String> attachmentList = CommonUtils.getListFromCommaSeparatedString(attachmentPaths);

                    if (attachmentList != null && !attachmentList.isEmpty()) {
                        addAttachmentsToDraftFromS3(draftMessageId, attachmentList, email, httpClient, successfullyAttachedFiles);
                    }

                    if (sendDraft) {
                        deleteAttachedFiles(successfullyAttachedFiles);
                        return sendDraftEmail(httpClient, email, draftMessageId);
                    }

                    deleteAttachedFiles(successfullyAttachedFiles);
                    return draftMessageId;
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                auditLog.error("Error while drafting reply", errorMessage, errorMessage, messageId, null, DRAFT_REPLY_GRAPH, email, null, null, null, null, Map.of("url", url, "content", content, "isReplyAll", isReplyAll), null);
                return EmailConstants.FAILED;
            } catch (Exception e) {
                auditLog.error("Error while drafting reply", e.getMessage(), getStackTraceAsString(e), messageId, null, DRAFT_REPLY_GRAPH, email, null, null, null, null, Map.of("url", url, "content", content, "isReplyAll", isReplyAll), null);
                return EmailConstants.FAILED;
            }
        }
    }


    /**
     * Forward email.
     *
     * @param email         the email
     * @param messageId     the message id
     * @param toRecipients  the to recipients
     * @param ccRecipients  the cc recipients
     * @param bccRecipients the bcc recipients
     * @param comment       the comment
     * @return the string
     * @throws Exception the exception
     */
    public String forwardEmail(String email, String messageId, String toRecipients, String ccRecipients, String bccRecipients, String comment) throws Exception {
        log.debug("Forwarding email using Graph API. @param: email -> {}, messageId -> {}, toRecipients -> {}, ccRecipients -> {}, bccRecipients -> {}", email, messageId, toRecipients, ccRecipients, bccRecipients);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/forward", email, messageId);

        JSONObject forwardBody = new JSONObject();
        forwardBody.put("comment", comment);

        if (toRecipients != null && !toRecipients.isEmpty()) {
            forwardBody.put("toRecipients", createRecipientArray(toRecipients));
        }
        if (ccRecipients != null && !ccRecipients.isEmpty()) {
            forwardBody.put("ccRecipients", createRecipientArray(ccRecipients));
        }
        if (bccRecipients != null && !bccRecipients.isEmpty()) {
            forwardBody.put("bccRecipients", createRecipientArray(bccRecipients));
        }

        String requestBody = forwardBody.toString();

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(requestBody);
            request.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 202) {
                    log.debug("Email forwarded successfully using Graph API");
                    return EmailConstants.SUCCESS;
                } else {
                    String errorMessage = EntityUtils.toString(response.getEntity());
                    log.error("Error forwarding email using Graph API. Status code: {}, Error: {}", statusCode, errorMessage);
                    return EmailConstants.FAILED;
                }
            }
        } catch (Exception e) {
            log.error("Exception while forwarding email using Graph API", e);
            return EmailConstants.FAILED;
        }
    }

    private JSONArray createRecipientJsonArray(List<String> recipients) {
        JSONArray recipientArray = new JSONArray();
        for (String recipient : recipients) {
            JSONObject recipientObject = new JSONObject();
            JSONObject emailAddress = new JSONObject();
            emailAddress.put("address", recipient);
            recipientObject.put("emailAddress", emailAddress);
            recipientArray.put(recipientObject);
        }
        return recipientArray;
    }


    /**
     * Gets flagged emails.
     *
     * @param email    the email
     * @param folderId the folder id
     * @return the flagged emails
     */
    public List<String> getFlaggedEmails(String email, String folderId) {
        log.debug("Fetching flagged emails for userId {} and folderId {}", email, folderId);
        List<String> flaggedEmails = new ArrayList<>();
        try {
            String url = String.format("https://graph.microsoft.com/v1.0/users/%s/mailFolders/%s/messages?$filter=flag/flagStatus eq 'flagged'&$select=internetMessageId,flag",
                    email, folderId);
            url = url.replaceAll(" ", "%20");

            log.debug("URL to get flagged emails: {}", url);

            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet request = new HttpGet(url);
                request.setHeader("Authorization", "Bearer " + tokenUtils.getAccessToken());
                request.setHeader("Prefer", "outlook.body-content-type='text'");

                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                        String result = EntityUtils.toString(entity);

                        JSONObject jsonObject = new JSONObject(result);
                        JSONArray messages = jsonObject.getJSONArray("value");

                        for (int i = 0; i < messages.length(); i++) {
                            JSONObject message = messages.getJSONObject(i);
                            String internetMessageId = message.getString("internetMessageId");
                            flaggedEmails.add(internetMessageId);
                        }

                        return flaggedEmails;
                    } else if (entity != null) {
                        String errorMessage = EntityUtils.toString(entity);
                        log.error("Error while getting flagged emails: {}", errorMessage);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error while getting flagged emails from graph", e);
        }

        return flaggedEmails;
    }

    /**
     * Delete Email by ID from Graph API.
     *
     * @param email   the email address of the user
     * @param messageId the ID of the email to be deleted
     * @return true if deletion is successful, false otherwise
     */
    public boolean deleteEmailById(String email, String messageId) throws IOException {
        log.debug("Attempting to delete email with ID: {} for user: {}", messageId, email);

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s", email, messageId);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpDelete request = new HttpDelete(url);
            request.setHeader("Authorization", "Bearer " + tokenUtils.getAccessToken());

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 204) {
                    log.info("Successfully deleted email with ID: {} for user: {}", messageId, email);
                    return true;
                } else {
                    String errorMessage = EntityUtils.toString(response.getEntity());
                    log.error("Error deleting email with ID: {}. Response: {}", messageId, errorMessage);
                    return false;
                }
            }
        } catch (IOException e) {
            auditLog.error("IOException while trying to delete email via Graph API", e.getMessage(), getStackTraceAsString(e), messageId, null, "DELETE_GRAPH_EMAIL", email, null, null, null, null, null, null);
            throw e;
        } catch (Exception e) {
            auditLog.error("Unexpected error occurred while deleting email via Graph API", e.getMessage(), getStackTraceAsString(e), messageId, null, "DELETE_GRAPH_EMAIL", email, null, null, null, null, null, null);
            throw new IOException("Unexpected error occurred while deleting email.", e);
        }
    }

    /**
     * Send draft string.
     *
     * @param emailOfUser    the email of user
     * @param draftMessageId the draft message id
     * @return the string
     */
    public String sendDraft(String emailOfUser, String draftMessageId) {
        log.debug("Inside @method sendDraft. @param: emailOfUser -> {}, draftMessageId -> {}", emailOfUser, draftMessageId);

        EmailUser details = emailUserDao.findByEmail(emailOfUser);
        if (details == null) throw new ResourceNotFoundException(EmailConstants.EMAIL_DOES_NOT_EXIST_IN_OUR_STRING);
        String userId = details.getUserId();

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/send", userId, draftMessageId);
        log.debug("URL to sendDraft: {}", url);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 202) {
                    log.debug("Draft email sent successfully for emailOfUser: {}, draftMessageId: {}", emailOfUser, draftMessageId);
                    return EmailConstants.SUCCESS;
                } else {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    auditLog.error("Error while sending draft", responseBody, responseBody, null, null, "SEND_DRAFT_GRAPH", emailOfUser, null, null, null, null, null, null);
                    return EmailConstants.FAILED;
                }
            }
        } catch (Exception e) {
            auditLog.error("Error while sending draft", e.getMessage(), getStackTraceAsString(e), null, null, "SEND_DRAFT_GRAPH", emailOfUser, null, null, null, null, null, null);
            return EmailConstants.FAILED;
        }
    }

    private Map<String, List<Meeting>> fetchCalendarEventsForEmailsV1(List<String> emails, String startDateTime, String endDateTime, int slotDuration) throws Exception {
        Map<String, List<Meeting>> result = new HashMap<>();

        // Get timezone from preferences
        String email = userContextHolder.getCurrentUser().getEmail();
        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
        String timeZone = preferences.getTimeZone();

        // Prepare request body
        JSONObject requestBody = new JSONObject();
        requestBody.put("schedules", emails);

        JSONObject startTime = new JSONObject();
        startTime.put("dateTime", startDateTime);
        startTime.put("timeZone", timeZone);
        requestBody.put("startTime", startTime);

        JSONObject endTime = new JSONObject();
        endTime.put("dateTime", endDateTime);
        endTime.put("timeZone", timeZone);
        requestBody.put("endTime", endTime);

        requestBody.put("availabilityViewInterval", slotDuration);

        // Make API call
        String url = "https://graph.microsoft.com/v1.0/users/" + email + "/calendar/getSchedule";
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Authorization", "Bearer " + tokenUtils.getAccessToken());
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Prefer", "outlook.timezone=\"" + timeZone + "\"");
            httpPost.setEntity(new StringEntity(requestBody.toString()));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                JSONObject jsonResponse = new JSONObject(responseBody);
                JSONArray scheduleInfoArray = jsonResponse.getJSONArray("value");

                // Process each user's schedule
                for (int i = 0; i < scheduleInfoArray.length(); i++) {
                    JSONObject scheduleInfo = scheduleInfoArray.getJSONObject(i);
                    String scheduleId = scheduleInfo.getString("scheduleId");
                    if (!scheduleInfo.toMap().containsKey("scheduleItems")) {
                        log.warn("There is problem getting data for : {}", scheduleId);
                        continue;
                    }
                    JSONArray scheduleItems = scheduleInfo.getJSONArray("scheduleItems");

                    List<Meeting> meetings = new ArrayList<>();

                    // Process schedule items
                    for (int j = 0; j < scheduleItems.length(); j++) {
                        JSONObject item = scheduleItems.getJSONObject(j);
                        if ("busy".equals(item.getString("status"))) {
                            Meeting meeting = new Meeting();

                            Date dateTimeStart = getDateFromJSONObject(item, "start", timeZone);
                            meeting.setStartTime(dateTimeStart);

                            Date dateTimeEnd = getDateFromJSONObject(item, "end", timeZone);
                            meeting.setEndTime(dateTimeEnd);

                            meetings.add(meeting);
                        }
                    }

                    result.put(scheduleId, meetings);
                }
            }
        }

        return result;
    }

    public AvailableSlots getAvailableSlotsAndConflictV1(List<String> emails, String startDateTime, String endDateTime, int slotDuration) throws Exception {
        log.debug("Inside @method getAvailableSlotsAndConflictV1. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
        String[] dateTimeRange = setupDateTimeRange(startDateTime, endDateTime);
        startDateTime = dateTimeRange[0];
        endDateTime = dateTimeRange[1];
        AvailableSlots availableSlotsAndConflict = new AvailableSlots();

        String email = userContextHolder.getCurrentUser().getEmail();
        String timeZone = "UTC";

        // Prepare request body
        JSONObject requestBody = new JSONObject();
        requestBody.put("schedules", emails);

        JSONObject startTime = new JSONObject();
        startTime.put("dateTime", startDateTime);
        startTime.put("timeZone", timeZone);
        requestBody.put("startTime", startTime);

        JSONObject endTime = new JSONObject();
        endTime.put("dateTime", endDateTime);
        endTime.put("timeZone", timeZone);
        requestBody.put("endTime", endTime);

        requestBody.put("availabilityViewInterval", slotDuration);

        // Make API call
        String url = "https://graph.microsoft.com/v1.0/users/" + email + "/calendar/getSchedule";
        String response = executePostRequest(url, requestBody.toString());

        // Parse response
        JSONObject jsonResponse = new JSONObject(response);
        JSONArray scheduleInfoArray = jsonResponse.getJSONArray("value");

        // Create response
        List<JSONObject> userWorkingHoursList = new ArrayList<>();
        // Process each schedule info to get availabilityView
        for (int i = 0; i < scheduleInfoArray.length(); i++) {
            JSONObject scheduleInfo = scheduleInfoArray.getJSONObject(i);
            if (!scheduleInfo.has("availabilityView")) {
                continue;
            }
            JSONObject userWorkingHours = scheduleInfo.getJSONObject("workingHours");
            userWorkingHoursList.add(userWorkingHours);
        }

        List<Meeting> availableTimeSlots = getAvailableTimeSlots(emails, startDateTime, slotDuration);
        WorkingHoursSummary workingHoursSummary = WorkingHoursUtil.getWorkingHoursSummary(userWorkingHoursList);
        List<Meeting> meetingList = filterMeetingSlots(availableTimeSlots, workingHoursSummary);
        availableSlotsAndConflict.setAvailableSlots(meetingList);
        return availableSlotsAndConflict;
    }

    private static <T> List<T> firstN(List<T> list, int n) {
        if (list == null || list.isEmpty()) {
            return List.of(); // or Collections.emptyList()
        }
        return list.subList(0, Math.min(n, list.size()));
    }


    public AvailabilityResponse getAvailability(List<String> emails, String startDateTime, String endDateTime, int slotDuration) throws Exception {
        log.debug("Inside @method getAvailability. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);

        // Get timezone from preferences
        String email = userContextHolder.getCurrentUser().getEmail();
        String timeZone = "UTC";

        // Prepare request body
        JSONObject requestBody = new JSONObject();
        requestBody.put("schedules", emails);

        JSONObject startTime = new JSONObject();
        startTime.put("dateTime", startDateTime);
        startTime.put("timeZone", timeZone);
        requestBody.put("startTime", startTime);

        JSONObject endTime = new JSONObject();
        endTime.put("dateTime", endDateTime);
        endTime.put("timeZone", timeZone);
        requestBody.put("endTime", endTime);

        requestBody.put("availabilityViewInterval", slotDuration);

        // Make API call
        String url = "https://graph.microsoft.com/v1.0/users/" + email + "/calendar/getSchedule";
        String response = executePostRequest(url, requestBody.toString());

        // Parse response
        JSONObject jsonResponse = new JSONObject(response);
        JSONArray scheduleInfoArray = jsonResponse.getJSONArray("value");

        // Create response
        List<TimeSlot> slots = new ArrayList<>();
        Map<String, String> availabilityMap = new HashMap<>();
        Map<String, JSONObject> workingHoursMap = new HashMap<>();
        Object organizerMeeting = null;
        List<JSONObject> userWorkingHoursList = new ArrayList<>();
        // Process each schedule info to get availabilityView
        for (int i = 0; i < scheduleInfoArray.length(); i++) {
            JSONObject scheduleInfo = scheduleInfoArray.getJSONObject(i);
            String scheduleId = scheduleInfo.getString("scheduleId");
            if (!scheduleInfo.has("availabilityView")) {
                continue;
            }
            String availabilityView = scheduleInfo.getString("availabilityView");
            JSONObject userWorkingHours = scheduleInfo.getJSONObject("workingHours");
            userWorkingHoursList.add(userWorkingHours);

            if (scheduleId.equals(email) && (availabilityView.equals("1") || availabilityView.equals("2"))) {
                JSONArray scheduleItems = scheduleInfo.getJSONArray("scheduleItems");
                if (!scheduleItems.isEmpty()) organizerMeeting = scheduleItems.get(0).toString();
            }

            availabilityMap.put(scheduleId, availabilityView);
            workingHoursMap.put(scheduleId, userWorkingHours);
        }

        // Convert availability view to time slots
        Date start = convertStringToDate(startDateTime);
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);

        // For each character in the availabilityView string
        String firstAvailabilityView = availabilityMap.values().iterator().next();
        for (int i = 0; i < firstAvailabilityView.length(); i++) {
            Date slotStart = cal.getTime();
            cal.add(Calendar.MINUTE, slotDuration);
            Date slotEnd = cal.getTime();

            List<AvailabilityStatus> availability = new ArrayList<>();
            for (String userEmail : emails) {
                String availabilityView = availabilityMap.get(userEmail);
                if (availabilityView != null && i < availabilityView.length()) {
                    char status = availabilityView.charAt(i);
                    String availabilityStatus = switch (status) {
                        case '0' -> "free";
                        case '1' -> "tentative";
                        case '2' -> "busy";
                        case '3' -> "outOfOffice";
                        case '4' -> "workingElsewhere";
                        default -> "unknown";
                    };
                    // Check for away
                    JSONObject workingHoursObject = workingHoursMap.get(userEmail);
                    String startTime1 = workingHoursObject.getString("startTime"); // e.g., "08:00:00.0000000"
                    String endTime1 = workingHoursObject.getString("endTime");     // e.g., "17:00:00.0000000"
                    String timeZone1 = workingHoursObject.getJSONObject("timeZone").getString("name");
                    String ianaTimezone = GRAPH_API_TO_IANA_TIMEZONE.getOrDefault(timeZone1, "UTC");

                    ZoneId userZoneId = ZoneId.of(ianaTimezone);
                    ZonedDateTime slotStartZoned = slotStart.toInstant().atZone(userZoneId);
                    ZonedDateTime slotEndZoned = slotEnd.toInstant().atZone(userZoneId);

                    // Extract working hours
                    LocalTime workStartTime = LocalTime.parse(startTime1.substring(0, 8)); // "08:00:00"
                    LocalTime workEndTime = LocalTime.parse(endTime1.substring(0, 8));     // "17:00:00"
                    DayOfWeek slotDay = slotStartZoned.getDayOfWeek();

                    // Check if the day is a working day
                    JSONArray workingDays = workingHoursObject.getJSONArray("daysOfWeek");
                    boolean isWorkingDay = false;
                    for (int d = 0; d < workingDays.length(); d++) {
                        if (workingDays.getString(d).equalsIgnoreCase(slotDay.name().toLowerCase())) {
                            isWorkingDay = true;
                            break;
                        }
                    }

                    // Check if slot is within working hours
                    boolean isWithinWorkingHours = isWorkingDay &&
                            !slotStartZoned.toLocalTime().isBefore(workStartTime) &&
                            !slotEndZoned.toLocalTime().isAfter(workEndTime);

                    //if slot is outside working hours, then set the status to away.
                    if (!isWithinWorkingHours) {
                        availabilityStatus = "away";
                    }

                    if (userEmail.equals(email)) {
                        availability.add(new AvailabilityStatus(userEmail, availabilityStatus, organizerMeeting));
                    } else {
                        availability.add(new AvailabilityStatus(userEmail, availabilityStatus, null));
                    }
                }
            }

            slots.add(new TimeSlot(slotStart, slotEnd, availability));
        }

        WorkingHoursSummary workingHoursSummary = WorkingHoursUtil.getWorkingHoursSummary(userWorkingHoursList);
//        List<TimeSlot> timeSlots = filterSlots(slots, workingHoursSummary);

        List<Meeting> availableSlots = getAvailableTimeSlots(emails, startDateTime, slotDuration);
        List<Meeting> meetingList = filterMeetingSlots(availableSlots, workingHoursSummary);

        List<Meeting> finalMeetingList = firstN(meetingList, 4);
        return new AvailabilityResponse(slots, finalMeetingList);
    }

    private List<Meeting> getAvailableTimeSlots(List<String> emails, String startDateTime, int slotDuration) throws Exception {
        Date startDate = convertStringToDate(startDateTime);

        LocalDateTime localDateTime = DateUtils.convertToLocalDateTime(startDateTime);
        localDateTime = DateUtils.roundOffTime(localDateTime);

        LocalDateTime plus7Days = localDateTime.plusDays(7);
        String date7DaysAhead = DateUtils.convertToUtcIsoString(plus7Days);
        Date end7DaysAhead = convertStringToDate(date7DaysAhead);
        Map<String, List<Meeting>> resultForNext7Days = fetchCalendarEventsForEmailsV1(emails, startDateTime, date7DaysAhead, slotDuration);
        return findAvailableTimeSlots(resultForNext7Days, startDate, end7DaysAhead, slotDuration * 60 * 1000L);
    }


    private String executePostRequest(String url, String requestBody) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
            request.setHeader(EmailConstants.PREFER, "outlook.timezone=\"" + "UTC" + "\"");
            request.setEntity(new StringEntity(requestBody));

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();
                String apiResponse = EntityUtils.toString(entity);
                if (statusCode == 200) {
                    return apiResponse;
                } else {
                    log.error("error from /getSchedule : {}", apiResponse);
                }
                throw new Exception("Failed to get availability. Status code: " + statusCode + " Message: " + apiResponse);
            }
        }
    }

    private List<Meeting> filterMeetingSlots(List<Meeting> meetingList, WorkingHoursSummary workingHoursSummary) {
        List<TimeSlot> timeSlots = meetingList.stream().map(meeting -> new TimeSlot(meeting.getStartTime(), meeting.getEndTime(), null)).toList();

        List<TimeSlot> timeSlots1 = filterSlots(timeSlots, workingHoursSummary);

        return timeSlots1.stream().map(timeSlot -> new Meeting(timeSlot.getStartTime(), timeSlot.getEndTime())).toList();
    }

    private List<TimeSlot> filterSlots(List<TimeSlot> slots, WorkingHoursSummary summary) {
        if (summary == null || summary.getOverlappingTimeRangesUTC().isEmpty()) return List.of();

        return slots.stream()
                .filter(slot -> {
                    ZonedDateTime zdt = slot.getStartTime().toInstant().atZone(ZoneOffset.UTC);
                    String day = zdt.getDayOfWeek().name().toLowerCase();
                    int minutes = zdt.getHour() * 60 + zdt.getMinute();

                    List<WorkingHoursSummary.TimeRange> dayRanges = summary.getOverlappingTimeRangesUTC().get(day);
                    if (dayRanges == null) return false;

                    for (WorkingHoursSummary.TimeRange range : dayRanges) {
                        if (minutes >= range.getStartMinutesUTC() && minutes < range.getEndMinutesUTC()) {
                            return true;
                        }
                    }
                    return false;
                })
                .collect(Collectors.toList());
    }

    public Map<String, String> cancelEvent(String eventId, String comment) throws Exception {
        String emailId = userContextHolder.getCurrentUser().getEmail();
        String url = String.format(GRAPH_API_CANCEL_EVENT, emailId, eventId);
        log.info("Attempting to cancel event for user: {}, eventId: {}", emailId, eventId);

        JSONObject requestBody = new JSONObject();
        requestBody.put("Comment", comment);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Authorization", "Bearer " + tokenUtils.getAccessToken());

            StringEntity entity = new StringEntity(requestBody.toString());
            httpPost.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                Map<String, String> result = new HashMap<>();
                String responseBody = EntityUtils.toString(response.getEntity());
                log.debug("Received response with status code: {} and body: {}", statusCode, responseBody);

                if (statusCode == 202) {
                    result.put("status", "success");
                    result.put("message", "Event cancelled successfully");
                    log.info("Successfully cancelled event for user: {}, eventId: {}", emailId, eventId);
                } else {
                    // Event not found or already cancelled
                    result.put("status", "failed");
                    JSONObject errorJson = new JSONObject(responseBody);
                    String errorMessage = errorJson.getJSONObject("error").getString("message");
                    result.put("message", errorMessage);
                    log.warn("Failed to cancel event for user: {}, eventId: {}, error: {}", emailId, eventId, errorMessage);
                }
                return result;
            }
        } catch (Exception e) {
            log.error("Error occurred while cancelling event for user: {}, eventId: {}, error: {}", emailId, eventId, e.getMessage(), e);
            throw e;
        }
    }

    public Map<String, String> forwardEvent(String eventId, List<String> emailIds, String comment) throws IOException, InterruptedException {
        log.debug("Inside @method forwardEvent. eventId: {}, emailIds: {}", eventId, emailIds);
        if (eventId == null || emailIds == null || emailIds.isEmpty()) {
            log.error("Required parameters missing. eventId: {}, emailIds: {}", eventId, emailIds);
            return Map.of(
                EmailConstants.STATUS, EmailConstants.FAILED,
                EmailConstants.MESSAGE, "Event ID and at least one email ID are required"
            );
        }

        String emailId = userContextHolder.getCurrentUser().getEmail();
        try {
            String url = String.format(GRAPH_API_FORWARD_EVENT, emailId, eventId);
            
            JSONObject requestBody = new JSONObject();
            if (comment != null && !comment.trim().isEmpty()) {
                requestBody.put("Comment", comment);
            }
            
            JSONArray recipients = new JSONArray();
            for (String email : emailIds) {
                JSONObject recipientObj = new JSONObject();
                JSONObject emailAddressObj = new JSONObject();
                emailAddressObj.put("Address", email);
                recipientObj.put("EmailAddress", emailAddressObj);
                recipients.put(recipientObj);
            }
            requestBody.put("ToRecipients", recipients);

            log.debug("Sending forward request to Graph API. URL: {}, Request body: {}", url, requestBody);
            
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeader("Authorization", "Bearer " + tokenUtils.getAccessToken());
                httpPost.setHeader("Content-Type", "application/json");
                
                StringEntity entity = new StringEntity(requestBody.toString());
                httpPost.setEntity(entity);

                try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                    int statusCode = response.getStatusLine().getStatusCode();
                    Map<String, String> result = new HashMap<>();
                    String responseBody = EntityUtils.toString(response.getEntity());
                    log.debug("Received response with status code: {} and body: {}", statusCode, responseBody);

                    if (statusCode == 202) {
                        result.put("status", "success");
                        result.put("message", "Event forwarded successfully");
                        log.info("Successfully forwarded event for user: {}, eventId: {}", emailId, eventId);
                    } else {
                        result.put("status", "failed");
                        JSONObject errorJson = new JSONObject(responseBody);
                        String errorMessage = errorJson.getJSONObject("error").getString("message");
                        result.put("message", errorMessage);
                        log.warn("Failed to forward event for user: {}, eventId: {}, error: {}", emailId, eventId, errorMessage);
                    }
                    return result;
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while forwarding event for user: {}, eventId: {}, error: {}", emailId, eventId, e.getMessage(), e);
            throw e;
        }
    }

    public EventDto getCalendarEventByEventId(String eventId) {
        String userEmail = userContextHolder.getCurrentUser().getEmail();
        if (userEmail == null || userEmail.isEmpty()) {
            throw new RuntimeException("User email not found in context");
        }

        String url = String.format(GRAPH_API_GET_EVENT, userEmail, eventId);
        log.debug("URL to get calendar event: {}", url);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();
                
                if (entity != null && statusCode == 200) {
                    String responseBody = EntityUtils.toString(entity);
                    JSONObject jsonObject = new JSONObject(responseBody);
                    return getEventDto(jsonObject);
                } else {
                    log.error("Error fetching calendar event. Status code: {}, Response: {}", statusCode, entity != null ? EntityUtils.toString(entity) : "No response body");
                    throw new BusinessException("Failed to fetch calendar event. Status code: " + statusCode);
                }
            }
        } catch (Exception e) {
            log.error("Error in getCalendarEventByEventId: {}", e.getMessage(), e);
            throw new BusinessException("Error fetching calendar event: " + e.getMessage());
        }
    }
}
