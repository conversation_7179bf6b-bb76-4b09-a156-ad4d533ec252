package com.enttribe.emailagent.jenkins;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class JenkinsJobManager {

    private static final Logger log = LoggerFactory.getLogger(JenkinsJobManager.class);
    static String jenkinsUrl = "https://dev.visionwaves.com/jenkins";

    public static void main(String[] args) {
        String jenkinsUser = "deepak";
        String jenkinsToken = "1115330afa1a6afc68703994d23595ac7f";
        String jobName = "MicroFrontend_Dev_angular_17";

        JenkinsApiClient client = new JenkinsApiClient(jenkinsUrl, jenkinsUser, jenkinsToken);
        JenkinsBuildTrigger buildTrigger = new JenkinsBuildTrigger(client);
        JenkinsQueueChecker queueChecker = new JenkinsQueueChecker(client);
        JenkinsBuildStatusChecker buildStatusChecker = new JenkinsBuildStatusChecker(client);

        try {
            // Step 1: Trigger Build
            JSONObject buildParams = new JSONObject();
            buildParams.put("param1", "value1");
            buildParams.put("param2", "value2");

            //location header value contains queue url
            String queueUrl = buildTrigger.triggerBuild(jenkinsUrl + "/job/" + jobName, buildParams);
            log.debug("Build Triggered!");

            // Step 2: Check Queue Status
            String buildUrl = queueChecker.waitForBuildToStart(queueUrl);
            log.debug("Build URL: {}", buildUrl);

            String blueOceanUrl = getBlueOceanUrl(buildUrl);
            String buildStatus = buildStatusChecker.checkBuildStatus(blueOceanUrl);

            log.debug("Build Status: {}", buildStatus);
        } catch (Exception e) {
            log.error("error in jenkins job manager : {}", e.getMessage(), e);
        }
    }

    private static String getBlueOceanUrl(String buildUrl) {
        String[] buildUrlParts = buildUrl.split("/");
        String buildId = buildUrlParts[buildUrlParts.length - 1];
        String jobName = buildUrlParts[buildUrlParts.length - 2];
        return String.format("%s/blue/rest/organizations/jenkins/pipelines/%s/runs/%s/", jenkinsUrl, jobName, buildId);
    }

}
