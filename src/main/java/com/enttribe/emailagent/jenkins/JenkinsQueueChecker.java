package com.enttribe.emailagent.jenkins;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class JenkinsQueueChecker {

    private static final Logger log = LoggerFactory.getLogger(JenkinsQueueChecker.class);
    private final JenkinsApiClient jenkinsClient;

    public JenkinsQ<PERSON>ue<PERSON><PERSON><PERSON>(JenkinsApiClient client) {
        this.jenkinsClient = client;
    }

    public String waitForBuildToStart(String queueUrl) throws IOException, InterruptedException {
        while (true) {
            String response = jenkinsClient.sendGetRequest(queueUrl + "/api/json");
            JSONObject json = new JSONObject(response);

            if (json.has("executable")) {
                return json.getJSONObject("executable").getString("url");
            }

            log.debug("Waiting for job to start...");
            TimeUnit.SECONDS.sleep(2);
        }
    }
}
