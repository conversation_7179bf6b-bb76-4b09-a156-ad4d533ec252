package com.enttribe.emailagent.jenkins;

import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class JenkinsBuildStatusChecker {

    private static final Logger log = LoggerFactory.getLogger(JenkinsBuildStatusChecker.class);
    private final JenkinsApiClient jenkinsClient;

    public JenkinsBuildStatusChecker(JenkinsApiClient client) {
        this.jenkinsClient = client;
    }

    public String checkBuildStatus(String blueOceanUrl) throws IOException{

        String response = jenkinsClient.sendGetRequest(blueOceanUrl);
        log.debug("response from blueOceanUrl : {}", blueOceanUrl);

        JSONObject json = new JSONObject();
        try {
            json = new JSONObject(response);
        } catch (JSONException e) {
            log.error("unable to parse json : {}", response);
        }

        String state = json.optString("state", "UNKNOWN");
        String result = json.optString("result", "UNKNOWN");
        log.debug("state : {} / result : {}", state, result);

        if ("FINISHED".equalsIgnoreCase(state)) {
            return "SUCCESS".equalsIgnoreCase(result) ? "Build Successful!" : getBuildLogs(json);
        } else {
            return null;
        }

    }

    private String getBuildLogs(JSONObject json) throws IOException {
        String logUrl = json.getJSONObject("_links").getJSONObject("log").getString("href");
        String fullLogUrl = "https://dev.visionwaves.com/jenkins" + logUrl;
        return jenkinsClient.sendGetRequest(fullLogUrl);
    }

}
