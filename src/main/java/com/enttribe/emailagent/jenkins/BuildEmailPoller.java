package com.enttribe.emailagent.jenkins;

import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.dao.IBuildEmailDao;
import com.enttribe.emailagent.dto.UserEmailDto;
import com.enttribe.emailagent.dto.UserEmailResponseDto;
import com.enttribe.emailagent.entity.BuildApplication;
import com.enttribe.emailagent.entity.BuildControl;
import com.enttribe.emailagent.entity.BuildEmails;
import com.enttribe.emailagent.entity.PollTimeInfo;
import com.enttribe.emailagent.entity.ProductionBuild;
import com.enttribe.emailagent.repository.BuildApplicationDao;
import com.enttribe.emailagent.repository.BuildControlDao;
import com.enttribe.emailagent.repository.ProductionBuildDao;
import com.enttribe.emailagent.service.DeploymentService;
import com.enttribe.emailagent.service.EmailService;
import com.enttribe.emailagent.service.GraphIntegrationService;
import com.enttribe.emailagent.service.PollTimeInfoService;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.RequiredArgsConstructor;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class BuildEmailPoller {

    @Value("classpath:draft/build_success.st")
    private Resource successDraft;

    @Value("classpath:draft/build_fail.st")
    private Resource failedDraft;

    @Value("classpath:draft/format_issue.st")
    private Resource formatIssue;

    @Value("${build.approval.list}")
    private List<String> approver;

    private final List<String> supportedEnvironments = List.of("dev", "demo", "sai-dev", "sai-demo", "sai-sit", "sai-prod","hos-dev","hos-tst");

    private static final Logger log = LoggerFactory.getLogger(BuildEmailPoller.class);

    private final BuildControlDao buildControlDao;
    private final IBuildEmailDao iBuildEmailDao;
    private final BuildApplicationDao buildApplicationDao;
    private final GraphIntegrationService graphIntegrationService;
    private final EmailService emailService;
    private final PollTimeInfoService pollTimeInfoService;
    private final AiChatModel aiChatModel;
    private final DeploymentService deploymentService;
    private final ProductionBuildDao productionBuildDao;

    private final String emailId = "<EMAIL>";

    private static final Map<String, JobDetails> jenkinsMap = new HashMap<>();

    static {
        JobDetails demoJobDetails = new JobDetails("https://dev.visionwaves.com/jenkins",
                "MicroFrontend_Demo_angular_17",
                "Backend_Microservice_Demo_java_21",
                "Shell-App_Demo",
                "admin", "11629737631912200622ebe10fcdce3279");


        JobDetails devJobDetails = new JobDetails("https://dev.visionwaves.com/jenkins",
                "MicroFrontend_Dev_angular_17",
                "Backend_Microservice_Dev_java_21",
                "Shell-App_Dev",
                "admin", "11629737631912200622ebe10fcdce3279");

        JobDetails saiDemoJobDetails = new JobDetails("https://humainos-demo.thefutureai.co/jenkins/",
                "MicroFrontend_Cd_Pipeline",
                "Backend_Microservice_Cd_Pipeline",
                "Shell_Cd_Pipeline",
                "developer", "1112ad884d73422b474b9fdbb8ae376227");

        JobDetails saiDevJobDetails = new JobDetails("http://jenkins-dev.thefutureai.co:8080",
                "MicroFrontend_Cd_Pipeline",
                "Backend_Microservice_Cd_Pipeline",
                "Shell_Cd_Pipeline",
                "dev_user", "114fdb7e0a2dfa065edd19369974f7ca38");

        JobDetails saiSitJobDetails = new JobDetails("http://jenkins-sit.thefutureai.co:8080",
                "MicroFrontend_Cd_Pipeline",
                "Backend_Microservice_Cd_Pipeline",
                "Shell_Cd_Pipeline",
                "dev_user", "119d644cf87b7b8d7058fb6540efac0c34");

        JobDetails hosDevJobDetails = new JobDetails("https://marketplace-dev.thefutureai.co/jenkins/",
                "MicroFrontend_Cd_Pipeline",
                "Backend_Microservice_Cd_Pipeline",
                "Shell_Cd_Pipeline",
                "developer", "11f32503552f1203b78fe839134b1f0edd");

        JobDetails hosTstJobDetails = new JobDetails("https://marketplace-sit.thefutureai.co/jenkins/",
                "MicroFrontend_Cd_Pipeline",
                "Backend_Microservice_Cd_Pipeline",
                "Shell_Cd_Pipeline",
                "developer", "1135751f50a48f8bbe31d804cd416052f2");

        JobDetails saiProdJobDetails = new JobDetails("https://humainos.thefutureai.co/jenkins/",
                "MicroFrontend_Cd_Pipeline",
                "Backend_Microservice_Cd_Pipeline",
                "Shell_Cd_Pipeline",
                "developer", "116c0ed9eaf87e951c2f58af25cb8b747e");

        jenkinsMap.put("dev", devJobDetails);
        jenkinsMap.put("demo", demoJobDetails);
        jenkinsMap.put("sai-demo", saiDemoJobDetails);
        jenkinsMap.put("sai-dev", saiDevJobDetails);
        jenkinsMap.put("sai-sit", saiSitJobDetails);
        jenkinsMap.put("hos-dev", hosDevJobDetails);
        jenkinsMap.put("hos-tst", hosTstJobDetails);
        jenkinsMap.put("sai-prod", saiProdJobDetails);

    }

    record JobDetails(String jenkinsUrl, String frontendJobName, String backendJobName,
                      String shellAppJobName, String jenkinsUser, String jenkinsToken) {
    }

//    @Scheduled(fixedDelay = 3 * 60 * 1000, initialDelay = 30000)
    public void pollBuildEmail() {
        log.info("started polling build emails");
        List<UserEmailDto> emails = getBuildEmails();
        log.info("total {} build emails are being polled at {}", emails.size(), LocalDateTime.now());

        for (UserEmailDto dto : emails) {
            String conversationId = dto.getConversationId();
            String internetMessageId = dto.getInternetMessageId();
            String messageId = dto.getId();
            String subject = dto.getSubject();
            String body = dto.getBody();
            String from = dto.getFrom();

            log.info("polling build email with subject : {}", subject);
            BuildEmails buildEmails = iBuildEmailDao.getBuildEmailByInternetMessageId(internetMessageId);
            if (buildEmails == null) {
                buildEmails = new BuildEmails();
            }

            buildEmails.setConversationId(conversationId);
            buildEmails.setMessageId(messageId);
            buildEmails.setContent(body);
            buildEmails.setInternetMessageId(internetMessageId);
            buildEmails.setSubject(subject);
            buildEmails.setEmailFrom(from);
            buildEmails.setCreatedTime(new Date());

            String type = null;
            Boolean isApproved = null;
            String jobFor = null;
            String applicationName = null;
            String environment1 = null;
            try {
                if (subject.toLowerCase().contains("backend")) {
                    BackendParameters backendParameters = aiChatModel.chatCompletion(
                            "EMAIL_assistant_APP_NAME-Jenkins-Parameter_extractor-v-1",
                            Map.of("subject", subject, "mailContent", body),
                            BackendParameters.class);
                    type = backendParameters.TYPE();
                    isApproved = backendParameters.APPROVED();
                    jobFor = "backend";
                    applicationName = backendParameters.APPLICATION_NAME();
                    environment1 = backendParameters.ENV();
                    buildEmails.setBuildParameters(AIUtils.convertToJSON(backendParameters, true));
                } else if (subject.toLowerCase().contains("frontend")) {
                    FrontendParameters frontendParameters = aiChatModel.chatCompletion(
                            "EMAIL_assistant_APP_NAME-Jenkins-Parameter_extractor-v-1",
                            Map.of("subject", subject, "mailContent", body),
                            FrontendParameters.class);
                    type = frontendParameters.TYPE();
                    isApproved = frontendParameters.APPROVED();
                    jobFor = "frontend";
                    applicationName = frontendParameters.APPLICATION_NAME();
                    environment1 = frontendParameters.ENV();
                    buildEmails.setBuildParameters(AIUtils.convertToJSON(frontendParameters, true));
                } else {
                    log.error("unable to determine job for frontend or backend");
                    buildEmails.setError("unable to determine job for frontend or backend");
                    buildEmails.setStatus(BuildConstants.ERROR);
                    String issue = "we were unable to determine whether the build is for frontend or backend";
                    String draft = AIUtils.getResolvedPrompt(formatIssue, Map.of("email", dto.getFrom(), "issue", issue));
                    deploymentService.replyBuildEmail(emailId, dto.getId(), draft);
                    iBuildEmailDao.save(buildEmails);
                    //send email
                    continue;
                }

                boolean isProductionJob = !(environment1.equalsIgnoreCase("dev") || environment1.equalsIgnoreCase("demo"));

                buildEmails.setIsProductionJob(isProductionJob);
                buildEmails.setApplicationName(applicationName);
                buildEmails.setType(type);

                if (type == null) {
                    log.error("type is null.");
                    //send email to Shiv and Deepak.
                } else if (type.equalsIgnoreCase(BuildConstants.APPROVER)) {
                    log.debug("processing approver's email. approver : {}", dto.getFrom());
                    if (approver.contains(dto.getFrom().toLowerCase())) {
                        List<BuildEmails> forApprovalEmails = iBuildEmailDao.getForApprovalEmails(conversationId);
                        if (forApprovalEmails.isEmpty()) continue;
                        BuildEmails emailPendingForApproval = forApprovalEmails.getFirst();
                        String buildParameters = emailPendingForApproval.getBuildParameters();
                        boolean forProduction = emailPendingForApproval.getIsProductionJob();

                        JSONObject buildParams = new JSONObject(buildParameters);
                        String environment = buildParams.optString("ENV", null);
                        if (environment == null || !supportedEnvironments.contains(environment.toLowerCase())) {
                            log.error("unable to determine environment for deployment");
                            emailPendingForApproval.setStatus(BuildConstants.ERROR);
                            emailPendingForApproval.setError("unsupported environment!!!");
                            iBuildEmailDao.save(emailPendingForApproval);
                            throw new RuntimeException("unsupported environment!!!");
                        }

                        Integer successfullDeploymentCount = buildControlDao.findSuccessDeploymentCountByDate(emailPendingForApproval.getApplicationName(), LocalDate.now(ZoneId.of("Asia/Kolkata")), environment);
                        BuildApplication applicationDetail = buildApplicationDao.findByApplicationName(emailPendingForApproval.getApplicationName(), environment);
                        int allowedDeployment;
                        if (applicationDetail == null || applicationDetail.getAllowedDeployment() == null) {
                            log.warn("there is no application detail found for {}. going to create one", emailPendingForApproval.getApplicationName());
                            allowedDeployment = 2;
                            BuildApplication buildApplication = new BuildApplication();
                            buildApplication.setApplicationName(emailPendingForApproval.getApplicationName());
                            buildApplication.setEnvironment(environment);
                            buildApplication.setAllowedDeployment(allowedDeployment);
                            buildApplicationDao.save(buildApplication);
                        } else {
                            allowedDeployment = applicationDetail.getAllowedDeployment();
                        }

                        log.debug("allowedDeployment : {} for application : {}", allowedDeployment, emailPendingForApproval.getApplicationName());
                        if (successfullDeploymentCount == null) successfullDeploymentCount = 0;
                        if (successfullDeploymentCount >= allowedDeployment) {
                            deploymentService.replyBuildEmail(emailId, dto.getId(), LIMITED_DEPLOYMENT);
                            continue;
                        }
                        if (isApproved) {
                            emailPendingForApproval.setReplyTo(dto.getId());
                            try {

                                //here I need to choose from dev and demo only.
                                JobDetails jenkinsDetails = null;
                                if (forProduction) {
                                    jenkinsDetails = jenkinsMap.get("dev");
                                } else {
                                    jenkinsDetails = jenkinsMap.get(environment.toLowerCase());
                                }
                                JenkinsApiClient client = new JenkinsApiClient(jenkinsDetails.jenkinsUrl(), jenkinsDetails.jenkinsUser(), jenkinsDetails.jenkinsToken());
                                JenkinsBuildTrigger buildTrigger = new JenkinsBuildTrigger(client);
                                JenkinsQueueChecker queueChecker = new JenkinsQueueChecker(client);

                                //location header value contains queue url
                                //Step 1: Trigger the build
                                String queueUrl = null;
                                if (jobFor.equals("frontend")) {
                                    boolean isShellApp = buildParams.optBoolean("IS_SHELL_APP", false);
                                    String jobName = isShellApp ? jenkinsDetails.shellAppJobName() : jenkinsDetails.frontendJobName();
                                    if (forProduction) {
                                        jobName = isShellApp ? "Gcp_Shell_Ci_Pipeline" : "Gcp_MicroFrontend_Ci_Pipeline";
                                    }
                                    log.debug("job name is : {}", jobName);
                                    queueUrl = buildTrigger.triggerBuild(jenkinsDetails.jenkinsUrl() + "/job/" + jobName, buildParams);
                                } else {
                                    String jobName = forProduction ? "Gcp_Backend_Microservice_Ci_Pipeline" : jenkinsDetails.backendJobName();
                                    queueUrl = buildTrigger.triggerBuild(jenkinsDetails.jenkinsUrl() + "/job/" + jobName, buildParams);
                                }
                                log.debug("Build Triggered! Queue url is : {}", queueUrl);
                                emailPendingForApproval.setQueueUrl(queueUrl);

                                // Step 2: Check Queue Status
                                String buildUrl = queueChecker.waitForBuildToStart(queueUrl);
                                log.debug("Build URL: {}", buildUrl);
                                emailPendingForApproval.setBuildUrl(buildUrl);

                                // Step 3: Check Build Status
                                String blueOceanUrl = getBlueOceanUrl(buildUrl, jenkinsDetails.jenkinsUrl());
                                emailPendingForApproval.setStatus(BuildConstants.WAITING);
                                emailPendingForApproval.setBlueOceanUrl(blueOceanUrl);
                                emailPendingForApproval.setApprovedBy(dto.getFrom());

                            } catch (Exception e) {
                                log.error("error in Jenkins pipeline : {}", e.getMessage(), e);
                                emailPendingForApproval.setStatus(BuildConstants.ERROR);
                                emailPendingForApproval.setError(e.getMessage());
                                sendUnexpectedErrorMail(emailPendingForApproval.getReplyTo(), emailPendingForApproval.getInternetMessageId());
                            }
                        } else {
                            emailPendingForApproval.setError("Email is not approved by : " + dto.getFrom());
                        }
                        iBuildEmailDao.save(emailPendingForApproval);
                    } else {
                        log.error("{} is not allowed to approve jenkins pipeline.", dto.getFrom());
                        deploymentService.replyBuildEmail(emailId, dto.getId(), UNAUTHORIZED);
                        buildEmails.setStatus(BuildConstants.ERROR);
                        buildEmails.setError("not allowed to approve jenkins pipeline");
                    }
                } else if (type.equalsIgnoreCase(BuildConstants.REQUESTEE)) {
                    buildEmails.setStatus(BuildConstants.PENDING);
                }
            } catch (Exception e) {
                log.error("error processing email with subject : {}", dto.getSubject());
                buildEmails.setStatus(BuildConstants.ERROR);
                buildEmails.setError(e.getMessage());
                sendUnexpectedErrorMail(buildEmails.getMessageId(), buildEmails.getInternetMessageId());
            }

            iBuildEmailDao.save(buildEmails);
            log.debug("build email successfully saved. subject : {}", buildEmails.getSubject());
        }
    }

//    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 60000)
    public void checkProductionBuildStatus() {
        log.info("started checking build status of production builds");
        List<ProductionBuild> waitingBuildEmails = productionBuildDao.getWaitingProductionBuild();
        for (ProductionBuild waitingEmail : waitingBuildEmails) {
            try {
                String buildParameters = waitingEmail.getBuildParameters();
                JSONObject buildParams = new JSONObject(buildParameters);

                String environment = buildParams.optString("ENV", null);
                JobDetails jenkinsDetails = jenkinsMap.get(environment.toLowerCase());
                JenkinsApiClient client = new JenkinsApiClient(jenkinsDetails.jenkinsUrl(), jenkinsDetails.jenkinsUser(), jenkinsDetails.jenkinsToken());
                JenkinsBuildStatusChecker buildStatusChecker = new JenkinsBuildStatusChecker(client);

                String buildStatus = buildStatusChecker.checkBuildStatus(waitingEmail.getBlueOceanUrl());
                if (buildStatus == null) {
                    log.info("build is still not finished for production for application : {}", waitingEmail.getApplicationName());
                    continue;
                }
                log.debug("Build Status for production: {}", buildStatus);

                String applicationName = buildParams.optString("APPLICATION_NAME");
                String branch = buildParams.optString("GIT_BRANCH");
                String gitTag = buildParams.optString("GIT_TAG");
                String env = buildParams.optString("ENV");

                String buildUrl = waitingEmail.getBuildUrl();
                String buildNumber = extractBuildNumber(buildUrl);
                Map<String, Object> variableMap = Map.of("APPLICATION_NAME", applicationName,
                        "ENV", env, "GIT_TAG", gitTag,
                        "BRANCH", branch, "BUILD_NUMBER", buildNumber);


                if (buildStatus.contains("Build Successful!")) {
                    //Send confirmation email for successful deployment
                    String draft = AIUtils.getResolvedPrompt(successDraft, variableMap);
                    deploymentService.replyBuildEmail(emailId, waitingEmail.getReplyTo(), draft);
                    waitingEmail.setStatus(BuildConstants.APPROVED);
                    waitingEmail.setDeployedDate(new Date());
                    List<BuildControl> deployment = buildControlDao.findByApplicationNameAndDeploymentDate(waitingEmail.getApplicationName(), LocalDate.now(ZoneId.of("Asia/Kolkata")), environment);
                    if (deployment.size() > 1) {
                        deploymentService.replyBuildEmail(emailId, waitingEmail.getReplyTo(), """
                                Dear team,
                                
                                Your deployment is successfully done but some other issue is observed.
                                
                                Please contact Deepak Dangi(<EMAIL>).
                                
                                Best Regards,
                                AI Devops(visionwaves) Where automation comes first!!!
                                """);
                    } else if (deployment.size() == 1) {
                        BuildControl deploymentFirst = deployment.getFirst();
                        int successCount = deploymentFirst.getSuccessCount() == null ? 0 : deploymentFirst.getSuccessCount();
                        deploymentFirst.setSuccessCount(successCount + 1);
                        deploymentFirst.setEnvironment(environment);
                        buildControlDao.save(deploymentFirst);
                    } else {
                        BuildControl buildControl = new BuildControl();
                        buildControl.setApplicationName(waitingEmail.getApplicationName());
                        buildControl.setSuccessCount(1);
                        buildControl.setEnvironment(environment);
                        buildControl.setDeploymentDate(LocalDate.now(ZoneId.of("Asia/Kolkata")));
                        buildControlDao.save(buildControl);
                    }

                } else {
                    ProductionBuild productionBuild = productionBuildDao.getProductionBuildByInternetMessageId(waitingEmail.getInternetMessageId());
                    productionBuild.setStatus(BuildConstants.ERROR);
                    productionBuildDao.save(productionBuild);

                    //Send email for failed deployment with error logs
                    waitingEmail.setStatus(BuildConstants.ERROR);
                    String draft = AIUtils.getResolvedPrompt(failedDraft, variableMap);
                    BufferedInputStream attachmentStream = new BufferedInputStream
                            (new ByteArrayInputStream(buildStatus.getBytes(StandardCharsets.UTF_8)));
                    deploymentService.replyWithAttachment(emailId, waitingEmail.getReplyTo(), draft, attachmentStream, "pipeline_error.log");

                    List<BuildControl> deployment = buildControlDao.findByApplicationNameAndDeploymentDate(waitingEmail.getApplicationName(), LocalDate.now(ZoneId.of("Asia/Kolkata")), environment);

                    if (deployment.size() > 1) {
                        deploymentService.replyBuildEmail(emailId, waitingEmail.getReplyTo(), """
                                Dear team,
                                
                                Your deployment is unsuccessful.
                                
                                Please contact Deepak Dangi(<EMAIL>).
                                
                                Best Regards,
                                AI Devops(visionwaves) Where automation comes first!!!
                                """);
                    } else if (deployment.size() == 1) {
                        BuildControl deploymentFirst = deployment.getFirst();
                        int failedCount = deploymentFirst.getFailedCount() == null ? 0 : deploymentFirst.getFailedCount();
                        deploymentFirst.setFailedCount(failedCount + 1);
                        deploymentFirst.setEnvironment(environment);
                        buildControlDao.save(deploymentFirst);
                    } else {
                        BuildControl buildControl = new BuildControl();
                        buildControl.setApplicationName(waitingEmail.getApplicationName());
                        buildControl.setFailedCount(1);
                        buildControl.setEnvironment(environment);
                        buildControl.setDeploymentDate(LocalDate.now(ZoneId.of("Asia/Kolkata")));
                        buildControlDao.save(buildControl);
                    }

                }

            } catch (Exception e) {
                log.error("error in Jenkins pipeline : {}", e.getMessage(), e);
                waitingEmail.setStatus(BuildConstants.ERROR);
                waitingEmail.setError(e.getMessage());
                sendUnexpectedErrorMail(waitingEmail.getReplyTo(), waitingEmail.getInternetMessageId());
            }
            productionBuildDao.save(waitingEmail);
        }
    }


//    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 30000)
    public void checkBuildStatus() {
        log.info("started checking build status");
        List<BuildEmails> waitingBuildEmails = iBuildEmailDao.getWaitingBuildEmails();
        for (BuildEmails waitingEmail : waitingBuildEmails) {
            try {
                String buildParameters = waitingEmail.getBuildParameters();
                JSONObject buildParams = new JSONObject(buildParameters);

                String environment = buildParams.optString("ENV", null);
                JobDetails jenkinsDetails = jenkinsMap.get("dev");
                JenkinsApiClient client = new JenkinsApiClient(jenkinsDetails.jenkinsUrl(), jenkinsDetails.jenkinsUser(), jenkinsDetails.jenkinsToken());
                JenkinsBuildStatusChecker buildStatusChecker = new JenkinsBuildStatusChecker(client);

                String buildStatus = buildStatusChecker.checkBuildStatus(waitingEmail.getBlueOceanUrl());
                if (buildStatus == null) {
                    log.info("build is still not finished for application : {}", waitingEmail.getApplicationName());
                    continue;
                }
//                log.debug("Build Status: {}", buildStatus);

                String applicationName = buildParams.optString("APPLICATION_NAME");
                String branch = buildParams.optString("GIT_BRANCH");
                String gitTag = buildParams.optString("GIT_TAG");
                String env = buildParams.optString("ENV");

                String buildUrl = waitingEmail.getBuildUrl();
                String buildNumber = extractBuildNumber(buildUrl);
                Map<String, Object> variableMap = Map.of("APPLICATION_NAME", applicationName,
                        "ENV", env, "GIT_TAG", gitTag,
                        "BRANCH", branch, "BUILD_NUMBER", buildNumber);

                Boolean forProduction = waitingEmail.getIsProductionJob();
                if (forProduction) {
                    ProductionBuild productionBuild = ProductionBuild.builder()
                            .buildEmail(waitingEmail)
                            .internetMessageId(waitingEmail.getInternetMessageId())
                            .approvedBy(waitingEmail.getApprovedBy())
                            .replyTo(waitingEmail.getReplyTo())
                            .createdTime(new Date())
                            .status(BuildConstants.PENDING)
                            .buildParameters(buildParameters)
                            .applicationName(waitingEmail.getApplicationName())
                            .build();
                    productionBuildDao.save(productionBuild);
                }

                processPipelineInformation(waitingEmail, buildStatus, variableMap, environment);
            } catch (Exception e) {
                log.error("error in Jenkins pipeline : {}", e.getMessage(), e);
                waitingEmail.setStatus(BuildConstants.ERROR);
                waitingEmail.setError(e.getMessage());
                sendUnexpectedErrorMail(waitingEmail.getMessageId(), waitingEmail.getInternetMessageId());
            }
            iBuildEmailDao.save(waitingEmail);
        }

    }

    private void processPipelineInformation(BuildEmails waitingEmail, String buildStatus, Map<String, Object> variableMap, String environment) throws Exception {
        Boolean forProduction = waitingEmail.getIsProductionJob();

        if (buildStatus.contains("Build Successful!") && !forProduction) {
            //Send confirmation email for successful deployment
            String draft = AIUtils.getResolvedPrompt(successDraft, variableMap);
            deploymentService.replyBuildEmail(emailId, waitingEmail.getReplyTo(), draft);
            waitingEmail.setStatus(BuildConstants.APPROVED);
            waitingEmail.setDeployedDate(new Date());
            List<BuildControl> deployment = buildControlDao.findByApplicationNameAndDeploymentDate(waitingEmail.getApplicationName(), LocalDate.now(ZoneId.of("Asia/Kolkata")), environment);
            if (deployment.size() > 1) {
                deploymentService.replyBuildEmail(emailId, waitingEmail.getReplyTo(), """
                        Dear team,
                        
                        Your deployment is successfully done but some other issue is observed.
                        
                        Please contact Deepak Dangi(<EMAIL>).
                        
                        Best Regards,
                        AI Devops(visionwaves) Where automation comes first!!!
                        """);
            } else if (deployment.size() == 1) {
                BuildControl deploymentFirst = deployment.getFirst();
                int successCount = deploymentFirst.getSuccessCount() == null ? 0 : deploymentFirst.getSuccessCount();
                deploymentFirst.setSuccessCount(successCount + 1);
                deploymentFirst.setEnvironment(environment);
                buildControlDao.save(deploymentFirst);
            } else {
                BuildControl buildControl = new BuildControl();
                buildControl.setApplicationName(waitingEmail.getApplicationName());
                buildControl.setSuccessCount(1);
                buildControl.setEnvironment(environment);
                buildControl.setDeploymentDate(LocalDate.now(ZoneId.of("Asia/Kolkata")));
                buildControlDao.save(buildControl);
            }
        } else if (buildStatus.contains("Build Successful!") && forProduction) {
            ProductionBuild productionBuild = productionBuildDao.getProductionBuildByInternetMessageId(waitingEmail.getInternetMessageId());
            waitingEmail.setStatus(BuildConstants.APPROVED);
            try {
                JobDetails jenkinsDetails = jenkinsMap.get(environment.toLowerCase());
                JenkinsApiClient client = new JenkinsApiClient(jenkinsDetails.jenkinsUrl(), jenkinsDetails.jenkinsUser(), jenkinsDetails.jenkinsToken());
                JenkinsBuildTrigger buildTrigger = new JenkinsBuildTrigger(client);
                JenkinsQueueChecker queueChecker = new JenkinsQueueChecker(client);

                //location header value contains queue url
                //Step 1: Trigger the build
                String queueUrl = null;
                String buildParameters = waitingEmail.getBuildParameters();
                JSONObject buildParams = new JSONObject(buildParameters);
                if (waitingEmail.getSubject().toLowerCase().contains("frontend")) {
                    boolean isShellApp = buildParams.optBoolean("IS_SHELL_APP", false);
                    String jobName = isShellApp ? jenkinsDetails.shellAppJobName() : jenkinsDetails.frontendJobName();
                    log.debug("job name is : {}", jobName);
                    queueUrl = buildTrigger.triggerBuild(jenkinsDetails.jenkinsUrl() + "/job/" + jobName, buildParams);
                } else {
                    String jobName = jenkinsDetails.backendJobName();
                    queueUrl = buildTrigger.triggerBuild(jenkinsDetails.jenkinsUrl() + "/job/" + jobName, buildParams);
                }
                log.debug("Build Triggered! Queue url is : {}", queueUrl);
                productionBuild.setQueueUrl(queueUrl);

                // Step 2: Check Queue Status
                String buildUrl = queueChecker.waitForBuildToStart(queueUrl);
                log.debug("Build URL: {}", buildUrl);
                productionBuild.setBuildUrl(buildUrl);

                // Step 3: Check Build Status
                String blueOceanUrl = getBlueOceanUrl(buildUrl, jenkinsDetails.jenkinsUrl());
                productionBuild.setStatus(BuildConstants.WAITING);
                productionBuild.setBlueOceanUrl(blueOceanUrl);
                productionBuild.setApprovedBy(waitingEmail.getApprovedBy());

            } catch (Exception e) {
                log.error("error in Jenkins pipeline : {}", e.getMessage(), e);
                productionBuild.setStatus(BuildConstants.ERROR);
                productionBuild.setError(e.getMessage());
                sendUnexpectedErrorMail(productionBuild.getReplyTo(), productionBuild.getInternetMessageId());
            }

            productionBuildDao.save(productionBuild);

        } else {
            ProductionBuild productionBuild = productionBuildDao.getProductionBuildByInternetMessageId(waitingEmail.getInternetMessageId());
            if (productionBuild != null) {
                productionBuild.setStatus(BuildConstants.ERROR);
                productionBuildDao.save(productionBuild);
            }

            //Send email for failed deployment with error logs
            waitingEmail.setStatus(BuildConstants.ERROR);
            String draft = AIUtils.getResolvedPrompt(failedDraft, variableMap);
            BufferedInputStream attachmentStream = new BufferedInputStream
                    (new ByteArrayInputStream(buildStatus.getBytes(StandardCharsets.UTF_8)));
            deploymentService.replyWithAttachment(emailId, waitingEmail.getReplyTo(), draft, attachmentStream, "pipeline_error.log");

            List<BuildControl> deployment = buildControlDao.findByApplicationNameAndDeploymentDate(waitingEmail.getApplicationName(), LocalDate.now(ZoneId.of("Asia/Kolkata")), environment);

            if (deployment.size() > 1) {
                deploymentService.replyBuildEmail(emailId, waitingEmail.getReplyTo(), """
                        Dear team,
                        
                        Your deployment is unsuccessful.
                        
                        Please contact Deepak Dangi(<EMAIL>).
                        
                        Best Regards,
                        AI Devops(visionwaves) Where automation comes first!!!
                        """);
            } else if (deployment.size() == 1) {
                BuildControl deploymentFirst = deployment.getFirst();
                int failedCount = deploymentFirst.getFailedCount() == null ? 0 : deploymentFirst.getFailedCount();
                deploymentFirst.setFailedCount(failedCount + 1);
                deploymentFirst.setEnvironment(environment);
                buildControlDao.save(deploymentFirst);
            } else {
                BuildControl buildControl = new BuildControl();
                buildControl.setApplicationName(waitingEmail.getApplicationName());
                buildControl.setFailedCount(1);
                buildControl.setEnvironment(environment);
                buildControl.setDeploymentDate(LocalDate.now(ZoneId.of("Asia/Kolkata")));
                buildControlDao.save(buildControl);
            }

        }
    }


    public record BackendParameters(
            @JsonPropertyDescription("This is mail type: it can either be APPROVER or REQUESTEE") String TYPE,
            @JsonPropertyDescription("True if approved by APPROVER, else false") Boolean APPROVED,
            @JsonPropertyDescription("The environment for the build to deploy") String ENV,
            String APPLICATION_NAME,
            String K8S_NAME_SPACE,
            @JsonPropertyDescription("True if provided true, default is false") boolean SKIP_TEST_CASES,
            @JsonPropertyDescription("True if provided true, default is false") boolean SKIP_OBFUSCATION,
            @JsonPropertyDescription("True if provided true, default is false") boolean SKIP_JAVADOC,
            @JsonPropertyDescription("True if provided true, default is false") boolean SKIP_DEPENDENCY_CHECK,
            String GIT_TAG,
            String GIT_BRANCH,
            String GIT_REPO_URL,
            String DOCKER_REGISTRY,
            @JsonPropertyDescription("True if provided true, default is false") boolean UPGRADE_DATABASE
    ) {
    }

    public record FrontendParameters(
            @JsonPropertyDescription("This is mail type: it can either be APPROVER or REQUESTEE") String TYPE,
            @JsonPropertyDescription("True if approved by APPROVER, else false") Boolean APPROVED,
            @JsonPropertyDescription("The environment for the build to deploy") String ENV,
            String APPLICATION_NAME,
            String PROJECT,
            String K8S_NAME_SPACE,
            String GIT_TAG,
            String GIT_BRANCH,
            String GIT_REPO_URL,
            String DOCKER_REGISTRY,
            @JsonPropertyDescription("True if provided, default is false") Boolean IS_SHELL_APP
    ) {
    }

    private List<UserEmailDto> getBuildEmails() {
        try {
//            String dateTimeFilter = "receivedDateTime ge 2025-03-08T18:28:48Z";
            String dateTimeFilter = emailService.getReceivedDateTimeFilter(emailId);
            log.debug("dateTimeFilter : {}", dateTimeFilter);
            pollTimeInfoService.setStartTime(emailId, LocalDateTime.now(), PollTimeInfo.Status.SUCCESS);
            pollTimeInfoService.setStartTime(emailId, LocalDateTime.now(), PollTimeInfo.Status.PROCESSING);
            UserEmailResponseDto emailsOfUser = graphIntegrationService.getEmailsOfUser(emailId, emailId, dateTimeFilter, null, null, "Inbox", 100, 0);
            List<List<UserEmailDto>> userMessages = emailsOfUser.getMessages();
            return flattenAndSortEmails(userMessages);
        } catch (Exception e) {
            log.error("error while getting build emails : {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    private static List<UserEmailDto> flattenAndSortEmails(List<List<UserEmailDto>> userMessages) {
        if (userMessages == null || userMessages.isEmpty()) {
            return new ArrayList<>();
        }

        return userMessages.stream()
                .flatMap(List::stream) // Flatten the nested list
                .sorted(Comparator.comparing(UserEmailDto::getCreatedTime)) // Sort by createdTime (oldest first)
                .collect(Collectors.toList()); // Collect to List
    }

    private String getBlueOceanUrl(String buildUrl, String jenkinsUrl) {
        String[] buildUrlParts = buildUrl.split("/");
        String buildId = buildUrlParts[buildUrlParts.length - 1];
        String jobName = buildUrlParts[buildUrlParts.length - 2];
        return String.format("%s/blue/rest/organizations/jenkins/pipelines/%s/runs/%s/", jenkinsUrl, jobName, buildId);
    }

    private String extractBuildNumber(String buildUrl) {
        Pattern pattern = Pattern.compile(".*/(\\d+)/?$"); // Capture last number
        Matcher matcher = pattern.matcher(buildUrl);

        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return "";
        }
    }

    private void sendUnexpectedErrorMail(String messageId, String internetMessageId) {
        try {
            deploymentService.replyBuildEmail(emailId, messageId, String.format("""
                    Dear team,
                    
                    Your request could not be processed due to an unexpected error.
                    
                    Please contact Devops team or initiate another request.
                    
                    Request id : %s
                    
                    Best Regards,
                    AI Devops(visionwaves) Where automation comes first!!!
                    """, internetMessageId));
        } catch (Exception ex) {
            log.error("error while sending email : {}", ex.getMessage());
        }
    }

    private static final String LIMITED_DEPLOYMENT = """
            Dear Team member,
            
            We received your deployment request; however, it exceeds the allowed limit of deployments per day. To ensure system stability and fair resource utilization, we cannot process additional deployments beyond this limit.
            
            If your request is urgent, please reach out to DevOps Team to discuss possible exceptions. Otherwise, you may resubmit your request on the next business day.
            
            Thank you for your understanding. Let us know if you have any questions.
            
            Best Regards,
            AI Devops(visionwaves) Where automation comes first!!!
            """;

    private static final String UNAUTHORIZED = """
            Dear Team member,
            
            We received your request to approve a deployment, but according to our policy, only authorized personnel are permitted to approve pipeline executions. Unfortunately, you are not listed as an authorized approver, and we are unable to process your request.
            
            If this approval was made in error, please inform the designated approver within your team. If you believe you should be authorized, kindly reach out to Mr. Shiv Chandra Pathak(<EMAIL>) for further clarification.
            
            Thank you for your cooperation.
            
            Best Regards,
            
            AI Devops(visionwaves) Where automation comes first!!!
            """;
}
