package com.enttribe.emailagent.jenkins;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class JenkinsBuildTrigger {
    private static final Logger log = LoggerFactory.getLogger(JenkinsBuildTrigger.class);
    private final JenkinsApiClient jenkinsClient;

    private final List<String> keysToSkip = List.of("APPROVED", "SKIP_OBFUSCATION", "SKIP_TEST_CASES",
            "SKIP_JAVADOC", "SKIP_DEPENDENCY_CHECK", "UPGRADE_DATABASE");
    public JenkinsBuildTrigger(JenkinsApiClient client) {
        this.jenkinsClient = client;
    }

    public String triggerBuild(String jobUrl, JSONObject buildParams) throws IOException {
        StringBuilder data = new StringBuilder();

        for (String key : buildParams.keySet()) {
            String encodedKey = URLEncoder.encode(key, StandardCharsets.UTF_8);
            // Convert all values to string properly
            Object value = buildParams.get(key);
            String encodedValue = URLEncoder.encode(String.valueOf(value), StandardCharsets.UTF_8);

            data.append(encodedKey).append("=").append(encodedValue).append("&");
        }

        // Remove the trailing "&" if present
        if (!data.isEmpty()) {
            data.setLength(data.length() - 1);
        }

        String locationHeader = jenkinsClient.sendPostRequest(jobUrl + "/buildWithParameters", data.toString());
        log.debug("locationHeader is : {}", locationHeader);
        return locationHeader;
    }
}
