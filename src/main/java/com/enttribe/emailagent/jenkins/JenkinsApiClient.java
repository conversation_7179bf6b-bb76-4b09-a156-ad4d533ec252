package com.enttribe.emailagent.jenkins;

import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class JenkinsApiClient {

    private static final Logger log = LoggerFactory.getLogger(JenkinsApiClient.class);

    private final String jenkinsUser;
    private final String jenkinsToken;
    private final String baseUrl;

    public JenkinsApiClient(String baseUrl, String user, String token) {
        this.baseUrl = baseUrl;
        this.jenkinsUser = user;
        this.jenkinsToken = token;
    }

    private String getAuthHeader() {
        String auth = jenkinsUser + ":" + jenkinsToken;
        return "Basic " + Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
    }

    public String sendPostRequest(String url, String data) throws IOException {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost postRequest = new HttpPost(url);
            postRequest.setHeader("Authorization", getAuthHeader());
            postRequest.setHeader("Content-Type", "application/x-www-form-urlencoded");

            if (data != null) {
                postRequest.setEntity(new StringEntity(data));
            }

            try (CloseableHttpResponse response = client.execute(postRequest)) {
                if (response.getStatusLine().getStatusCode() != 201) {
                    log.error("did not trigger Jenkins job : {}", response);
                }
                Header location = response.getFirstHeader("Location");
                return location != null ? location.getValue() : null;
            }
        }
    }

    public String sendGetRequest(String url) throws IOException {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpGet getRequest = new HttpGet(url);
            getRequest.setHeader("Authorization", getAuthHeader());

            try (CloseableHttpResponse response = client.execute(getRequest)) {
                return EntityUtils.toString(response.getEntity());
            }
        }
    }
}