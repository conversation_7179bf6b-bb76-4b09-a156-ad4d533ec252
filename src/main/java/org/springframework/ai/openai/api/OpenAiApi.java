////
//// Source code recreated from a .class file by IntelliJ IDEA
//// (powered by FernFlower decompiler)
////
//
//package org.springframework.ai.openai.api;
//
//import com.fasterxml.jackson.annotation.JsonFormat;
//import com.fasterxml.jackson.annotation.JsonIgnore;
//import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
//import com.fasterxml.jackson.annotation.JsonInclude;
//import com.fasterxml.jackson.annotation.JsonProperty;
//import com.fasterxml.jackson.annotation.JsonFormat.Feature;
//import com.fasterxml.jackson.annotation.JsonInclude.Include;
//import java.util.List;
//import java.util.Map;
//import java.util.function.Consumer;
//import java.util.function.Predicate;
//import org.springframework.ai.model.ChatModelDescription;
//import org.springframework.ai.model.ModelOptionsUtils;
//import org.springframework.ai.retry.RetryUtils;
//import org.springframework.core.ParameterizedTypeReference;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.util.Assert;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.LinkedMultiValueMap;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.client.ResponseErrorHandler;
//import org.springframework.web.client.RestClient;
//import org.springframework.web.reactive.function.client.WebClient;
//
//public class OpenAiApi {
//    public static final ChatModel DEFAULT_CHAT_MODEL;
//    public static final String DEFAULT_EMBEDDING_MODEL;
//    private static final Predicate<String> SSE_DONE_PREDICATE;
//    private final String completionsPath;
//    private final String embeddingsPath;
//    private final RestClient restClient;
//    private final WebClient webClient;
//    private OpenAiStreamFunctionCallingHelper chunkMerger;
//
//    public OpenAiApi(String apiKey) {
//        this("https://api.openai.com", apiKey);
//    }
//
//    public OpenAiApi(String baseUrl, String apiKey) {
//        this(baseUrl, apiKey, RestClient.builder(), WebClient.builder());
//    }
//
//    public OpenAiApi(String baseUrl, String apiKey, RestClient.Builder restClientBuilder, WebClient.Builder webClientBuilder) {
//        this(baseUrl, apiKey, restClientBuilder, webClientBuilder, RetryUtils.DEFAULT_RESPONSE_ERROR_HANDLER);
//    }
//
//    public OpenAiApi(String baseUrl, String apiKey, RestClient.Builder restClientBuilder, WebClient.Builder webClientBuilder, ResponseErrorHandler responseErrorHandler) {
//        this(baseUrl, apiKey, "/v1/chat/completions", "/v1/embeddings", restClientBuilder, webClientBuilder, responseErrorHandler);
//    }
//
//    public OpenAiApi(String baseUrl, String apiKey, String completionsPath, String embeddingsPath, RestClient.Builder restClientBuilder, WebClient.Builder webClientBuilder, ResponseErrorHandler responseErrorHandler) {
//        this(baseUrl, apiKey, CollectionUtils.toMultiValueMap(Map.of()), completionsPath, embeddingsPath, restClientBuilder, webClientBuilder, responseErrorHandler);
//    }
//
//    public OpenAiApi(String baseUrl, String apiKey, MultiValueMap<String, String> headers, String completionsPath, String embeddingsPath, RestClient.Builder restClientBuilder, WebClient.Builder webClientBuilder, ResponseErrorHandler responseErrorHandler) {
//        this.chunkMerger = new OpenAiStreamFunctionCallingHelper();
//        Assert.hasText(completionsPath, "Completions Path must not be null");
//        Assert.hasText(embeddingsPath, "Embeddings Path must not be null");
//        Assert.notNull(headers, "Headers must not be null");
//        this.completionsPath = completionsPath;
//        this.embeddingsPath = embeddingsPath;
//        Consumer<HttpHeaders> finalHeaders = (h) -> {
//            h.setBearerAuth(apiKey);
//            h.setContentType(MediaType.APPLICATION_JSON);
//            h.addAll(headers);
//        };
//        this.restClient = restClientBuilder.baseUrl(baseUrl).defaultHeaders(finalHeaders).defaultStatusHandler(responseErrorHandler).build();
//        this.webClient = webClientBuilder.baseUrl(baseUrl).defaultHeaders(finalHeaders).build();
//    }
//
//    public static String getTextContent(List<ChatCompletionMessage.MediaContent> content) {
//        return (String)content.stream().filter((c) -> "text".equals(c.type())).map(ChatCompletionMessage.MediaContent::text).reduce("", (a, b) -> a + b);
//    }
//
//    public ResponseEntity<ChatCompletion> chatCompletionEntity(ChatCompletionRequest chatRequest) {
//        return this.chatCompletionEntity(chatRequest, new LinkedMultiValueMap());
//    }
//
//    public ResponseEntity<ChatCompletion> chatCompletionEntity(ChatCompletionRequest chatRequest, MultiValueMap<String, String> additionalHttpHeader) {
//        Assert.notNull(chatRequest, "The request body can not be null.");
//        Assert.isTrue(!chatRequest.stream(), "Request must set the stream property to false.");
//        Assert.notNull(additionalHttpHeader, "The additional HTTP headers can not be null.");
//        return ((RestClient.RequestBodySpec)((RestClient.RequestBodySpec)this.restClient.post().uri(this.completionsPath, new Object[0])).headers((headers) -> headers.addAll(additionalHttpHeader))).body(chatRequest).retrieve().toEntity(ChatCompletion.class);
//    }
//
////    public Flux<ChatCompletionChunk> chatCompletionStream(ChatCompletionRequest chatRequest) {
////        return this.chatCompletionStream(chatRequest, new LinkedMultiValueMap());
////    }
//
////    public Flux<ChatCompletionChunk> chatCompletionStream(ChatCompletionRequest chatRequest, MultiValueMap<String, String> additionalHttpHeader) {
////        Assert.notNull(chatRequest, "The request body can not be null.");
////        Assert.isTrue(chatRequest.stream(), "Request must set the stream property to true.");
////        AtomicBoolean isInsideTool = new AtomicBoolean(false);
////        return ((WebClient.RequestBodySpec)((WebClient.RequestBodySpec)this.webClient.post().uri(this.completionsPath, new Object[0])).headers((headers) -> headers.addAll(additionalHttpHeader))).body(Mono.just(chatRequest), ChatCompletionRequest.class).retrieve().bodyToFlux(String.class).takeUntil(SSE_DONE_PREDICATE).filter(SSE_DONE_PREDICATE.negate()).map((content) -> (ChatCompletionChunk)ModelOptionsUtils.jsonToObject(content, ChatCompletionChunk.class)).map((chunk) -> {
////            if (this.chunkMerger.isStreamingToolFunctionCall(chunk)) {
////                isInsideTool.set(true);
////            }
////
////            return chunk;
////        }).windowUntil((chunk) -> {
////            if (isInsideTool.get() && this.chunkMerger.isStreamingToolFunctionCallFinish(chunk)) {
////                isInsideTool.set(false);
////                return true;
////            } else {
////                return !isInsideTool.get();
////            }
////        }).concatMapIterable((window) -> {
////            Mono<ChatCompletionChunk> monoChunk = window.reduce(new ChatCompletionChunk((String)null, (List)null, (Long)null, (String)null, (String)null, (String)null, (Usage)null), (previous, current) -> this.chunkMerger.merge(previous, current));
////            return List.of(monoChunk);
////        }).flatMap((mono) -> mono);
////    }
//
//    public <T> ResponseEntity<EmbeddingList<Embedding>> embeddings(EmbeddingRequest<T> embeddingRequest) {
//        Assert.notNull(embeddingRequest, "The request body can not be null.");
//        Assert.notNull(embeddingRequest.input(), "The input can not be null.");
//        Assert.isTrue(embeddingRequest.input() instanceof String || embeddingRequest.input() instanceof List, "The input must be either a String, or a List of Strings or List of List of integers.");
//        Object var3 = embeddingRequest.input();
//        if (var3 instanceof List list) {
//            Assert.isTrue(!CollectionUtils.isEmpty(list), "The input list can not be empty.");
//            Assert.isTrue(list.size() <= 2048, "The list must be 2048 dimensions or less");
//            Assert.isTrue(list.get(0) instanceof String || list.get(0) instanceof Integer || list.get(0) instanceof List, "The input must be either a String, or a List of Strings or list of list of integers.");
//        }
//
//        return ((RestClient.RequestBodySpec)this.restClient.post().uri(this.embeddingsPath, new Object[0])).body(embeddingRequest).retrieve().toEntity(new ParameterizedTypeReference<EmbeddingList<Embedding>>() {
//        });
//    }
//
//    static {
//        DEFAULT_CHAT_MODEL = OpenAiApi.ChatModel.GPT_4_O;
//        DEFAULT_EMBEDDING_MODEL = OpenAiApi.EmbeddingModel.TEXT_EMBEDDING_ADA_002.getValue();
//        SSE_DONE_PREDICATE = "[DONE]"::equals;
//    }
//
//    public static enum ChatModel implements ChatModelDescription {
//        O1_PREVIEW("o1-preview"),
//        O1_PREVIEW_2024_09_12("o1-preview-2024-09-12"),
//        O1_MINI("o1-mini"),
//        O1_MINI_2024_09_12("o1-mini-2024-09-12"),
//        GPT_4_O("gpt-4o"),
//        GPT_4_O_MINI("gpt-4o-mini"),
//        GPT_4_TURBO("gpt-4-turbo"),
//        GPT_4_TURBO_2024_04_09("gpt-4-turbo-2024-04-09"),
//        GPT_4_0125_PREVIEW("gpt-4-0125-preview"),
//        GPT_4_TURBO_PREVIEW("gpt-4-turbo-preview"),
//        GPT_4("gpt-4"),
//        GPT_3_5_TURBO("gpt-3.5-turbo"),
//        GPT_3_5_TURBO_0125("gpt-3.5-turbo-0125"),
//        GPT_3_5_TURBO_1106("gpt-3.5-turbo-1106");
//
//        public final String value;
//
//        private ChatModel(String value) {
//            this.value = value;
//        }
//
//        public String getValue() {
//            return this.value;
//        }
//
//        public String getName() {
//            return this.value;
//        }
//    }
//
//    public static enum ChatCompletionFinishReason {
//        @JsonProperty("stop")
//        STOP,
//        @JsonProperty("length")
//        LENGTH,
//        @JsonProperty("content_filter")
//        CONTENT_FILTER,
//        @JsonProperty("tool_calls")
//        TOOL_CALLS,
//        @JsonProperty("tool_call")
//        TOOL_CALL;
//
//        private ChatCompletionFinishReason() {
//        }
//    }
//
//    public static enum EmbeddingModel {
//        TEXT_EMBEDDING_3_LARGE("text-embedding-3-large"),
//        TEXT_EMBEDDING_3_SMALL("text-embedding-3-small"),
//        TEXT_EMBEDDING_ADA_002("text-embedding-ada-002");
//
//        public final String value;
//
//        private EmbeddingModel(String value) {
//            this.value = value;
//        }
//
//        public String getValue() {
//            return this.value;
//        }
//    }
//
//    @JsonInclude(Include.NON_NULL)
//    public static class FunctionTool {
//        @JsonProperty("type")
//        private Type type;
//        @JsonProperty("function")
//        private Function function;
//
//        public FunctionTool() {
//            this.type = OpenAiApi.FunctionTool.Type.FUNCTION;
//        }
//
//        public FunctionTool(Type type, Function function) {
//            this.type = OpenAiApi.FunctionTool.Type.FUNCTION;
//            this.type = type;
//            this.function = function;
//        }
//
//        public FunctionTool(Function function) {
//            this(OpenAiApi.FunctionTool.Type.FUNCTION, function);
//        }
//
//        public Type getType() {
//            return this.type;
//        }
//
//        public Function getFunction() {
//            return this.function;
//        }
//
//        public void setType(Type type) {
//            this.type = type;
//        }
//
//        public void setFunction(Function function) {
//            this.function = function;
//        }
//
//        public static enum Type {
//            @JsonProperty("function")
//            FUNCTION;
//
//            private Type() {
//            }
//        }
//
//        public static class Function {
//            @JsonProperty("description")
//            private String description;
//            @JsonProperty("name")
//            private String name;
//            @JsonProperty("parameters")
//            private Map<String, Object> parameters;
//            @JsonIgnore
//            private String jsonSchema;
//
//            private Function() {
//            }
//
//            public Function(String description, String name, Map<String, Object> parameters) {
//                this.description = description;
//                this.name = name;
//                this.parameters = parameters;
//            }
//
//            public Function(String description, String name, String jsonSchema) {
//                this(description, name, ModelOptionsUtils.jsonToMap(jsonSchema));
//            }
//
//            public String getDescription() {
//                return this.description;
//            }
//
//            public String getName() {
//                return this.name;
//            }
//
//            public Map<String, Object> getParameters() {
//                return this.parameters;
//            }
//
//            public void setDescription(String description) {
//                this.description = description;
//            }
//
//            public void setName(String name) {
//                this.name = name;
//            }
//
//            public void setParameters(Map<String, Object> parameters) {
//                this.parameters = parameters;
//            }
//
//            public String getJsonSchema() {
//                return this.jsonSchema;
//            }
//
//            public void setJsonSchema(String jsonSchema) {
//                this.jsonSchema = jsonSchema;
//                if (jsonSchema != null) {
//                    this.parameters = ModelOptionsUtils.jsonToMap(jsonSchema);
//                }
//
//            }
//        }
//    }
//
//    @JsonInclude(Include.NON_NULL)
//    public static record ChatCompletionRequest(@JsonProperty("messages") List<ChatCompletionMessage> messages, @JsonProperty("model") String model, @JsonProperty("frequency_penalty") Double frequencyPenalty, @JsonProperty("logit_bias") Map<String, Integer> logitBias, @JsonProperty("logprobs") Boolean logprobs, @JsonProperty("top_logprobs") Integer topLogprobs, @JsonProperty("max_tokens") Integer maxTokens, @JsonProperty("max_completion_tokens") Integer maxCompletionTokens, @JsonProperty("n") Integer n, @JsonProperty("presence_penalty") Double presencePenalty, @JsonProperty("response_format") ResponseFormat responseFormat, @JsonProperty("seed") Integer seed, @JsonProperty("stop") List<String> stop, @JsonProperty("stream") Boolean stream, @JsonProperty("stream_options") StreamOptions streamOptions, @JsonProperty("temperature") Double temperature, @JsonProperty("top_p") Double topP, @JsonProperty("tools") List<FunctionTool> tools, @JsonProperty("tool_choice") Object toolChoice, @JsonProperty("parallel_tool_calls") Boolean parallelToolCalls, @JsonProperty("user") String user) {
//
//        public ChatCompletionRequest(List<ChatCompletionMessage> messages, String model) {
//            this(messages, model, (Double)null, (Map)null, (Boolean)null, (Integer)null, (Integer)null, (Integer)null, (Integer)null, (Double)null, (ResponseFormat)null, (Integer)null, (List)null, false, (StreamOptions)null, 0.7, (Double)null, (List)null, (Object)null, (Boolean)null, (String)null);
//        }
//
//        public ChatCompletionRequest(List<ChatCompletionMessage> messages, String model, Double temperature) {
//            this(messages, model, (Double)null, (Map)null, (Boolean)null, (Integer)null, (Integer)null, (Integer)null, (Integer)null, (Double)null, (ResponseFormat)null, (Integer)null, (List)null, false, (StreamOptions)null, temperature, (Double)null, (List)null, (Object)null, (Boolean)null, (String)null);
//        }
//
//        public ChatCompletionRequest(List<ChatCompletionMessage> messages, String model, Double temperature, boolean stream) {
//            this(messages, model, (Double)null, (Map)null, (Boolean)null, (Integer)null, (Integer)null, (Integer)null, (Integer)null, (Double)null, (ResponseFormat)null, (Integer)null, (List)null, stream, (StreamOptions)null, temperature, (Double)null, (List)null, (Object)null, (Boolean)null, (String)null);
//        }
//
//        public ChatCompletionRequest(List<ChatCompletionMessage> messages, String model, Double temperature, Double topP, Integer maxTokens) {
//            this(messages, model, (Double)null, (Map)null, (Boolean)null, (Integer)null, (Integer) maxTokens, (Integer)null, (Integer)null, (Double)null, (ResponseFormat)null, (Integer)null, (List)null, false, (StreamOptions)null, temperature, (Double) topP, (List)null, (Object)null, (Boolean)null, (String)null);
//        }
//
//        public ChatCompletionRequest(List<ChatCompletionMessage> messages, String model, List<FunctionTool> tools, Object toolChoice) {
//            this(messages, model, (Double)null, (Map)null, (Boolean)null, (Integer)null, (Integer)null, (Integer)null, (Integer)null, (Double)null, (ResponseFormat)null, (Integer)null, (List)null, false, (StreamOptions)null, 0.8, (Double)null, tools, toolChoice, (Boolean)null, (String)null);
//        }
//
//        public ChatCompletionRequest(List<ChatCompletionMessage> messages, Boolean stream) {
//            this(messages, (String)null, (Double)null, (Map)null, (Boolean)null, (Integer)null, (Integer)null, (Integer)null, (Integer)null, (Double)null, (ResponseFormat)null, (Integer)null, (List)null, stream, (StreamOptions)null, (Double)null, (Double)null, (List)null, (Object)null, (Boolean)null, (String)null);
//        }
//
//        public ChatCompletionRequest(@JsonProperty("messages") List<ChatCompletionMessage> messages, @JsonProperty("model") String model, @JsonProperty("frequency_penalty") Double frequencyPenalty, @JsonProperty("logit_bias") Map<String, Integer> logitBias, @JsonProperty("logprobs") Boolean logprobs, @JsonProperty("top_logprobs") Integer topLogprobs, @JsonProperty("max_tokens") Integer maxTokens, @JsonProperty("max_completion_tokens") Integer maxCompletionTokens, @JsonProperty("n") Integer n, @JsonProperty("presence_penalty") Double presencePenalty, @JsonProperty("response_format") ResponseFormat responseFormat, @JsonProperty("seed") Integer seed, @JsonProperty("stop") List<String> stop, @JsonProperty("stream") Boolean stream, @JsonProperty("stream_options") StreamOptions streamOptions, @JsonProperty("temperature") Double temperature, @JsonProperty("top_p") Double topP, @JsonProperty("tools") List<FunctionTool> tools, @JsonProperty("tool_choice") Object toolChoice, @JsonProperty("parallel_tool_calls") Boolean parallelToolCalls, @JsonProperty("user") String user) {
//            this.messages = messages;
//            this.model = model;
//            this.frequencyPenalty = frequencyPenalty;
//            this.logitBias = logitBias;
//            this.logprobs = logprobs;
//            this.topLogprobs = topLogprobs;
//            this.maxTokens = maxTokens;
//            this.maxCompletionTokens = maxCompletionTokens;
//            this.n = n;
//            this.presencePenalty = presencePenalty;
//            this.responseFormat = responseFormat;
//            this.seed = seed;
//            this.stop = stop;
//            this.stream = stream;
//            this.streamOptions = streamOptions;
//            this.temperature = temperature;
//            this.topP = topP;
//            this.tools = tools;
//            this.toolChoice = toolChoice;
//            this.parallelToolCalls = parallelToolCalls;
//            this.user = user;
//        }
//
//        public ChatCompletionRequest withStreamOptions(StreamOptions streamOptions) {
//            return new ChatCompletionRequest(this.messages, this.model, this.frequencyPenalty, this.logitBias, this.logprobs, this.topLogprobs, this.maxTokens, this.maxCompletionTokens, this.n, this.presencePenalty, this.responseFormat, this.seed, this.stop, this.stream, streamOptions, this.temperature, this.topP, this.tools, this.toolChoice, this.parallelToolCalls, this.user);
//        }
//
//        @JsonProperty("messages")
//        public List<ChatCompletionMessage> messages() {
//            return this.messages;
//        }
//
//        @JsonProperty("model")
//        public String model() {
//            return this.model;
//        }
//
//        @JsonProperty("frequency_penalty")
//        public Double frequencyPenalty() {
//            return this.frequencyPenalty;
//        }
//
//        @JsonProperty("logit_bias")
//        public Map<String, Integer> logitBias() {
//            return this.logitBias;
//        }
//
//        @JsonProperty("logprobs")
//        public Boolean logprobs() {
//            return this.logprobs;
//        }
//
//        @JsonProperty("top_logprobs")
//        public Integer topLogprobs() {
//            return this.topLogprobs;
//        }
//
//        @JsonProperty("max_tokens")
//        public Integer maxTokens() {
//            return this.maxTokens;
//        }
//
//        @JsonProperty("max_completion_tokens")
//        public Integer maxCompletionTokens() {
//            return this.maxCompletionTokens;
//        }
//
//        @JsonProperty("n")
//        public Integer n() {
//            return this.n;
//        }
//
//        @JsonProperty("presence_penalty")
//        public Double presencePenalty() {
//            return this.presencePenalty;
//        }
//
//        @JsonProperty("response_format")
//        public ResponseFormat responseFormat() {
//            return this.responseFormat;
//        }
//
//        @JsonProperty("seed")
//        public Integer seed() {
//            return this.seed;
//        }
//
//        @JsonProperty("stop")
//        public List<String> stop() {
//            return this.stop;
//        }
//
//        @JsonProperty("stream")
//        public Boolean stream() {
//            return this.stream;
//        }
//
//        @JsonProperty("stream_options")
//        public StreamOptions streamOptions() {
//            return this.streamOptions;
//        }
//
//        @JsonProperty("temperature")
//        public Double temperature() {
//            return this.temperature;
//        }
//
//        @JsonProperty("top_p")
//        public Double topP() {
//            return this.topP;
//        }
//
//        @JsonProperty("tools")
//        public List<FunctionTool> tools() {
//            return this.tools;
//        }
//
//        @JsonProperty("tool_choice")
//        public Object toolChoice() {
//            return this.toolChoice;
//        }
//
//        @JsonProperty("parallel_tool_calls")
//        public Boolean parallelToolCalls() {
//            return this.parallelToolCalls;
//        }
//
//        @JsonProperty("user")
//        public String user() {
//            return this.user;
//        }
//
//        public static class ToolChoiceBuilder {
//            public static final String AUTO = "auto";
//            public static final String NONE = "none";
//
//            public ToolChoiceBuilder() {
//            }
//
//            public static Object FUNCTION(String functionName) {
//                return Map.of("type", "function", "function", Map.of("name", functionName));
//            }
//        }
//
//        @JsonInclude(Include.NON_NULL)
//        public static record StreamOptions(Boolean includeUsage) {
//            public static StreamOptions INCLUDE_USAGE = new StreamOptions(true);
//
//            public StreamOptions(@JsonProperty("include_usage") Boolean includeUsage) {
//                this.includeUsage = includeUsage;
//            }
//
//            @JsonProperty("include_usage")
//            public Boolean includeUsage() {
//                return this.includeUsage;
//            }
//        }
//    }
//
//    @JsonInclude(Include.NON_NULL)
//    public static record ChatCompletionMessage(Object rawContent, Role role, String name, String toolCallId, List<ToolCall> toolCalls, String refusal) {
//        public ChatCompletionMessage(Object content, Role role) {
//            this(content, role, (String)null, (String)null, (List)null, (String)null);
//        }
//
//        public ChatCompletionMessage(@JsonProperty("content") Object rawContent, @JsonProperty("role") Role role, @JsonProperty("name") String name, @JsonProperty("tool_call_id") String toolCallId, @JsonProperty("tool_calls") @JsonFormat(with = {Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY}) List<ToolCall> toolCalls, @JsonProperty("refusal") String refusal) {
//            this.rawContent = rawContent;
//            this.role = role;
//            this.name = name;
//            this.toolCallId = toolCallId;
//            this.toolCalls = toolCalls;
//            this.refusal = refusal;
//        }
//
//        public String content() {
//            if (this.rawContent == null) {
//                return null;
//            } else {
//                Object var2 = this.rawContent;
//                if (var2 instanceof String) {
//                    String text = (String)var2;
//                    return text;
//                } else {
//                    throw new IllegalStateException("The content is not a string!");
//                }
//            }
//        }
//
//        @JsonProperty("content")
//        public Object rawContent() {
//            return this.rawContent;
//        }
//
//        @JsonProperty("role")
//        public Role role() {
//            return this.role;
//        }
//
//        @JsonProperty("name")
//        public String name() {
//            return this.name;
//        }
//
//        @JsonProperty("tool_call_id")
//        public String toolCallId() {
//            return this.toolCallId;
//        }
//
//        @JsonProperty("tool_calls")
//        @JsonFormat(
//                with = {Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY}
//        )
//        public List<ToolCall> toolCalls() {
//            return this.toolCalls;
//        }
//
//        @JsonProperty("refusal")
//        public String refusal() {
//            return this.refusal;
//        }
//
//        public static enum Role {
//            @JsonProperty("system")
//            SYSTEM,
//            @JsonProperty("user")
//            USER,
//            @JsonProperty("assistant")
//            ASSISTANT,
//            @JsonProperty("tool")
//            TOOL;
//
//            private Role() {
//            }
//        }
//
//        @JsonInclude(Include.NON_NULL)
//        public static record MediaContent(String type, String text, ImageUrl imageUrl) {
//            public MediaContent(String text) {
//                this("text", text, (ImageUrl)null);
//            }
//
//            public MediaContent(ImageUrl imageUrl) {
//                this("image_url", (String)null, imageUrl);
//            }
//
//            public MediaContent(@JsonProperty("type") String type, @JsonProperty("text") String text, @JsonProperty("image_url") ImageUrl imageUrl) {
//                this.type = type;
//                this.text = text;
//                this.imageUrl = imageUrl;
//            }
//
//            @JsonProperty("type")
//            public String type() {
//                return this.type;
//            }
//
//            @JsonProperty("text")
//            public String text() {
//                return this.text;
//            }
//
//            @JsonProperty("image_url")
//            public ImageUrl imageUrl() {
//                return this.imageUrl;
//            }
//
//            @JsonInclude(Include.NON_NULL)
//            public static record ImageUrl(String url, String detail) {
//                public ImageUrl(String url) {
//                    this(url, (String)null);
//                }
//
//                public ImageUrl(@JsonProperty("url") String url, @JsonProperty("detail") String detail) {
//                    this.url = url;
//                    this.detail = detail;
//                }
//
//                @JsonProperty("url")
//                public String url() {
//                    return this.url;
//                }
//
//                @JsonProperty("detail")
//                public String detail() {
//                    return this.detail;
//                }
//            }
//        }
//
//        @JsonInclude(Include.NON_NULL)
//        public static record ToolCall(Integer index, String id, String type, ChatCompletionFunction function) {
//            public ToolCall(String id, String type, ChatCompletionFunction function) {
//                this((Integer)null, id, type, function);
//            }
//
//            public ToolCall(@JsonProperty("index") Integer index, @JsonProperty("id") String id, @JsonProperty("type") String type, @JsonProperty("function") ChatCompletionFunction function) {
//                this.index = index;
//                this.id = id;
//                this.type = type;
//                this.function = function;
//            }
//
//            @JsonProperty("index")
//            public Integer index() {
//                return this.index;
//            }
//
//            @JsonProperty("id")
//            public String id() {
//                return this.id;
//            }
//
//            @JsonProperty("type")
//            public String type() {
//                return this.type;
//            }
//
//            @JsonProperty("function")
//            public ChatCompletionFunction function() {
//                return this.function;
//            }
//        }
//
//        @JsonInclude(Include.NON_NULL)
//        public static record ChatCompletionFunction(String name, String arguments) {
//            public ChatCompletionFunction(@JsonProperty("name") String name, @JsonProperty("arguments") String arguments) {
//                this.name = name;
//                this.arguments = arguments;
//            }
//
//            @JsonProperty("name")
//            public String name() {
//                return this.name;
//            }
//
//            @JsonProperty("arguments")
//            public String arguments() {
//                return this.arguments;
//            }
//        }
//    }
//
//    @JsonInclude(Include.NON_NULL)
//    public static record ChatCompletion(String id, List<Choice> choices, Long created, String model, String systemFingerprint, String object, Usage usage) {
//        public ChatCompletion(@JsonProperty("id") String id, @JsonProperty("choices") List<Choice> choices, @JsonProperty("created") Long created, @JsonProperty("model") String model, @JsonProperty("system_fingerprint") String systemFingerprint, @JsonProperty("object") String object, @JsonProperty("usage") Usage usage) {
//            this.id = id;
//            this.choices = choices;
//            this.created = created;
//            this.model = model;
//            this.systemFingerprint = systemFingerprint;
//            this.object = object;
//            this.usage = usage;
//        }
//
//        @JsonProperty("id")
//        public String id() {
//            return this.id;
//        }
//
//        @JsonProperty("choices")
//        public List<Choice> choices() {
//            return this.choices;
//        }
//
//        @JsonProperty("created")
//        public Long created() {
//            return this.created;
//        }
//
//        @JsonProperty("model")
//        public String model() {
//            return this.model;
//        }
//
//        @JsonProperty("system_fingerprint")
//        public String systemFingerprint() {
//            return this.systemFingerprint;
//        }
//
//        @JsonProperty("object")
//        public String object() {
//            return this.object;
//        }
//
//        @JsonProperty("usage")
//        public Usage usage() {
//            return this.usage;
//        }
//
//        @JsonInclude(Include.NON_NULL)
//        public static record Choice(ChatCompletionFinishReason finishReason, Integer index, ChatCompletionMessage message, LogProbs logprobs) {
//            public Choice(@JsonProperty("finish_reason") ChatCompletionFinishReason finishReason, @JsonProperty("index") Integer index, @JsonProperty("message") ChatCompletionMessage message, @JsonProperty("logprobs") LogProbs logprobs) {
//                this.finishReason = finishReason;
//                this.index = index;
//                this.message = message;
//                this.logprobs = logprobs;
//            }
//
//            @JsonProperty("finish_reason")
//            public ChatCompletionFinishReason finishReason() {
//                return this.finishReason;
//            }
//
//            @JsonProperty("index")
//            public Integer index() {
//                return this.index;
//            }
//
//            @JsonProperty("message")
//            public ChatCompletionMessage message() {
//                return this.message;
//            }
//
//            @JsonProperty("logprobs")
//            public LogProbs logprobs() {
//                return this.logprobs;
//            }
//        }
//    }
//
//    @JsonInclude(Include.NON_NULL)
//    public static record LogProbs(List<Content> content) {
//        public LogProbs(@JsonProperty("content") List<Content> content) {
//            this.content = content;
//        }
//
//        @JsonProperty("content")
//        public List<Content> content() {
//            return this.content;
//        }
//
//        @JsonInclude(Include.NON_NULL)
//        public static record Content(String token, Float logprob, List<Integer> probBytes, List<TopLogProbs> topLogprobs) {
//            public Content(@JsonProperty("token") String token, @JsonProperty("logprob") Float logprob, @JsonProperty("bytes") List<Integer> probBytes, @JsonProperty("top_logprobs") List<TopLogProbs> topLogprobs) {
//                this.token = token;
//                this.logprob = logprob;
//                this.probBytes = probBytes;
//                this.topLogprobs = topLogprobs;
//            }
//
//            @JsonProperty("token")
//            public String token() {
//                return this.token;
//            }
//
//            @JsonProperty("logprob")
//            public Float logprob() {
//                return this.logprob;
//            }
//
//            @JsonProperty("bytes")
//            public List<Integer> probBytes() {
//                return this.probBytes;
//            }
//
//            @JsonProperty("top_logprobs")
//            public List<TopLogProbs> topLogprobs() {
//                return this.topLogprobs;
//            }
//
//            @JsonInclude(Include.NON_NULL)
//            public static record TopLogProbs(String token, Float logprob, List<Integer> probBytes) {
//                public TopLogProbs(@JsonProperty("token") String token, @JsonProperty("logprob") Float logprob, @JsonProperty("bytes") List<Integer> probBytes) {
//                    this.token = token;
//                    this.logprob = logprob;
//                    this.probBytes = probBytes;
//                }
//
//                @JsonProperty("token")
//                public String token() {
//                    return this.token;
//                }
//
//                @JsonProperty("logprob")
//                public Float logprob() {
//                    return this.logprob;
//                }
//
//                @JsonProperty("bytes")
//                public List<Integer> probBytes() {
//                    return this.probBytes;
//                }
//            }
//        }
//    }
//
//    @JsonInclude(Include.NON_NULL)
//    public static record Usage(Integer completionTokens, Integer promptTokens, Integer totalTokens, PromptTokensDetails promptTokensDetails, CompletionTokenDetails completionTokenDetails) {
//        public Usage(Integer completionTokens, Integer promptTokens, Integer totalTokens) {
//            this(completionTokens, promptTokens, totalTokens, (PromptTokensDetails)null, (CompletionTokenDetails)null);
//        }
//
//        public Usage(@JsonProperty("completion_tokens") Integer completionTokens, @JsonProperty("prompt_tokens") Integer promptTokens, @JsonProperty("total_tokens") Integer totalTokens, @JsonProperty("prompt_tokens_details") PromptTokensDetails promptTokensDetails, @JsonProperty("completion_tokens_details") CompletionTokenDetails completionTokenDetails) {
//            this.completionTokens = completionTokens;
//            this.promptTokens = promptTokens;
//            this.totalTokens = totalTokens;
//            this.promptTokensDetails = promptTokensDetails;
//            this.completionTokenDetails = completionTokenDetails;
//        }
//
//        @JsonProperty("completion_tokens")
//        public Integer completionTokens() {
//            return this.completionTokens;
//        }
//
//        @JsonProperty("prompt_tokens")
//        public Integer promptTokens() {
//            return this.promptTokens;
//        }
//
//        @JsonProperty("total_tokens")
//        public Integer totalTokens() {
//            return this.totalTokens;
//        }
//
//        @JsonProperty("prompt_tokens_details")
//        public PromptTokensDetails promptTokensDetails() {
//            return this.promptTokensDetails;
//        }
//
//        @JsonProperty("completion_tokens_details")
//        public CompletionTokenDetails completionTokenDetails() {
//            return this.completionTokenDetails;
//        }
//
//        @JsonInclude(Include.NON_NULL)
//        public static record PromptTokensDetails(Integer audioTokens, Integer cachedTokens) {
//            public PromptTokensDetails(@JsonProperty("audio_tokens") Integer audioTokens, @JsonProperty("cached_tokens") Integer cachedTokens) {
//                this.audioTokens = audioTokens;
//                this.cachedTokens = cachedTokens;
//            }
//
//            @JsonProperty("audio_tokens")
//            public Integer audioTokens() {
//                return this.audioTokens;
//            }
//
//            @JsonProperty("cached_tokens")
//            public Integer cachedTokens() {
//                return this.cachedTokens;
//            }
//        }
//
//        @JsonInclude(Include.NON_NULL)
//        @JsonIgnoreProperties(
//                ignoreUnknown = true
//        )
//        public static record CompletionTokenDetails(Integer reasoningTokens, Integer acceptedPredictionTokens, Integer audioTokens, Integer rejectedPredictionTokens) {
//            public CompletionTokenDetails(@JsonProperty("reasoning_tokens") Integer reasoningTokens, @JsonProperty("accepted_prediction_tokens") Integer acceptedPredictionTokens, @JsonProperty("audio_tokens") Integer audioTokens, @JsonProperty("rejected_prediction_tokens") Integer rejectedPredictionTokens) {
//                this.reasoningTokens = reasoningTokens;
//                this.acceptedPredictionTokens = acceptedPredictionTokens;
//                this.audioTokens = audioTokens;
//                this.rejectedPredictionTokens = rejectedPredictionTokens;
//            }
//
//            @JsonProperty("reasoning_tokens")
//            public Integer reasoningTokens() {
//                return this.reasoningTokens;
//            }
//
//            @JsonProperty("accepted_prediction_tokens")
//            public Integer acceptedPredictionTokens() {
//                return this.acceptedPredictionTokens;
//            }
//
//            @JsonProperty("audio_tokens")
//            public Integer audioTokens() {
//                return this.audioTokens;
//            }
//
//            @JsonProperty("rejected_prediction_tokens")
//            public Integer rejectedPredictionTokens() {
//                return this.rejectedPredictionTokens;
//            }
//        }
//    }
//
//    @JsonInclude(Include.NON_NULL)
//    public static record ChatCompletionChunk(String id, List<ChunkChoice> choices, Long created, String model, String systemFingerprint, String object, Usage usage) {
//        public ChatCompletionChunk(@JsonProperty("id") String id, @JsonProperty("choices") List<ChunkChoice> choices, @JsonProperty("created") Long created, @JsonProperty("model") String model, @JsonProperty("system_fingerprint") String systemFingerprint, @JsonProperty("object") String object, @JsonProperty("usage") Usage usage) {
//            this.id = id;
//            this.choices = choices;
//            this.created = created;
//            this.model = model;
//            this.systemFingerprint = systemFingerprint;
//            this.object = object;
//            this.usage = usage;
//        }
//
//        @JsonProperty("id")
//        public String id() {
//            return this.id;
//        }
//
//        @JsonProperty("choices")
//        public List<ChunkChoice> choices() {
//            return this.choices;
//        }
//
//        @JsonProperty("created")
//        public Long created() {
//            return this.created;
//        }
//
//        @JsonProperty("model")
//        public String model() {
//            return this.model;
//        }
//
//        @JsonProperty("system_fingerprint")
//        public String systemFingerprint() {
//            return this.systemFingerprint;
//        }
//
//        @JsonProperty("object")
//        public String object() {
//            return this.object;
//        }
//
//        @JsonProperty("usage")
//        public Usage usage() {
//            return this.usage;
//        }
//
//        @JsonInclude(Include.NON_NULL)
//        public static record ChunkChoice(ChatCompletionFinishReason finishReason, Integer index, ChatCompletionMessage delta, LogProbs logprobs) {
//            public ChunkChoice(@JsonProperty("finish_reason") ChatCompletionFinishReason finishReason, @JsonProperty("index") Integer index, @JsonProperty("delta") ChatCompletionMessage delta, @JsonProperty("logprobs") LogProbs logprobs) {
//                this.finishReason = finishReason;
//                this.index = index;
//                this.delta = delta;
//                this.logprobs = logprobs;
//            }
//
//            @JsonProperty("finish_reason")
//            public ChatCompletionFinishReason finishReason() {
//                return this.finishReason;
//            }
//
//            @JsonProperty("index")
//            public Integer index() {
//                return this.index;
//            }
//
//            @JsonProperty("delta")
//            public ChatCompletionMessage delta() {
//                return this.delta;
//            }
//
//            @JsonProperty("logprobs")
//            public LogProbs logprobs() {
//                return this.logprobs;
//            }
//        }
//    }
//
//    @JsonInclude(Include.NON_NULL)
//    public static record Embedding(Integer index, float[] embedding, String object) {
//        public Embedding(Integer index, float[] embedding) {
//            this(index, embedding, "embedding");
//        }
//
//        public Embedding(@JsonProperty("index") Integer index, @JsonProperty("embedding") float[] embedding, @JsonProperty("object") String object) {
//            this.index = index;
//            this.embedding = embedding;
//            this.object = object;
//        }
//
//        @JsonProperty("index")
//        public Integer index() {
//            return this.index;
//        }
//
//        @JsonProperty("embedding")
//        public float[] embedding() {
//            return this.embedding;
//        }
//
//        @JsonProperty("object")
//        public String object() {
//            return this.object;
//        }
//    }
//
//    @JsonInclude(Include.NON_NULL)
//    public static record EmbeddingRequest<T>(T input, String model, String encodingFormat, Integer dimensions, String user) {
//        public EmbeddingRequest(T input, String model) {
//            this(input, model, "float", (Integer)null, (String)null);
//        }
//
//        public EmbeddingRequest(T input) {
//            this(input, OpenAiApi.DEFAULT_EMBEDDING_MODEL);
//        }
//
//        public EmbeddingRequest(@JsonProperty("input") T input, @JsonProperty("model") String model, @JsonProperty("encoding_format") String encodingFormat, @JsonProperty("dimensions") Integer dimensions, @JsonProperty("user") String user) {
//            this.input = input;
//            this.model = model;
//            this.encodingFormat = encodingFormat;
//            this.dimensions = dimensions;
//            this.user = user;
//        }
//
//        @JsonProperty("input")
//        public T input() {
//            return this.input;
//        }
//
//        @JsonProperty("model")
//        public String model() {
//            return this.model;
//        }
//
//        @JsonProperty("encoding_format")
//        public String encodingFormat() {
//            return this.encodingFormat;
//        }
//
//        @JsonProperty("dimensions")
//        public Integer dimensions() {
//            return this.dimensions;
//        }
//
//        @JsonProperty("user")
//        public String user() {
//            return this.user;
//        }
//    }
//
//    @JsonInclude(Include.NON_NULL)
//    public static record EmbeddingList<T>(String object, List<T> data, String model, Usage usage) {
//        public EmbeddingList(@JsonProperty("object") String object, @JsonProperty("data") List<T> data, @JsonProperty("model") String model, @JsonProperty("usage") Usage usage) {
//            this.object = object;
//            this.data = data;
//            this.model = model;
//            this.usage = usage;
//        }
//
//        @JsonProperty("object")
//        public String object() {
//            return this.object;
//        }
//
//        @JsonProperty("data")
//        public List<T> data() {
//            return this.data;
//        }
//
//        @JsonProperty("model")
//        public String model() {
//            return this.model;
//        }
//
//        @JsonProperty("usage")
//        public Usage usage() {
//            return this.usage;
//        }
//    }
//}
