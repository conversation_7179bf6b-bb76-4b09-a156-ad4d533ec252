#!/bin/sh

. /vault/secrets/secrets.env

mkdir -p /opt/visionwaves/sql_ssl
chmod 700 /opt/visionwaves/sql_ssl

if [ ! -z "$MYSQL_KEYSTORE" ]; then
    echo "$MYSQL_KEYSTORE" | base64 -d > /opt/visionwaves/sql_ssl/keystore.p12
    chmod 600 /opt/visionwaves/sql_ssl/keystore.p12
fi

if [ ! -z "$MYSQL_TRUSTSTORE" ]; then
    echo "$MYSQL_TRUSTSTORE" | base64 -d > /opt/visionwaves/sql_ssl/truststore.p12
    chmod 600 /opt/visionwaves/sql_ssl/truststore.p12
fi

NAME=emailagent-1.0.0
APP_DIR=.
LOG_DIR=${APP_DIR}/logs
JAR=${APP_DIR}/${NAME}.jar

echo $JAR

CMD="java -jar -Xms3448m -Xmx3448m \
-XX:+UseShenandoahGC \
-XX:ShenandoahGCHeuristics=adaptive \
-XX:TieredStopAtLevel=1 \
-XX:NewRatio=2 \
-Dspring.config.location=file:./application.properties \
-XX:ErrorFile=logs/error.log \
-XX:HeapDumpPath=logs/dumps \
-XX:+HeapDumpOnOutOfMemoryError \
-Xlog:gc*:file=logs/gc.log:time,uptime:filecount=5,filesize=10M \
$JAR"

LOG_FILE="$LOG_DIR/$NAME.log"
STDERR_LOG="$LOG_DIR/$NAME.err"
PID_FILE="$LOG_DIR/$NAME.pid"

# Make the log directory if it doesn't exist
if [ ! -d "$LOG_DIR" ] ; then
	mkdir -p $LOG_DIR
	chmod 777 -R $LOG_DIR
fi

isRunning() {
	[ -f "$PID_FILE" ] && ps `cat $PID_FILE` > /dev/null 2>&1
}

case $1 in
	start)
		if isRunning; then
			echo "Already started"
		else
			echo "Starting $NAME"
			echo "$CMD" >> "$LOG_FILE"
			$CMD > "$LOG_FILE" 2> "$STDERR_LOG" &
			echo $! > "$PID_FILE"
			if ! isRunning; then
				echo "Unable to start, see $LOG_FILE and $STDERR_LOG"
				exit 1
			fi
		fi
		# Tail the log file to keep the container running
		tail -f "$LOG_FILE"
	;;
	stop)
		if isRunning; then
			echo "Stopping $NAME"
			kill `cat $PID_FILE`
			rm "$PID_FILE"
		else
			echo "Not running"
		fi
	;;
	restart)
		$0 stop
		$0 start
	;;
	status)
		if isRunning; then
			echo "Running"
		else
			echo "Not running"
		fi
	;;
	*)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
    ;;
esac

exit 0
