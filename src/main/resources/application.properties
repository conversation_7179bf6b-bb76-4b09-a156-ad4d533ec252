server.port=8088

spring.application.name=emailagent
spring.datasource.url=${MYSQL_URL:************************************************************************************************}
spring.datasource.username=${MYSQL_USERNAME:root}
spring.datasource.password=${MYSQL_CHECKSUM:root}
# Enable detailed logging for Redis operations
logging.level.org.springframework.data.redis=DEBUG
logging.level.org.springframework.cache=DEBUG

# Enable detailed logging for serialization and Jackson
logging.level.org.springframework.core.serializer=DEBUG
logging.level.com.fasterxml.jackson.databind=DEBUG

springdoc.api-docs.path=/api-docs


# General logging for your application

logging.level.org.springframework=ERROR
logging.level.root=ERROR
logging.level.org.hibernate=ERROR
logging.level.com.enttribe=DEBUG
logging.level.net.bull.javamelody=ERROR
attachment.file.path=/tmp/gmail-credentials/
audit.enabled=true
create_user=false

email.body.content.token.limit=6000
notification.enable=${ALLOW_NOTIFICATION:true}
notification.url=https://fcm.googleapis.com/v1/projects/email-singularity-4ba04/messages:send
notification.json.path=/opt/visionwaves/emailagent/notification.json
pollingMode=${POLLING_MODE:Graph}


email.client.id=${EMAIL_CLIENT_ID:bZ1zETEbbcCym1o8EAsctP4DupQGI/twrlfZ69tadQQXKp3kDKIyLvB5s1O+tKZX}
email.client.secret=${EMAIL_CLIENT_SECRET:nrtLtkbw5yv8gHrHoUjJ9J1s8uEi91yMBmNU0Vj/r5A5v7ro41vpqB3y8DeQo4Ew}
email.tenant.id=${TENANT_ID:I5Usli811zarx7HdSTIG4bY2m9SkkNa2XIEda6Trv76P4lTC5j3vVTdknmJCZ61s}


##Dev
getChartJsonUrl=${CHART_JSON_URL:https://aramco.visionwaves.com/apim/admin-service/1.0}
insightQuestionUrl=${INSIGHTS_URL:https://aramco.visionwaves.com/apim/admin-service/1.0}
org.domain.name=${ORG_DOMAIN:vision.com,visionwaves.com}
outsiders.events.lookup.allow=${ALLOW_EVENT_LOOKUP:false}

#Redis
redis.enable=${REDIS_ENABLE:true}
commons.ai.sdk.chat.redis.enable=${SDK_REDIS_ENABLE:false}
commons.ai.sdk.chat.redis.type=${SDK_REDIS_TYPE:sentinel}
spring.cache.type=${SPRING_CACHE_TYPE:redis}
redis.sentinel.master=${REDIS_SENTINEL_MASTER:mymaster}
redis.sentinel.nodes=${REDIS_SENTINEL_NODES:redis-node-0.redis-headless.ansible.svc.cluster.local:26379}
redis.password=${REDIS_PASSWORD:BootF#123}

answerUrl=http://x101-document-service.ansible.svc.cluster.local/x101-document-service/UserMailAttachment/getAnswer
summaryUploadUrl=http://x101-document-service.ansible.svc.cluster.local/x101-document-service/UserMailAttachment/storePluginMessageSummary
summaryAnswerUrl=http://x101-document-service.ansible.svc.cluster.local/x101-document-service/UserMailAttachment/queryPluginMessageSummary
documentUploadUrl=http://x101-ai-service.ansible.svc.cluster.local/api/knowledgeGraph/generate_document_embedding

#promptAuditUrl=http://x101-service.ansible.svc.cluster.local/admin-service/PromptAudit/create
promptAuditUrl=http://x101-service.ansible.svc.cluster.local/admin-service/message/createPromptAudit


attachmentFilePath=/opt/visionwaves/emailagent
userCreateUrl=
generatedBy=${GENERATED_BY:<br><a style="font-style: italic; font-size: 11px;" href="https://emailsingularity.com/" target="_blank">Generated by Email Singularity</a>}
total.meeting.limit=${MEETING_LIMIT:500}

app.supported.formats=pdf,doc,docx
org.admin.list=<EMAIL>,<EMAIL>

##S3
s3Region=${S3_REGION:us-east-2}
s3User=${S3_USER:bootadmin}
s3Checksum=${S3_CHECKSUM:bootadmin}
s3Url=${S3_URL:http://seaweedfs-s3.swf.svc.cluster.local:8333}

ews.serviceAccountUsername=${EWS_SERVICE_ACCOUNT_USERNAME:<EMAIL>}
ews.serviceAccountPassword=${EWS_SERVICE_ACCOUNT_PASSWORD:Vision@12345}
ews.ewsURL=${EWS_URL:https://exchange-server.vision.com/EWS/Exchange.asmx}

build.approval.list=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

#Keep this (5min or 300000ms) lower than actual value to have some buffer time
token.refresh.interval=3300000
fcm.token.refresh.interval=3000000

#Determines cache eviction from the map. The value should be in minutes
user.info.cache.evict.time=30

# Optional properties
spring.datasource.driver-class-name=org.mariadb.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

## Hikari connection pool properties

# Minimum number of idle connections maintained by HikariCP in the pool
spring.datasource.hikari.minimum-idle=15
# Maximum number of connections in the pool
spring.datasource.hikari.maximum-pool-size=200
# Maximum idle time for a connection in milliseconds (30 seconds)
spring.datasource.hikari.idle-timeout=30000
# Maximum time to wait for a connection from the pool in milliseconds (20 seconds)
spring.datasource.hikari.connection-timeout=20000
# Maximum lifetime of a connection in the pool in milliseconds (30 minutes)
spring.datasource.hikari.max-lifetime=1800000
# Name of the connection pool
spring.datasource.hikari.pool-name=HikariPool
# Default auto-commit behavior for connections in the pool
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.connection-test-query=SELECT 1  # Query to test the connection validity (optional if driver supports JDBC4 isValid())


spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

server.servlet.context-path=/emailagent
server.tomcat.connection-timeout=60000
server.connection-timeout=60000
spring.mvc.async.request-timeout=60000

server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.directory=logs
server.tomcat.accesslog.pattern=%h %l %u %t "%r" %s %b
server.tomcat.accesslog.prefix=access_log
server.tomcat.accesslog.suffix=.log

#mistralai configurations local
#spring.ai.mistralai.api-key=49e78184beb55456026c82e8f5a2550d7cd974bcdfc5a272a10ea926dabe8994
#spring.ai.mistralai.chat.options.model=meta-llama/Llama-3-8b-chat-hf
#spring.ai.mistralai.chat.base-url=https://api.together.xyz

spring.ai.mistralai.api-key=${SPRING_AI_MISTRALAI_API_KEY:********************************************************}
#spring.ai.mistralai.chat.options.model=llama-3.2-3b-preview
#spring.ai.mistralai.chat.options.model=llama3-70b-8192
spring.ai.mistralai.chat.options.model=llama-3.3-70b-versatile
spring.ai.mistralai.chat.base-url=${SPRING_AI_MISTRALAI_CHAT_BASE_URL:https://api.groq.com/openai/}

spring.ai.mistralai.chat.options.maxTokens=8192
spring.ai.mistralai.chat.options.temperature=0.3
spring.ai.retry.max-attempts=3
spring.ai.retry.backoff.initial-interval=7
spring.ai.retry.backoff.multiplier=2

##Gmail integration set-up
#service.account.key=${SERVICE_ACCOUNT_KEY:/Users/<USER>/Downloads/emailassistant-1f35aa9e9c8c.json}
service.account.key=/Users/<USER>/Downloads/emailassistant-1f35aa9e9c8c.json
gmail.admin.id=${GMAIL_ADMIN_ID:<EMAIL>}

auth.secret.publicKey=${AUTH_SECRET_PUBLIC_KEY:MIIDKTCCAhGgAwIBAgIQWDlNVeQ4QrRE2BbvIH9UUjANBgkqhkiG9w0BAQsFADA1MTMwMQYDVQQDEypNaWNyb3NvZnQgRXhjaGFuZ2UgU2VydmVyIEF1dGggQ2VydGlmaWNhdGUwHhcNMjQwNzE3MTczMTIxWhcNMjkwNjIxMTczMTIxWjA1MTMwMQYDVQQDEypNaWNyb3NvZnQgRXhjaGFuZ2UgU2VydmVyIEF1dGggQ2VydGlmaWNhdGUwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCk+C6LLe/YDQWieAaTwYowzUSNVQb1/qfh9bk7g98jBRl5c6jKPh4/3Sdvm46VyOSN51AdcEZN4AGsnt1knCR/M5jZcq4AqkYY52B8PuzGdaKV8SrShu0NnQJCSaJgXso7AaGRB5pdjgsNqcNOGghLeSHPJqL0/F50+WYrQOLenjjFPrhin6oWJL3Z+ma/CviIH5uM3JMO3Ml2keQ2Vt5x0CTfyE9Ird/5JP731wRpuImSb+w2ZOldenRxPv1beKURMmHoJ/2MkIXQrM0JUOJUJQeNeGjcH3dBBpSAp+Z+lja82OxJYIeNrE0Z8eYVEZlATaTg9z9sChIBoerkqZrhAgMBAAGjNTAzMA4GA1UdDwEB/wQEAwIFoDATBgNVHSUEDDAKBggrBgEFBQcDATAMBgNVHRMBAf8EAjAAMA0GCSqGSIb3DQEBCwUAA4IBAQChbBJ08l8bsyJpTDlTITwOOqMVbhPgS0QtFae+5proHITJhxjr+vZMeRQdNC6vJhIHaJRwND40AaKOishFiObQOEdsUsQmLMzA4Qc58YxmqR1WmULjv72jRl8FYuyRDypg5Gc9sVnJ0eZrktvkEyH4jKUxUFv8P7rP5rf4GWUkAd0Edug2E9D0T14VOH+U7CoCTnJF0oXUYh3rVgwpaXJqid6W8a7JsXvF20fAixFK38/+he1/TfdRogQ4Y8J4vdGnbC32aeLhA2aVf2zcCBupr1kvhrHnl+/vEZ7CD42WavSE7mS705y3oJxetAbxGZptufS448tnjBJ54eeBfR4R}

spring.threads.virtual.enabled=true

kafka.emailTopic=${KAFKA_EMAIL_TOPIC:email-messages}
kafka.kafkaUrl=${KAFKA_URL:localhost:9092}
kafka.enabled=${KAFKA_ENABLE:true}

threadConcurency=10

analyzerUrl= ${ANALYZER_URL:http://************:5002/analyze}
maskingUrl=${MASKING_URL:http://************:5001/}
encryptedEntities=IN_VEHICLE_REGISTRATION,US_DRIVER_LICENSE,IN_PAN,US_PASSPORT,CREDIT_CARD,IN_AADHAAR,PHONE_NUMBER
encryptionKey=${ENCRYPTION_KEY:WmZq4t7w!z%ABC)J}

zoom.clientId=${ZOOM_CLIENT_ID:ya067IWRRD6WhB58EVQxvQ}
zoom.tokenUrl=${ZOOM_TOKEN_URL:https://zoom.us/oauth/token}
zoom.apiURL=${ZOOM_API_URL:https://api.zoom.us/v2}
zoom.clientSecret=${ZOOM_CLIENT_SECRET:vhVnxUJUTA4HkehH3ELxv3Vv7CGdGDqA}
zoom.accountId=${ZOOM_ACCOUNT_ID:WDPKtUbyT36OI-qIvo5a_g}



# Enable JavaMelody monitoring
javamelody.enabled=true

# Path to access the JavaMelody monitoring page
javamelody.init-parameters.monitoring-path=/monitoring
 
# (Optional) Enable logging for JavaMelody
javamelody.init-parameters.log=true
 
# (Optional) Storage location for data
#javamelody.init-parameters.storage-directory=/tmp/javamelody
 
# Enable monitoring for Spring Beans
javamelody.spring-monitoring-enabled=true
spring.security.ignored=/monitoring

consumer.mode=${MODE:KAFKA}
pubsub.projectId=${PROJECT_ID:prj-adc-gcp-nouros-poc}
pubsub.enabled=${PUBSUB_ENABLED:false}
pubsub.subscriptionId=${PUBSUB_SUBSCRIPTIONID:email-messages-priority-queue-sub}
s3.bucketName=${BUCKET_NAME:emailattachments}



##AI-Commons properties
#prompt.service.url=http://localhost:8081/prompt-analyzer/rest
commons.ai.sdk.is_local=${IS_LOCAL:false}
commons.ai.sdk.app.name=EMAIL_ASSISTANT_APP_NAME
commons.ai.sdk.default.llm.model=llama-3.3-70b-versatile
#commons.ai.sdk.vector_store.config=[{"vectorDatabase":"milvus","inference":"groq","embeddingModel":"nomic-embed-text-v1_5-preview1","host":"localhost","port":19530,"username":"","password":"","databaseName":"default","collectionName":"vector_store_knowledge_base","embeddingDimension":768,"indexType":"IVF_FLAT","metricType":"COSINE","initializeSchema":true}]

exception.audit.enable=true
prompt.audit.enable=true

## All .st file Insert into DB All Prompt_id's
actionTakenPromptId=EMAIL_ASSISTANT_APP_NAME-Mail-Find_Actions-v-1
categoryPromptId=EMAIL_ASSISTANT_APP_NAME-Assessment-Category_&_Priority-v-1
emailProfilePromptId=EMAIL_ASSISTANT_APP_NAME-Email-_Email_Profile-v-1
formatAnswerPromptId=EMAIL_ASSISTANT_APP_NAME-Format-Format_Answer-v-1
forwardDraftPromptId=EMAIL_ASSISTANT_APP_NAME-Draft-Forward_Draft-v-1
freshDraftPromptId=EMAIL_ASSISTANT_APP_NAME-Draft-Fresh_Draft-v-1
generateDraftPromptId=EMAIL_ASSISTANT_APP_NAME-Draft-Generate_Draft-v-1
generateDraftFromContentPromptId=EMAIL_ASSISTANT_APP_NAME-Draft-Generate_Draft_From_Content-v-1
generateFreshMeetingPromptId=EMAIL_ASSISTANT_APP_NAME-Event-Generate_Fresh_Meeting-v-1
improveDraftPromptId=EMAIL_ASSISTANT_APP_NAME-Draft-Improve_Draft-v-1
improveFreshDraftPromptId=EMAIL_ASSISTANT_APP_NAME-Draft-Improve_Fresh_Draft-v-1
improveSelectionPromptId=EMAIL_ASSISTANT_APP_NAME-Draft-Improve_Selection-v-1
mailSummaryPromptId=EMAIL_ASSISTANT_APP_NAME-Assessment-Mail_Summary-v-1
objectivePromptId=EMAIL_ASSISTANT_APP_NAME-Mail-Quick_Reply-v-1
regenerateDraftPromptId=EMAIL_ASSISTANT_APP_NAME-Draft-Regenerate_Draft-v-1
threadSummaryPromptId=EMAIL_ASSISTANT_APP_NAME-Mail-Thread_Summary_Prompt-v-1
threadSummaryQueryPromptId=EMAIL_ASSISTANT_APP_NAME-Mail-Thread_Summary_Query-v-1
tagPromptId=EMAIL_ASSISTANT_APP_NAME-Mail-Tag_Prompt-v-1
tableInquiryPromptId=EMAIL_ASSISTANT_APP_NAME-Query-Table_Inquiry-v-1
shortSummaryPromptId=EMAIL_ASSISTANT_APP_NAME-Mail-Short_Thread_Summary-v-1
userQueryAnswerSummaryPromptId=EMAIL_ASSISTANT_APP_NAME-Query-User_Query_Answer-v-1
meetingSummaryPromptId=EMAIL_ASSISTANT_APP_NAME-Mail-Meeting_Summary-v-1
mailSummaryQueryPromptId=EMAIL_ASSISTANT_APP_NAME-Mail-_Mail_Summary_Query-v-1
regenerateObjectivePromptId=EMAIL_ASSISTANT_APP_NAME-Mail-Regenerate_Quick_Reply-v-1
intentPromptNewPromptId=EMAIL_ASSISTANT_APP_NAME-Event-Intent_Prompt-v-1
askSystemPromptId=EMAIL_ASSISTANT_APP_NAME-Query-Ask-v-1
chatMessage=EMAIL_ASSISTANT_APP_NAME-Conversation-Chat_Message-v-1

###  Jaeger end point
management.otlp.tracing.endpoint=${TRACING_ENDPOINT:http://jaeger-collector.jaeger.svc.cluster.local:4318/v1/traces}
#management.otlp.tracing.endpoint=${TRACING_ENDPOINT:http://simplest-collector.jaeger.svc.cluster.local:4318/v1/traces}


# Management endpoints configuration
management.observations.key-values.application=${spring.application.name}

# Management metrics tags
management.metrics.tags.service.name=${spring.application.name}

# Tracing configuration
management.tracing.sampling.probability=1.0

# OTLP tracing endpoint
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.endpoints.web.exposure.include=*
management.metrics.export.prometheus.enabled=true
management.endpoint.health.show-details=always
management.metrics.distribution.percentiles-histogram.gen_ai.client.operation=true
management.metrics.distribution.percentiles-histogram.db.vector.client.operation=true
management.metrics.distribution.percentiles-histogram.spring.ai.chat.client=true

## Context propagation for Reactor
spring.reactor.context-propagation=auto

######################################
# Spring AI observability settings
######################################

## Include the Chatclient input in observations
spring.ai.chat.client.observation.include-input=true

## Include the VectorStor query and response in observations
spring.ai.vector.store.observations.include-query-response=true

## Include prompt and completion contents in observations
spring.ai.chat.observations.include-prompt=true
spring.ai.chat.observations.include-completion=true

## Include error logging in observations (note: not needed for Spring Web apps)
spring.ai.chat.observations.include-error-logging=true

