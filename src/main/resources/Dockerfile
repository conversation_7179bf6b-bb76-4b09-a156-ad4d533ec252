# Use an official Eclipse Temurin base image
FROM eclipse-temurin:21-jdk

# Install dumb-init for proper signal handling
RUN apt-get update && apt-get install -y --no-install-recommends \
    dumb-init \
    bash \
    curl \
    tzdata \
    libstdc++6 \
    openssl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
# Set the timezone
ENV TZ=UTC
ENV SSL_VAULT_PATH=/opt/visionwaves/sql_ssl
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create a directory for Gmail credentials
RUN mkdir -p /tmp/gmail-credentials
RUN mkdir -p $SSL_VAULT_PATH

# Copy the Gmail credentials JSON into the folder
ADD gmailassistant-credentials.json /tmp/gmail-credentials/

# Argument for the application name
ARG APP_NAME

# Env variables
ENV SERVICE_PATH=/opt/visionwaves/$APP_NAME

# Create the service path directory
RUN mkdir -p $SERVICE_PATH

# Add the application TAR file to the service path
ADD ./$APP_NAME.tar $SERVICE_PATH

# Change the working directory to the service path
WORKDIR $SERVICE_PATH

# Create a non-root user with UID 1001
RUN useradd -m -u 1001 visionwaves

# Set ownership of the service path and Gmail credentials
RUN chown -R visionwaves:visionwaves $SERVICE_PATH $SSL_VAULT_PATH /tmp/gmail-credentials

# Switch to the non-root user
USER visionwaves

# Use dumb-init as PID 1 for proper signal handling and initiate the process
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# The main command (run the script directly)
CMD ["sh", "run.sh", "start"]