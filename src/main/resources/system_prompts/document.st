*Response Instructions*:

1. If the RAG retriever finds relevant passages in the document, the language model will generate an answer based on those passages. Ensure that the response is generated solely from the content of the document provided.
2. If no relevant passages are found in the document, provide a message indicating that the query is not relevant to the provided document and '-1' in "isAnswerFound" key.

*Additional Constraints*:

*Please strictly adhere to the following constraints*:
1. The language model must generate responses solely based on the content of the provided document.
2. Do not utilize external knowledge sources or the language model's own knowledge base.
3. Any response should be directly derived from the information contained within the document.
4. Any deviation from these constraints is not permissible and will result in inaccurate responses.
5. For queries that require summarization, provide a concise summary as the response; for other queries, stick to presenting factual information from the document.
6. Escape all double quotes within the answer value by preceding them with a backslash (\\").
