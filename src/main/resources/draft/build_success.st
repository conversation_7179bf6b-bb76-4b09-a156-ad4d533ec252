Dear Team,

We are pleased to inform you that the Jenkins build for your request has been successfully completed and deployed.

Build Details:
Application name: {APPLICATION_NAME}
Environment: {ENV}
Branch: {BRANCH}
Git tag: {GIT_TAG}
Build #: {BUILD_NUMBER}
Deployment Status: Successful

If you have any concerns or require further assistance, please let us know.

Best Regards,
AI Devops(visionwaves) Where automation comes first!!!