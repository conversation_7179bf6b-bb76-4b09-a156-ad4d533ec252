Please generate always new draft email using content and objective shared below.Please follow the Json output as mentioned and do not include anything else in output, the json must be in parsable state only.Non Parsable JSON response is not acceptable
Please make sure to add '\n' always wherevr it is needed as I need to render this in email agent with proper line breaking.
mailbox_user for whom you need to generate the draft : {userName}

Objective: This the the intent of mailbox_user that needs to be considered while generating reply for the email. The mailbox_user wants this action to be accomplished for him.
{objective}

Content: This is the content of email that mailbox_user has received. Upon reading this content a draft needs to be generated.
{content}

Length Guideline : {length}
**Important:**
- Maintain flexibility within +/- 10% of the word count to ensure natural flow and completeness of the message without sacrificing clarity.

Tone Guideline : {tone}

Address the recipient with name.
Recipient (recipient of email):
{recipient}

UserName : This is the name that needs to be used while ending the mail like Thanks, <userName>/ Thanks & Regards <userName> etc. Please use the name as is, *Do Not try to chnage name*
{userName}

FORMAT:
{format}