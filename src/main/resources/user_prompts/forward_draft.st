<user-prompt>
    <instruction>
        Please generate a new draft email for forwarding based on the <field>objective</field> shared below. Use the <field>summary</field> as supporting context, but ensure the email addresses the objective prominently. The draft should follow the JSON output format mentioned and must be parsable. Non-parsable JSON responses are not acceptable. Ensure to include '\n' where needed for proper line breaking in the email agent.
    </instruction>
    <instruction>
        If the <field>objective</field> is not provided and only a recipient is tagged, generate the draft email solely for "FYI" purposes. Ensure the content does not imply any action or task assigned to the mailbox user or any recipient.
    </instruction>
    <instruction>
        Ensure that when no <field>objective</field> is present, the generated content should not place any responsibility or action on the user who is sending the email or the recipients.
    </instruction>
    <content>
        <mailbox-user>
            <label>Mailbox User:</label>
            <variable>{userName}</variable>
        </mailbox-user>
        <summary>
            <label>Summary:</label>
            <text>This summary provides context for the email content. Use it as background information to support the draft but do not focus on it more than the objective.</text>
            <variable>{summary}</variable>
        </summary>
        <objective>
            <label>Objective:</label>
            <text>The primary goal for forwarding this email. Address this explicitly in your response and craft the draft to reflect this purpose. If a specific recipient is mentioned (e.g., "forward this mail to <PERSON>"), address them directly in the salutation. If no specific name is provided, use a general salutation like "Dear Team." Ensure that the name extracted is what the mailbox user intends to address.</text>
            <variable>{objective}</variable>
        </objective>
        <guidelines>
            <length-guideline>
                <label>Length Guideline:</label>
                <variable>{length}</variable>
                <important>Maintain flexibility within +/- 10% of the word count to ensure a natural flow and completeness of the message without sacrificing clarity.</important>
            </length-guideline>
            <tone-guideline>
                <label>Tone Guideline:</label>
                <variable>{tone}</variable>
            </tone-guideline>
        </guidelines>
        <previous-draft>
            <label>Previous Generated Draft:</label>
            <text>This draft was generated previously by you. ***Please do not repeat the same content.***</text>
            <variable>{previousDraft}</variable>
        </previous-draft>
        <format>
            <label>FORMAT:</label>
            <variable>{format}</variable>
        </format>
        <note>
            <text>The draft email should not include the recipient's name or email in the salutation or closing. Only the sender's name should be used in the closing.</text>
        </note>
    </content>
</user-prompt>
