package com.enttribe.emailagent.rest;

import static org.mockito.Mockito.when;

import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.service.EmailPreferencesService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Disabled;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {EmailPreferencesRest.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class EmailPreferencesRestTest {
    @Autowired
    private EmailPreferencesRest emailPreferencesRest;

    @MockBean
    private EmailPreferencesService emailPreferencesService;

    @MockBean
    private IMailSummaryDao iMailSummaryDao;

    @MockBean
    private UserContextHolder userContextHolder;

    /**
     * Test {@link EmailPreferencesRest#updateTimeZone(Map)}.
     * <p>
     * Method under test: {@link EmailPreferencesRest#updateTimeZone(Map)}
     */
    @Test
    @DisplayName("Test updateTimeZone(Map)")
    void testUpdateTimeZone() throws Exception {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(emailPreferencesService.updateTimeZone(Mockito.<String>any())).thenReturn(emailPreferences);

        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("timeZone", "timeZone");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/preferences/updateTimeZone")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"id\":1,\"userId\":\"42\",\"emailSubject\":\"<EMAIL>\",\"senderCompany\":\"Sender Company\",\"importantTags"
                                        + "\":\"Important Tags\",\"fontFamily\":\"Font Family\",\"fontSize\":\"Font Size\",\"emailSender\":\"jane.doe@example"
                                        + ".org\",\"blackListedSender\":\"Black Listed Sender\",\"blackListedDomain\":\"Black Listed Domain\",\"blackListedSubject"
                                        + "\":\"Hello from the Dreaming Spires\",\"timeZone\":\"UTC\",\"keyboardShortcuts\":\"Keyboard Shortcuts\",\"createdTime"
                                        + "\":0,\"modifiedTime\":0,\"conversationId\":\"42\",\"contactNumber\":\"42\",\"checkin\":[0,0],\"checkout\":[0,0],"
                                        + "\"fontColor\":\"Font Color\",\"displayName\":\"Display Name\",\"lastPollTime\":[1970,1,1,0,0],\"nextPollTime\":"
                                        + "[1970,1,1,0,0],\"maskContent\":true,\"meetingType\":\"Meeting Type\",\"debugMode\":true,\"allowNotification\""
                                        + ":true,\"deviceId\":\"42\",\"gcmId\":\"42\",\"onDemandEnabled\":true,\"preferredMeetingDuration\":1,\"dateFormat\":"
                                        + "\"2020-03-01\",\"timeFormat\":\"Time Format\",\"isCategoryEnabled\":true,\"isPriorityEnabled\":true,\"isToneEnabled"
                                        + "\":true}"));
    }

    /**
     * Test {@link EmailPreferencesRest#updateDeviceId(Map)}.
     * <p>
     * Method under test: {@link EmailPreferencesRest#updateDeviceId(Map)}
     */
    @Test
    @DisplayName("Test updateDeviceId(Map)")
    void testUpdateDeviceId() throws Exception {
        // Arrange
        when(emailPreferencesService.updateDeviceId(Mockito.<String>any())).thenReturn("2020-03-01");

        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("deviceId", "deviceId");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/preferences/updateDeviceId")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{\"result\":\"2020-03-01\"}"));
    }

    /**
     * Test {@link EmailPreferencesRest#update(EmailPreferences)}.
     * <p>
     * Method under test: {@link EmailPreferencesRest#update(EmailPreferences)}
     */
    @Test
    @DisplayName("Test update(EmailPreferences)")
    @Disabled("TODO: Complete this test")
    void testUpdate() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Java 8 date/time type `java.time.LocalTime` not supported by default: add Module "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" to enable handling (through reference chain: com.enttribe.emailagent.entity.EmailPreferences["checkin"])
        //       at com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:77)
        //       at com.fasterxml.jackson.databind.SerializerProvider.reportBadDefinition(SerializerProvider.java:1308)
        //       at com.fasterxml.jackson.databind.ser.impl.UnsupportedTypeSerializer.serialize(UnsupportedTypeSerializer.java:35)
        //       at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
        //       at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
        //       at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
        //       at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:479)
        //       at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:318)
        //       at com.fasterxml.jackson.databind.ObjectMapper._writeValueAndClose(ObjectMapper.java:4719)
        //       at com.fasterxml.jackson.databind.ObjectMapper.writeValueAsString(ObjectMapper.java:3964)
        //   See https://diff.blue/R013 to resolve this issue.

        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        String content = (new ObjectMapper()).writeValueAsString(emailPreferences);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/preferences/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(emailPreferencesRest).build().perform(requestBuilder);
    }

    /**
     * Test
     * {@link EmailPreferencesRest#saveOrUpdateEmailPreferences(EmailPreferences)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesRest#saveOrUpdateEmailPreferences(EmailPreferences)}
     */
    @Test
    @DisplayName("Test saveOrUpdateEmailPreferences(EmailPreferences)")
    @Disabled("TODO: Complete this test")
    void testSaveOrUpdateEmailPreferences() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Java 8 date/time type `java.time.LocalTime` not supported by default: add Module "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" to enable handling (through reference chain: com.enttribe.emailagent.entity.EmailPreferences["checkin"])
        //       at com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:77)
        //       at com.fasterxml.jackson.databind.SerializerProvider.reportBadDefinition(SerializerProvider.java:1308)
        //       at com.fasterxml.jackson.databind.ser.impl.UnsupportedTypeSerializer.serialize(UnsupportedTypeSerializer.java:35)
        //       at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
        //       at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
        //       at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
        //       at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:479)
        //       at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:318)
        //       at com.fasterxml.jackson.databind.ObjectMapper._writeValueAndClose(ObjectMapper.java:4719)
        //       at com.fasterxml.jackson.databind.ObjectMapper.writeValueAsString(ObjectMapper.java:3964)
        //   See https://diff.blue/R013 to resolve this issue.

        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        String content = (new ObjectMapper()).writeValueAsString(emailPreferences);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/preferences/saveOrUpdate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(emailPreferencesRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link EmailPreferencesRest#getEmailPreferences()}.
     * <p>
     * Method under test: {@link EmailPreferencesRest#getEmailPreferences()}
     */
    @Test
    @DisplayName("Test getEmailPreferences()")
    void testGetEmailPreferences() throws Exception {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(emailPreferencesService.getEmailPreferences()).thenReturn(emailPreferences);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/preferences/get");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"id\":1,\"userId\":\"42\",\"emailSubject\":\"<EMAIL>\",\"senderCompany\":\"Sender Company\",\"importantTags"
                                        + "\":\"Important Tags\",\"fontFamily\":\"Font Family\",\"fontSize\":\"Font Size\",\"emailSender\":\"jane.doe@example"
                                        + ".org\",\"blackListedSender\":\"Black Listed Sender\",\"blackListedDomain\":\"Black Listed Domain\",\"blackListedSubject"
                                        + "\":\"Hello from the Dreaming Spires\",\"timeZone\":\"UTC\",\"keyboardShortcuts\":\"Keyboard Shortcuts\",\"createdTime"
                                        + "\":0,\"modifiedTime\":0,\"conversationId\":\"42\",\"contactNumber\":\"42\",\"checkin\":[0,0],\"checkout\":[0,0],"
                                        + "\"fontColor\":\"Font Color\",\"displayName\":\"Display Name\",\"lastPollTime\":[1970,1,1,0,0],\"nextPollTime\":"
                                        + "[1970,1,1,0,0],\"maskContent\":true,\"meetingType\":\"Meeting Type\",\"debugMode\":true,\"allowNotification\""
                                        + ":true,\"deviceId\":\"42\",\"gcmId\":\"42\",\"onDemandEnabled\":true,\"preferredMeetingDuration\":1,\"dateFormat\":"
                                        + "\"2020-03-01\",\"timeFormat\":\"Time Format\",\"isCategoryEnabled\":true,\"isPriorityEnabled\":true,\"isToneEnabled"
                                        + "\":true}"));
    }

    /**
     * Test {@link EmailPreferencesRest#create(EmailPreferences)}.
     * <p>
     * Method under test: {@link EmailPreferencesRest#create(EmailPreferences)}
     */
    @Test
    @DisplayName("Test create(EmailPreferences)")
    @Disabled("TODO: Complete this test")
    void testCreate() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Java 8 date/time type `java.time.LocalTime` not supported by default: add Module "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" to enable handling (through reference chain: com.enttribe.emailagent.entity.EmailPreferences["checkin"])
        //       at com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:77)
        //       at com.fasterxml.jackson.databind.SerializerProvider.reportBadDefinition(SerializerProvider.java:1308)
        //       at com.fasterxml.jackson.databind.ser.impl.UnsupportedTypeSerializer.serialize(UnsupportedTypeSerializer.java:35)
        //       at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
        //       at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
        //       at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
        //       at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:479)
        //       at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:318)
        //       at com.fasterxml.jackson.databind.ObjectMapper._writeValueAndClose(ObjectMapper.java:4719)
        //       at com.fasterxml.jackson.databind.ObjectMapper.writeValueAsString(ObjectMapper.java:3964)
        //   See https://diff.blue/R013 to resolve this issue.

        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        String content = (new ObjectMapper()).writeValueAsString(emailPreferences);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/preferences/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(emailPreferencesRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link EmailPreferencesRest#checkPreference(String, String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesRest#checkPreference(String, String)}
     */
    @Test
    @DisplayName("Test checkPreference(String, String)")
    void testCheckPreference() throws Exception {
        // Arrange
        when(emailPreferencesService.checkPreference(Mockito.<String>any(), Mockito.<String>any())).thenReturn(true);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/preferences/check")
                .param("preference", "foo")
                .param("type", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{\"result\":true}"));
    }

    /**
     * Test {@link EmailPreferencesRest#addConversationId(String, String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesRest#addConversationId(String, String)}
     */
    @Test
    @DisplayName("Test addConversationId(String, String)")
    void testAddConversationId() throws Exception {
        // Arrange
        when(emailPreferencesService.addConversationId(Mockito.<String>any())).thenReturn(new HashMap<>());

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/preferences/addConversationId");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{}"));
    }

    /**
     * Test {@link EmailPreferencesRest#deleteConversationId(String, String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesRest#deleteConversationId(String, String)}
     */
    @Test
    @DisplayName("Test deleteConversationId(String, String)")
    void testDeleteConversationId() throws Exception {
        // Arrange
        when(emailPreferencesService.deleteConversationId(Mockito.<String>any())).thenReturn(new HashMap<>());

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/preferences/deleteConversationId")
                .param("conversationId", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{}"));
    }

    /**
     * Test {@link EmailPreferencesRest#addPreferences(String, String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesRest#addPreferences(String, String)}
     */
    @Test
    @DisplayName("Test addPreferences(String, String)")
    void testAddPreferences() throws Exception {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(emailPreferencesService.addPreferences(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(emailPreferences);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/preferences/addPreferences")
                .param("preference", "foo")
                .param("type", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"id\":1,\"userId\":\"42\",\"emailSubject\":\"<EMAIL>\",\"senderCompany\":\"Sender Company\",\"importantTags"
                                        + "\":\"Important Tags\",\"fontFamily\":\"Font Family\",\"fontSize\":\"Font Size\",\"emailSender\":\"jane.doe@example"
                                        + ".org\",\"blackListedSender\":\"Black Listed Sender\",\"blackListedDomain\":\"Black Listed Domain\",\"blackListedSubject"
                                        + "\":\"Hello from the Dreaming Spires\",\"timeZone\":\"UTC\",\"keyboardShortcuts\":\"Keyboard Shortcuts\",\"createdTime"
                                        + "\":0,\"modifiedTime\":0,\"conversationId\":\"42\",\"contactNumber\":\"42\",\"checkin\":[0,0],\"checkout\":[0,0],"
                                        + "\"fontColor\":\"Font Color\",\"displayName\":\"Display Name\",\"lastPollTime\":[1970,1,1,0,0],\"nextPollTime\":"
                                        + "[1970,1,1,0,0],\"maskContent\":true,\"meetingType\":\"Meeting Type\",\"debugMode\":true,\"allowNotification\""
                                        + ":true,\"deviceId\":\"42\",\"gcmId\":\"42\",\"onDemandEnabled\":true,\"preferredMeetingDuration\":1,\"dateFormat\":"
                                        + "\"2020-03-01\",\"timeFormat\":\"Time Format\",\"isCategoryEnabled\":true,\"isPriorityEnabled\":true,\"isToneEnabled"
                                        + "\":true}"));
    }

    /**
     * Test {@link EmailPreferencesRest#addPreferencesV1(Map)}.
     * <p>
     * Method under test: {@link EmailPreferencesRest#addPreferencesV1(Map)}
     */
    @Test
    @DisplayName("Test addPreferencesV1(Map)")
    void testAddPreferencesV1() throws Exception {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(emailPreferencesService.addPreferences(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(emailPreferences);

        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("preference", "foo");
        stringStringMap.put("type", "sender");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/preferences/addPreferences")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"id\":1,\"userId\":\"42\",\"emailSubject\":\"<EMAIL>\",\"senderCompany\":\"Sender Company\",\"importantTags"
                                        + "\":\"Important Tags\",\"fontFamily\":\"Font Family\",\"fontSize\":\"Font Size\",\"emailSender\":\"jane.doe@example"
                                        + ".org\",\"blackListedSender\":\"Black Listed Sender\",\"blackListedDomain\":\"Black Listed Domain\",\"blackListedSubject"
                                        + "\":\"Hello from the Dreaming Spires\",\"timeZone\":\"UTC\",\"keyboardShortcuts\":\"Keyboard Shortcuts\",\"createdTime"
                                        + "\":0,\"modifiedTime\":0,\"conversationId\":\"42\",\"contactNumber\":\"42\",\"checkin\":[0,0],\"checkout\":[0,0],"
                                        + "\"fontColor\":\"Font Color\",\"displayName\":\"Display Name\",\"lastPollTime\":[1970,1,1,0,0],\"nextPollTime\":"
                                        + "[1970,1,1,0,0],\"maskContent\":true,\"meetingType\":\"Meeting Type\",\"debugMode\":true,\"allowNotification\""
                                        + ":true,\"deviceId\":\"42\",\"gcmId\":\"42\",\"onDemandEnabled\":true,\"preferredMeetingDuration\":1,\"dateFormat\":"
                                        + "\"2020-03-01\",\"timeFormat\":\"Time Format\",\"isCategoryEnabled\":true,\"isPriorityEnabled\":true,\"isToneEnabled"
                                        + "\":true}"));
    }

    /**
     * Test {@link EmailPreferencesRest#deletePreferences(String, String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesRest#deletePreferences(String, String)}
     */
    @Test
    @DisplayName("Test deletePreferences(String, String)")
    void testDeletePreferences() throws Exception {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(emailPreferencesService.deletePreferences(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(emailPreferences);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/preferences/deletePreferences")
                .param("preference", "foo")
                .param("type", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"id\":1,\"userId\":\"42\",\"emailSubject\":\"<EMAIL>\",\"senderCompany\":\"Sender Company\",\"importantTags"
                                        + "\":\"Important Tags\",\"fontFamily\":\"Font Family\",\"fontSize\":\"Font Size\",\"emailSender\":\"jane.doe@example"
                                        + ".org\",\"blackListedSender\":\"Black Listed Sender\",\"blackListedDomain\":\"Black Listed Domain\",\"blackListedSubject"
                                        + "\":\"Hello from the Dreaming Spires\",\"timeZone\":\"UTC\",\"keyboardShortcuts\":\"Keyboard Shortcuts\",\"createdTime"
                                        + "\":0,\"modifiedTime\":0,\"conversationId\":\"42\",\"contactNumber\":\"42\",\"checkin\":[0,0],\"checkout\":[0,0],"
                                        + "\"fontColor\":\"Font Color\",\"displayName\":\"Display Name\",\"lastPollTime\":[1970,1,1,0,0],\"nextPollTime\":"
                                        + "[1970,1,1,0,0],\"maskContent\":true,\"meetingType\":\"Meeting Type\",\"debugMode\":true,\"allowNotification\""
                                        + ":true,\"deviceId\":\"42\",\"gcmId\":\"42\",\"onDemandEnabled\":true,\"preferredMeetingDuration\":1,\"dateFormat\":"
                                        + "\"2020-03-01\",\"timeFormat\":\"Time Format\",\"isCategoryEnabled\":true,\"isPriorityEnabled\":true,\"isToneEnabled"
                                        + "\":true}"));
    }

    /**
     * Test {@link EmailPreferencesRest#getTimeZonesLike(String)}.
     * <p>
     * Method under test: {@link EmailPreferencesRest#getTimeZonesLike(String)}
     */
    @Test
    @DisplayName("Test getTimeZonesLike(String)")
    void testGetTimeZonesLike() throws Exception {
        // Arrange
        when(emailPreferencesService.getTimeZonesLike(Mockito.<String>any())).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/preferences/getTimeZones")
                .param("zone", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test
     * {@link EmailPreferencesRest#search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesRest#search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}
     */
    @Test
    @DisplayName("Test search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)")
    void testSearch() throws Exception {
        // Arrange
        when(emailPreferencesService.search(Mockito.<Integer>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<Date>any(),
                Mockito.<Date>any(), Mockito.<String>any(), Mockito.<LocalTime>any(), Mockito.<LocalTime>any(),
                Mockito.<String>any(), Mockito.<String>any())).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/preferences/search");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test
     * {@link EmailPreferencesRest#count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesRest#count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}
     */
    @Test
    @DisplayName("Test count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)")
    void testCount() throws Exception {
        // Arrange
        when(emailPreferencesService.count(Mockito.<Integer>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<Date>any(),
                Mockito.<Date>any(), Mockito.<String>any(), Mockito.<LocalTime>any(), Mockito.<LocalTime>any(),
                Mockito.<String>any(), Mockito.<String>any())).thenReturn(3L);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/preferences/count");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("3"));
    }

    /**
     * Test {@link EmailPreferencesRest#preferenceByFilter(Integer, Integer, Map)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesRest#preferenceByFilter(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test preferenceByFilter(Integer, Integer, Map)")
    void testPreferenceByFilter() throws Exception {
        // Arrange
        when(emailPreferencesService.preferenceByFilter(Mockito.<Integer>any(), Mockito.<Integer>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders.post("/preferences/preferenceByFilter")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link EmailPreferencesRest#preferenceCountByFilter(Map)}.
     * <p>
     * Method under test: {@link EmailPreferencesRest#preferenceCountByFilter(Map)}
     */
    @Test
    @DisplayName("Test preferenceCountByFilter(Map)")
    void testPreferenceCountByFilter() throws Exception {
        // Arrange
        when(emailPreferencesService.preferenceCountByFilter(Mockito.<Map<String, Object>>any())).thenReturn(3L);
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/preferences/preferenceCountByFilter")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("3"));
    }

    /**
     * Test {@link EmailPreferencesRest#updateNotificationInfo(Map)}.
     * <ul>
     *   <li>Given {@code null}.</li>
     *   <li>When {@link HashMap#HashMap()} {@code allowNotification} is
     * {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesRest#updateNotificationInfo(Map)}
     */
    @Test
    @DisplayName("Test updateNotificationInfo(Map); given 'null'; when HashMap() 'allowNotification' is 'true'")
    void testUpdateNotificationInfo_givenNull_whenHashMapAllowNotificationIsTrue() throws Exception {
        // Arrange
        when(emailPreferencesService.updateNotificationInfo(Mockito.<Boolean>any(), Mockito.<String>any()))
                .thenReturn("2020-03-01");

        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("allowNotification", true);
        stringObjectMap.put("deviceId", null);
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/preferences/updateNotificationInfo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{\"result\":\"2020-03-01\"}"));
    }

    /**
     * Test {@link EmailPreferencesRest#updateNotificationInfo(Map)}.
     * <ul>
     *   <li>When {@link HashMap#HashMap()} {@code deviceId} is {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesRest#updateNotificationInfo(Map)}
     */
    @Test
    @DisplayName("Test updateNotificationInfo(Map); when HashMap() 'deviceId' is 'true'")
    void testUpdateNotificationInfo_whenHashMapDeviceIdIsTrue() throws Exception {
        // Arrange
        when(emailPreferencesService.updateNotificationInfo(Mockito.<Boolean>any(), Mockito.<String>any()))
                .thenReturn("2020-03-01");

        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("deviceId", true);
        stringObjectMap.put("deviceId", "allowNotification");
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/preferences/updateNotificationInfo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailPreferencesRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{\"result\":\"2020-03-01\"}"));
    }
}
