package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.service.UserTemplateService;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {UserTemplateRest.class, UserTemplateService.class})
@ExtendWith(SpringExtension.class)
class UserTemplateRestTest {
    @Autowired
    private UserTemplateRest userTemplateRest;

    /**
     * Test {@link UserTemplateRest#update(Map)}.
     * <p>
     * Method under test: {@link UserTemplateRest#update(Map)}
     */
    @Test
    @DisplayName("Test update(Map)")
    @Disabled("TODO: Complete this test")
    void testUpdate() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@141fb6c3 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass162, locations = [], classes = [com.enttribe.emailagent.rest.UserTemplateRest, com.enttribe.emailagent.service.UserTemplateService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3e006700, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@1faade2d, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@31ccebba], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders.post("/template/update")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act
        MockMvcBuilders.standaloneSetup(userTemplateRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link UserTemplateRest#add(Map)}.
     * <p>
     * Method under test: {@link UserTemplateRest#add(Map)}
     */
    @Test
    @DisplayName("Test add(Map)")
    @Disabled("TODO: Complete this test")
    void testAdd() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   jakarta.servlet.ServletException: Request processing failed: java.lang.NullPointerException: Cannot invoke "com.enttribe.emailagent.userinfo.UserInfo.getEmail()" because the return value of "com.enttribe.emailagent.userinfo.UserContextHolder.getCurrentUser()" is null
        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
        //   java.lang.NullPointerException: Cannot invoke "com.enttribe.emailagent.userinfo.UserInfo.getEmail()" because the return value of "com.enttribe.emailagent.userinfo.UserContextHolder.getCurrentUser()" is null
        //       at com.enttribe.emailagent.service.UserTemplateService.add(UserTemplateService.java:40)
        //       at com.enttribe.emailagent.rest.UserTemplateRest.add(UserTemplateRest.java:32)
        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
        //   See https://diff.blue/R013 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders.post("/template/add")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act
        MockMvcBuilders.standaloneSetup(userTemplateRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link UserTemplateRest#get(String)}.
     * <p>
     * Method under test: {@link UserTemplateRest#get(String)}
     */
    @Test
    @DisplayName("Test get(String)")
    @Disabled("TODO: Complete this test")
    void testGet() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@6301f661 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass159, locations = [], classes = [com.enttribe.emailagent.rest.UserTemplateRest, com.enttribe.emailagent.service.UserTemplateService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3e006700, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@1faade2d, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@31ccebba], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/template/get");

        // Act
        MockMvcBuilders.standaloneSetup(userTemplateRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link UserTemplateRest#delete(String)}.
     * <p>
     * Method under test: {@link UserTemplateRest#delete(String)}
     */
    @Test
    @DisplayName("Test delete(String)")
    @Disabled("TODO: Complete this test")
    void testDelete() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@200e0415 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass156, locations = [], classes = [com.enttribe.emailagent.rest.UserTemplateRest, com.enttribe.emailagent.service.UserTemplateService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3e006700, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@1faade2d, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@31ccebba], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/template/delete");

        // Act
        MockMvcBuilders.standaloneSetup(userTemplateRest).build().perform(requestBuilder);
    }
}
