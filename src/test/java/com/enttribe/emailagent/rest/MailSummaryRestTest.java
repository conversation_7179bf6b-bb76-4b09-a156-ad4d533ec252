package com.enttribe.emailagent.rest;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.service.ImailSummaryService;

import java.util.ArrayList;
import java.util.HashMap;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {MailSummaryRest.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class MailSummaryRestTest {
    @MockBean
    private ImailSummaryService imailSummaryService;

    @Autowired
    private MailSummaryRest mailSummaryRest;

    /**
     * Test {@link MailSummaryRest#mailSummary(String, String)}.
     * <p>
     * Method under test: {@link MailSummaryRest#mailSummary(String, String)}
     */
    @Test
    @DisplayName("Test mailSummary(String, String)")
    void testMailSummary() throws Exception {
        // Arrange
        when(imailSummaryService.getMailSummaryAndThreadSummary(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new HashMap<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/mailSummary/getSummary")
                .param("internetMessageId", "foo")
                .param("userId", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(mailSummaryRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{}"));
    }

    /**
     * Test {@link MailSummaryRest#getAllMailSummary(Integer, Integer, String)}.
     * <p>
     * Method under test:
     * {@link MailSummaryRest#getAllMailSummary(Integer, Integer, String)}
     */
    @Test
    @DisplayName("Test getAllMailSummary(Integer, Integer, String)")
    void testGetAllMailSummary() throws Exception {
        // Arrange
        when(imailSummaryService.getAllMailSummary(Mockito.<Integer>any(), Mockito.<Integer>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/mailSummary/getAllMailSummary");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(mailSummaryRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link MailSummaryRest#updateBatch()}.
     * <p>
     * Method under test: {@link MailSummaryRest#updateBatch()}
     */
    @Test
    @DisplayName("Test updateBatch()")
    void testUpdateBatch() throws Exception {
        // Arrange
        doNothing().when(imailSummaryService).updateBatchByAvgTime();
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/mailSummary/updateBatch");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(mailSummaryRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    /**
     * Test {@link MailSummaryRest#saveContacts()}.
     * <p>
     * Method under test: {@link MailSummaryRest#saveContacts()}
     */
    @Test
    @DisplayName("Test saveContacts()")
    void testSaveContacts() throws Exception {
        // Arrange
        doNothing().when(imailSummaryService).saveContactSummary();
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/mailSummary/saveContacts");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(mailSummaryRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }
}
