package com.enttribe.emailagent.rest;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.ai.polling.OutlookPollingAI;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.UserEmailDto;
import com.enttribe.emailagent.dto.UserEmailResponseDto;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.utils.KafkaConsumer;
import com.enttribe.emailagent.wrapper.MessageWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.calendar.model.Event;
import com.google.api.services.calendar.model.Events;
import com.google.api.services.gmail.model.ListMessagesResponse;
import com.google.api.services.gmail.model.Message;
import com.google.api.services.gmail.model.VacationSettings;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {GmailRestController.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class GmailRestControllerTest {
    @MockBean
    private GmailIntegration gmailIntegration;

    @Autowired
    private GmailRestController gmailRestController;

    @MockBean
    private KafkaConsumer kafkaConsumer;

    @MockBean
    private OutlookPollingAI outlookPollingAI;

    /**
     * Test
     * {@link GmailRestController#summarizeMailAttachment(String, String, String)}.
     * <p>
     * Method under test:
     * {@link GmailRestController#summarizeMailAttachment(String, String, String)}
     */
    @Test
    @DisplayName("Test summarizeMailAttachment(String, String, String)")
    void testSummarizeMailAttachment() throws Exception {
        // Arrange
        when(gmailIntegration.summarizeMailAttachment(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/gmailRestController/summarizeMailAttachment")
                .param("conversationId", "foo")
                .param("emailId", "foo")
                .param("messageId", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<List/>"));
    }

    /**
     * Test {@link GmailRestController#pollGmailFolderWise(String, String, String)}.
     * <p>
     * Method under test:
     * {@link GmailRestController#pollGmailFolderWise(String, String, String)}
     */
    @Test
    @DisplayName("Test pollGmailFolderWise(String, String, String)")
    void testPollGmailFolderWise() throws Exception {
        // Arrange
        UserEmailResponseDto userEmailResponseDto = new UserEmailResponseDto();
        userEmailResponseDto.setMailBoxUserEmail("<EMAIL>");
        userEmailResponseDto.setMailBoxUserId("42");
        userEmailResponseDto.setMessages(new ArrayList<>());
        when(gmailIntegration.pollGmailFolderWise(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userEmailResponseDto);
        doNothing().when(outlookPollingAI)
                .consumeKafka(Mockito.<String>any(), Mockito.<String>any(), Mockito.<List<List<UserEmailDto>>>any(),
                        Mockito.<String>any());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/gmailRestController/pollGmailFolderWise")
                .param("folderId", "foo")
                .param("time", "foo")
                .param("userEmail", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content()
                        .string("<UserEmailResponseDto><mailBoxUserEmail><EMAIL></mailBoxUserEmail><mailBoxUserId>42<"
                                + "/mailBoxUserId><messages/></UserEmailResponseDto>"));
    }

    /**
     * Test {@link GmailRestController#getLogs()}.
     * <p>
     * Method under test: {@link GmailRestController#getLogs()}
     */
    @Test
    @DisplayName("Test getLogs()")
    void testGetLogs() throws Exception {
        // Arrange
        when(gmailIntegration.getLogs()).thenReturn("Logs");
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/gmailRestController/getLogs");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("text/plain;charset=ISO-8859-1"))
                .andExpect(MockMvcResultMatchers.content().string("Logs"));
    }

    /**
     * Test {@link GmailRestController#getMessageById(String, String)}.
     * <p>
     * Method under test: {@link GmailRestController#getMessageById(String, String)}
     */
    @Test
    @DisplayName("Test getMessageById(String, String)")
    void testGetMessageById() throws Exception {
        // Arrange
        when(gmailIntegration.getMessageById(Mockito.<String>any(), Mockito.<String>any())).thenReturn(new Message());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/gmailRestController/getMessageById")
                .param("message", "foo")
                .param("userEmail", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<Message/>"));
    }

    /**
     * Test {@link GmailRestController#getMessages(String)}.
     * <p>
     * Method under test: {@link GmailRestController#getMessages(String)}
     */
    @Test
    @DisplayName("Test getMessages(String)")
    void testGetMessages() throws Exception {
        // Arrange
        when(gmailIntegration.getMessages(Mockito.<String>any())).thenReturn(new ListMessagesResponse());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/gmailRestController/getMessages")
                .param("userEmail", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<ListMessagesResponse/>"));
    }

    /**
     * Test {@link GmailRestController#fetchAttachmentBtId(String, String, String)}.
     * <p>
     * Method under test:
     * {@link GmailRestController#fetchAttachmentBtId(String, String, String)}
     */
    @Test
    @DisplayName("Test fetchAttachmentBtId(String, String, String)")
    void testFetchAttachmentBtId() throws Exception {
        // Arrange
        EventDto eventDto = new EventDto();
        eventDto.setAccepted("Accepted");
        eventDto.setAttendees(new ArrayList<>());
        eventDto.setBodyPreview("Not all who wander are lost");
        eventDto.setCreatedDateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        eventDto.setHasAttachments(true);
        eventDto.setId("42");
        eventDto.setJoinUrl("https://example.org/example");
        eventDto
                .setLastModifiedDateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        eventDto.setLocation("Location");
        eventDto.setMeetingEndTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        eventDto.setMeetingStartTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        eventDto.setOptionalAttendees(new ArrayList<>());
        eventDto.setOrganizer("Organizer");
        eventDto.setRequiredAttendees(new ArrayList<>());
        eventDto.setSubject("Hello from the Dreaming Spires");
        when(gmailIntegration.fetchAttachmentBtId(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(eventDto);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/gmailRestController/fetchAttachmentBtId")
                .param("event", "foo")
                .param("message", "foo")
                .param("userEmail", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "<EventDto><organizer>Organizer</organizer><attendees/><requiredAttendees/><optionalAttendees/><subject>Hello"
                                        + " from the Dreaming Spires</subject><accepted>Accepted</accepted><bodyPreview>Not all who wander are"
                                        + " lost</bodyPreview><joinUrl>https://example.org/example</joinUrl><location>Location</location>"
                                        + "<hasAttachments>true</hasAttachments><meetingStartTime>0</meetingStartTime><meetingEndTime>0<"
                                        + "/meetingEndTime><createdDateTime>0</createdDateTime><lastModifiedDateTime>0</lastModifiedDateTime><id"
                                        + ">42</id></EventDto>"));
    }

    /**
     * Test {@link GmailRestController#getEvent(String)}.
     * <p>
     * Method under test: {@link GmailRestController#getEvent(String)}
     */
    @Test
    @DisplayName("Test getEvent(String)")
    void testGetEvent() throws Exception {
        // Arrange
        when(gmailIntegration.getEvent(Mockito.<String>any())).thenReturn(new Events());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/gmailRestController/getEvent")
                .param("userEmail", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<Events/>"));
    }

    /**
     * Test {@link GmailRestController#getEventById(String, String)}.
     * <p>
     * Method under test: {@link GmailRestController#getEventById(String, String)}
     */
    @Test
    @DisplayName("Test getEventById(String, String)")
    void testGetEventById() throws Exception {
        // Arrange
        when(gmailIntegration.getEventById(Mockito.<String>any(), Mockito.<String>any())).thenReturn(new Event());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/gmailRestController/getEventById")
                .param("messageId", "foo")
                .param("userEmail", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<Event/>"));
    }

    /**
     * Test {@link GmailRestController#getLabels(String)}.
     * <p>
     * Method under test: {@link GmailRestController#getLabels(String)}
     */
    @Test
    @DisplayName("Test getLabels(String)")
    void testGetLabels() throws Exception {
        // Arrange
        when(gmailIntegration.usersMailFolders(Mockito.<String>any())).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/gmailRestController/getLabels")
                .param("userEmail", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<List/>"));
    }

    /**
     * Test
     * {@link GmailRestController#getAvailableMeetingSlots(List, String, String, Integer)}.
     * <p>
     * Method under test:
     * {@link GmailRestController#getAvailableMeetingSlots(List, String, String, Integer)}
     */
    @Test
    @DisplayName("Test getAvailableMeetingSlots(List, String, String, Integer)")
    void testGetAvailableMeetingSlots() throws Exception {
        // Arrange
        MockHttpServletRequestBuilder paramResult = MockMvcRequestBuilders
                .get("/gmailRestController/getAvailableMeetingSlots")
                .param("endDateTime", "foo");
        MockHttpServletRequestBuilder requestBuilder = paramResult.param("slotDuration", String.valueOf(1))
                .param("startDateTime", "foo");

        // Act
        ResultActions actualPerformResult = MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder);

        // Assert
        actualPerformResult.andExpect(MockMvcResultMatchers.status().is(400));
    }

    /**
     * Test
     * {@link GmailRestController#getAllScheduledMeetings(String, String, String)}.
     * <p>
     * Method under test:
     * {@link GmailRestController#getAllScheduledMeetings(String, String, String)}
     */
    @Test
    @DisplayName("Test getAllScheduledMeetings(String, String, String)")
    void testGetAllScheduledMeetings() throws Exception {
        // Arrange
        when(gmailIntegration.getAllScheduledMeetings(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/gmailRestController/getAllScheduledMeetings")
                .param("endDateTime", "foo")
                .param("startDateTime", "foo")
                .param("userEmails", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<List/>"));
    }

    /**
     * Test {@link GmailRestController#consumeEmailMessages()}.
     * <p>
     * Method under test: {@link GmailRestController#consumeEmailMessages()}
     */
    @Test
    @DisplayName("Test consumeEmailMessages()")
    void testConsumeEmailMessages() throws Exception {
        // Arrange
        doNothing().when(kafkaConsumer).consumeEmailMessages();
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/gmailRestController/consumeEmailMessages");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("text/plain;charset=ISO-8859-1"))
                .andExpect(MockMvcResultMatchers.content().string("yes"));
    }

    /**
     * Test {@link GmailRestController#getVacationResponderSettings(String)}.
     * <p>
     * Method under test:
     * {@link GmailRestController#getVacationResponderSettings(String)}
     */
    @Test
    @DisplayName("Test getVacationResponderSettings(String)")
    void testGetVacationResponderSettings() throws Exception {
        // Arrange
        when(gmailIntegration.getVacationResponderSettings(Mockito.<String>any())).thenReturn(new VacationSettings());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/gmailRestController/getVacationResponderSettings")
                .param("userEmails", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<VacationSettings/>"));
    }

    /**
     * Test
     * {@link GmailRestController#setVacationResponder(String, VacationSettings)}.
     * <p>
     * Method under test:
     * {@link GmailRestController#setVacationResponder(String, VacationSettings)}
     */
    @Test
    @DisplayName("Test setVacationResponder(String, VacationSettings)")
    void testSetVacationResponder() throws Exception {
        // Arrange
        VacationSettings vacationSettings = new VacationSettings();
        vacationSettings.put("Field Name", "Value");
        vacationSettings.set("Field Name", "Value");
        vacationSettings.setEndTime(1L);
        vacationSettings.setFactory(GsonFactory.getDefaultInstance());
        vacationSettings.setResponseBodyHtml("Not all who wander are lost");
        vacationSettings.setResponseBodyPlainText("Not all who wander are lost");
        vacationSettings.setResponseSubject("Hello from the Dreaming Spires");
        vacationSettings.setRestrictToContacts(true);
        vacationSettings.setRestrictToDomain(true);
        vacationSettings.setStartTime(1L);
        vacationSettings.setUnknownKeys(new HashMap<>());
        String content = (new ObjectMapper()).writeValueAsString(vacationSettings);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/gmailRestController/setVacationResponder")
                .param("userEmails", "foo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        ResultActions actualPerformResult = MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder);

        // Assert
        actualPerformResult.andExpect(MockMvcResultMatchers.status().is(400));
    }

    /**
     * Test {@link GmailRestController#createMeetingEvent(String, Map)}.
     * <p>
     * Method under test:
     * {@link GmailRestController#createMeetingEvent(String, Map)}
     */
    @Test
    @DisplayName("Test createMeetingEvent(String, Map)")
    void testCreateMeetingEvent() throws Exception {
        // Arrange
        when(gmailIntegration.createMeetingEvent(Mockito.<String>any(), Mockito.<Map<String, Object>>any()))
                .thenReturn("Create Meeting Event");
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/gmailRestController/createMeetingEvent")
                .param("userId", "foo")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    /**
     * Test {@link GmailRestController#handleNotification(String, String)}.
     * <p>
     * Method under test:
     * {@link GmailRestController#handleNotification(String, String)}
     */
    @Test
    @DisplayName("Test handleNotification(String, String)")
    void testHandleNotification() throws Exception {
        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders.post("/gmailRestController/eventcomes")
                .contentType(MediaType.APPLICATION_JSON);
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content((new ObjectMapper()).writeValueAsString("foo"));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    /**
     * Test {@link GmailRestController#getMessageDetails(String)}.
     * <p>
     * Method under test: {@link GmailRestController#getMessageDetails(String)}
     */
    @Test
    @DisplayName("Test getMessageDetails(String)")
    void testGetMessageDetails() throws Exception {
        // Arrange
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setAttachmentPathList(new ArrayList<>());
        messageWrapper.setBccRecipients(new ArrayList<>());
        messageWrapper.setCcRecipients(new ArrayList<>());
        messageWrapper.setContent("Not all who wander are lost");
        messageWrapper.setConvercationId("42");
        messageWrapper.setEmail("<EMAIL>");
        messageWrapper.setFrom("<EMAIL>");
        messageWrapper.setHashAttachment(true);
        messageWrapper.setInternetMessageId("42");
        messageWrapper.setIsDraft(true);
        messageWrapper.setIsRead(true);
        messageWrapper.setMessageId("42");
        messageWrapper.setReplyBody("Not all who wander are lost");
        messageWrapper.setSubject("Hello from the Dreaming Spires");
        messageWrapper.setToRecipients(new ArrayList<>());
        messageWrapper.setUserId("42");
        messageWrapper.setUserName("janedoe");
        when(gmailIntegration.getMessageWrapperById(Mockito.<String>any())).thenReturn(messageWrapper);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/gmailRestController/getMessageDetails")
                .param("messageId", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(gmailRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content()
                        .string("<MessageWrapper><userName>janedoe</userName><userId>42</userId><email><EMAIL></email>"
                                + "<from><EMAIL></from><subject>Hello from the Dreaming Spires</subject><content>Not all who"
                                + " wander are lost</content><replyBody>Not all who wander are lost</replyBody><isDraft>true</isDraft>"
                                + "<isRead>true</isRead><hashAttachment>true</hashAttachment><messageId>42</messageId><convercationId>42"
                                + "</convercationId><internetMessageId>42</internetMessageId><toRecipients/><ccRecipients/><bccRecipients"
                                + "/><attachmentPathList/></MessageWrapper>"));
    }
}
