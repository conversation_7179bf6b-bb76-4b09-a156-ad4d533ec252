package com.enttribe.emailagent.rest;

import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.dto.EmailUserDto;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.service.EmailUserService;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.ByteArrayInputStream;
import java.io.IOException;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Disabled;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.ContentResultMatchers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.multipart.MultipartFile;

@ContextConfiguration(classes = {EmailUserRestController.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class EmailUserRestControllerTest {
    @Autowired
    private EmailUserRestController emailUserRestController;

    @MockBean
    private EmailUserService emailUserService;

    /**
     * Test {@link EmailUserRestController#getEmailUserById(Integer)}.
     * <p>
     * Method under test: {@link EmailUserRestController#getEmailUserById(Integer)}
     */
    @Test
    @DisplayName("Test getEmailUserById(Integer)")
    void testGetEmailUserById() throws Exception {
        // Arrange
        EmailUser emailUser = new EmailUser();
        emailUser.setBatchId("42");
        emailUser.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setDeleted(true);
        emailUser.setEmail("<EMAIL>");
        emailUser.setId(1);
        emailUser.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setName("Name");
        emailUser.setPhoneNumber("6625550144");
        emailUser.setQueueName("Queue Name");
        emailUser.setType("Type");
        emailUser.setUserId("42");
        emailUser.setUserOID("User OID");
        when(emailUserService.findById(Mockito.<Integer>any())).thenReturn(emailUser);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailUserRestController/{id}", 1);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"createdTime\":0,\"modifiedTime\":0,\"id\":1,\"userId\":\"42\",\"batchId\":\"42\",\"email\":\"<EMAIL>\""
                                        + ",\"phoneNumber\":\"6625550144\",\"type\":\"Type\",\"deleted\":true,\"name\":\"Name\",\"userOID\":\"User OID\",\"queueName"
                                        + "\":\"Queue Name\"}"));
    }

    /**
     * Test {@link EmailUserRestController#getAllEmailUsers()}.
     * <p>
     * Method under test: {@link EmailUserRestController#getAllEmailUsers()}
     */
    @Test
    @DisplayName("Test getAllEmailUsers()")
    void testGetAllEmailUsers() throws Exception {
        // Arrange
        when(emailUserService.findAll()).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailUserRestController");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link EmailUserRestController#createUsersFromCsv(MultipartFile)}.
     * <p>
     * Method under test:
     * {@link EmailUserRestController#createUsersFromCsv(MultipartFile)}
     */
    @Test
    @DisplayName("Test createUsersFromCsv(MultipartFile)")
    @Disabled("TODO: Complete this test")
    void testCreateUsersFromCsv() throws IOException {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   jakarta.servlet.ServletException: Request processing failed: org.springframework.web.multipart.MultipartException: Current request is not a multipart request
        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
        //   org.springframework.web.multipart.MultipartException: Current request is not a multipart request
        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
        //   See https://diff.blue/R013 to resolve this issue.

        // Arrange
        EmailUserRestController emailUserRestController = new EmailUserRestController();

        // Act
        emailUserRestController
                .createUsersFromCsv(new MockMultipartFile("Name", new ByteArrayInputStream("AXAXAXAX".getBytes("UTF-8"))));
    }

    /**
     * Test {@link EmailUserRestController#updateStatus(Integer, boolean)}.
     * <p>
     * Method under test:
     * {@link EmailUserRestController#updateStatus(Integer, boolean)}
     */
    @Test
    @DisplayName("Test updateStatus(Integer, boolean)")
    void testUpdateStatus() throws Exception {
        // Arrange
        EmailUser emailUser = new EmailUser();
        emailUser.setBatchId("42");
        emailUser.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setDeleted(true);
        emailUser.setEmail("<EMAIL>");
        emailUser.setId(1);
        emailUser.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setName("Name");
        emailUser.setPhoneNumber("6625550144");
        emailUser.setQueueName("Queue Name");
        emailUser.setType("Type");
        emailUser.setUserId("42");
        emailUser.setUserOID("User OID");
        when(emailUserService.updateStatus(Mockito.<Integer>any(), anyBoolean())).thenReturn(emailUser);
        MockHttpServletRequestBuilder postResult = MockMvcRequestBuilders.post("/emailUserRestController/updateStatus/{id}",
                1);
        MockHttpServletRequestBuilder requestBuilder = postResult.param("deleted", String.valueOf(true));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"createdTime\":0,\"modifiedTime\":0,\"id\":1,\"userId\":\"42\",\"batchId\":\"42\",\"email\":\"<EMAIL>\""
                                        + ",\"phoneNumber\":\"6625550144\",\"type\":\"Type\",\"deleted\":true,\"name\":\"Name\",\"userOID\":\"User OID\",\"queueName"
                                        + "\":\"Queue Name\"}"));
    }

    /**
     * Test {@link EmailUserRestController#updateEmailUser(Integer, EmailUserDto)}.
     * <p>
     * Method under test:
     * {@link EmailUserRestController#updateEmailUser(Integer, EmailUserDto)}
     */
    @Test
    @DisplayName("Test updateEmailUser(Integer, EmailUserDto)")
    void testUpdateEmailUser() throws Exception {
        // Arrange
        EmailUserDto emailUserDto = new EmailUserDto();
        emailUserDto.setBatchId("42");
        emailUserDto.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUserDto.setDeleted(true);
        emailUserDto.setEmail("<EMAIL>");
        emailUserDto.setId(1);
        emailUserDto.setLastPollTime(1L);
        emailUserDto.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUserDto.setName("Name");
        emailUserDto.setNextPollTime(1L);
        emailUserDto.setPhoneNumber("6625550144");
        emailUserDto.setType("Type");
        emailUserDto.setUserId("42");
        when(emailUserService.update(Mockito.<Integer>any(), Mockito.<EmailUserDto>any())).thenReturn(emailUserDto);

        EmailUserDto emailUserDto2 = new EmailUserDto();
        emailUserDto2.setBatchId("42");
        emailUserDto2.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUserDto2.setDeleted(true);
        emailUserDto2.setEmail("<EMAIL>");
        emailUserDto2.setId(1);
        emailUserDto2.setLastPollTime(1L);
        emailUserDto2
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUserDto2.setName("Name");
        emailUserDto2.setNextPollTime(1L);
        emailUserDto2.setPhoneNumber("6625550144");
        emailUserDto2.setType("Type");
        emailUserDto2.setUserId("42");
        String content = (new ObjectMapper()).writeValueAsString(emailUserDto2);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/emailUserRestController/updateEmailUser/{id}", 1)
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"id\":1,\"userId\":\"42\",\"batchId\":\"42\",\"email\":\"<EMAIL>\",\"phoneNumber\":\"6625550144\","
                                        + "\"type\":\"Type\",\"deleted\":true,\"name\":\"Name\",\"lastPollTime\":1,\"nextPollTime\":1,\"createdTime\":0,"
                                        + "\"modifiedTime\":0}"));
    }

    /**
     * Test {@link EmailUserRestController#getDistinctBatchId(String)}.
     * <p>
     * Method under test: {@link EmailUserRestController#getDistinctBatchId(String)}
     */
    @Test
    @DisplayName("Test getDistinctBatchId(String)")
    void testGetDistinctBatchId() throws Exception {
        // Arrange
        when(emailUserService.getDistinctBatchId(Mockito.<String>any())).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/emailUserRestController/getDistinctBatchId");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link EmailUserRestController#getBatchId()}.
     * <p>
     * Method under test: {@link EmailUserRestController#getBatchId()}
     */
    @Test
    @DisplayName("Test getBatchId()")
    void testGetBatchId() throws Exception {
        // Arrange
        when(emailUserService.getBatchId()).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailUserRestController/getBatchId");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test
     * {@link EmailUserRestController#search(Integer, String, String, String, Boolean, String, int, int)}.
     * <p>
     * Method under test:
     * {@link EmailUserRestController#search(Integer, String, String, String, Boolean, String, int, int)}
     */
    @Test
    @DisplayName("Test search(Integer, String, String, String, Boolean, String, int, int)")
    void testSearch() throws Exception {
        // Arrange
        when(emailUserService.search(Mockito.<Integer>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<Boolean>any(), Mockito.<String>any(), anyInt(), anyInt()))
                .thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder getResult = MockMvcRequestBuilders.get("/emailUserRestController/search");
        MockHttpServletRequestBuilder paramResult = getResult.param("lLimit", String.valueOf(1));
        MockHttpServletRequestBuilder requestBuilder = paramResult.param("uLimit", String.valueOf(1));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link EmailUserRestController#deleteEmailUser(Integer)}.
     * <p>
     * Method under test: {@link EmailUserRestController#deleteEmailUser(Integer)}
     */
    @Test
    @DisplayName("Test deleteEmailUser(Integer)")
    void testDeleteEmailUser() throws Exception {
        // Arrange
        when(emailUserService.deleteById(Mockito.<Integer>any())).thenReturn(true);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailUserRestController/delete/{id}",
                1);

        // Act and Assert
        ResultActions resultActions = MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"));
        ContentResultMatchers contentResult = MockMvcResultMatchers.content();
        resultActions.andExpect(contentResult.string(Boolean.TRUE.toString()));
    }

    /**
     * Test {@link EmailUserRestController#createEmailUser(EmailUser)}.
     * <p>
     * Method under test: {@link EmailUserRestController#createEmailUser(EmailUser)}
     */
    @Test
    @DisplayName("Test createEmailUser(EmailUser)")
    void testCreateEmailUser() throws Exception {
        // Arrange
        EmailUser emailUser = new EmailUser();
        emailUser.setBatchId("42");
        emailUser.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setDeleted(true);
        emailUser.setEmail("<EMAIL>");
        emailUser.setId(1);
        emailUser.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setName("Name");
        emailUser.setPhoneNumber("6625550144");
        emailUser.setQueueName("Queue Name");
        emailUser.setType("Type");
        emailUser.setUserId("42");
        emailUser.setUserOID("User OID");
        when(emailUserService.save(Mockito.<EmailUser>any())).thenReturn(emailUser);

        EmailUser emailUser2 = new EmailUser();
        emailUser2.setBatchId("42");
        emailUser2.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser2.setDeleted(true);
        emailUser2.setEmail("<EMAIL>");
        emailUser2.setId(1);
        emailUser2.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser2.setName("Name");
        emailUser2.setPhoneNumber("6625550144");
        emailUser2.setQueueName("Queue Name");
        emailUser2.setType("Type");
        emailUser2.setUserId("42");
        emailUser2.setUserOID("User OID");
        String content = (new ObjectMapper()).writeValueAsString(emailUser2);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/emailUserRestController/createEmailUser")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"createdTime\":0,\"modifiedTime\":0,\"id\":1,\"userId\":\"42\",\"batchId\":\"42\",\"email\":\"<EMAIL>\""
                                        + ",\"phoneNumber\":\"6625550144\",\"type\":\"Type\",\"deleted\":true,\"name\":\"Name\",\"userOID\":\"User OID\",\"queueName"
                                        + "\":\"Queue Name\"}"));
    }

    /**
     * Test
     * {@link EmailUserRestController#count(Integer, String, String, String, Boolean, String, int, int)}.
     * <p>
     * Method under test:
     * {@link EmailUserRestController#count(Integer, String, String, String, Boolean, String, int, int)}
     */
    @Test
    @DisplayName("Test count(Integer, String, String, String, Boolean, String, int, int)")
    void testCount() throws Exception {
        // Arrange
        when(emailUserService.count(Mockito.<Integer>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<Boolean>any(), Mockito.<String>any(), anyInt(), anyInt())).thenReturn(3L);
        MockHttpServletRequestBuilder getResult = MockMvcRequestBuilders.get("/emailUserRestController/count");
        MockHttpServletRequestBuilder paramResult = getResult.param("lLimit", String.valueOf(1));
        MockHttpServletRequestBuilder requestBuilder = paramResult.param("uLimit", String.valueOf(1));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("3"));
    }

    /**
     * Test
     * {@link EmailUserRestController#emailUserByFilter(Integer, Integer, Map)}.
     * <p>
     * Method under test:
     * {@link EmailUserRestController#emailUserByFilter(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test emailUserByFilter(Integer, Integer, Map)")
    void testEmailUserByFilter() throws Exception {
        // Arrange
        when(emailUserService.emailUserByFilter(Mockito.<Integer>any(), Mockito.<Integer>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/emailUserRestController/emailUserByFilter")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link EmailUserRestController#emailUserCountByFilter(Map)}.
     * <p>
     * Method under test:
     * {@link EmailUserRestController#emailUserCountByFilter(Map)}
     */
    @Test
    @DisplayName("Test emailUserCountByFilter(Map)")
    void testEmailUserCountByFilter() throws Exception {
        // Arrange
        when(emailUserService.emailUserCountByFilter(Mockito.<Map<String, Object>>any())).thenReturn(3L);
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/emailUserRestController/emailUserCountByFilter")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("3"));
    }

    /**
     * Test {@link EmailUserRestController#saveContacts()}.
     * <p>
     * Method under test: {@link EmailUserRestController#saveContacts()}
     */
    @Test
    @DisplayName("Test saveContacts()")
    void testSaveContacts() throws Exception {
        // Arrange
        doNothing().when(emailUserService).saveContactsForUsers();
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/emailUserRestController/saveContactUsers");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(emailUserRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("Contacts saved successfully!"));
    }
}
