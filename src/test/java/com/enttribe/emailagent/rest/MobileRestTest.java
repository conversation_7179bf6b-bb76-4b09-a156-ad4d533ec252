package com.enttribe.emailagent.rest;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.service.EwsService;
import com.enttribe.emailagent.service.GraphIntegrationService;
import com.enttribe.emailagent.service.PollTimeInfoService;
import com.enttribe.emailagent.service.UserMailAttachmentService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.S3Service;
import com.enttribe.emailagent.utils.TokenUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.multipart.MultipartFile;

@ContextConfiguration(classes = {MobileRest.class, EwsService.class, GraphIntegrationService.class, S3Service.class,
        UserContextHolder.class, UserMailAttachmentService.class, PollTimeInfoService.class, TokenUtils.class,
        GmailIntegration.class, IMailSummaryDao.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class MobileRestTest {
    @MockBean
    private GmailIntegration gmailIntegration;

    @MockBean
    private IMailSummaryDao iMailSummaryDao;

    @Autowired
    private MobileRest mobileRest;

    /**
     * Test {@link MobileRest#createDraft(Map)}.
     * <p>
     * Method under test: {@link MobileRest#createDraft(Map)}
     */
    @Test
    @DisplayName("Test createDraft(Map)")
    @Disabled("TODO: Complete this test")
    void testCreateDraft() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Missing Spring properties.
        //   Failed to create Spring context due to unresolvable @Value
        //   properties: field 'refreshInterval'
        //   Please check that at least one of the property files is provided
        //   and contains required variables:
        //   - application-test.properties (file missing)
        //   See https://diff.blue/R033 to resolve this issue.

        // Arrange
        // TODO: Populate arranged inputs
        Object[] uriVariables = new Object[]{};
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders.post("/mobile/createDraft", uriVariables)
                .contentType(MediaType.APPLICATION_JSON);

        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("subject", "foo");
        stringStringMap.put("content", "foo");
        stringStringMap.put("attachmentList", "foo");
        stringStringMap.put("sendDraft", "foo");
        stringStringMap.put("toEmail", "foo");
        stringStringMap.put("ccEmail", null);
        stringStringMap.put("bccEmail", null);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(stringStringMap));
        Object[] controllers = new Object[]{mobileRest};
        MockMvc buildResult = MockMvcBuilders.standaloneSetup(controllers).build();

        // Act
        ResultActions actualPerformResult = buildResult.perform(requestBuilder);

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Test {@link MobileRest#uploadAttachmentForMail(MultipartFile)}.
     * <p>
     * Method under test: {@link MobileRest#uploadAttachmentForMail(MultipartFile)}
     */
    @Test
    @DisplayName("Test uploadAttachmentForMail(MultipartFile)")
    @Disabled("TODO: Complete this test")
    void testUploadAttachmentForMail() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@7b27daca testClass = com.enttribe.emailagent.rest.DiffblueFakeClass42, locations = [], classes = [com.enttribe.emailagent.rest.MobileRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.repository.IMailSummaryDao, com.enttribe.emailagent.utils.S3Service, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@2ead7c1e, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@697e8e72, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@c0684a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder postResult = MockMvcRequestBuilders.post("/mobile/V1/upload/stream");
        MockHttpServletRequestBuilder requestBuilder = postResult.param("fileInputStream",
                String.valueOf(new MockMultipartFile("Name", (InputStream) null)));

        // Act
        MockMvcBuilders.standaloneSetup(mobileRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link MobileRest#uploadAttachmentForMail(MultipartFile)}.
     * <ul>
     *   <li>When {@code A}.</li>
     * </ul>
     * <p>
     * Method under test: {@link MobileRest#uploadAttachmentForMail(MultipartFile)}
     */
    @Test
    @DisplayName("Test uploadAttachmentForMail(MultipartFile); when 'A'")
    void testUploadAttachmentForMail_whenA() throws IOException {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        MobileRest mobileRest = new MobileRest();

        // Act
        Map<String, String> actualUploadAttachmentForMailResult = mobileRest
                .uploadAttachmentForMail(new MockMultipartFile("File name cannot be empty", "foo.txt", "text/plain",
                        new ByteArrayInputStream(new byte[]{'A', 1, 'A', 1, 'A', 1, 'A', 1})));

        // Assert
        assertEquals(1, actualUploadAttachmentForMailResult.size());
        assertEquals("FAILURE", actualUploadAttachmentForMailResult.get("Response"));
    }

    /**
     * Test {@link MobileRest#uploadAttachmentForMail(MultipartFile)}.
     * <ul>
     *   <li>When {@link ByteArrayInputStream#ByteArrayInputStream(byte[])} with
     * {@code AXAXAXAX} Bytes is {@code UTF-8}.</li>
     * </ul>
     * <p>
     * Method under test: {@link MobileRest#uploadAttachmentForMail(MultipartFile)}
     */
    @Test
    @DisplayName("Test uploadAttachmentForMail(MultipartFile); when ByteArrayInputStream(byte[]) with 'AXAXAXAX' Bytes is 'UTF-8'")
    void testUploadAttachmentForMail_whenByteArrayInputStreamWithAxaxaxaxBytesIsUtf8() throws IOException {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        MobileRest mobileRest = new MobileRest();

        // Act
        Map<String, String> actualUploadAttachmentForMailResult = mobileRest
                .uploadAttachmentForMail(new MockMultipartFile("Name", new ByteArrayInputStream("AXAXAXAX".getBytes("UTF-8"))));

        // Assert
        assertEquals(1, actualUploadAttachmentForMailResult.size());
        assertEquals("FAILURE", actualUploadAttachmentForMailResult.get("Response"));
    }

    /**
     * Test {@link MobileRest#uploadAttachmentForMail(MultipartFile)}.
     * <ul>
     *   <li>When {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link MobileRest#uploadAttachmentForMail(MultipartFile)}
     */
    @Test
    @DisplayName("Test uploadAttachmentForMail(MultipartFile); when 'null'")
    void testUploadAttachmentForMail_whenNull() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange and Act
        Map<String, String> actualUploadAttachmentForMailResult = (new MobileRest()).uploadAttachmentForMail(null);

        // Assert
        assertEquals(1, actualUploadAttachmentForMailResult.size());
        assertEquals("FAILURE", actualUploadAttachmentForMailResult.get("Response"));
    }

    /**
     * Test {@link MobileRest#setAutoReply(Map)}.
     * <p>
     * Method under test: {@link MobileRest#setAutoReply(Map)}
     */
    @Test
    @DisplayName("Test setAutoReply(Map)")
    @Disabled("TODO: Complete this test")
    void testSetAutoReply() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@48d31134 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass39, locations = [], classes = [com.enttribe.emailagent.rest.MobileRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.repository.IMailSummaryDao, com.enttribe.emailagent.utils.S3Service, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@2ead7c1e, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@697e8e72, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@c0684a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("internalReplyMessage", "foo");
        stringStringMap.put("externalReplyMessage", "foo");
        stringStringMap.put("scheduledStartDateTime", null);
        stringStringMap.put("scheduledEndDateTime", null);
        stringStringMap.put("timeZone", null);
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/mobile/setAutoReply")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(mobileRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link MobileRest#getMessageDetails(String)}.
     * <p>
     * Method under test: {@link MobileRest#getMessageDetails(String)}
     */
    @Test
    @DisplayName("Test getMessageDetails(String)")
    @Disabled("TODO: Complete this test")
    void testGetMessageDetails() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@5c8ce2fd testClass = com.enttribe.emailagent.rest.DiffblueFakeClass36, locations = [], classes = [com.enttribe.emailagent.rest.MobileRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.repository.IMailSummaryDao, com.enttribe.emailagent.utils.S3Service, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@2ead7c1e, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@697e8e72, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@c0684a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/mobile/getMessageDetails")
                .param("integnetMessageId", "foo");

        // Act
        MockMvcBuilders.standaloneSetup(mobileRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link MobileRest#forwardMail(Map)}.
     * <p>
     * Method under test: {@link MobileRest#forwardMail(Map)}
     */
    @Test
    @DisplayName("Test forwardMail(Map)")
    @Disabled("TODO: Complete this test")
    void testForwardMail() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@1ea33e94 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass33, locations = [], classes = [com.enttribe.emailagent.rest.MobileRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.repository.IMailSummaryDao, com.enttribe.emailagent.utils.S3Service, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@2ead7c1e, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@697e8e72, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@c0684a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("messageId", "foo");
        stringStringMap.put("content", null);
        stringStringMap.put("toEmail", null);
        stringStringMap.put("ccEmail", null);
        stringStringMap.put("bccEmail", null);
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/mobile/forwardEmail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(mobileRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link MobileRest#flagEmailBot(Map)}.
     * <p>
     * Method under test: {@link MobileRest#flagEmailBot(Map)}
     */
    @Test
    @DisplayName("Test flagEmailBot(Map)")
    @Disabled("TODO: Complete this test")
    void testFlagEmailBot() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@6060ebd7 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass30, locations = [], classes = [com.enttribe.emailagent.rest.MobileRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.repository.IMailSummaryDao, com.enttribe.emailagent.utils.S3Service, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@2ead7c1e, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@697e8e72, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@c0684a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("messageId", "foo");
        stringStringMap.put("flagStatus", "flagged");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/mobile/v2/flagEmail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(mobileRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link MobileRest#downloadFileAsStreamByDocId(String)}.
     * <p>
     * Method under test: {@link MobileRest#downloadFileAsStreamByDocId(String)}
     */
    @Test
    @DisplayName("Test downloadFileAsStreamByDocId(String)")
    @Disabled("TODO: Complete this test")
    void testDownloadFileAsStreamByDocId() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@51c7379a testClass = com.enttribe.emailagent.rest.DiffblueFakeClass27, locations = [], classes = [com.enttribe.emailagent.rest.MobileRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.repository.IMailSummaryDao, com.enttribe.emailagent.utils.S3Service, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@2ead7c1e, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@697e8e72, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@c0684a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/mobile/V1/download/stream")
                .param("docId", "foo");

        // Act
        MockMvcBuilders.standaloneSetup(mobileRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link MobileRest#downloadFileAsStream(String, String)}.
     * <p>
     * Method under test: {@link MobileRest#downloadFileAsStream(String, String)}
     */
    @Test
    @DisplayName("Test downloadFileAsStream(String, String)")
    @Disabled("TODO: Complete this test")
    void testDownloadFileAsStream() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@4aaf7928 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass24, locations = [], classes = [com.enttribe.emailagent.rest.MobileRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.repository.IMailSummaryDao, com.enttribe.emailagent.utils.S3Service, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@2ead7c1e, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@697e8e72, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@c0684a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/mobile/download/stream")
                .param("bucketName", "foo")
                .param("key", "foo");

        // Act
        MockMvcBuilders.standaloneSetup(mobileRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link MobileRest#deleteMailById(String, Boolean)}.
     * <p>
     * Method under test: {@link MobileRest#deleteMailById(String, Boolean)}
     */
    @Test
    @DisplayName("Test deleteMailById(String, Boolean)")
    @Disabled("TODO: Complete this test")
    void testDeleteMailById() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@455df234 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass21, locations = [], classes = [com.enttribe.emailagent.rest.MobileRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.repository.IMailSummaryDao, com.enttribe.emailagent.utils.S3Service, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@2ead7c1e, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@697e8e72, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@c0684a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder paramResult = MockMvcRequestBuilders.get("/mobile/deleteMailById")
                .param("internetMessageId", "foo");
        MockHttpServletRequestBuilder requestBuilder = paramResult.param("softDelete", String.valueOf(true));

        // Act
        MockMvcBuilders.standaloneSetup(mobileRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link MobileRest#createDraftReply(Map)}.
     * <p>
     * Method under test: {@link MobileRest#createDraftReply(Map)}
     */
    @Test
    @DisplayName("Test createDraftReply(Map)")
    @Disabled("TODO: Complete this test")
    void testCreateDraftReply() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@53901f94 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass18, locations = [], classes = [com.enttribe.emailagent.rest.MobileRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.repository.IMailSummaryDao, com.enttribe.emailagent.utils.S3Service, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@2ead7c1e, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@697e8e72, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@c0684a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("messageId", "foo");
        stringStringMap.put("content", null);
        stringStringMap.put("sendDraft", "foo");
        stringStringMap.put("isReplyAll", "foo");
        stringStringMap.put("attachmentList", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/mobile/createDraftReply")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(mobileRest).build().perform(requestBuilder);
    }
}
