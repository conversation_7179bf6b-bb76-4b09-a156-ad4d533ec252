package com.enttribe.emailagent.rest;

import static org.mockito.Mockito.when;

import com.enttribe.emailagent.entity.FailureLogs;
import com.enttribe.emailagent.service.FailureLogsService;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.ContentResultMatchers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {FailureLogsRestController.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class FailureLogsRestControllerTest {
    @Autowired
    private FailureLogsRestController failureLogsRestController;

    @MockBean
    private FailureLogsService failureLogsService;

    /**
     * Test {@link FailureLogsRestController#getFailureLogById(Integer)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#getFailureLogById(Integer)}
     */
    @Test
    @DisplayName("Test getFailureLogById(Integer)")
    void testGetFailureLogById() throws Exception {
        // Arrange
        FailureLogs failureLogs = new FailureLogs();
        failureLogs.setAttachment(true);
        failureLogs.setAttendees("Attendees");
        failureLogs.setConversationId("42");
        failureLogs.setCustomExceptionMessage("An error occurred");
        failureLogs.setEmail("<EMAIL>");
        failureLogs.setEmailSubject("<EMAIL>");
        failureLogs.setErrorDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        failureLogs.setExceptionMessage("An error occurred");
        failureLogs.setExceptionTrace("Exception Trace");
        failureLogs.setId(1);
        failureLogs.setIntent("Intent");
        failureLogs.setInternetMessageId("42");
        failureLogs.setMessageId("42");
        failureLogs.setStatus("Status");
        failureLogs.setType("Type");
        failureLogs.setUserMailAttachment("User Mail Attachment");
        when(failureLogsService.findById(Mockito.<Integer>any())).thenReturn(failureLogs);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/failureLogsRestController/failureLogsById/{id}", 1);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"id\":1,\"internetMessageId\":\"42\",\"email\":\"<EMAIL>\",\"emailSubject\":\"<EMAIL>"
                                        + "\",\"messageId\":\"42\",\"conversationId\":\"42\",\"type\":\"Type\",\"exceptionMessage\":\"An error occurred\","
                                        + "\"customExceptionMessage\":\"An error occurred\",\"exceptionTrace\":\"Exception Trace\",\"attachment\":true,"
                                        + "\"userMailAttachment\":\"User Mail Attachment\",\"intent\":\"Intent\",\"attendees\":\"Attendees\",\"errorDate\":0,"
                                        + "\"status\":\"Status\"}"));
    }

    /**
     * Test {@link FailureLogsRestController#getFailureLogByEmail(String)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#getFailureLogByEmail(String)}
     */
    @Test
    @DisplayName("Test getFailureLogByEmail(String)")
    void testGetFailureLogByEmail() throws Exception {
        // Arrange
        when(failureLogsService.getFailureLogByEmail(Mockito.<String>any())).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/failureLogsRestController/failureLogsByEmail/{email}", "<EMAIL>");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test
     * {@link FailureLogsRestController#updateFailureLog(Integer, FailureLogs)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#updateFailureLog(Integer, FailureLogs)}
     */
    @Test
    @DisplayName("Test updateFailureLog(Integer, FailureLogs)")
    void testUpdateFailureLog() throws Exception {
        // Arrange
        FailureLogs failureLogs = new FailureLogs();
        failureLogs.setAttachment(true);
        failureLogs.setAttendees("Attendees");
        failureLogs.setConversationId("42");
        failureLogs.setCustomExceptionMessage("An error occurred");
        failureLogs.setEmail("<EMAIL>");
        failureLogs.setEmailSubject("<EMAIL>");
        failureLogs.setErrorDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        failureLogs.setExceptionMessage("An error occurred");
        failureLogs.setExceptionTrace("Exception Trace");
        failureLogs.setId(1);
        failureLogs.setIntent("Intent");
        failureLogs.setInternetMessageId("42");
        failureLogs.setMessageId("42");
        failureLogs.setStatus("Status");
        failureLogs.setType("Type");
        failureLogs.setUserMailAttachment("User Mail Attachment");
        when(failureLogsService.update(Mockito.<Integer>any(), Mockito.<FailureLogs>any())).thenReturn(failureLogs);

        FailureLogs failureLogs2 = new FailureLogs();
        failureLogs2.setAttachment(true);
        failureLogs2.setAttendees("Attendees");
        failureLogs2.setConversationId("42");
        failureLogs2.setCustomExceptionMessage("An error occurred");
        failureLogs2.setEmail("<EMAIL>");
        failureLogs2.setEmailSubject("<EMAIL>");
        failureLogs2.setErrorDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        failureLogs2.setExceptionMessage("An error occurred");
        failureLogs2.setExceptionTrace("Exception Trace");
        failureLogs2.setId(1);
        failureLogs2.setIntent("Intent");
        failureLogs2.setInternetMessageId("42");
        failureLogs2.setMessageId("42");
        failureLogs2.setStatus("Status");
        failureLogs2.setType("Type");
        failureLogs2.setUserMailAttachment("User Mail Attachment");
        String content = (new ObjectMapper()).writeValueAsString(failureLogs2);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .put("/failureLogsRestController/updateFailureLogs/{id}", 1)
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"id\":1,\"internetMessageId\":\"42\",\"email\":\"<EMAIL>\",\"emailSubject\":\"<EMAIL>"
                                        + "\",\"messageId\":\"42\",\"conversationId\":\"42\",\"type\":\"Type\",\"exceptionMessage\":\"An error occurred\","
                                        + "\"customExceptionMessage\":\"An error occurred\",\"exceptionTrace\":\"Exception Trace\",\"attachment\":true,"
                                        + "\"userMailAttachment\":\"User Mail Attachment\",\"intent\":\"Intent\",\"attendees\":\"Attendees\",\"errorDate\":0,"
                                        + "\"status\":\"Status\"}"));
    }

    /**
     * Test {@link FailureLogsRestController#getAllFailureLogs()}.
     * <p>
     * Method under test: {@link FailureLogsRestController#getAllFailureLogs()}
     */
    @Test
    @DisplayName("Test getAllFailureLogs()")
    void testGetAllFailureLogs() throws Exception {
        // Arrange
        when(failureLogsService.findAll()).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/failureLogsRestController/getAllFailureLogs");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link FailureLogsRestController#deleteFailureLog(Integer)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#deleteFailureLog(Integer)}
     */
    @Test
    @DisplayName("Test deleteFailureLog(Integer)")
    void testDeleteFailureLog() throws Exception {
        // Arrange
        when(failureLogsService.deleteById(Mockito.<Integer>any())).thenReturn(true);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .delete("/failureLogsRestController/deletefailureLogById/{id}", 1);

        // Act and Assert
        ResultActions resultActions = MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"));
        ContentResultMatchers contentResult = MockMvcResultMatchers.content();
        resultActions.andExpect(contentResult.string(Boolean.TRUE.toString()));
    }

    /**
     * Test {@link FailureLogsRestController#createFailureLog(FailureLogs)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#createFailureLog(FailureLogs)}
     */
    @Test
    @DisplayName("Test createFailureLog(FailureLogs)")
    void testCreateFailureLog() throws Exception {
        // Arrange
        FailureLogs failureLogs = new FailureLogs();
        failureLogs.setAttachment(true);
        failureLogs.setAttendees("Attendees");
        failureLogs.setConversationId("42");
        failureLogs.setCustomExceptionMessage("An error occurred");
        failureLogs.setEmail("<EMAIL>");
        failureLogs.setEmailSubject("<EMAIL>");
        failureLogs.setErrorDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        failureLogs.setExceptionMessage("An error occurred");
        failureLogs.setExceptionTrace("Exception Trace");
        failureLogs.setId(1);
        failureLogs.setIntent("Intent");
        failureLogs.setInternetMessageId("42");
        failureLogs.setMessageId("42");
        failureLogs.setStatus("Status");
        failureLogs.setType("Type");
        failureLogs.setUserMailAttachment("User Mail Attachment");
        when(failureLogsService.save(Mockito.<FailureLogs>any())).thenReturn(failureLogs);

        FailureLogs failureLogs2 = new FailureLogs();
        failureLogs2.setAttachment(true);
        failureLogs2.setAttendees("Attendees");
        failureLogs2.setConversationId("42");
        failureLogs2.setCustomExceptionMessage("An error occurred");
        failureLogs2.setEmail("<EMAIL>");
        failureLogs2.setEmailSubject("<EMAIL>");
        failureLogs2.setErrorDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        failureLogs2.setExceptionMessage("An error occurred");
        failureLogs2.setExceptionTrace("Exception Trace");
        failureLogs2.setId(1);
        failureLogs2.setIntent("Intent");
        failureLogs2.setInternetMessageId("42");
        failureLogs2.setMessageId("42");
        failureLogs2.setStatus("Status");
        failureLogs2.setType("Type");
        failureLogs2.setUserMailAttachment("User Mail Attachment");
        String content = (new ObjectMapper()).writeValueAsString(failureLogs2);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/failureLogsRestController/failureLogs")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"id\":1,\"internetMessageId\":\"42\",\"email\":\"<EMAIL>\",\"emailSubject\":\"<EMAIL>"
                                        + "\",\"messageId\":\"42\",\"conversationId\":\"42\",\"type\":\"Type\",\"exceptionMessage\":\"An error occurred\","
                                        + "\"customExceptionMessage\":\"An error occurred\",\"exceptionTrace\":\"Exception Trace\",\"attachment\":true,"
                                        + "\"userMailAttachment\":\"User Mail Attachment\",\"intent\":\"Intent\",\"attendees\":\"Attendees\",\"errorDate\":0,"
                                        + "\"status\":\"Status\"}"));
    }

    /**
     * Test
     * {@link FailureLogsRestController#count(Integer, String, String, String, String, String, String, String, String, String, String)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#count(Integer, String, String, String, String, String, String, String, String, String, String)}
     */
    @Test
    @DisplayName("Test count(Integer, String, String, String, String, String, String, String, String, String, String)")
    void testCount() throws Exception {
        // Arrange
        when(failureLogsService.count(Mockito.<Integer>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any())).thenReturn(3L);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/failureLogsRestController/count");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("3"));
    }

    /**
     * Test {@link FailureLogsRestController#typesOfFailureLogs()}.
     * <p>
     * Method under test: {@link FailureLogsRestController#typesOfFailureLogs()}
     */
    @Test
    @DisplayName("Test typesOfFailureLogs()")
    void testTypesOfFailureLogs() throws Exception {
        // Arrange
        when(failureLogsService.typesOfFailureLogs()).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/failureLogsRestController/typesOfFailureLogs");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test
     * {@link FailureLogsRestController#failureLogByFilter(Integer, Integer, Map)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#failureLogByFilter(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test failureLogByFilter(Integer, Integer, Map)")
    void testFailureLogByFilter() throws Exception {
        // Arrange
        when(failureLogsService.failureLogByFilter(Mockito.<Integer>any(), Mockito.<Integer>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/failureLogsRestController/failureLogsByFilter")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link FailureLogsRestController#failureLogCountByFilter(Map)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#failureLogCountByFilter(Map)}
     */
    @Test
    @DisplayName("Test failureLogCountByFilter(Map)")
    void testFailureLogCountByFilter() throws Exception {
        // Arrange
        when(failureLogsService.failureLogCountByFilter(Mockito.<Map<String, Object>>any())).thenReturn(3L);
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/failureLogsRestController/failureLogsCountByFilter")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("3"));
    }

    /**
     * Test {@link FailureLogsRestController#updateStatus(Integer, String)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#updateStatus(Integer, String)}
     */
    @Test
    @DisplayName("Test updateStatus(Integer, String)")
    void testUpdateStatus() throws Exception {
        // Arrange
        when(failureLogsService.updateStatus(Mockito.<Integer>any(), Mockito.<String>any())).thenReturn("2020-03-01");
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/failureLogsRestController/updateStatus/{id}", 1)
                .param("status", "foo");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("2020-03-01"));
    }

    /**
     * Test
     * {@link FailureLogsRestController#mailSummaryByFilter(Integer, Integer, Map)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#mailSummaryByFilter(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test mailSummaryByFilter(Integer, Integer, Map)")
    void testMailSummaryByFilter() throws Exception {
        // Arrange
        when(failureLogsService.mailSummaryByFilter(Mockito.<Integer>any(), Mockito.<Integer>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/failureLogsRestController/mailSummaryByFilter")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link FailureLogsRestController#mailSummaryCountByFilter(Map)}.
     * <p>
     * Method under test:
     * {@link FailureLogsRestController#mailSummaryCountByFilter(Map)}
     */
    @Test
    @DisplayName("Test mailSummaryCountByFilter(Map)")
    void testMailSummaryCountByFilter() throws Exception {
        // Arrange
        when(failureLogsService.mailSummaryCountByFilter(Mockito.<Map<String, Object>>any())).thenReturn(3L);
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/failureLogsRestController/mailSummaryCountByFilter")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(failureLogsRestController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("3"));
    }
}
