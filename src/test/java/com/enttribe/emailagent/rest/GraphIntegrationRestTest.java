package com.enttribe.emailagent.rest;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.enttribe.emailagent.ai.polling.OutlookPollingAI;
import com.enttribe.emailagent.ai.service.AIService;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.FailureLogsServiceDao;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IEmailThreadDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.IOrganisationRepository;
import com.enttribe.emailagent.repository.UserActionsDao;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.service.DeploymentService;
import com.enttribe.emailagent.service.EwsService;
import com.enttribe.emailagent.service.GraphIntegrationService;
import com.enttribe.emailagent.service.ImailSummaryService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;

import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Map;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {GraphIntegrationRest.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class GraphIntegrationRestTest {
    @MockBean
    private AIService aIService;

    @MockBean
    private DeploymentService deploymentService;

    @MockBean
    private EmailUserDao emailUserDao;

    @MockBean
    private EwsService ewsService;

    @MockBean
    private FailureLogsServiceDao failureLogsServiceDao;

    @MockBean
    private GmailIntegration gmailIntegration;

    @Autowired
    private GraphIntegrationRest graphIntegrationRest;

    @MockBean
    private GraphIntegrationService graphIntegrationService;

    @MockBean
    private IEmailPreferencesDao iEmailPreferencesDao;

    @MockBean
    private IEmailThreadDao iEmailThreadDao;

    @MockBean
    private IMailSummaryDao iMailSummaryDao;

    @MockBean
    private IOrganisationRepository iOrganisationRepository;

    @MockBean
    private ImailSummaryService imailSummaryService;

    @MockBean
    private OutlookPollingAI outlookPollingAI;

    @MockBean
    private UserActionsDao userActionsDao;

    @MockBean
    private UserContextHolder userContextHolder;

    @MockBean
    private UserFoldersRepository userFoldersRepository;

    /**
     * Test {@link GraphIntegrationRest#setAutoReply(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#setAutoReply(Map)}
     */
    @Test
    @DisplayName("Test setAutoReply(Map)")
    @Disabled("TODO: Complete this test")
    void testSetAutoReply() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@4e2eba9d testClass = com.enttribe.emailagent.rest.DiffblueFakeClass72, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("internalReplyMessage", "foo");
        stringStringMap.put("externalReplyMessage", "foo");
        stringStringMap.put("scheduledStartDateTime", null);
        stringStringMap.put("scheduledEndDateTime", null);
        stringStringMap.put("timeZone", null);
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/setAutoReply")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getUpcomingEvents()}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getUpcomingEvents()}
     */
    @Test
    @DisplayName("Test getUpcomingEvents()")
    @Disabled("TODO: Complete this test")
    void testGetUpcomingEvents() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@6ef0ea8b testClass = com.enttribe.emailagent.rest.DiffblueFakeClass56, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/v1/getUpcomingEvents");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getSummaryByTypeV1(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getSummaryByTypeV1(Map)}
     */
    @Test
    @DisplayName("Test getSummaryByTypeV1(Map)")
    @Disabled("TODO: Complete this test")
    void testGetSummaryByTypeV1() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@2e1ae728 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass52, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("type", "today");
        stringStringMap.put("action", null);
        stringStringMap.put("date", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/v1/getSummaryByType")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getFlagStatus(String, String)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getFlagStatus(String, String)}
     */
    @Test
    @DisplayName("Test getFlagStatus(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetFlagStatus() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@2d2d6ec6 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass50, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/getFlagStatus");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test
     * {@link GraphIntegrationRest#getEmailsOfUser(String, String, String, String, Boolean, Integer, Integer)}
     * with {@code userId}, {@code email}, {@code receivedDateTime},
     * {@code categories}, {@code isRead}, {@code limit}, {@code offset}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#getEmailsOfUser(String, String, String, String, Boolean, Integer, Integer)}
     */
    @Test
    @DisplayName("Test getEmailsOfUser(String, String, String, String, Boolean, Integer, Integer) with 'userId', 'email', 'receivedDateTime', 'categories', 'isRead', 'limit', 'offset'")
    @Disabled("TODO: Complete this test")
    void testGetEmailsOfUserWithUserIdEmailReceivedDateTimeCategoriesIsReadLimitOffset() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@6068cd0e testClass = com.enttribe.emailagent.rest.DiffblueFakeClass48, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/emails");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getEmailsOfUser(String, Integer, Integer)}
     * with {@code mailFolder}, {@code lowerLimit}, {@code upperLimit}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#getEmailsOfUser(String, Integer, Integer)}
     */
    @Test
    @DisplayName("Test getEmailsOfUser(String, Integer, Integer) with 'mailFolder', 'lowerLimit', 'upperLimit'")
    @Disabled("TODO: Complete this test")
    void testGetEmailsOfUserWithMailFolderLowerLimitUpperLimit() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@41b2a3b6 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass46, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder getResult = MockMvcRequestBuilders.get("/emailservice/getEmails");
        MockHttpServletRequestBuilder paramResult = getResult.param("lowerLimit", String.valueOf(1))
                .param("mailFolder", "foo");
        MockHttpServletRequestBuilder requestBuilder = paramResult.param("upperLimit", String.valueOf(1));

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#acceptMeeting(String)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#acceptMeeting(String)}
     */
    @Test
    @DisplayName("Test acceptMeeting(String)")
    @Disabled("TODO: Complete this test")
    void testAcceptMeeting() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Missing Spring properties.
        //   Failed to create Spring context due to unresolvable @Value
        //   properties: field 'threadConcurency'
        //   Please check that at least one of the property files is provided
        //   and contains required variables:
        //   - application-test.properties (file missing)
        //   See https://diff.blue/R033 to resolve this issue.

        // Arrange
        // TODO: Populate arranged inputs
        Object[] uriVariables = new Object[]{};
        String[] values = new String[]{"foo"};
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/emailservice/acceptMeeting", uriVariables)
                .param("eventId", values);
        Object[] controllers = new Object[]{graphIntegrationRest};
        MockMvc buildResult = MockMvcBuilders.standaloneSetup(controllers).build();

        // Act
        ResultActions actualPerformResult = buildResult.perform(requestBuilder);

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Test {@link GraphIntegrationRest#getEmailByInternetMessageId(Map)}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#getEmailByInternetMessageId(Map)}
     */
    @Test
    @DisplayName("Test getEmailByInternetMessageId(Map)")
    void testGetEmailByInternetMessageId() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        GraphIntegrationRest graphIntegrationRest = new GraphIntegrationRest();

        // Act and Assert
        assertThrows(BusinessException.class, () -> graphIntegrationRest.getEmailByInternetMessageId(new HashMap<>()));
    }

    /**
     * Test {@link GraphIntegrationRest#tentativelyAcceptMeeting(String)}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#tentativelyAcceptMeeting(String)}
     */
    @Test
    @DisplayName("Test tentativelyAcceptMeeting(String)")
    @Disabled("TODO: Complete this test")
    void testTentativelyAcceptMeeting() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@5bbaf791 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass74, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/tentativelyAccept")
                .param("eventId", "foo");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#scheduleEventRecurring(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#scheduleEventRecurring(Map)}
     */
    @Test
    @DisplayName("Test scheduleEventRecurring(Map)")
    @Disabled("TODO: Complete this test")
    void testScheduleEventRecurring() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@6614f537 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass70, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        LinkedHashSet<Object> objectSet = new LinkedHashSet<>();
        objectSet.add("foo");

        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("timeZone", null);
        stringObjectMap.put("conversationId", null);
        stringObjectMap.put("body", null);
        stringObjectMap.put("requiredAttendees", new ArrayList<>());
        stringObjectMap.put("optionalAttendees", new ArrayList<>());
        stringObjectMap.put("meetingType", null);
        stringObjectMap.put("location", null);
        stringObjectMap.put("isRecurring", null);
        stringObjectMap.put("recurrenceType", "foo");
        stringObjectMap.put("interval", 1);
        stringObjectMap.put("daysOfWeek", objectSet);
        stringObjectMap.put("dayOfMonth", 1);
        stringObjectMap.put("monthOfYear", 1);
        stringObjectMap.put("rangeType", "foo");
        stringObjectMap.put("subject", "foo");
        stringObjectMap.put("meetingStartTime", "Z");
        stringObjectMap.put("meetingEndTime", "foo");
        stringObjectMap.put("recurrenceStartDate", "foo");
        stringObjectMap.put("recurrenceEndDate", "foo");
        stringObjectMap.put("optionalAttendees", new Object());
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/scheduleEventRecurring")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#scheduleEvent(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#scheduleEvent(Map)}
     */
    @Test
    @DisplayName("Test scheduleEvent(Map)")
    @Disabled("TODO: Complete this test")
    void testScheduleEvent() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@4bc321f8 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass68, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("timeZone", null);
        stringObjectMap.put("subject", "foo");
        stringObjectMap.put("body", "foo");
        stringObjectMap.put("meetingStartTime", "Z");
        stringObjectMap.put("meetingEndTime", "foo");
        stringObjectMap.put("requiredAttendees", new ArrayList<>());
        stringObjectMap.put("location", "foo");
        stringObjectMap.put("locationUri", "foo");
        stringObjectMap.put("meetingType", null);
        stringObjectMap.put("optionalAttendees", new ArrayList<>());
        stringObjectMap.put("optionalAttendees", new Object());
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/scheduleEvent")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#rescheduleEvent(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#rescheduleEvent(Map)}
     */
    @Test
    @DisplayName("Test rescheduleEvent(Map)")
    @Disabled("TODO: Complete this test")
    void testRescheduleEvent() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@381e2008 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass66, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("timeZone", null);
        stringStringMap.put("eventId", "foo");
        stringStringMap.put("startTime", "foo");
        stringStringMap.put("endTime", "foo");
        stringStringMap.put("rescheduleReason", null);
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/rescheduleEvent")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#regenrateObjectives(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#regenrateObjectives(Map)}
     */
    @Test
    @DisplayName("Test regenrateObjectives(Map)")
    @Disabled("TODO: Complete this test")
    void testRegenrateObjectives() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@4788ed73 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass64, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("internetMessageId", null);
        stringStringMap.put("messageId", null);
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/regenrateObjectives")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#reProcessEmail(Integer)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#reProcessEmail(Integer)}
     */
    @Test
    @DisplayName("Test reProcessEmail(Integer)")
    @Disabled("TODO: Complete this test")
    void testReProcessEmail() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@7544ea1 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass62, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/reProcessEmail/{id}", 1);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#onDemandProcessEmail(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#onDemandProcessEmail(Map)}
     */
    @Test
    @DisplayName("Test onDemandProcessEmail(Map)")
    @Disabled("TODO: Complete this test")
    void testOnDemandProcessEmail() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@5d0e79cd testClass = com.enttribe.emailagent.rest.DiffblueFakeClass60, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("internetMessageId", "foo");
        stringStringMap.put("messageId", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/onDemandProcessEmail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#markMailRead(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#markMailRead(Map)}
     */
    @Test
    @DisplayName("Test markMailRead(Map)")
    @Disabled("TODO: Complete this test")
    void testMarkMailRead() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@766bcf20 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass58, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("internetMessageId", "foo");
        stringStringMap.put("folderName", "foo");
        stringStringMap.put("markAsRead", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/markMailRead")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getSupportedMeetingTypes()}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getSupportedMeetingTypes()}
     */
    @Test
    @DisplayName("Test getSupportedMeetingTypes()")
    @Disabled("TODO: Complete this test")
    void testGetSupportedMeetingTypes() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@af0eb75 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass54, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/getSupportedMeetingTypes");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getEmailByInternetMessageId(Map)}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#getEmailByInternetMessageId(Map)}
     */
    @Test
    @DisplayName("Test getEmailByInternetMessageId(Map)")
    @Disabled("TODO: Complete this test")
    void testGetEmailByInternetMessageId2() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@2e4a514c testClass = com.enttribe.emailagent.rest.DiffblueFakeClass44, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/emailservice/getEmailByInternetMessageId")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getDaySummaryV2(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getDaySummaryV2(Map)}
     */
    @Test
    @DisplayName("Test getDaySummaryV2(Map)")
    @Disabled("TODO: Complete this test")
    void testGetDaySummaryV2() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@f3ac720 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass42, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("type", null);
        stringStringMap.put("date", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/v2/getDaySummary")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getCalendarEvents(String, String)} with
     * {@code startDateTime}, {@code endDateTime}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#getCalendarEvents(String, String)}
     */
    @Test
    @DisplayName("Test getCalendarEvents(String, String) with 'startDateTime', 'endDateTime'")
    @Disabled("TODO: Complete this test")
    void testGetCalendarEventsWithStartDateTimeEndDateTime() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@41bf060 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass38, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/getCalendarEvents");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getCalendarEventByIdV1(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getCalendarEventByIdV1(Map)}
     */
    @Test
    @DisplayName("Test getCalendarEventByIdV1(Map)")
    @Disabled("TODO: Complete this test")
    void testGetCalendarEventByIdV1() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@2748c84 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass36, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("id", "foo");
        stringStringMap.put("internetMessageId", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/v1/getCalendarEventById")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getCalendarEvents(Map)} with
     * {@code request}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getCalendarEvents(Map)}
     */
    @Test
    @DisplayName("Test getCalendarEvents(Map) with 'request'")
    @Disabled("TODO: Complete this test")
    void testGetCalendarEventsWithRequest() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@3de6b0af testClass = com.enttribe.emailagent.rest.DiffblueFakeClass40, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("startDateTime", null);
        stringStringMap.put("endDateTime", null);
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/getCalendarEvents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getCalendarEventById(String, String)}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#getCalendarEventById(String, String)}
     */
    @Test
    @DisplayName("Test getCalendarEventById(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetCalendarEventById() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@516b73ed testClass = com.enttribe.emailagent.rest.DiffblueFakeClass34, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/getCalendarEventById")
                .param("id", "foo");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getAvailableSlotsAndConflict(Map)}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#getAvailableSlotsAndConflict(Map)}
     */
    @Test
    @DisplayName("Test getAvailableSlotsAndConflict(Map)")
    @Disabled("TODO: Complete this test")
    void testGetAvailableSlotsAndConflict() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@41649a0f testClass = com.enttribe.emailagent.rest.DiffblueFakeClass32, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        LinkedHashSet<Object> objectSet = new LinkedHashSet<>();
        objectSet.add("foo");

        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("requiredAttendees", objectSet);
        stringObjectMap.put("startTime", "foo");
        stringObjectMap.put("endTime", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/emailservice/getAvailableSlotsAndConflict")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getAvailableMeetingSlotsV3(Map)}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#getAvailableMeetingSlotsV3(Map)}
     */
    @Test
    @DisplayName("Test getAvailableMeetingSlotsV3(Map)")
    @Disabled("TODO: Complete this test")
    void testGetAvailableMeetingSlotsV3() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@e16af57 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass30, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/emailservice/v3/getAvailableMeetingSlots")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getAvailableMeetingSlotsV2(Map)}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#getAvailableMeetingSlotsV2(Map)}
     */
    @Test
    @DisplayName("Test getAvailableMeetingSlotsV2(Map)")
    @Disabled("TODO: Complete this test")
    void testGetAvailableMeetingSlotsV2() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@13ee9b78 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass28, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        LinkedHashSet<Object> objectSet = new LinkedHashSet<>();
        objectSet.add("foo");

        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("participants", objectSet);
        stringObjectMap.put("startDateTime", "Z");
        stringObjectMap.put("endDateTime", null);
        stringObjectMap.put("slotDuration", 1);
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/emailservice/v2/getAvailableMeetingSlots")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getAvailableMeetingSlots(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getAvailableMeetingSlots(Map)}
     */
    @Test
    @DisplayName("Test getAvailableMeetingSlots(Map)")
    @Disabled("TODO: Complete this test")
    void testGetAvailableMeetingSlots() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@3171d385 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass26, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/emailservice/v1/getAvailableMeetingSlots")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getAutoReplySettings()}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getAutoReplySettings()}
     */
    @Test
    @DisplayName("Test getAutoReplySettings()")
    @Disabled("TODO: Complete this test")
    void testGetAutoReplySettings() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@5d2713a9 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass24, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/getAutoReplySettings");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getAttachmentStatus(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getAttachmentStatus(Map)}
     */
    @Test
    @DisplayName("Test getAttachmentStatus(Map)")
    @Disabled("TODO: Complete this test")
    void testGetAttachmentStatus() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@6648c454 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass22, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("internetMessageId", null);
        stringStringMap.put("messageId", null);
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/getAttachmentStatus")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#getAnswerFromSummary(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#getAnswerFromSummary(Map)}
     */
    @Test
    @DisplayName("Test getAnswerFromSummary(Map)")
    @Disabled("TODO: Complete this test")
    void testGetAnswerFromSummary() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@2646fa88 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass20, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders.post("/emailservice/getAnswerFromSummary")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#generateMeetingSummary()}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#generateMeetingSummary()}
     */
    @Test
    @DisplayName("Test generateMeetingSummary()")
    @Disabled("TODO: Complete this test")
    void testGenerateMeetingSummary() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@14567ea testClass = com.enttribe.emailagent.rest.DiffblueFakeClass18, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/generateMeetingSummary");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#flagEmail(String, String)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#flagEmail(String, String)}
     */
    @Test
    @DisplayName("Test flagEmail(String, String)")
    @Disabled("TODO: Complete this test")
    void testFlagEmail() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@7d3c053d testClass = com.enttribe.emailagent.rest.DiffblueFakeClass16, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders.post("/emailservice/flagEmail")
                .param("messageId", "foo")
                .contentType(MediaType.APPLICATION_JSON);
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content((new ObjectMapper()).writeValueAsString("flagged"));

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#failureLogCountByFilter(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#failureLogCountByFilter(Map)}
     */
    @Test
    @DisplayName("Test failureLogCountByFilter(Map)")
    @Disabled("TODO: Complete this test")
    void testFailureLogCountByFilter() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@41195226 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass14, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("userId", null);
        stringObjectMap.put("type", null);
        stringObjectMap.put("id", null);
        stringObjectMap.put("statsDate", null);
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/emailStatsCountByFilter")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#failureLogByFilter(Integer, Integer, Map)}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationRest#failureLogByFilter(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test failureLogByFilter(Integer, Integer, Map)")
    @Disabled("TODO: Complete this test")
    void testFailureLogByFilter() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@4e6512e8 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass12, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("userId", null);
        stringObjectMap.put("type", null);
        stringObjectMap.put("id", null);
        stringObjectMap.put("statsDate", null);
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/emailStatsByFilter")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#deployBuild()}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#deployBuild()}
     */
    @Test
    @DisplayName("Test deployBuild()")
    @Disabled("TODO: Complete this test")
    void testDeployBuild() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@47c6292c testClass = com.enttribe.emailagent.rest.DiffblueFakeClass10, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/deployBuild");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#deleteEmail(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#deleteEmail(Map)}
     */
    @Test
    @DisplayName("Test deleteEmail(Map)")
    @Disabled("TODO: Complete this test")
    void testDeleteEmail() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@297b454a testClass = com.enttribe.emailagent.rest.DiffblueFakeClass8, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("messageId", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/deleteEmail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#declineMeeting(String)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#declineMeeting(String)}
     */
    @Test
    @DisplayName("Test declineMeeting(String)")
    @Disabled("TODO: Complete this test")
    void testDeclineMeeting() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@18c10117 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass6, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/emailservice/declineMeeting")
                .param("eventId", "foo");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#createCategoriesForAllUsers()}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#createCategoriesForAllUsers()}
     */
    @Test
    @DisplayName("Test createCategoriesForAllUsers()")
    @Disabled("TODO: Complete this test")
    void testCreateCategoriesForAllUsers() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@79f5e216 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass4, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/emailservice/createCategoriesForAllUsers");

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link GraphIntegrationRest#cancelAutoReply(Map)}.
     * <p>
     * Method under test: {@link GraphIntegrationRest#cancelAutoReply(Map)}
     */
    @Test
    @DisplayName("Test cancelAutoReply(Map)")
    @Disabled("TODO: Complete this test")
    void testCancelAutoReply() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@228f3c7e testClass = com.enttribe.emailagent.rest.DiffblueFakeClass2, locations = [], classes = [com.enttribe.emailagent.rest.GraphIntegrationRest], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1dab5f15, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@33552173, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d389a250, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@350d89f1], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders.post("/emailservice/cancelAutoReply")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act
        MockMvcBuilders.standaloneSetup(graphIntegrationRest).build().perform(requestBuilder);
    }
}
