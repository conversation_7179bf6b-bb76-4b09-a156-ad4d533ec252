package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.service.EwsService;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {EwsRest.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class EwsRestTest {
    @Autowired
    private EwsRest ewsRest;

    @MockBean
    private EwsService ewsService;

    /**
     * Test {@link EwsRest#login(Map)}.
     * <ul>
     *   <li>Given empty string.</li>
     *   <li>When {@code Uri Variables}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsRest#login(Map)}
     */
    @Test
    @DisplayName("Test login(Map); given empty string; when 'Uri Variables'")
    void testLogin_givenEmptyString_whenUriVariables() throws Exception {
        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("username", "");
        stringStringMap.put("password", "");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/ews/login", "Uri Variables")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(ewsRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<Map><status>UnAuthorised</status></Map>"));
    }

    /**
     * Test {@link EwsRest#refreshToken(Map)}.
     * <p>
     * Method under test: {@link EwsRest#refreshToken(Map)}
     */
    @Test
    @DisplayName("Test refreshToken(Map)")
    void testRefreshToken() throws Exception {
        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("access_token", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/ews/refresh-token")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(ewsRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<Map><status>failed</status></Map>"));
    }

    /**
     * Test {@link EwsRest#login(Map)}.
     * <ul>
     *   <li>Given empty string.</li>
     *   <li>When {@link HashMap#HashMap()} {@code password} is empty string.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsRest#login(Map)}
     */
    @Test
    @DisplayName("Test login(Map); given empty string; when HashMap() 'password' is empty string")
    void testLogin_givenEmptyString_whenHashMapPasswordIsEmptyString() throws Exception {
        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("username", "");
        stringStringMap.put("password", "");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/ews/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(ewsRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<Map><status>UnAuthorised</status></Map>"));
    }

    /**
     * Test {@link EwsRest#login(Map)}.
     * <ul>
     *   <li>Given empty string.</li>
     *   <li>When {@link HashMap#HashMap()} {@code username} is empty string.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsRest#login(Map)}
     */
    @Test
    @DisplayName("Test login(Map); given empty string; when HashMap() 'username' is empty string")
    void testLogin_givenEmptyString_whenHashMapUsernameIsEmptyString() throws Exception {
        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("username", "");
        stringStringMap.put("password", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/ews/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(ewsRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<Map><status>UnAuthorised</status></Map>"));
    }

    /**
     * Test {@link EwsRest#introspectToken(Map)}.
     * <p>
     * Method under test: {@link EwsRest#introspectToken(Map)}
     */
    @Test
    @DisplayName("Test introspectToken(Map)")
    void testIntrospectToken() throws Exception {
        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("access_token", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/ews/introspect")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(ewsRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(
                        MockMvcResultMatchers.content().string("<Map><active>false</active><error>Invalid token</error></Map>"));
    }

    /**
     * Test {@link EwsRest#login(Map)}.
     * <ul>
     *   <li>Given {@code foo}.</li>
     *   <li>When {@link HashMap#HashMap()} {@code username} is {@code foo}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsRest#login(Map)}
     */
    @Test
    @DisplayName("Test login(Map); given 'foo'; when HashMap() 'username' is 'foo'")
    void testLogin_givenFoo_whenHashMapUsernameIsFoo() throws Exception {
        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("username", "foo");
        stringStringMap.put("password", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/ews/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(ewsRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<Map><status>UnAuthorised</status></Map>"));
    }

    /**
     * Test {@link EwsRest#introspectToken(Map)}.
     * <ul>
     *   <li>When {@code Uri Variables}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsRest#introspectToken(Map)}
     */
    @Test
    @DisplayName("Test introspectToken(Map); when 'Uri Variables'")
    void testIntrospectToken_whenUriVariables() throws Exception {
        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("access_token", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/ews/introspect", "Uri Variables")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(ewsRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(
                        MockMvcResultMatchers.content().string("<Map><active>false</active><error>Invalid token</error></Map>"));
    }

    /**
     * Test {@link EwsRest#login(Map)}.
     * <ul>
     *   <li>Given {@code foo}.</li>
     *   <li>When {@link HashMap#HashMap()} {@code password} is {@code foo}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsRest#login(Map)}
     */
    @Test
    @DisplayName("Test login(Map); given 'foo'; when HashMap() 'password' is 'foo'")
    void testLogin_givenFoo_whenHashMapPasswordIsFoo() throws Exception {
        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("password", "foo");
        stringStringMap.put("password", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/ews/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(ewsRest)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("<Map><status>UnAuthorised</status></Map>"));
    }
}
