package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.service.EwsService;
import com.enttribe.emailagent.service.GraphIntegrationService;
import com.enttribe.emailagent.service.ImailSummaryService;
import com.enttribe.emailagent.service.PollTimeInfoService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.TokenUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;

import java.util.ArrayList;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {VoiceRest.class, EwsService.class, GraphIntegrationService.class,
        ImailSummaryService.class, UserContextHolder.class, PollTimeInfoService.class, TokenUtils.class,
        GmailIntegration.class, IEmailPreferencesDao.class, UserFoldersRepository.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class VoiceRestTest {
    @MockBean
    private GmailIntegration gmailIntegration;

    @MockBean
    private IEmailPreferencesDao iEmailPreferencesDao;

    @MockBean
    private UserFoldersRepository userFoldersRepository;

    @Autowired
    private VoiceRest voiceRest;

    /**
     * Test {@link VoiceRest#createDraft(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#createDraft(Map)}
     */
    @Test
    @DisplayName("Test createDraft(Map)")
    @Disabled("TODO: Complete this test")
    void testCreateDraft() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Missing Spring properties.
        //   Failed to create Spring context due to unresolvable @Value
        //   properties: field 'refreshInterval'
        //   Please check that at least one of the property files is provided
        //   and contains required variables:
        //   - application-test.properties (file missing)
        //   See https://diff.blue/R033 to resolve this issue.

        // Arrange
        // TODO: Populate arranged inputs
        Object[] uriVariables = new Object[]{};
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/emailservice/createDraft", uriVariables)
                .contentType(MediaType.APPLICATION_JSON);

        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("subject", "foo");
        stringStringMap.put("content", "foo");
        stringStringMap.put("sendDraft", "foo");
        stringStringMap.put("toEmail", "foo");
        stringStringMap.put("ccEmail", null);
        stringStringMap.put("bccEmail", null);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(stringStringMap));
        Object[] controllers = new Object[]{voiceRest};
        MockMvc buildResult = MockMvcBuilders.standaloneSetup(controllers).build();

        // Act
        ResultActions actualPerformResult = buildResult.perform(requestBuilder);

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Test {@link VoiceRest#searchMultipleContacts(List)}.
     * <p>
     * Method under test: {@link VoiceRest#searchMultipleContacts(List)}
     */
    @Test
    @DisplayName("Test searchMultipleContacts(List)")
    @Disabled("TODO: Complete this test")
    void testSearchMultipleContacts() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@20459ba5 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass63, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        ArrayList<String> stringList = new ArrayList<>();
        stringList.add("foo");
        String content = (new ObjectMapper()).writeValueAsString(stringList);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/searchMultipleContacts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#searchContacts(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#searchContacts(Map)}
     */
    @Test
    @DisplayName("Test searchContacts(Map)")
    @Disabled("TODO: Complete this test")
    void testSearchContacts() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@76dd3af5 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass60, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("contactName", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/searchContacts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#scheduleEventV1(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#scheduleEventV1(Map)}
     */
    @Test
    @DisplayName("Test scheduleEventV1(Map)")
    @Disabled("TODO: Complete this test")
    void testScheduleEventV1() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@556ab7a5 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass57, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("timeZone", null);
        stringObjectMap.put("meetingType", null);
        stringObjectMap.put("requiredAttendees", new ArrayList<>());
        stringObjectMap.put("optionalAttendees", new ArrayList<>());
        stringObjectMap.put("location", "foo");
        stringObjectMap.put("locationUri", "foo");
        stringObjectMap.put("subject", "foo");
        stringObjectMap.put("body", "foo");
        stringObjectMap.put("meetingStartTime", "Z");
        stringObjectMap.put("meetingEndTime", "foo");
        stringObjectMap.put("optionalAttendees", new Object());
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/v1/scheduleEvent")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#getMailSummaries(Map, HttpServletRequest)}.
     * <p>
     * Method under test:
     * {@link VoiceRest#getMailSummaries(Map, HttpServletRequest)}
     */
    @Test
    @DisplayName("Test getMailSummaries(Map, HttpServletRequest)")
    @Disabled("TODO: Complete this test")
    void testGetMailSummaries() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@1236fd99 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass54, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("startDate", "foo");
        stringStringMap.put("endDate", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/getMailSummaries")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#getLongAndShortSummaryForDocument(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#getLongAndShortSummaryForDocument(Map)}
     */
    @Test
    @DisplayName("Test getLongAndShortSummaryForDocument(Map)")
    @Disabled("TODO: Complete this test")
    void testGetLongAndShortSummaryForDocument() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@17fb8cff testClass = com.enttribe.emailagent.rest.DiffblueFakeClass51, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("docId", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/getDocSummary")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#getDaySummaryV2(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#getDaySummaryV2(Map)}
     */
    @Test
    @DisplayName("Test getDaySummaryV2(Map)")
    @Disabled("TODO: Complete this test")
    void testGetDaySummaryV2() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@704c9f94 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass48, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("type", null);
        stringStringMap.put("date", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/v3/getDaySummary")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#getDaySummaryForBot(Map, HttpServletRequest)}.
     * <p>
     * Method under test:
     * {@link VoiceRest#getDaySummaryForBot(Map, HttpServletRequest)}
     */
    @Test
    @DisplayName("Test getDaySummaryForBot(Map, HttpServletRequest)")
    @Disabled("TODO: Complete this test")
    void testGetDaySummaryForBot() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@e8c45da testClass = com.enttribe.emailagent.rest.DiffblueFakeClass45, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("date", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/getAllSummaryForToday")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test
     * {@link VoiceRest#getAvailableMeetingSlots(List, String, String, HttpServletRequest)}.
     * <p>
     * Method under test:
     * {@link VoiceRest#getAvailableMeetingSlots(List, String, String, HttpServletRequest)}
     */
    @Test
    @DisplayName("Test getAvailableMeetingSlots(List, String, String, HttpServletRequest)")
    @Disabled("TODO: Complete this test")
    void testGetAvailableMeetingSlots() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@43f1b55e testClass = com.enttribe.emailagent.rest.DiffblueFakeClass42, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        ArrayList<String> stringList = new ArrayList<>();
        stringList.add("foo");
        String content = (new ObjectMapper()).writeValueAsString(stringList);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/getAvailableMeetingSlots")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#getAnswerFromAttachment(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#getAnswerFromAttachment(Map)}
     */
    @Test
    @DisplayName("Test getAnswerFromAttachment(Map)")
    @Disabled("TODO: Complete this test")
    void testGetAnswerFromAttachment() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@38e1de8e testClass = com.enttribe.emailagent.rest.DiffblueFakeClass39, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("docId", "foo");
        stringStringMap.put("question", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/getAnswerFromAttachment")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#getAllSummaryBetweenDate(Map, HttpServletRequest)}.
     * <p>
     * Method under test:
     * {@link VoiceRest#getAllSummaryBetweenDate(Map, HttpServletRequest)}
     */
    @Test
    @DisplayName("Test getAllSummaryBetweenDate(Map, HttpServletRequest)")
    @Disabled("TODO: Complete this test")
    void testGetAllSummaryBetweenDate() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@45ae99ea testClass = com.enttribe.emailagent.rest.DiffblueFakeClass36, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("startDate", null);
        stringStringMap.put("endDate", null);
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/getAllSummaryBetweenDate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#flagEmailBot(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#flagEmailBot(Map)}
     */
    @Test
    @DisplayName("Test flagEmailBot(Map)")
    @Disabled("TODO: Complete this test")
    void testFlagEmailBot() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@36cd80aa testClass = com.enttribe.emailagent.rest.DiffblueFakeClass33, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("messageId", "foo");
        stringStringMap.put("flagStatus", "flagged");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/v1/flagEmail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#flagEmail(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#flagEmail(Map)}
     */
    @Test
    @DisplayName("Test flagEmail(Map)")
    @Disabled("TODO: Complete this test")
    void testFlagEmail() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@25bc6906 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass30, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("id", 1);
        stringObjectMap.put("flagStatus", "flagged");
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/v3/flagEmail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#createDraftV1(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#createDraftV1(Map)}
     */
    @Test
    @DisplayName("Test createDraftV1(Map)")
    @Disabled("TODO: Complete this test")
    void testCreateDraftV1() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@746fbeec testClass = com.enttribe.emailagent.rest.DiffblueFakeClass27, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("subject", "foo");
        stringStringMap.put("content", "foo");
        stringStringMap.put("sendDraft", "foo");
        stringStringMap.put("toEmail", "foo");
        stringStringMap.put("ccEmail", null);
        stringStringMap.put("bccEmail", null);
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/v1/createDraft")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#createDraftReplyV1(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#createDraftReplyV1(Map)}
     */
    @Test
    @DisplayName("Test createDraftReplyV1(Map)")
    @Disabled("TODO: Complete this test")
    void testCreateDraftReplyV1() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@5831a6dc testClass = com.enttribe.emailagent.rest.DiffblueFakeClass24, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("messageId", "foo");
        stringStringMap.put("content", null);
        stringStringMap.put("type", null);
        stringStringMap.put("sendDraft", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/v1/createDraftReply")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#createDraftReply(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#createDraftReply(Map)}
     */
    @Test
    @DisplayName("Test createDraftReply(Map)")
    @Disabled("TODO: Complete this test")
    void testCreateDraftReply() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@16c27832 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass21, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, String> stringStringMap = new HashMap<>();
        stringStringMap.put("messageId", "foo");
        stringStringMap.put("content", null);
        stringStringMap.put("type", null);
        stringStringMap.put("sendDraft", "foo");
        stringStringMap.put("contentType", "foo");
        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/createDraftReply")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }

    /**
     * Test {@link VoiceRest#createDraftForwardV1(Map)}.
     * <p>
     * Method under test: {@link VoiceRest#createDraftForwardV1(Map)}
     */
    @Test
    @DisplayName("Test createDraftForwardV1(Map)")
    @Disabled("TODO: Complete this test")
    void testCreateDraftForwardV1() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@32f069a4 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass18, locations = [], classes = [com.enttribe.emailagent.rest.VoiceRest, com.enttribe.emailagent.service.EwsService, com.enttribe.emailagent.integration.GmailIntegration, com.enttribe.emailagent.service.GraphIntegrationService, com.enttribe.emailagent.service.ImailSummaryService, com.enttribe.emailagent.repository.IEmailPreferencesDao, com.enttribe.emailagent.userinfo.UserContextHolder, com.enttribe.emailagent.repository.UserFoldersRepository], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@746e48d2, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@e9ba345, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@54a3f0e7], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        HashMap<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("messageId", "foo");
        stringObjectMap.put("comment", "foo");
        stringObjectMap.put("toRecipients", new ArrayList<>());
        stringObjectMap.put("ccRecipients", new ArrayList<>());
        stringObjectMap.put("sendDraft", true);
        String content = (new ObjectMapper()).writeValueAsString(stringObjectMap);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/emailservice/v1/createDraftForward")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(voiceRest).build().perform(requestBuilder);
    }
}
