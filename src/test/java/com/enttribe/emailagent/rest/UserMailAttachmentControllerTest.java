package com.enttribe.emailagent.rest;

import static org.mockito.Mockito.when;

import com.enttribe.emailagent.entity.UserMailAttachment;
import com.enttribe.emailagent.service.UserMailAttachmentService;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.sql.Date;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Disabled;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {UserMailAttachmentController.class, UserMailAttachmentService.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class UserMailAttachmentControllerTest {
    @Autowired
    private UserMailAttachmentController userMailAttachmentController;

    @MockBean
    private UserMailAttachmentService userMailAttachmentService;

    /**
     * Test
     * {@link UserMailAttachmentController#updateUserMailAttachment(Integer, UserMailAttachment)}.
     * <p>
     * Method under test:
     * {@link UserMailAttachmentController#updateUserMailAttachment(Integer, UserMailAttachment)}
     */
    @Test
    @DisplayName("Test updateUserMailAttachment(Integer, UserMailAttachment)")
    @Disabled("TODO: Complete this test")
    void testUpdateUserMailAttachment() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@7ad3e82b testClass = com.enttribe.emailagent.rest.DiffblueFakeClass183, locations = [], classes = [com.enttribe.emailagent.rest.UserMailAttachmentController, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3c1f7287, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@73541878, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@4c8ad3a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        UserMailAttachment userMailAttachment = new UserMailAttachment();
        userMailAttachment.setAttachmentId("42");
        userMailAttachment.setBatchId("42");
        userMailAttachment.setConversationId("42");
        userMailAttachment.setCreationTime(
                java.util.Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        userMailAttachment.setDocPath("Doc Path");
        userMailAttachment.setErrorDisplay("An error occurred");
        userMailAttachment.setId(1);
        userMailAttachment.setInternetMessageId("42");
        userMailAttachment.setLongSummary("Long Summary");
        userMailAttachment.setMessageId("42");
        userMailAttachment.setModifiedTime(
                java.util.Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        userMailAttachment.setName("Name");
        userMailAttachment.setProcessingError("An error occurred");
        userMailAttachment.setProcessingStatus("Processing Status");
        userMailAttachment.setRagDocumentId("42");
        userMailAttachment.setRetryCount(3);
        userMailAttachment.setShortSummary("Short Summary");
        userMailAttachment.setSubject("Hello from the Dreaming Spires");
        userMailAttachment.setType("Type");
        userMailAttachment.setUniqueName("Unique Name");
        userMailAttachment.setUserId("42");
        String content = (new ObjectMapper()).writeValueAsString(userMailAttachment);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/userMailAttachmentController/updateUserMailAttachment/{id}", 1)
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(userMailAttachmentController).build().perform(requestBuilder);
    }

    /**
     * Test {@link UserMailAttachmentController#updateStatus(Integer, boolean)}.
     * <p>
     * Method under test:
     * {@link UserMailAttachmentController#updateStatus(Integer, boolean)}
     */
    @Test
    @DisplayName("Test updateStatus(Integer, boolean)")
    @Disabled("TODO: Complete this test")
    void testUpdateStatus() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@65ed4157 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass180, locations = [], classes = [com.enttribe.emailagent.rest.UserMailAttachmentController, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3c1f7287, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@73541878, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@4c8ad3a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder postResult = MockMvcRequestBuilders
                .post("/userMailAttachmentController/updateStatus/{id}", 1);
        MockHttpServletRequestBuilder requestBuilder = postResult.param("deleted", String.valueOf(true));

        // Act
        MockMvcBuilders.standaloneSetup(userMailAttachmentController).build().perform(requestBuilder);
    }

    /**
     * Test
     * {@link UserMailAttachmentController#search(Integer, String, String, String, String, String, String, String, String, String, String, String, Date, Date)}.
     * <p>
     * Method under test:
     * {@link UserMailAttachmentController#search(Integer, String, String, String, String, String, String, String, String, String, String, String, Date, Date)}
     */
    @Test
    @DisplayName("Test search(Integer, String, String, String, String, String, String, String, String, String, String, String, Date, Date)")
    @Disabled("TODO: Complete this test")
    void testSearch() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@5a9d5a75 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass177, locations = [], classes = [com.enttribe.emailagent.rest.UserMailAttachmentController, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3c1f7287, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@73541878, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@4c8ad3a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/userMailAttachmentController/search");

        // Act
        MockMvcBuilders.standaloneSetup(userMailAttachmentController).build().perform(requestBuilder);
    }

    /**
     * Test {@link UserMailAttachmentController#getUserMailAttachmentById(Integer)}.
     * <p>
     * Method under test:
     * {@link UserMailAttachmentController#getUserMailAttachmentById(Integer)}
     */
    @Test
    @DisplayName("Test getUserMailAttachmentById(Integer)")
    @Disabled("TODO: Complete this test")
    void testGetUserMailAttachmentById() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@5b18b206 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass171, locations = [], classes = [com.enttribe.emailagent.rest.UserMailAttachmentController, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3c1f7287, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@73541878, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@4c8ad3a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/userMailAttachmentController/getUserMailAttachmentById/{id}", 1);

        // Act
        MockMvcBuilders.standaloneSetup(userMailAttachmentController).build().perform(requestBuilder);
    }

    /**
     * Test
     * {@link UserMailAttachmentController#createUserMailAttachment(UserMailAttachment)}.
     * <p>
     * Method under test:
     * {@link UserMailAttachmentController#createUserMailAttachment(UserMailAttachment)}
     */
    @Test
    @DisplayName("Test createUserMailAttachment(UserMailAttachment)")
    @Disabled("TODO: Complete this test")
    void testCreateUserMailAttachment() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@2c5c3dfa testClass = com.enttribe.emailagent.rest.DiffblueFakeClass168, locations = [], classes = [com.enttribe.emailagent.rest.UserMailAttachmentController, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3c1f7287, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@73541878, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@4c8ad3a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        UserMailAttachment userMailAttachment = new UserMailAttachment();
        userMailAttachment.setAttachmentId("42");
        userMailAttachment.setBatchId("42");
        userMailAttachment.setConversationId("42");
        userMailAttachment.setCreationTime(
                java.util.Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        userMailAttachment.setDocPath("Doc Path");
        userMailAttachment.setErrorDisplay("An error occurred");
        userMailAttachment.setId(1);
        userMailAttachment.setInternetMessageId("42");
        userMailAttachment.setLongSummary("Long Summary");
        userMailAttachment.setMessageId("42");
        userMailAttachment.setModifiedTime(
                java.util.Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        userMailAttachment.setName("Name");
        userMailAttachment.setProcessingError("An error occurred");
        userMailAttachment.setProcessingStatus("Processing Status");
        userMailAttachment.setRagDocumentId("42");
        userMailAttachment.setRetryCount(3);
        userMailAttachment.setShortSummary("Short Summary");
        userMailAttachment.setSubject("Hello from the Dreaming Spires");
        userMailAttachment.setType("Type");
        userMailAttachment.setUniqueName("Unique Name");
        userMailAttachment.setUserId("42");
        String content = (new ObjectMapper()).writeValueAsString(userMailAttachment);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMailAttachmentController/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        MockMvcBuilders.standaloneSetup(userMailAttachmentController).build().perform(requestBuilder);
    }

    /**
     * Test
     * {@link UserMailAttachmentController#count(Integer, String, String, String, String, String, String, String, String, String, String, String, Date, Date)}.
     * <p>
     * Method under test:
     * {@link UserMailAttachmentController#count(Integer, String, String, String, String, String, String, String, String, String, String, String, Date, Date)}
     */
    @Test
    @DisplayName("Test count(Integer, String, String, String, String, String, String, String, String, String, String, String, Date, Date)")
    void testCount() throws Exception {
        // Arrange
        when(userMailAttachmentService.count(Mockito.<Integer>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<java.util.Date>any(), Mockito.<java.util.Date>any())).thenReturn(3L);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/userMailAttachmentController/count");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(userMailAttachmentController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("3"));
    }

    /**
     * Test
     * {@link UserMailAttachmentController#userMailAttachmentCountByFilter(Map)}.
     * <p>
     * Method under test:
     * {@link UserMailAttachmentController#userMailAttachmentCountByFilter(Map)}
     */
    @Test
    @DisplayName("Test userMailAttachmentCountByFilter(Map)")
    @Disabled("TODO: Complete this test")
    void testUserMailAttachmentCountByFilter() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@6ac34f53 testClass = com.enttribe.emailagent.rest.DiffblueFakeClass189, locations = [], classes = [com.enttribe.emailagent.rest.UserMailAttachmentController, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3c1f7287, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@73541878, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@4c8ad3a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/userMailAttachmentController/userMailAttachmentCountByFilter")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act
        MockMvcBuilders.standaloneSetup(userMailAttachmentController).build().perform(requestBuilder);
    }

    /**
     * Test
     * {@link UserMailAttachmentController#userMailAttachmentByFilter(Integer, Integer, Map)}.
     * <p>
     * Method under test:
     * {@link UserMailAttachmentController#userMailAttachmentByFilter(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test userMailAttachmentByFilter(Integer, Integer, Map)")
    @Disabled("TODO: Complete this test")
    void testUserMailAttachmentByFilter() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@7f8e8dbf testClass = com.enttribe.emailagent.rest.DiffblueFakeClass186, locations = [], classes = [com.enttribe.emailagent.rest.UserMailAttachmentController, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3c1f7287, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@73541878, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@4c8ad3a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/userMailAttachmentController/userMailAttachmentByFilter")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new HashMap<>()));

        // Act
        MockMvcBuilders.standaloneSetup(userMailAttachmentController).build().perform(requestBuilder);
    }

    /**
     * Test {@link UserMailAttachmentController#resetUsermailAttachment(Integer)}.
     * <p>
     * Method under test:
     * {@link UserMailAttachmentController#resetUsermailAttachment(Integer)}
     */
    @Test
    @DisplayName("Test resetUsermailAttachment(Integer)")
    @Disabled("TODO: Complete this test")
    void testResetUsermailAttachment() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [MergedContextConfiguration@54ab9c3c testClass = com.enttribe.emailagent.rest.DiffblueFakeClass174, locations = [], classes = [com.enttribe.emailagent.rest.UserMailAttachmentController, com.enttribe.emailagent.service.UserMailAttachmentService], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = [], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3c1f7287, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@73541878, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@4c8ad3a9], contextLoader = org.springframework.test.context.support.DelegatingSmartContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange
        MockHttpServletRequestBuilder postResult = MockMvcRequestBuilders.post("/userMailAttachmentController/reset");
        MockHttpServletRequestBuilder requestBuilder = postResult.param("id", String.valueOf(1));

        // Act
        MockMvcBuilders.standaloneSetup(userMailAttachmentController).build().perform(requestBuilder);
    }
}
