//package com.enttribe.emailagent.rest;
//
//import static org.mockito.Mockito.when;
//
//import com.enttribe.emailagent.DraftWrapper;
//import com.enttribe.emailagent.ai.dto.draft.DraftResponse;
//import com.enttribe.emailagent.ai.dto.draft.FreshDraftAIResponse;
//import com.enttribe.emailagent.ai.service.DraftService;
//import com.enttribe.emailagent.entity.MailSummary;
//import com.enttribe.emailagent.repository.IMailSummaryDao;
//import com.enttribe.emailagent.userinfo.UserContextHolder;
//import com.enttribe.emailagent.userinfo.UserInfo;
//import com.enttribe.emailagent.utils.FreshDraftRequest;
//import com.fasterxml.jackson.databind.ObjectMapper;
//
//import java.time.LocalDate;
//import java.time.ZoneOffset;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.HashSet;
//import java.util.Map;
//
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.http.MediaType;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.aot.DisabledInAotMode;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
//import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
//import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
//import org.springframework.test.web.servlet.setup.MockMvcBuilders;
//
//@ContextConfiguration(classes = {DraftRest.class})
//@ExtendWith(SpringExtension.class)
//@DisabledInAotMode
//class DraftRestTest {
//    @Autowired
//    private DraftRest draftRest;
//
//    @MockBean(name = "ds")
//    private DraftService draftService;
//
//    @MockBean
//    private IMailSummaryDao iMailSummaryDao;
//
//    @MockBean
//    private UserContextHolder userContextHolder;
//
//    /**
//     * Test {@link DraftRest#generateDraftFromContent(DraftWrapper)}.
//     * <ul>
//     *   <li>Given {@code ds}
//     * {@link DraftService#generateDraftFromContent(String, String, String, String, String, String, String, Map)}
//     * return {@link DraftResponse} (default constructor).</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link DraftRest#generateDraftFromContent(DraftWrapper)}
//     */
//    @Test
//    @DisplayName("Test generateDraftFromContent(DraftWrapper); given 'ds' generateDraftFromContent(String, String, String, String, String, String, String, Map) return DraftResponse (default constructor)")
//    void testGenerateDraftFromContent_givenDsGenerateDraftFromContentReturnDraftResponse() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        DraftResponse draftResponse = new DraftResponse();
//        draftResponse.setEmail("<EMAIL>");
//        draftResponse.setParticipants(new ArrayList<>());
//
//        DraftResponse draftResponse2 = new DraftResponse();
//        draftResponse2.setEmail("<EMAIL>");
//        draftResponse2.setParticipants(new ArrayList<>());
//        when(draftService.generateDraftFromContent(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<Map<String, String>>any())).thenReturn(draftResponse2);
//        when(draftService.reGenerateDraft(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<Map<String, String>>any())).thenReturn(draftResponse);
//
//        DraftWrapper draftWrapper = new DraftWrapper();
//        draftWrapper.setContent("Not all who wander are lost");
//        draftWrapper.setConversationId("42");
//        draftWrapper.setDraft("Draft");
//        draftWrapper.setForward(true);
//        draftWrapper.setInternetMessageId("42");
//        draftWrapper.setLength("Length");
//        draftWrapper.setMessageId("42");
//        draftWrapper.setObjective("Objective");
//        draftWrapper.setPreviousDraft(null);
//        draftWrapper.setRecipients("Recipients");
//        draftWrapper.setSelection("Selection");
//        draftWrapper.setStatus("Status");
//        draftWrapper.setTone("Tone");
//        draftWrapper.setUserEmail("<EMAIL>");
//        draftWrapper.setUserPrompt("User Prompt");
//        String content = (new ObjectMapper()).writeValueAsString(draftWrapper);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/generateDraftFromContent")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string("<Map><result>{\"email\":\"<EMAIL>\",\"participants\":[]}</result></Map>"));
//    }
//
//    /**
//     * Test {@link DraftRest#improveDraft(DraftWrapper)}.
//     * <p>
//     * Method under test: {@link DraftRest#improveDraft(DraftWrapper)}
//     */
//    @Test
//    @DisplayName("Test improveDraft(DraftWrapper)")
//    void testImproveDraft() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        MailSummary mailSummary = new MailSummary();
//        mailSummary.setActionClearReason("Just cause");
//        mailSummary.setActionOwner("Action Owner");
//        mailSummary.setActionTaken(true);
//        mailSummary.setAttachmentsList(new ArrayList<>());
//        mailSummary.setCategory("Category");
//        mailSummary.setCategoryReason("Just cause");
//        mailSummary.setCcUser("Cc User");
//        mailSummary.setConversationId("42");
//        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        mailSummary.setDecryptSummary("Decrypt Summary");
//        mailSummary.setDeleted(true);
//        mailSummary.setEmailTone("<EMAIL>");
//        mailSummary.setEmailToneReason("Just cause");
//        mailSummary.setFlagStatus("Flag Status");
//        mailSummary.setFolderName("Folder Name");
//        mailSummary.setFromUser("<EMAIL>");
//        mailSummary.setHasAttachment(true);
//        mailSummary.setId(1);
//        mailSummary.setInternetMessageId("42");
//        mailSummary.setIsActionable(true);
//        mailSummary.setIsAttention(true);
//        mailSummary.setIsHighPriority(true);
//        mailSummary.setIsStarMarked(true);
//        mailSummary.setIsUnread(true);
//        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        mailSummary.setMeetingPreview("Meeting Preview");
//        mailSummary.setMessageId("42");
//        mailSummary.setMessageSummary("Message Summary");
//        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
//        mailSummary.setObjective("Objective");
//        mailSummary.setPriority("Priority");
//        mailSummary.setPriorityReason("Just cause");
//        mailSummary.setStarMarkReason("Just cause");
//        mailSummary.setStarMarked(true);
//        mailSummary.setSubject("Hello from the Dreaming Spires");
//        mailSummary.setTag("Tag");
//        mailSummary.setToUser("To User");
//        mailSummary.setType("Type");
//        mailSummary.setUserActions(new HashSet<>());
//        mailSummary.setUserId("42");
//        when(iMailSummaryDao.findByInternetMessageId(Mockito.<String>any(), Mockito.<String>any())).thenReturn(mailSummary);
//
//        DraftResponse draftResponse = new DraftResponse();
//        draftResponse.setEmail("<EMAIL>");
//        draftResponse.setParticipants(new ArrayList<>());
//        when(draftService.improveDraft(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<Map<String, String>>any())).thenReturn(draftResponse);
//
//        DraftWrapper draftWrapper = new DraftWrapper();
//        draftWrapper.setContent("Not all who wander are lost");
//        draftWrapper.setConversationId("42");
//        draftWrapper.setDraft("Draft");
//        draftWrapper.setForward(true);
//        draftWrapper.setInternetMessageId("42");
//        draftWrapper.setLength("Length");
//        draftWrapper.setMessageId("42");
//        draftWrapper.setObjective("Objective");
//        draftWrapper.setPreviousDraft("Previous Draft");
//        draftWrapper.setRecipients("Recipients");
//        draftWrapper.setSelection("Selection");
//        draftWrapper.setStatus("Status");
//        draftWrapper.setTone("Tone");
//        draftWrapper.setUserEmail("<EMAIL>");
//        draftWrapper.setUserPrompt("User Prompt");
//        String content = (new ObjectMapper()).writeValueAsString(draftWrapper);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/improveDraft")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string("<Map><result>{\"email\":\"<EMAIL>\",\"participants\":[]}</result></Map>"));
//    }
//
//    /**
//     * Test {@link DraftRest#improveSelection(DraftWrapper)}.
//     * <ul>
//     *   <li>Given {@code null}.</li>
//     *   <li>When {@link DraftWrapper} (default constructor) MessageId is
//     * {@code null}.</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link DraftRest#improveSelection(DraftWrapper)}
//     */
//    @Test
//    @DisplayName("Test improveSelection(DraftWrapper); given 'null'; when DraftWrapper (default constructor) MessageId is 'null'")
//    void testImproveSelection_givenNull_whenDraftWrapperMessageIdIsNull() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        DraftResponse draftResponse = new DraftResponse();
//        draftResponse.setEmail("<EMAIL>");
//        draftResponse.setParticipants(new ArrayList<>());
//        when(draftService.improveSelection(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(), Mockito.<String>any(), Mockito.<Map<String, String>>any())).thenReturn(draftResponse);
//
//        DraftWrapper draftWrapper = new DraftWrapper();
//        draftWrapper.setContent("Not all who wander are lost");
//        draftWrapper.setConversationId("42");
//        draftWrapper.setDraft("Draft");
//        draftWrapper.setForward(true);
//        draftWrapper.setInternetMessageId("42");
//        draftWrapper.setLength("Length");
//        draftWrapper.setMessageId(null);
//        draftWrapper.setObjective("Objective");
//        draftWrapper.setPreviousDraft("Previous Draft");
//        draftWrapper.setRecipients("Recipients");
//        draftWrapper.setSelection("Selection");
//        draftWrapper.setStatus("Status");
//        draftWrapper.setTone("Tone");
//        draftWrapper.setUserEmail("<EMAIL>");
//        draftWrapper.setUserPrompt("User Prompt");
//        String content = (new ObjectMapper()).writeValueAsString(draftWrapper);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/improveSelection")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string("<Map><result>{\"email\":\"<EMAIL>\",\"participants\":[]}</result></Map>"));
//    }
//
//    /**
//     * Test {@link DraftRest#improveSelection(DraftWrapper)}.
//     * <ul>
//     *   <li>Given {@code null}.</li>
//     *   <li>When {@link DraftWrapper} (default constructor) UserPrompt is
//     * {@code null}.</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link DraftRest#improveSelection(DraftWrapper)}
//     */
//    @Test
//    @DisplayName("Test improveSelection(DraftWrapper); given 'null'; when DraftWrapper (default constructor) UserPrompt is 'null'")
//    void testImproveSelection_givenNull_whenDraftWrapperUserPromptIsNull() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        DraftResponse draftResponse = new DraftResponse();
//        draftResponse.setEmail("<EMAIL>");
//        draftResponse.setParticipants(new ArrayList<>());
//        when(draftService.improveSelection(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(), Mockito.<String>any(), Mockito.<Map<String, String>>any())).thenReturn(draftResponse);
//
//        DraftWrapper draftWrapper = new DraftWrapper();
//        draftWrapper.setContent("Not all who wander are lost");
//        draftWrapper.setConversationId("42");
//        draftWrapper.setDraft("Draft");
//        draftWrapper.setForward(true);
//        draftWrapper.setInternetMessageId("42");
//        draftWrapper.setLength("Length");
//        draftWrapper.setMessageId("42");
//        draftWrapper.setObjective("Objective");
//        draftWrapper.setPreviousDraft("Previous Draft");
//        draftWrapper.setRecipients("Recipients");
//        draftWrapper.setSelection("Selection");
//        draftWrapper.setStatus("Status");
//        draftWrapper.setTone("Tone");
//        draftWrapper.setUserEmail("<EMAIL>");
//        draftWrapper.setUserPrompt(null);
//        String content = (new ObjectMapper()).writeValueAsString(draftWrapper);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/improveSelection")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string("<Map><result>{\"email\":\"<EMAIL>\",\"participants\":[]}</result></Map>"));
//    }
//
//    /**
//     * Test {@link DraftRest#improveSelection(DraftWrapper)}.
//     * <ul>
//     *   <li>Given {@code User Prompt}.</li>
//     *   <li>When {@link DraftWrapper} (default constructor) MessageId is
//     * {@code 42}.</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link DraftRest#improveSelection(DraftWrapper)}
//     */
//    @Test
//    @DisplayName("Test improveSelection(DraftWrapper); given 'User Prompt'; when DraftWrapper (default constructor) MessageId is '42'")
//    void testImproveSelection_givenUserPrompt_whenDraftWrapperMessageIdIs42() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        DraftResponse draftResponse = new DraftResponse();
//        draftResponse.setEmail("<EMAIL>");
//        draftResponse.setParticipants(new ArrayList<>());
//        when(draftService.improveSelection(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(), Mockito.<String>any(), Mockito.<Map<String, String>>any())).thenReturn(draftResponse);
//
//        DraftWrapper draftWrapper = new DraftWrapper();
//        draftWrapper.setContent("Not all who wander are lost");
//        draftWrapper.setConversationId("42");
//        draftWrapper.setDraft("Draft");
//        draftWrapper.setForward(true);
//        draftWrapper.setInternetMessageId("42");
//        draftWrapper.setLength("Length");
//        draftWrapper.setMessageId("42");
//        draftWrapper.setObjective("Objective");
//        draftWrapper.setPreviousDraft("Previous Draft");
//        draftWrapper.setRecipients("Recipients");
//        draftWrapper.setSelection("Selection");
//        draftWrapper.setStatus("Status");
//        draftWrapper.setTone("Tone");
//        draftWrapper.setUserEmail("<EMAIL>");
//        draftWrapper.setUserPrompt("User Prompt");
//        String content = (new ObjectMapper()).writeValueAsString(draftWrapper);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/improveSelection")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string("<Map><result>{\"email\":\"<EMAIL>\",\"participants\":[]}</result></Map>"));
//    }
//
//    /**
//     * Test {@link DraftRest#getFreshDraft(FreshDraftRequest)}.
//     * <p>
//     * Method under test: {@link DraftRest#getFreshDraft(FreshDraftRequest)}
//     */
//    @Test
//    @DisplayName("Test getFreshDraft(FreshDraftRequest)")
//    void testGetFreshDraft() throws Exception {
//        // Arrange
//        FreshDraftAIResponse freshDraftAIResponse = new FreshDraftAIResponse();
//        freshDraftAIResponse.setEmailContent("<EMAIL>");
//        freshDraftAIResponse.setEmailSubject("<EMAIL>");
//        freshDraftAIResponse.setParticipants(new ArrayList<>());
//        when(draftService.getFreshDraft(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any())).thenReturn(freshDraftAIResponse);
//
//        FreshDraftRequest freshDraftRequest = new FreshDraftRequest();
//        freshDraftRequest.setDraft("Draft");
//        freshDraftRequest.setIntent("Intent");
//        freshDraftRequest.setLength("Length");
//        freshDraftRequest.setTone("Tone");
//        freshDraftRequest.setUser("User");
//        String content = (new ObjectMapper()).writeValueAsString(freshDraftRequest);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/getFreshDraft")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string(
//                                "<FreshDraftAIResponse><emailSubject><EMAIL></emailSubject><emailContent>jane.doe@example"
//                                        + ".org</emailContent><participants/></FreshDraftAIResponse>"));
//    }
//
//    /**
//     * Test {@link DraftRest#improveFreshDraft(FreshDraftRequest)}.
//     * <p>
//     * Method under test: {@link DraftRest#improveFreshDraft(FreshDraftRequest)}
//     */
//    @Test
//    @DisplayName("Test improveFreshDraft(FreshDraftRequest)")
//    void testImproveFreshDraft() throws Exception {
//        // Arrange
//        FreshDraftAIResponse freshDraftAIResponse = new FreshDraftAIResponse();
//        freshDraftAIResponse.setEmailContent("<EMAIL>");
//        freshDraftAIResponse.setEmailSubject("<EMAIL>");
//        freshDraftAIResponse.setParticipants(new ArrayList<>());
//        when(draftService.improveFreshDraft(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(), Mockito.<String>any())).thenReturn(freshDraftAIResponse);
//
//        FreshDraftRequest freshDraftRequest = new FreshDraftRequest();
//        freshDraftRequest.setDraft("Draft");
//        freshDraftRequest.setIntent("Intent");
//        freshDraftRequest.setLength("Length");
//        freshDraftRequest.setTone("Tone");
//        freshDraftRequest.setUser("User");
//        String content = (new ObjectMapper()).writeValueAsString(freshDraftRequest);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/improveFreshDraft")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string(
//                                "<FreshDraftAIResponse><emailSubject><EMAIL></emailSubject><emailContent>jane.doe@example"
//                                        + ".org</emailContent><participants/></FreshDraftAIResponse>"));
//    }
//
//    /**
//     * Test {@link DraftRest#generateDraftFromContent(DraftWrapper)}.
//     * <ul>
//     *   <li>Given {@code null}.</li>
//     *   <li>When {@link DraftWrapper} (default constructor) MessageId is
//     * {@code null}.</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link DraftRest#generateDraftFromContent(DraftWrapper)}
//     */
//    @Test
//    @DisplayName("Test generateDraftFromContent(DraftWrapper); given 'null'; when DraftWrapper (default constructor) MessageId is 'null'")
//    void testGenerateDraftFromContent_givenNull_whenDraftWrapperMessageIdIsNull() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        DraftResponse draftResponse = new DraftResponse();
//        draftResponse.setEmail("<EMAIL>");
//        draftResponse.setParticipants(new ArrayList<>());
//        when(draftService.reGenerateDraft(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<Map<String, String>>any())).thenReturn(draftResponse);
//
//        DraftWrapper draftWrapper = new DraftWrapper();
//        draftWrapper.setContent("Not all who wander are lost");
//        draftWrapper.setConversationId("42");
//        draftWrapper.setDraft("Draft");
//        draftWrapper.setForward(true);
//        draftWrapper.setInternetMessageId("42");
//        draftWrapper.setLength("Length");
//        draftWrapper.setMessageId(null);
//        draftWrapper.setObjective("Objective");
//        draftWrapper.setPreviousDraft("Previous Draft");
//        draftWrapper.setRecipients("Recipients");
//        draftWrapper.setSelection("Selection");
//        draftWrapper.setStatus("Status");
//        draftWrapper.setTone("Tone");
//        draftWrapper.setUserEmail("<EMAIL>");
//        draftWrapper.setUserPrompt("User Prompt");
//        String content = (new ObjectMapper()).writeValueAsString(draftWrapper);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/generateDraftFromContent")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string("<Map><result>{\"email\":\"<EMAIL>\",\"participants\":[]}</result></Map>"));
//    }
//
//    /**
//     * Test {@link DraftRest#generateDraftFromContent(DraftWrapper)}.
//     * <ul>
//     *   <li>Given {@link UserInfo} (default constructor) Email is
//     * {@code <EMAIL>}.</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link DraftRest#generateDraftFromContent(DraftWrapper)}
//     */
//    @Test
//    @DisplayName("Test generateDraftFromContent(DraftWrapper); given UserInfo (default constructor) Email is '<EMAIL>'")
//    void testGenerateDraftFromContent_givenUserInfoEmailIsJohnSmithExampleOrg() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        DraftResponse draftResponse = new DraftResponse();
//        draftResponse.setEmail("<EMAIL>");
//        draftResponse.setParticipants(new ArrayList<>());
//        when(draftService.reGenerateDraft(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<Map<String, String>>any())).thenReturn(draftResponse);
//
//        DraftWrapper draftWrapper = new DraftWrapper();
//        draftWrapper.setContent("Not all who wander are lost");
//        draftWrapper.setConversationId("42");
//        draftWrapper.setDraft("Draft");
//        draftWrapper.setForward(true);
//        draftWrapper.setInternetMessageId("42");
//        draftWrapper.setLength("Length");
//        draftWrapper.setMessageId("42");
//        draftWrapper.setObjective("Objective");
//        draftWrapper.setPreviousDraft("Previous Draft");
//        draftWrapper.setRecipients("Recipients");
//        draftWrapper.setSelection("Selection");
//        draftWrapper.setStatus("Status");
//        draftWrapper.setTone("Tone");
//        draftWrapper.setUserEmail("<EMAIL>");
//        draftWrapper.setUserPrompt("User Prompt");
//        String content = (new ObjectMapper()).writeValueAsString(draftWrapper);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/generateDraftFromContent")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string("<Map><result>{\"email\":\"<EMAIL>\",\"participants\":[]}</result></Map>"));
//    }
//
//    /**
//     * Test {@link DraftRest#generateDraftFromContent(DraftWrapper)}.
//     * <ul>
//     *   <li>Given {@link UserInfo} (default constructor) Email is
//     * {@code <EMAIL>}.</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link DraftRest#generateDraftFromContent(DraftWrapper)}
//     */
//    @Test
//    @DisplayName("Test generateDraftFromContent(DraftWrapper); given UserInfo (default constructor) Email is '<EMAIL>'")
//    void testGenerateDraftFromContent_givenUserInfoEmailIsJaneDoeExampleOrg() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        DraftResponse draftResponse = new DraftResponse();
//        draftResponse.setEmail("<EMAIL>");
//        draftResponse.setParticipants(new ArrayList<>());
//        when(draftService.reGenerateDraft(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<Map<String, String>>any())).thenReturn(draftResponse);
//
//        DraftWrapper draftWrapper = new DraftWrapper();
//        draftWrapper.setContent("Not all who wander are lost");
//        draftWrapper.setConversationId("42");
//        draftWrapper.setDraft("Draft");
//        draftWrapper.setForward(true);
//        draftWrapper.setInternetMessageId("42");
//        draftWrapper.setLength("Length");
//        draftWrapper.setMessageId("42");
//        draftWrapper.setObjective("Objective");
//        draftWrapper.setPreviousDraft("Previous Draft");
//        draftWrapper.setRecipients("Recipients");
//        draftWrapper.setSelection("Selection");
//        draftWrapper.setStatus("Status");
//        draftWrapper.setTone("Tone");
//        draftWrapper.setUserEmail("<EMAIL>");
//        draftWrapper.setUserPrompt("User Prompt");
//        String content = (new ObjectMapper()).writeValueAsString(draftWrapper);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/generateDraftFromContent")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string("<Map><result>{\"email\":\"<EMAIL>\",\"participants\":[]}</result></Map>"));
//    }
//
//    /**
//     * Test {@link DraftRest#generateDraft(DraftWrapper)}.
//     * <ul>
//     *   <li>Given {@link UserInfo} (default constructor) Email is
//     * {@code <EMAIL>}.</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link DraftRest#generateDraft(DraftWrapper)}
//     */
//    @Test
//    @DisplayName("Test generateDraft(DraftWrapper); given UserInfo (default constructor) Email is '<EMAIL>'")
//    void testGenerateDraft_givenUserInfoEmailIsJaneDoeExampleOrg() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        MailSummary mailSummary = new MailSummary();
//        mailSummary.setActionClearReason("Just cause");
//        mailSummary.setActionOwner("Action Owner");
//        mailSummary.setActionTaken(true);
//        mailSummary.setAttachmentsList(new ArrayList<>());
//        mailSummary.setCategory("Category");
//        mailSummary.setCategoryReason("Just cause");
//        mailSummary.setCcUser("Cc User");
//        mailSummary.setConversationId("42");
//        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        mailSummary.setDecryptSummary("Decrypt Summary");
//        mailSummary.setDeleted(true);
//        mailSummary.setEmailTone("<EMAIL>");
//        mailSummary.setEmailToneReason("Just cause");
//        mailSummary.setFlagStatus("Flag Status");
//        mailSummary.setFolderName("Folder Name");
//        mailSummary.setFromUser("<EMAIL>");
//        mailSummary.setHasAttachment(true);
//        mailSummary.setId(1);
//        mailSummary.setInternetMessageId("42");
//        mailSummary.setIsActionable(true);
//        mailSummary.setIsAttention(true);
//        mailSummary.setIsHighPriority(true);
//        mailSummary.setIsStarMarked(true);
//        mailSummary.setIsUnread(true);
//        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        mailSummary.setMeetingPreview("Meeting Preview");
//        mailSummary.setMessageId("42");
//        mailSummary.setMessageSummary("Message Summary");
//        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
//        mailSummary.setObjective("Objective");
//        mailSummary.setPriority("Priority");
//        mailSummary.setPriorityReason("Just cause");
//        mailSummary.setStarMarkReason("Just cause");
//        mailSummary.setStarMarked(true);
//        mailSummary.setSubject("Hello from the Dreaming Spires");
//        mailSummary.setTag("Tag");
//        mailSummary.setToUser("To User");
//        mailSummary.setType("Type");
//        mailSummary.setUserActions(new HashSet<>());
//        mailSummary.setUserId("42");
//
//        MailSummary mailSummary2 = new MailSummary();
//        mailSummary2.setActionClearReason("Just cause");
//        mailSummary2.setActionOwner("Action Owner");
//        mailSummary2.setActionTaken(true);
//        mailSummary2.setAttachmentsList(new ArrayList<>());
//        mailSummary2.setCategory("Category");
//        mailSummary2.setCategoryReason("Just cause");
//        mailSummary2.setCcUser("Cc User");
//        mailSummary2.setConversationId("42");
//        mailSummary2.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        mailSummary2.setDecryptSummary("Decrypt Summary");
//        mailSummary2.setDeleted(true);
//        mailSummary2.setEmailTone("<EMAIL>");
//        mailSummary2.setEmailToneReason("Just cause");
//        mailSummary2.setFlagStatus("Flag Status");
//        mailSummary2.setFolderName("Folder Name");
//        mailSummary2.setFromUser("<EMAIL>");
//        mailSummary2.setHasAttachment(true);
//        mailSummary2.setId(1);
//        mailSummary2.setInternetMessageId("42");
//        mailSummary2.setIsActionable(true);
//        mailSummary2.setIsAttention(true);
//        mailSummary2.setIsHighPriority(true);
//        mailSummary2.setIsStarMarked(true);
//        mailSummary2.setIsUnread(true);
//        mailSummary2.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        mailSummary2.setMeetingPreview("Meeting Preview");
//        mailSummary2.setMessageId("42");
//        mailSummary2.setMessageSummary("Message Summary");
//        mailSummary2.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        mailSummary2.setNotificationStatus(MailSummary.NotificationStatus.SENT);
//        mailSummary2.setObjective("Objective");
//        mailSummary2.setPriority("Priority");
//        mailSummary2.setPriorityReason("Just cause");
//        mailSummary2.setStarMarkReason("Just cause");
//        mailSummary2.setStarMarked(true);
//        mailSummary2.setSubject("Hello from the Dreaming Spires");
//        mailSummary2.setTag("Tag");
//        mailSummary2.setToUser("To User");
//        mailSummary2.setType("Type");
//        mailSummary2.setUserActions(new HashSet<>());
//        mailSummary2.setUserId("42");
//        when(iMailSummaryDao.findByInternetMessageId(Mockito.<String>any(), Mockito.<String>any())).thenReturn(mailSummary);
//        when(iMailSummaryDao.findByMessageId(Mockito.<String>any(), Mockito.<String>any())).thenReturn(mailSummary2);
//
//        DraftWrapper draftWrapper = new DraftWrapper();
//        draftWrapper.setContent("Not all who wander are lost");
//        draftWrapper.setConversationId("42");
//        draftWrapper.setDraft("Draft");
//        draftWrapper.setForward(true);
//        draftWrapper.setInternetMessageId("42");
//        draftWrapper.setLength("Length");
//        draftWrapper.setMessageId("42");
//        draftWrapper.setObjective("Objective");
//        draftWrapper.setPreviousDraft("Previous Draft");
//        draftWrapper.setRecipients("Recipients");
//        draftWrapper.setSelection("Selection");
//        draftWrapper.setStatus("Status");
//        draftWrapper.setTone("Tone");
//        draftWrapper.setUserEmail("<EMAIL>");
//        draftWrapper.setUserPrompt("User Prompt");
//        String content = (new ObjectMapper()).writeValueAsString(draftWrapper);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/generateDraft")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content().string("<Map><result>{\"email\":null}</result></Map>"));
//    }
//
//    /**
//     * Test {@link DraftRest#generateDraft(DraftWrapper)}.
//     * <ul>
//     *   <li>Given {@link UserInfo} (default constructor) Email is
//     * {@code <EMAIL>}.</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link DraftRest#generateDraft(DraftWrapper)}
//     */
//    @Test
//    @DisplayName("Test generateDraft(DraftWrapper); given UserInfo (default constructor) Email is '<EMAIL>'")
//    void testGenerateDraft_givenUserInfoEmailIsJohnSmithExampleOrg() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        MailSummary mailSummary = new MailSummary();
//        mailSummary.setActionClearReason("Just cause");
//        mailSummary.setActionOwner("Action Owner");
//        mailSummary.setActionTaken(true);
//        mailSummary.setAttachmentsList(new ArrayList<>());
//        mailSummary.setCategory("Category");
//        mailSummary.setCategoryReason("Just cause");
//        mailSummary.setCcUser("Cc User");
//        mailSummary.setConversationId("42");
//        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        mailSummary.setDecryptSummary("Decrypt Summary");
//        mailSummary.setDeleted(true);
//        mailSummary.setEmailTone("<EMAIL>");
//        mailSummary.setEmailToneReason("Just cause");
//        mailSummary.setFlagStatus("Flag Status");
//        mailSummary.setFolderName("Folder Name");
//        mailSummary.setFromUser("<EMAIL>");
//        mailSummary.setHasAttachment(true);
//        mailSummary.setId(1);
//        mailSummary.setInternetMessageId("42");
//        mailSummary.setIsActionable(true);
//        mailSummary.setIsAttention(true);
//        mailSummary.setIsHighPriority(true);
//        mailSummary.setIsStarMarked(true);
//        mailSummary.setIsUnread(true);
//        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        mailSummary.setMeetingPreview("Meeting Preview");
//        mailSummary.setMessageId("42");
//        mailSummary.setMessageSummary("Message Summary");
//        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
//        mailSummary.setObjective("Objective");
//        mailSummary.setPriority("Priority");
//        mailSummary.setPriorityReason("Just cause");
//        mailSummary.setStarMarkReason("Just cause");
//        mailSummary.setStarMarked(true);
//        mailSummary.setSubject("Hello from the Dreaming Spires");
//        mailSummary.setTag("Tag");
//        mailSummary.setToUser("To User");
//        mailSummary.setType("Type");
//        mailSummary.setUserActions(new HashSet<>());
//        mailSummary.setUserId("42");
//
//        MailSummary mailSummary2 = new MailSummary();
//        mailSummary2.setActionClearReason("Just cause");
//        mailSummary2.setActionOwner("Action Owner");
//        mailSummary2.setActionTaken(true);
//        mailSummary2.setAttachmentsList(new ArrayList<>());
//        mailSummary2.setCategory("Category");
//        mailSummary2.setCategoryReason("Just cause");
//        mailSummary2.setCcUser("Cc User");
//        mailSummary2.setConversationId("42");
//        mailSummary2.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        mailSummary2.setDecryptSummary("Decrypt Summary");
//        mailSummary2.setDeleted(true);
//        mailSummary2.setEmailTone("<EMAIL>");
//        mailSummary2.setEmailToneReason("Just cause");
//        mailSummary2.setFlagStatus("Flag Status");
//        mailSummary2.setFolderName("Folder Name");
//        mailSummary2.setFromUser("<EMAIL>");
//        mailSummary2.setHasAttachment(true);
//        mailSummary2.setId(1);
//        mailSummary2.setInternetMessageId("42");
//        mailSummary2.setIsActionable(true);
//        mailSummary2.setIsAttention(true);
//        mailSummary2.setIsHighPriority(true);
//        mailSummary2.setIsStarMarked(true);
//        mailSummary2.setIsUnread(true);
//        mailSummary2.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        mailSummary2.setMeetingPreview("Meeting Preview");
//        mailSummary2.setMessageId("42");
//        mailSummary2.setMessageSummary("Message Summary");
//        mailSummary2.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        mailSummary2.setNotificationStatus(MailSummary.NotificationStatus.SENT);
//        mailSummary2.setObjective("Objective");
//        mailSummary2.setPriority("Priority");
//        mailSummary2.setPriorityReason("Just cause");
//        mailSummary2.setStarMarkReason("Just cause");
//        mailSummary2.setStarMarked(true);
//        mailSummary2.setSubject("Hello from the Dreaming Spires");
//        mailSummary2.setTag("Tag");
//        mailSummary2.setToUser("To User");
//        mailSummary2.setType("Type");
//        mailSummary2.setUserActions(new HashSet<>());
//        mailSummary2.setUserId("42");
//        when(iMailSummaryDao.findByInternetMessageId(Mockito.<String>any(), Mockito.<String>any())).thenReturn(mailSummary);
//        when(iMailSummaryDao.findByMessageId(Mockito.<String>any(), Mockito.<String>any())).thenReturn(mailSummary2);
//
//        DraftWrapper draftWrapper = new DraftWrapper();
//        draftWrapper.setContent("Not all who wander are lost");
//        draftWrapper.setConversationId("42");
//        draftWrapper.setDraft("Draft");
//        draftWrapper.setForward(true);
//        draftWrapper.setInternetMessageId("42");
//        draftWrapper.setLength("Length");
//        draftWrapper.setMessageId("42");
//        draftWrapper.setObjective("Objective");
//        draftWrapper.setPreviousDraft("Previous Draft");
//        draftWrapper.setRecipients("Recipients");
//        draftWrapper.setSelection("Selection");
//        draftWrapper.setStatus("Status");
//        draftWrapper.setTone("Tone");
//        draftWrapper.setUserEmail("<EMAIL>");
//        draftWrapper.setUserPrompt("User Prompt");
//        String content = (new ObjectMapper()).writeValueAsString(draftWrapper);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/draft/generateDraft")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(draftRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content().string("<Map><result>{\"email\":null}</result></Map>"));
//    }
//}
