//package com.enttribe.emailagent.rest;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertNull;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.ArgumentMatchers.eq;
//import static org.mockito.ArgumentMatchers.isA;
//import static org.mockito.Mockito.anyInt;
//import static org.mockito.Mockito.doNothing;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//import com.enttribe.emailagent.dao.UserMailAttachmentRepository;
//import com.enttribe.emailagent.entity.EmailPreferences;
//import com.enttribe.emailagent.integration.GmailIntegration;
//import com.enttribe.emailagent.repository.EmailUserDao;
//import com.enttribe.emailagent.repository.IEmailPreferencesDao;
//import com.enttribe.emailagent.repository.IMailSummaryDao;
//import com.enttribe.emailagent.repository.UserFoldersRepository;
//import com.enttribe.emailagent.service.EwsService;
//import com.enttribe.emailagent.service.GraphIntegrationService;
//import com.enttribe.emailagent.userinfo.UserContextHolder;
//import com.enttribe.emailagent.userinfo.UserInfo;
//import com.fasterxml.jackson.databind.ObjectMapper;
//
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.LocalTime;
//import java.time.ZoneOffset;
//
//import java.util.ArrayList;
//import java.util.Date;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import org.junit.jupiter.api.Disabled;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.http.MediaType;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.aot.DisabledInAotMode;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
//import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
//import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
//import org.springframework.test.web.servlet.setup.MockMvcBuilders;
//
//@ContextConfiguration(classes = {EventRest.class})
//@ExtendWith(SpringExtension.class)
//@DisabledInAotMode
//class EventRestTest {
//    @Autowired
//    private EventRest eventRest;
//
//    @MockBean
//    private EwsService ewsService;
//
//    @MockBean
//    private GmailIntegration gmailIntegration;
//
//    @MockBean
//    private GraphIntegrationService graphIntegrationService;
//
//    @MockBean
//    private IEmailPreferencesDao iEmailPreferencesDao;
//
//    @MockBean
//    private IMailSummaryDao iMailSummaryDao;
//
//    @MockBean
//    private UserContextHolder userContextHolder;
//
//    /**
//     * Test {@link EventRest#getCalendarEventById(Map)}.
//     * <p>
//     * Method under test: {@link EventRest#getCalendarEventById(Map)}
//     */
//    @Test
//    @DisplayName("Test getCalendarEventById(Map)")
//    void testGetCalendarEventById() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//        when(graphIntegrationService.getCalendarEventById(Mockito.<String>any(), Mockito.<String>any()))
//                .thenReturn(new ArrayList<>());
//
//        HashMap<String, String> stringStringMap = new HashMap<>();
//        stringStringMap.put("id", "foo");
//        stringStringMap.put("internetMessageId", "foo");
//        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/events/getCalendarEventById")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(eventRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content().string("<List/>"));
//    }
//
//    /**
//     * Test {@link EventRest#getAvailableMeetingSlotsV1(Map)}.
//     * <p>
//     * Method under test: {@link EventRest#getAvailableMeetingSlotsV1(Map)}
//     */
//    @Test
//    @DisplayName("Test getAvailableMeetingSlotsV1(Map)")
//    void testGetAvailableMeetingSlotsV1() throws Exception {
//        // Arrange
//        when(graphIntegrationService.getAvailableMeetingSlots(Mockito.<List<String>>any(), Mockito.<String>any(),
//                Mockito.<String>any(), anyInt())).thenReturn(new ArrayList<>());
//        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders.post("/events/v1/getAvailableMeetingSlots")
//                .contentType(MediaType.APPLICATION_JSON);
//
//        ObjectMapper objectMapper = new ObjectMapper();
//        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
//                .content(objectMapper.writeValueAsString(new HashMap<>()));
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(eventRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content().string("<List/>"));
//    }
//
//    /**
//     * Test {@link EventRest#getAvailableMeetingSlots(Map)}.
//     * <p>
//     * Method under test: {@link EventRest#getAvailableMeetingSlots(Map)}
//     */
//    @Test
//    @DisplayName("Test getAvailableMeetingSlots(Map)")
//    @Disabled("TODO: Complete this test")
//    void testGetAvailableMeetingSlots() {
//        // TODO: Diffblue Cover was only able to create a partial test for this method:
//        //   Reason: No inputs found that don't throw a trivial exception.
//        //   Diffblue Cover tried to run the arrange/act section, but the method under
//        //   test threw
//        //   jakarta.servlet.ServletException: Request processing failed: java.lang.NullPointerException: Cannot invoke "String.split(String)" because "startDateTime" is null
//        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
//        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
//        //   java.lang.NullPointerException: Cannot invoke "String.split(String)" because "startDateTime" is null
//        //       at com.enttribe.emailagent.rest.EventRest.getAvailableMeetingSlots(EventRest.java:123)
//        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
//        //       at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
//        //   See https://diff.blue/R013 to resolve this issue.
//
//        // Arrange
//        UserContextHolder userContextHolder = new UserContextHolder();
//        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
//        EmailUserDao emailUserDao = mock(EmailUserDao.class);
//        IEmailPreferencesDao preferencesDao2 = mock(IEmailPreferencesDao.class);
//        UserContextHolder userContextHolder2 = new UserContextHolder();
//        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);
//        EwsService ewsService = new EwsService(emailUserDao, preferencesDao2, userContextHolder2, mailSummaryDao,
//                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class));
//
//        GmailIntegration gmailIntegration = mock(GmailIntegration.class);
//        IMailSummaryDao mailSummaryDao2 = mock(IMailSummaryDao.class);
//        EventRest eventRest = new EventRest(userContextHolder, preferencesDao, ewsService, gmailIntegration,
//                mailSummaryDao2, new GraphIntegrationService());
//
//        // Act
//        eventRest.getAvailableMeetingSlots(new HashMap<>());
//    }
//
//    /**
//     * Test {@link EventRest#getUpcomingEvents()}.
//     * <p>
//     * Method under test: {@link EventRest#getUpcomingEvents()}
//     */
//    @Test
//    @DisplayName("Test getUpcomingEvents()")
//    @Disabled("TODO: Complete this test")
//    void testGetUpcomingEvents() throws Exception {
//        // TODO: Diffblue Cover was only able to create a partial test for this method:
//        //   Diffblue AI was unable to find a test
//
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        EmailPreferences emailPreferences = new EmailPreferences();
//        emailPreferences.setAllowNotification(true);
//        emailPreferences.setBlackListedDomain("Black Listed Domain");
//        emailPreferences.setBlackListedSender("Black Listed Sender");
//        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
//        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
//        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
//        emailPreferences.setContactNumber("42");
//        emailPreferences.setConversationId("42");
//        emailPreferences
//                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        emailPreferences.setDateFormat("2020-03-01");
//        emailPreferences.setDebugMode(true);
//        emailPreferences.setDeviceId("42");
//        emailPreferences.setDisplayName("Display Name");
//        emailPreferences.setEmailSender("<EMAIL>");
//        emailPreferences.setEmailSubject("<EMAIL>");
//        emailPreferences.setFontColor("Font Color");
//        emailPreferences.setFontFamily("Font Family");
//        emailPreferences.setFontSize("Font Size");
//        emailPreferences.setGcmId("42");
//        emailPreferences.setId(1);
//        emailPreferences.setImportantTags("Important Tags");
//        emailPreferences.setIsCategoryEnabled(true);
//        emailPreferences.setIsPriorityEnabled(true);
//        emailPreferences.setIsToneEnabled(true);
//        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
//        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        emailPreferences.setMaskContent(true);
//        emailPreferences.setMeetingType("Meeting Type");
//        emailPreferences
//                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        emailPreferences.setOnDemandEnabled(true);
//        emailPreferences.setPreferredMeetingDuration(1);
//        emailPreferences.setSenderCompany("Sender Company");
//        emailPreferences.setTimeFormat("Time Format");
//        emailPreferences.setTimeZone("UTC");
//        emailPreferences.setUserId("42");
//        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);
//        when(graphIntegrationService.getCalendarEventsV1(Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(),null)).thenReturn(new ArrayList<>());
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/events/getUpcomingEvents");
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(eventRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content()
//                        .string("<Map><result><content>Freedom! No meetings</content><mobileContent>Guess what? No meetings to trap"
//                                + " you!</mobileContent><value>0</value><action/></result><totalMeetingCount>0</totalMeetingCount></Map"
//                                + ">"));
//    }
//
//    /**
//     * Test {@link EventRest#getUpcomingEvents()}.
//     * <ul>
//     *   <li>Given {@link EmailPreferences} (default constructor) AllowNotification is
//     * {@code true}.</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link EventRest#getUpcomingEvents()}
//     */
//    @Test
//    @DisplayName("Test getUpcomingEvents(); given EmailPreferences (default constructor) AllowNotification is 'true'")
//    void testGetUpcomingEvents_givenEmailPreferencesAllowNotificationIsTrue() {
//        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.
//
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        UserContextHolder userContextHolder = mock(UserContextHolder.class);
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//
//        EmailPreferences emailPreferences = new EmailPreferences();
//        emailPreferences.setAllowNotification(true);
//        emailPreferences.setBlackListedDomain("Black Listed Domain");
//        emailPreferences.setBlackListedSender("Black Listed Sender");
//        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
//        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
//        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
//        emailPreferences.setContactNumber("42");
//        emailPreferences.setConversationId("42");
//        emailPreferences
//                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        emailPreferences.setDateFormat("2020-03-01");
//        emailPreferences.setDebugMode(true);
//        emailPreferences.setDeviceId("42");
//        emailPreferences.setDisplayName("Display Name");
//        emailPreferences.setEmailSender("<EMAIL>");
//        emailPreferences.setEmailSubject("<EMAIL>");
//        emailPreferences.setFontColor("Font Color");
//        emailPreferences.setFontFamily("Font Family");
//        emailPreferences.setFontSize("Font Size");
//        emailPreferences.setGcmId("42");
//        emailPreferences.setId(1);
//        emailPreferences.setImportantTags("Important Tags");
//        emailPreferences.setIsCategoryEnabled(true);
//        emailPreferences.setIsPriorityEnabled(true);
//        emailPreferences.setIsToneEnabled(true);
//        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
//        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        emailPreferences.setMaskContent(true);
//        emailPreferences.setMeetingType("Meeting Type");
//        emailPreferences
//                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        emailPreferences.setOnDemandEnabled(true);
//        emailPreferences.setPreferredMeetingDuration(1);
//        emailPreferences.setSenderCompany("Sender Company");
//        emailPreferences.setTimeFormat("Time Format");
//        emailPreferences.setTimeZone("UTC");
//        emailPreferences.setUserId("42");
//        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
//        when(preferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);
//        EmailUserDao emailUserDao = mock(EmailUserDao.class);
//        IEmailPreferencesDao preferencesDao2 = mock(IEmailPreferencesDao.class);
//        UserContextHolder userContextHolder2 = new UserContextHolder();
//        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);
//        EwsService ewsService = new EwsService(emailUserDao, preferencesDao2, userContextHolder2, mailSummaryDao,
//                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class));
//
//        GmailIntegration gmailIntegration = mock(GmailIntegration.class);
//        IMailSummaryDao mailSummaryDao2 = mock(IMailSummaryDao.class);
//
//        // Act
//        Map<String, Object> actualUpcomingEvents = (new EventRest(userContextHolder, preferencesDao, ewsService,
//                gmailIntegration, mailSummaryDao2, new GraphIntegrationService())).getUpcomingEvents();
//
//        // Assert
//        verify(preferencesDao).getEmailPreferencesByUserId(eq("<EMAIL>"));
//        verify(userContextHolder).getCurrentUser();
//        assertEquals(2, actualUpcomingEvents.size());
//        Object getResult = actualUpcomingEvents.get("result");
//        assertTrue(getResult instanceof List);
//        assertNull(actualUpcomingEvents.get("totalMeetingCount"));
//        assertTrue(((List<Object>) getResult).isEmpty());
//    }
//
//    /**
//     * Test {@link EventRest#getUpcomingEvents()}.
//     * <ul>
//     *   <li>Then calls {@link EmailPreferences#getTimeZone()}.</li>
//     * </ul>
//     * <p>
//     * Method under test: {@link EventRest#getUpcomingEvents()}
//     */
//    @Test
//    @DisplayName("Test getUpcomingEvents(); then calls getTimeZone()")
//    void testGetUpcomingEvents_thenCallsGetTimeZone() {
//        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.
//
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        UserContextHolder userContextHolder = mock(UserContextHolder.class);
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//        EmailPreferences emailPreferences = mock(EmailPreferences.class);
//        when(emailPreferences.getTimeZone()).thenReturn("foo");
//        doNothing().when(emailPreferences).setAllowNotification(Mockito.<Boolean>any());
//        doNothing().when(emailPreferences).setBlackListedDomain(Mockito.<String>any());
//        doNothing().when(emailPreferences).setBlackListedSender(Mockito.<String>any());
//        doNothing().when(emailPreferences).setBlackListedSubject(Mockito.<String>any());
//        doNothing().when(emailPreferences).setCheckin(Mockito.<LocalTime>any());
//        doNothing().when(emailPreferences).setCheckout(Mockito.<LocalTime>any());
//        doNothing().when(emailPreferences).setContactNumber(Mockito.<String>any());
//        doNothing().when(emailPreferences).setConversationId(Mockito.<String>any());
//        doNothing().when(emailPreferences).setCreatedTime(Mockito.<Date>any());
//        doNothing().when(emailPreferences).setDateFormat(Mockito.<String>any());
//        doNothing().when(emailPreferences).setDebugMode(Mockito.<Boolean>any());
//        doNothing().when(emailPreferences).setDeviceId(Mockito.<String>any());
//        doNothing().when(emailPreferences).setDisplayName(Mockito.<String>any());
//        doNothing().when(emailPreferences).setEmailSender(Mockito.<String>any());
//        doNothing().when(emailPreferences).setEmailSubject(Mockito.<String>any());
//        doNothing().when(emailPreferences).setFontColor(Mockito.<String>any());
//        doNothing().when(emailPreferences).setFontFamily(Mockito.<String>any());
//        doNothing().when(emailPreferences).setFontSize(Mockito.<String>any());
//        doNothing().when(emailPreferences).setGcmId(Mockito.<String>any());
//        doNothing().when(emailPreferences).setId(anyInt());
//        doNothing().when(emailPreferences).setImportantTags(Mockito.<String>any());
//        doNothing().when(emailPreferences).setIsCategoryEnabled(Mockito.<Boolean>any());
//        doNothing().when(emailPreferences).setIsPriorityEnabled(Mockito.<Boolean>any());
//        doNothing().when(emailPreferences).setIsToneEnabled(Mockito.<Boolean>any());
//        doNothing().when(emailPreferences).setKeyboardShortcuts(Mockito.<String>any());
//        doNothing().when(emailPreferences).setLastPollTime(Mockito.<LocalDateTime>any());
//        doNothing().when(emailPreferences).setMaskContent(Mockito.<Boolean>any());
//        doNothing().when(emailPreferences).setMeetingType(Mockito.<String>any());
//        doNothing().when(emailPreferences).setModifiedTime(Mockito.<Date>any());
//        doNothing().when(emailPreferences).setNextPollTime(Mockito.<LocalDateTime>any());
//        doNothing().when(emailPreferences).setOnDemandEnabled(Mockito.<Boolean>any());
//        doNothing().when(emailPreferences).setPreferredMeetingDuration(Mockito.<Integer>any());
//        doNothing().when(emailPreferences).setSenderCompany(Mockito.<String>any());
//        doNothing().when(emailPreferences).setTimeFormat(Mockito.<String>any());
//        doNothing().when(emailPreferences).setTimeZone(Mockito.<String>any());
//        doNothing().when(emailPreferences).setUserId(Mockito.<String>any());
//        emailPreferences.setAllowNotification(true);
//        emailPreferences.setBlackListedDomain("Black Listed Domain");
//        emailPreferences.setBlackListedSender("Black Listed Sender");
//        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
//        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
//        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
//        emailPreferences.setContactNumber("42");
//        emailPreferences.setConversationId("42");
//        emailPreferences
//                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        emailPreferences.setDateFormat("2020-03-01");
//        emailPreferences.setDebugMode(true);
//        emailPreferences.setDeviceId("42");
//        emailPreferences.setDisplayName("Display Name");
//        emailPreferences.setEmailSender("<EMAIL>");
//        emailPreferences.setEmailSubject("<EMAIL>");
//        emailPreferences.setFontColor("Font Color");
//        emailPreferences.setFontFamily("Font Family");
//        emailPreferences.setFontSize("Font Size");
//        emailPreferences.setGcmId("42");
//        emailPreferences.setId(1);
//        emailPreferences.setImportantTags("Important Tags");
//        emailPreferences.setIsCategoryEnabled(true);
//        emailPreferences.setIsPriorityEnabled(true);
//        emailPreferences.setIsToneEnabled(true);
//        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
//        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        emailPreferences.setMaskContent(true);
//        emailPreferences.setMeetingType("Meeting Type");
//        emailPreferences
//                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
//        emailPreferences.setOnDemandEnabled(true);
//        emailPreferences.setPreferredMeetingDuration(1);
//        emailPreferences.setSenderCompany("Sender Company");
//        emailPreferences.setTimeFormat("Time Format");
//        emailPreferences.setTimeZone("UTC");
//        emailPreferences.setUserId("42");
//        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
//        when(preferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);
//        EmailUserDao emailUserDao = mock(EmailUserDao.class);
//        IEmailPreferencesDao preferencesDao2 = mock(IEmailPreferencesDao.class);
//        UserContextHolder userContextHolder2 = new UserContextHolder();
//        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);
//        EwsService ewsService = new EwsService(emailUserDao, preferencesDao2, userContextHolder2, mailSummaryDao,
//                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class));
//
//        GmailIntegration gmailIntegration = mock(GmailIntegration.class);
//        IMailSummaryDao mailSummaryDao2 = mock(IMailSummaryDao.class);
//
//        // Act
//        Map<String, Object> actualUpcomingEvents = (new EventRest(userContextHolder, preferencesDao, ewsService,
//                gmailIntegration, mailSummaryDao2, new GraphIntegrationService())).getUpcomingEvents();
//
//        // Assert
//        verify(emailPreferences).getTimeZone();
//        verify(emailPreferences).setAllowNotification(eq(true));
//        verify(emailPreferences).setBlackListedDomain(eq("Black Listed Domain"));
//        verify(emailPreferences).setBlackListedSender(eq("Black Listed Sender"));
//        verify(emailPreferences).setBlackListedSubject(eq("Hello from the Dreaming Spires"));
//        verify(emailPreferences).setCheckin(isA(LocalTime.class));
//        verify(emailPreferences).setCheckout(isA(LocalTime.class));
//        verify(emailPreferences).setContactNumber(eq("42"));
//        verify(emailPreferences).setConversationId(eq("42"));
//        verify(emailPreferences).setCreatedTime(isA(Date.class));
//        verify(emailPreferences).setDateFormat(eq("2020-03-01"));
//        verify(emailPreferences).setDebugMode(eq(true));
//        verify(emailPreferences).setDeviceId(eq("42"));
//        verify(emailPreferences).setDisplayName(eq("Display Name"));
//        verify(emailPreferences).setEmailSender(eq("<EMAIL>"));
//        verify(emailPreferences).setEmailSubject(eq("<EMAIL>"));
//        verify(emailPreferences).setFontColor(eq("Font Color"));
//        verify(emailPreferences).setFontFamily(eq("Font Family"));
//        verify(emailPreferences).setFontSize(eq("Font Size"));
//        verify(emailPreferences).setGcmId(eq("42"));
//        verify(emailPreferences).setId(eq(1));
//        verify(emailPreferences).setImportantTags(eq("Important Tags"));
//        verify(emailPreferences).setIsCategoryEnabled(eq(true));
//        verify(emailPreferences).setIsPriorityEnabled(eq(true));
//        verify(emailPreferences).setIsToneEnabled(eq(true));
//        verify(emailPreferences).setKeyboardShortcuts(eq("Keyboard Shortcuts"));
//        verify(emailPreferences).setLastPollTime(isA(LocalDateTime.class));
//        verify(emailPreferences).setMaskContent(eq(true));
//        verify(emailPreferences).setMeetingType(eq("Meeting Type"));
//        verify(emailPreferences).setModifiedTime(isA(Date.class));
//        verify(emailPreferences).setNextPollTime(isA(LocalDateTime.class));
//        verify(emailPreferences).setOnDemandEnabled(eq(true));
//        verify(emailPreferences).setPreferredMeetingDuration(eq(1));
//        verify(emailPreferences).setSenderCompany(eq("Sender Company"));
//        verify(emailPreferences).setTimeFormat(eq("Time Format"));
//        verify(emailPreferences).setTimeZone(eq("UTC"));
//        verify(emailPreferences).setUserId(eq("42"));
//        verify(preferencesDao).getEmailPreferencesByUserId(eq("<EMAIL>"));
//        verify(userContextHolder).getCurrentUser();
//        assertEquals(2, actualUpcomingEvents.size());
//        Object getResult = actualUpcomingEvents.get("result");
//        assertTrue(getResult instanceof List);
//        assertNull(actualUpcomingEvents.get("totalMeetingCount"));
//        assertTrue(((List<Object>) getResult).isEmpty());
//    }
//
//    /**
//     * Test {@link EventRest#getCalendarEvents(Map)}.
//     * <p>
//     * Method under test: {@link EventRest#getCalendarEvents(Map)}
//     */
//    @Test
//    @DisplayName("Test getCalendarEvents(Map)")
//    void testGetCalendarEvents() throws Exception {
//        // Arrange
//        UserInfo userInfo = new UserInfo();
//        userInfo.setEmail("<EMAIL>");
//        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
//        userInfo.setId("42");
//        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
//        when(graphIntegrationService.getCalendarEventsV1(Mockito.<String>any(), Mockito.<String>any(),
//                Mockito.<String>any(),null)).thenReturn(new ArrayList<>());
//
//        HashMap<String, String> stringStringMap = new HashMap<>();
//        stringStringMap.put("startDateTime", null);
//        stringStringMap.put("endDateTime", null);
//        String content = (new ObjectMapper()).writeValueAsString(stringStringMap);
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/events/getCalendarEvents")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content);
//
//        // Act and Assert
//        MockMvcBuilders.standaloneSetup(eventRest)
//                .build()
//                .perform(requestBuilder)
//                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/xml;charset=UTF-8"))
//                .andExpect(MockMvcResultMatchers.content().string("<Map><meetingCount>0</meetingCount></Map>"));
//    }
//}
