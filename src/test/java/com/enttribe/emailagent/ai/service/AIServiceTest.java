package com.enttribe.emailagent.ai.service;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.dto.ActionTakenResponse;
import com.enttribe.emailagent.service.PromptAuditService;
import com.enttribe.emailagent.userinfo.UserContextHolder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Disabled;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class AIServiceTest {
    @Autowired
    private AIService aIService;

    @MockBean
    private ChatModel chatModel;

    @MockBean
    private PromptAuditService promptAuditService;

    @MockBean
    private UserContextHolder userContextHolder;

    /**
     * Test {@link AIService#checkActionTaken(String, String, String, Map)}.
     * <ul>
     *   <li>Then return Empty.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link AIService#checkActionTaken(String, String, String, Map)}
     */
    @Test
    @DisplayName("Test checkActionTaken(String, String, String, Map); then return Empty")
    void testCheckActionTaken_thenReturnEmpty() {
        // Arrange
        ArrayList<Generation> generations = new ArrayList<>();
//        generations.add(new Generation("[]"));
        ChatResponse buildResult = ChatResponse.builder().metadata("Key", "Value").generations(generations).build();
        when(chatModel.call(Mockito.<Prompt>any())).thenReturn(buildResult);
        when(chatModel.getDefaultOptions()).thenReturn(new OpenAiChatOptions());
        doNothing().when(promptAuditService).sendPromptAudit(Mockito.<PromptAuditService.AuditRequest>any());

        // Act
        List<ActionTakenResponse> actualCheckActionTakenResult = aIService.checkActionTaken("<EMAIL>",
                "Sender", "Just cause", new HashMap<>());

        // Assert
        verify(promptAuditService).sendPromptAudit(isA(PromptAuditService.AuditRequest.class));
        verify(chatModel).call(isA(Prompt.class));
        verify(chatModel, atLeast(1)).getDefaultOptions();
        assertTrue(actualCheckActionTakenResult.isEmpty());
    }

    /**
     * Test {@link AIService#formatAnswer(String)}.
     * <p>
     * Method under test: {@link AIService#formatAnswer(String)}
     */
    @Test
    @DisplayName("Test formatAnswer(String)")
    @Disabled("TODO: Complete this test")
    void testFormatAnswer() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.FutureTask@79be70d3[Not completed, task = java.util.concurrent.Executors$RunnableAdapter@1b5945b1[Wrapped task = com.enttribe.emailagent.exception.EmailAgentLogger$$Lambda/0x000000e8023a36b8@8c1d179]] rejected from java.util.concurrent.ThreadPoolExecutor@18629065[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
        //       at java.base/java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2081)
        //       at java.base/java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:841)
        //       at java.base/java.util.concurrent.ThreadPoolExecutor.execute(ThreadPoolExecutor.java:1376)
        //       at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:123)
        //       at com.enttribe.emailagent.exception.EmailAgentLogger.error(EmailAgentLogger.java:95)
        //       at com.enttribe.emailagent.ai.service.AIService.formatAnswer(AIService.java:933)
        //       at net.bull.javamelody.MonitoringSpringInterceptor.invoke(MonitoringSpringInterceptor.java:76)
        //       at com.enttribe.emailagent.ai.service.AIService$$SpringCGLIB$$0.formatAnswer(<generated>)
        //   See https://diff.blue/R013 to resolve this issue.

        // Arrange and Act
        aIService.formatAnswer("Answer");
    }
}
