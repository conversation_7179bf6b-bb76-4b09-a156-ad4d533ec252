package com.enttribe.emailagent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.UserActions;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IEmailThreadDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.impl.FailureLogsServiceDaoImpl;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;
import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.CriteriaBuilder;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.query.criteria.HibernateCriteriaBuilder;
import org.hibernate.query.criteria.spi.HibernateCriteriaBuilderDelegate;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {RecordService.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class RecordServiceTest {
    @MockBean
    private EmailUserDao emailUserDao;

    @MockBean
    private EntityManager entityManager;

    @MockBean
    private EwsService ewsService;

    @MockBean
    private FailureLogsServiceDaoImpl failureLogsServiceDaoImpl;

    @MockBean
    private GraphIntegrationService graphIntegrationService;

    @MockBean
    private IEmailThreadDao iEmailThreadDao;

    @MockBean
    private IMailSummaryDao iMailSummaryDao;

    @Autowired
    private RecordService recordService;

    @MockBean
    private UserContextHolder userContextHolder;

    /**
     * Test {@link RecordService#validateUserByEmailAndPhone(String, String)}.
     * <ul>
     *   <li>Then return containsKey {@code result}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link RecordService#validateUserByEmailAndPhone(String, String)}
     */
    @Test
    @DisplayName("Test validateUserByEmailAndPhone(String, String); then return containsKey 'result'")
    void testValidateUserByEmailAndPhone_thenReturnContainsKeyResult() {
        // Arrange
        EmailUser emailUser = new EmailUser();
        emailUser.setBatchId("42");
        emailUser.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setDeleted(true);
        emailUser.setEmail("<EMAIL>");
        emailUser.setId(1);
        emailUser.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setName("Name");
        emailUser.setPhoneNumber("6625550144");
        emailUser.setQueueName("Queue Name");
        emailUser.setType("Type");
        emailUser.setUserId("42");
        emailUser.setUserOID("User OID");
        when(emailUserDao.findByEmail(Mockito.<String>any())).thenReturn(emailUser);

        // Act
        Map<String, Object> actualValidateUserByEmailAndPhoneResult = recordService
                .validateUserByEmailAndPhone("<EMAIL>", "6625550144");

        // Assert
        verify(emailUserDao).findByEmail(eq("<EMAIL>"));
        assertEquals(1, actualValidateUserByEmailAndPhoneResult.size());
        assertTrue(actualValidateUserByEmailAndPhoneResult.containsKey("result"));
    }

    /**
     * Test {@link RecordService#validateUserByEmailAndPhone(String, String)}.
     * <ul>
     *   <li>Then return {@code result} is {@code Wrong phone number.}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link RecordService#validateUserByEmailAndPhone(String, String)}
     */
    @Test
    @DisplayName("Test validateUserByEmailAndPhone(String, String); then return 'result' is 'Wrong phone number.'")
    void testValidateUserByEmailAndPhone_thenReturnResultIsWrongPhoneNumber() {
        // Arrange
        EmailUser emailUser = mock(EmailUser.class);
        when(emailUser.getPhoneNumber()).thenReturn("foo");
        doNothing().when(emailUser).setCreatedTime(Mockito.<Date>any());
        doNothing().when(emailUser).setModifiedTime(Mockito.<Date>any());
        doNothing().when(emailUser).setBatchId(Mockito.<String>any());
        doNothing().when(emailUser).setDeleted(Mockito.<Boolean>any());
        doNothing().when(emailUser).setEmail(Mockito.<String>any());
        doNothing().when(emailUser).setId(Mockito.<Integer>any());
        doNothing().when(emailUser).setName(Mockito.<String>any());
        doNothing().when(emailUser).setPhoneNumber(Mockito.<String>any());
        doNothing().when(emailUser).setQueueName(Mockito.<String>any());
        doNothing().when(emailUser).setType(Mockito.<String>any());
        doNothing().when(emailUser).setUserId(Mockito.<String>any());
        doNothing().when(emailUser).setUserOID(Mockito.<String>any());
        emailUser.setBatchId("42");
        emailUser.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setDeleted(true);
        emailUser.setEmail("<EMAIL>");
        emailUser.setId(1);
        emailUser.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setName("Name");
        emailUser.setPhoneNumber("6625550144");
        emailUser.setQueueName("Queue Name");
        emailUser.setType("Type");
        emailUser.setUserId("42");
        emailUser.setUserOID("User OID");
        when(emailUserDao.findByEmail(Mockito.<String>any())).thenReturn(emailUser);

        // Act
        Map<String, Object> actualValidateUserByEmailAndPhoneResult = recordService
                .validateUserByEmailAndPhone("<EMAIL>", "6625550144");

        // Assert
        verify(emailUser).setCreatedTime(isA(Date.class));
        verify(emailUser).setModifiedTime(isA(Date.class));
        verify(emailUser).getPhoneNumber();
        verify(emailUser).setBatchId(eq("42"));
        verify(emailUser).setDeleted(eq(true));
        verify(emailUser).setEmail(eq("<EMAIL>"));
        verify(emailUser).setId(eq(1));
        verify(emailUser).setName(eq("Name"));
        verify(emailUser).setPhoneNumber(eq("6625550144"));
        verify(emailUser).setQueueName(eq("Queue Name"));
        verify(emailUser).setType(eq("Type"));
        verify(emailUser).setUserId(eq("42"));
        verify(emailUser).setUserOID(eq("User OID"));
        verify(emailUserDao).findByEmail(eq("<EMAIL>"));
        assertEquals(1, actualValidateUserByEmailAndPhoneResult.size());
        assertEquals("Wrong phone number.", actualValidateUserByEmailAndPhoneResult.get("result"));
    }

    /**
     * Test {@link RecordService#retainLatestRecords()}.
     * <p>
     * Method under test: {@link RecordService#retainLatestRecords()}
     */
    @Test
    @DisplayName("Test retainLatestRecords()")
    @Disabled("TODO: Complete this test")
    void testRetainLatestRecords() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.NullPointerException: Cannot invoke "jakarta.persistence.TypedQuery.setMaxResults(int)" because the return value of "jakarta.persistence.EntityManager.createQuery(String, java.lang.Class)" is null
        //       at com.enttribe.emailagent.service.RecordService.retainLatestRecords(RecordService.java:131)
        //   See https://diff.blue/R013 to resolve this issue.

        // Arrange and Act
        (new RecordService()).retainLatestRecords();
    }

    /**
     * Test {@link RecordService#getEmailsByTag(List, String)}.
     * <p>
     * Method under test: {@link RecordService#getEmailsByTag(List, String)}
     */
    @Test
    @DisplayName("Test getEmailsByTag(List, String)")
    @Disabled("TODO: Complete this test")
    void testGetEmailsByTag() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.NullPointerException: Cannot invoke "org.hibernate.query.criteria.HibernateCriteriaBuilder.createQuery(java.lang.Class)" because "this.criteriaBuilder" is null
        //       at org.hibernate.query.criteria.spi.HibernateCriteriaBuilderDelegate.createQuery(HibernateCriteriaBuilderDelegate.java:118)
        //       at org.hibernate.query.criteria.spi.HibernateCriteriaBuilderDelegate.createQuery(HibernateCriteriaBuilderDelegate.java:118)
        //       at org.hibernate.query.criteria.spi.HibernateCriteriaBuilderDelegate.createQuery(HibernateCriteriaBuilderDelegate.java:76)
        //       at com.enttribe.emailagent.service.RecordService.getEmailsByTag(RecordService.java:157)

        // Arrange
        when(entityManager.getCriteriaBuilder()).thenReturn(new HibernateCriteriaBuilderDelegate(
                (CriteriaBuilder) new HibernateCriteriaBuilderDelegate((HibernateCriteriaBuilder) null)));

        // Act
        recordService.getEmailsByTag(new ArrayList<>(), "42");
    }

    /**
     * Test {@link RecordService#getEmailsByTagV1(List, String)}.
     * <ul>
     *   <li>Given {@code EWS}.</li>
     *   <li>When {@link ArrayList#ArrayList()} add {@code EWS}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RecordService#getEmailsByTagV1(List, String)}
     */
    @Test
    @DisplayName("Test getEmailsByTagV1(List, String); given 'EWS'; when ArrayList() add 'EWS'")
    void testGetEmailsByTagV1_givenEws_whenArrayListAddEws() throws Exception {
        // Arrange
        when(graphIntegrationService.searchEmailsByTag(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());
        when(iMailSummaryDao.findByMessageIds(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new ArrayList<>());

        ArrayList<String> tags = new ArrayList<>();
        tags.add("EWS");

        // Act
        List<MailSummary> actualEmailsByTagV1 = recordService.getEmailsByTagV1(tags, "<EMAIL>");

        // Assert
        verify(iMailSummaryDao).findByMessageIds(eq("<EMAIL>"), isA(List.class));
        verify(graphIntegrationService).searchEmailsByTag(eq("<EMAIL>"), eq("EWS"));
        assertTrue(actualEmailsByTagV1.isEmpty());
    }

    /**
     * Test {@link RecordService#getEmailsByTagV1(List, String)}.
     * <ul>
     *   <li>Given {@code foo}.</li>
     *   <li>When {@link ArrayList#ArrayList()} add {@code foo}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RecordService#getEmailsByTagV1(List, String)}
     */
    @Test
    @DisplayName("Test getEmailsByTagV1(List, String); given 'foo'; when ArrayList() add 'foo'")
    void testGetEmailsByTagV1_givenFoo_whenArrayListAddFoo() throws Exception {
        // Arrange
        when(graphIntegrationService.searchEmailsByTag(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());
        when(iMailSummaryDao.findByMessageIds(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new ArrayList<>());

        ArrayList<String> tags = new ArrayList<>();
        tags.add("foo");
        tags.add("EWS");

        // Act
        List<MailSummary> actualEmailsByTagV1 = recordService.getEmailsByTagV1(tags, "<EMAIL>");

        // Assert
        verify(iMailSummaryDao).findByMessageIds(eq("<EMAIL>"), isA(List.class));
        verify(graphIntegrationService).searchEmailsByTag(eq("<EMAIL>"), eq("foo"));
        assertTrue(actualEmailsByTagV1.isEmpty());
    }

    /**
     * Test {@link RecordService#getEmailsByTagV1(List, String)}.
     * <ul>
     *   <li>When {@link ArrayList#ArrayList()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RecordService#getEmailsByTagV1(List, String)}
     */
    @Test
    @DisplayName("Test getEmailsByTagV1(List, String); when ArrayList()")
    void testGetEmailsByTagV1_whenArrayList() throws Exception {
        // Arrange
        when(graphIntegrationService.searchEmailsByTag(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());
        when(iMailSummaryDao.findByMessageIds(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new ArrayList<>());

        // Act
        List<MailSummary> actualEmailsByTagV1 = recordService.getEmailsByTagV1(new ArrayList<>(), "<EMAIL>");

        // Assert
        verify(iMailSummaryDao).findByMessageIds(eq("<EMAIL>"), isA(List.class));
        verify(graphIntegrationService).searchEmailsByTag(eq("<EMAIL>"), eq(""));
        assertTrue(actualEmailsByTagV1.isEmpty());
    }

    /**
     * Test {@link RecordService#introspectEwsToken(String)}.
     * <p>
     * Method under test: {@link RecordService#introspectEwsToken(String)}
     */
    @Test
    @DisplayName("Test introspectEwsToken(String)")
    void testIntrospectEwsToken() {
        // Arrange, Act and Assert
        assertFalse(recordService.introspectEwsToken("ABC123"));
    }

    /**
     * Test {@link RecordService#getQuestionsByIntentName(String, String)}.
     * <ul>
     *   <li>When {@code Intent Name}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link RecordService#getQuestionsByIntentName(String, String)}
     */
    @Test
    @DisplayName("Test getQuestionsByIntentName(String, String); when 'Intent Name'")
    @Disabled("TODO: Complete this test")
    void testGetQuestionsByIntentName_whenIntentName() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.FutureTask@2fdbac3a[Not completed, task = java.util.concurrent.Executors$RunnableAdapter@dc19390[Wrapped task = com.enttribe.emailagent.exception.EmailAgentLogger$$Lambda/0x00000070018f40a0@5cfa639d]] rejected from java.util.concurrent.ThreadPoolExecutor@50805ee0[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
        //       at java.base/java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2081)
        //       at java.base/java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:841)
        //       at java.base/java.util.concurrent.ThreadPoolExecutor.execute(ThreadPoolExecutor.java:1376)
        //       at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:123)
        //       at com.enttribe.emailagent.exception.EmailAgentLogger.error(EmailAgentLogger.java:95)
        //       at com.enttribe.emailagent.service.RecordService.getQuestionsByIntentName(RecordService.java:271)
        //   See https://diff.blue/R013 to resolve this issue.

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        recordService.getQuestionsByIntentName("Intent Name", "ABC123");
    }

    /**
     * Test {@link RecordService#getChartJson(String, String)}.
     * <ul>
     *   <li>Given {@link UserInfo} (default constructor) Email is
     * {@code <EMAIL>}.</li>
     *   <li>When {@code Question}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RecordService#getChartJson(String, String)}
     */
    @Test
    @DisplayName("Test getChartJson(String, String); given UserInfo (default constructor) Email is '<EMAIL>'; when 'Question'")
    @Disabled("TODO: Complete this test")
    void testGetChartJson_givenUserInfoEmailIsJaneDoeExampleOrg_whenQuestion() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.FutureTask@3c7fa6ae[Not completed, task = java.util.concurrent.Executors$RunnableAdapter@50e80488[Wrapped task = com.enttribe.emailagent.exception.EmailAgentLogger$$Lambda/0x00000070018f40a0@2f3d67ee]] rejected from java.util.concurrent.ThreadPoolExecutor@50805ee0[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
        //       at java.base/java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2081)
        //       at java.base/java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:841)
        //       at java.base/java.util.concurrent.ThreadPoolExecutor.execute(ThreadPoolExecutor.java:1376)
        //       at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:123)
        //       at com.enttribe.emailagent.exception.EmailAgentLogger.error(EmailAgentLogger.java:95)
        //       at com.enttribe.emailagent.service.RecordService.getChartJson(RecordService.java:315)
        //   See https://diff.blue/R013 to resolve this issue.

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        recordService.getChartJson("Question", "ABC123");
    }

    /**
     * Test {@link RecordService#userActionsByFilter(Integer, Integer, Map)}.
     * <p>
     * Method under test:
     * {@link RecordService#userActionsByFilter(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test userActionsByFilter(Integer, Integer, Map)")
    void testUserActionsByFilter() {
        // Arrange
        when(failureLogsServiceDaoImpl.getFilteredUserActions(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(new ArrayList<>());

        // Act
        List<UserActions> actualUserActionsByFilterResult = recordService.userActionsByFilter(1, 1, new HashMap<>());

        // Assert
        verify(failureLogsServiceDaoImpl).getFilteredUserActions(isA(Map.class), eq(1), eq(1));
        assertTrue(actualUserActionsByFilterResult.isEmpty());
    }

    /**
     * Test {@link RecordService#userActionsCountByFilter(Map)}.
     * <p>
     * Method under test: {@link RecordService#userActionsCountByFilter(Map)}
     */
    @Test
    @DisplayName("Test userActionsCountByFilter(Map)")
    void testUserActionsCountByFilter() {
        // Arrange
        when(failureLogsServiceDaoImpl.getUserActionsCountByFilter(Mockito.<Map<String, Object>>any())).thenReturn(3L);

        // Act
        Long actualUserActionsCountByFilterResult = recordService.userActionsCountByFilter(new HashMap<>());

        // Assert
        verify(failureLogsServiceDaoImpl).getUserActionsCountByFilter(isA(Map.class));
        assertEquals(3L, actualUserActionsCountByFilterResult.longValue());
    }
}
