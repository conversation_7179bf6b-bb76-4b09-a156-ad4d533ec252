package com.enttribe.emailagent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.entity.PollTimeInfo;
import com.enttribe.emailagent.repository.PollTimeInfoRepository;
import jakarta.persistence.EntityManagerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

import org.junit.jupiter.api.Disabled;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.repository.CrudRepository;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class PollTimeInfoServiceTest {
    @MockBean
    private EntityManagerFactory entityManagerFactory;

    @MockBean
    private PollTimeInfoRepository pollTimeInfoRepository;

    @Autowired
    private PollTimeInfoService pollTimeInfoService;

    /**
     * Test {@link PollTimeInfoService#getSecondLastPollTimeInfo(String)}.
     * <p>
     * Method under test:
     * {@link PollTimeInfoService#getSecondLastPollTimeInfo(String)}
     */
    @Test
    @DisplayName("Test getSecondLastPollTimeInfo(String)")
    @Disabled("TODO: Complete this test")
    void testGetSecondLastPollTimeInfo() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Sandboxing policy violation.
        //   Diffblue Cover ran code in your project that tried
        //     to access the network.
        //   Diffblue Cover's default sandboxing policy disallows this in order to prevent
        //   your code from damaging your system environment.
        //   See https://diff.blue/R011 to resolve this issue.

        // Arrange and Act
        pollTimeInfoService.getSecondLastPollTimeInfo("<EMAIL>");
    }

    /**
     * Test {@link PollTimeInfoService#setStartTime(String, LocalDateTime, Status)}.
     * <p>
     * Method under test:
     * {@link PollTimeInfoService#setStartTime(String, LocalDateTime, PollTimeInfo.Status)}
     */
    @Test
    @DisplayName("Test setStartTime(String, LocalDateTime, Status)")
    @Disabled("TODO: Complete this test")
    void testSetStartTime() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@770e1b4e testClass = com.enttribe.emailagent.service.DiffblueFakeClass300, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1d31654b, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@18ab6175, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@7023700b, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@44c22b2e, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@cf0e211, org.springframework.boot.test.context.SpringBootTestAnnotation@79f6eb44], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        pollTimeInfoService.setStartTime("<EMAIL>", LocalDate.of(1970, 1, 1).atStartOfDay(),
                PollTimeInfo.Status.PROCESSING);
    }

    /**
     * Test {@link PollTimeInfoService#setEndTime(String, LocalDateTime, Status)}.
     * <p>
     * Method under test:
     * {@link PollTimeInfoService#setEndTime(String, LocalDateTime, PollTimeInfo.Status)}
     */
    @Test
    @DisplayName("Test setEndTime(String, LocalDateTime, Status)")
    @Disabled("TODO: Complete this test")
    void testSetEndTime() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: Failed to load ApplicationContext for [WebMergedContextConfiguration@6dd8d3c8 testClass = com.enttribe.emailagent.service.DiffblueFakeClass298, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@1d31654b, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@18ab6175, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@d1737ca7, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@7023700b, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@44c22b2e, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@cf0e211, org.springframework.boot.test.context.SpringBootTestAnnotation@79f6eb44], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:180)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   org.springframework.core.task.TaskRejectedException: ExecutorService in active state did not accept task: com.enttribe.emailagent.utils.TokenUtils.refreshToken
        //       at org.springframework.scheduling.concurrent.SimpleAsyncTaskScheduler.scheduleAtFixedRate(SimpleAsyncTaskScheduler.java:257)
        //       at org.springframework.scheduling.config.TaskSchedulerRouter.scheduleAtFixedRate(TaskSchedulerRouter.java:126)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleFixedRateTask(ScheduledTaskRegistrar.java:555)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleTasks(ScheduledTaskRegistrar.java:446)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.afterPropertiesSet(ScheduledTaskRegistrar.java:421)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.finishRegistration(ScheduledAnnotationBeanPostProcessor.java:264)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:659)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:110)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
        //       at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)
        //       at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)
        //       at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:984)
        //       at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
        //       at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
        //       at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
        //       at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137)
        //       at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58)
        //       at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46)
        //       at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1454)
        //       at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:553)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108)
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225)
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   java.util.concurrent.RejectedExecutionException
        //       at java.base/java.util.concurrent.ForkJoinPool.submissionQueue(ForkJoinPool.java:2351)
        //       at java.base/java.util.concurrent.ForkJoinPool.poolSubmit(ForkJoinPool.java:2368)
        //       at java.base/java.util.concurrent.ForkJoinPool.execute(ForkJoinPool.java:2847)
        //       at java.base/java.lang.VirtualThread.submitRunContinuation(VirtualThread.java:264)
        //       at java.base/java.lang.VirtualThread.start(VirtualThread.java:560)
        //       at java.base/java.lang.System$2.start(System.java:2577)
        //       at java.base/jdk.internal.vm.SharedThreadContainer.start(SharedThreadContainer.java:152)
        //       at java.base/java.util.concurrent.ThreadPoolExecutor.addWorker(ThreadPoolExecutor.java:953)
        //       at java.base/java.util.concurrent.ThreadPoolExecutor.ensurePrestart(ThreadPoolExecutor.java:1609)
        //       at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.delayedExecute(ScheduledThreadPoolExecutor.java:346)
        //       at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.scheduleAtFixedRate(ScheduledThreadPoolExecutor.java:632)
        //       at org.springframework.scheduling.concurrent.SimpleAsyncTaskScheduler.scheduleAtFixedRate(SimpleAsyncTaskScheduler.java:253)
        //       at org.springframework.scheduling.config.TaskSchedulerRouter.scheduleAtFixedRate(TaskSchedulerRouter.java:126)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleFixedRateTask(ScheduledTaskRegistrar.java:555)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleTasks(ScheduledTaskRegistrar.java:446)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.afterPropertiesSet(ScheduledTaskRegistrar.java:421)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.finishRegistration(ScheduledAnnotationBeanPostProcessor.java:264)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:659)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:110)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
        //       at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)
        //       at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)
        //       at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:984)
        //       at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
        //       at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
        //       at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
        //       at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137)
        //       at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58)
        //       at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46)
        //       at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1454)
        //       at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:553)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108)
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225)
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        pollTimeInfoService.setEndTime("<EMAIL>", LocalDate.of(1970, 1, 1).atStartOfDay(),
                PollTimeInfo.Status.PROCESSING);
    }

    /**
     * Test {@link PollTimeInfoService#fetchTimeToPoll(String)}.
     * <p>
     * Method under test: {@link PollTimeInfoService#fetchTimeToPoll(String)}
     */
    @Test
    @DisplayName("Test fetchTimeToPoll(String)")
    void testFetchTimeToPoll() {
        // Arrange
        PollTimeInfo pollTimeInfo = new PollTimeInfo();
        pollTimeInfo.setEmail("<EMAIL>");
        pollTimeInfo.setEndTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setId(1);
        pollTimeInfo.setStartTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setStatus(PollTimeInfo.Status.PROCESSING);
        Optional<PollTimeInfo> ofResult = Optional.of(pollTimeInfo);
        when(pollTimeInfoRepository.findFirstByEmailAndStatusOrderByEndTimeDesc(Mockito.<String>any(),
                Mockito.<PollTimeInfo.Status>any())).thenReturn(ofResult);

        // Act
        LocalDateTime actualFetchTimeToPollResult = pollTimeInfoService.fetchTimeToPoll("<EMAIL>");

        // Assert
        verify(pollTimeInfoRepository).findFirstByEmailAndStatusOrderByEndTimeDesc(eq("<EMAIL>"),
                eq(PollTimeInfo.Status.SUCCESS));
        assertNull(actualFetchTimeToPollResult);
    }

    /**
     * Test {@link PollTimeInfoService#fetchTimeToPoll(String)}.
     * <p>
     * Method under test: {@link PollTimeInfoService#fetchTimeToPoll(String)}
     */
    @Test
    @DisplayName("Test fetchTimeToPoll(String)")
    void testFetchTimeToPoll2() {
        // Arrange
        PollTimeInfo pollTimeInfo = mock(PollTimeInfo.class);
        when(pollTimeInfo.getStatus()).thenReturn(PollTimeInfo.Status.SUCCESS);
        doNothing().when(pollTimeInfo).setEmail(Mockito.<String>any());
        doNothing().when(pollTimeInfo).setEndTime(Mockito.<LocalDateTime>any());
        doNothing().when(pollTimeInfo).setId(Mockito.<Integer>any());
        doNothing().when(pollTimeInfo).setStartTime(Mockito.<LocalDateTime>any());
        doNothing().when(pollTimeInfo).setStatus(Mockito.<PollTimeInfo.Status>any());
        pollTimeInfo.setEmail("<EMAIL>");
        pollTimeInfo.setEndTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setId(1);
        pollTimeInfo.setStartTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setStatus(PollTimeInfo.Status.PROCESSING);
        Optional<PollTimeInfo> ofResult = Optional.of(pollTimeInfo);
        Optional<PollTimeInfo> emptyResult = Optional.empty();
        when(pollTimeInfoRepository.findFirstByEmailOrderByStartTimeDesc(Mockito.<String>any())).thenReturn(emptyResult);
        when(pollTimeInfoRepository.findFirstByEmailAndStatusOrderByEndTimeDesc(Mockito.<String>any(),
                Mockito.<PollTimeInfo.Status>any())).thenReturn(ofResult);

        // Act
        LocalDateTime actualFetchTimeToPollResult = pollTimeInfoService.fetchTimeToPoll("<EMAIL>");

        // Assert
        verify(pollTimeInfo).getStatus();
        verify(pollTimeInfo).setEmail(eq("<EMAIL>"));
        verify(pollTimeInfo).setEndTime(isA(LocalDateTime.class));
        verify(pollTimeInfo).setId(eq(1));
        verify(pollTimeInfo).setStartTime(isA(LocalDateTime.class));
        verify(pollTimeInfo).setStatus(eq(PollTimeInfo.Status.PROCESSING));
        verify(pollTimeInfoRepository).findFirstByEmailAndStatusOrderByEndTimeDesc(eq("<EMAIL>"),
                eq(PollTimeInfo.Status.SUCCESS));
        verify(pollTimeInfoRepository).findFirstByEmailOrderByStartTimeDesc(eq("<EMAIL>"));
        assertNull(actualFetchTimeToPollResult);
    }

    /**
     * Test {@link PollTimeInfoService#fetchTimeToPoll(String)}.
     * <ul>
     *   <li>Given {@link PollTimeInfo} {@link PollTimeInfo#getStatus()} return
     * {@code null}.</li>
     *   <li>Then return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link PollTimeInfoService#fetchTimeToPoll(String)}
     */
    @Test
    @DisplayName("Test fetchTimeToPoll(String); given PollTimeInfo getStatus() return 'null'; then return 'null'")
    void testFetchTimeToPoll_givenPollTimeInfoGetStatusReturnNull_thenReturnNull() {
        // Arrange
        PollTimeInfo pollTimeInfo = mock(PollTimeInfo.class);
        when(pollTimeInfo.getStatus()).thenReturn(null);
        doNothing().when(pollTimeInfo).setEmail(Mockito.<String>any());
        doNothing().when(pollTimeInfo).setEndTime(Mockito.<LocalDateTime>any());
        doNothing().when(pollTimeInfo).setId(Mockito.<Integer>any());
        doNothing().when(pollTimeInfo).setStartTime(Mockito.<LocalDateTime>any());
        doNothing().when(pollTimeInfo).setStatus(Mockito.<PollTimeInfo.Status>any());
        pollTimeInfo.setEmail("<EMAIL>");
        pollTimeInfo.setEndTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setId(1);
        pollTimeInfo.setStartTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setStatus(PollTimeInfo.Status.PROCESSING);
        Optional<PollTimeInfo> ofResult = Optional.of(pollTimeInfo);
        when(pollTimeInfoRepository.findFirstByEmailAndStatusOrderByEndTimeDesc(Mockito.<String>any(),
                Mockito.<PollTimeInfo.Status>any())).thenReturn(ofResult);

        // Act
        LocalDateTime actualFetchTimeToPollResult = pollTimeInfoService.fetchTimeToPoll("<EMAIL>");

        // Assert
        verify(pollTimeInfo).getStatus();
        verify(pollTimeInfo).setEmail(eq("<EMAIL>"));
        verify(pollTimeInfo).setEndTime(isA(LocalDateTime.class));
        verify(pollTimeInfo).setId(eq(1));
        verify(pollTimeInfo).setStartTime(isA(LocalDateTime.class));
        verify(pollTimeInfo).setStatus(eq(PollTimeInfo.Status.PROCESSING));
        verify(pollTimeInfoRepository).findFirstByEmailAndStatusOrderByEndTimeDesc(eq("<EMAIL>"),
                eq(PollTimeInfo.Status.SUCCESS));
        assertNull(actualFetchTimeToPollResult);
    }

    /**
     * Test {@link PollTimeInfoService#fetchTimeToPoll(String)}.
     * <ul>
     *   <li>Then calls {@link CrudRepository#save(Object)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link PollTimeInfoService#fetchTimeToPoll(String)}
     */
    @Test
    @DisplayName("Test fetchTimeToPoll(String); then calls save(Object)")
    void testFetchTimeToPoll_thenCallsSave() {
        // Arrange
        PollTimeInfo pollTimeInfo = new PollTimeInfo();
        pollTimeInfo.setEmail("<EMAIL>");
        pollTimeInfo.setEndTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setId(1);
        LocalDate ofResult = LocalDate.of(1970, 1, 1);
        pollTimeInfo.setStartTime(ofResult.atStartOfDay());
        pollTimeInfo.setStatus(PollTimeInfo.Status.PROCESSING);
        when(pollTimeInfoRepository.save(Mockito.<PollTimeInfo>any())).thenReturn(pollTimeInfo);
        Optional<PollTimeInfo> emptyResult = Optional.empty();
        when(pollTimeInfoRepository.findFirstByEmailAndStatusOrderByEndTimeDesc(Mockito.<String>any(),
                Mockito.<PollTimeInfo.Status>any())).thenReturn(emptyResult);

        // Act
        LocalDateTime actualFetchTimeToPollResult = pollTimeInfoService.fetchTimeToPoll("<EMAIL>");

        // Assert
        verify(pollTimeInfoRepository).findFirstByEmailAndStatusOrderByEndTimeDesc(eq("<EMAIL>"),
                eq(PollTimeInfo.Status.SUCCESS));
        verify(pollTimeInfoRepository).save(isA(PollTimeInfo.class));
        assertEquals("00:00", actualFetchTimeToPollResult.toLocalTime().toString());
        LocalDate toLocalDateResult = actualFetchTimeToPollResult.toLocalDate();
        assertEquals("1970-01-01", toLocalDateResult.toString());
        assertSame(ofResult, toLocalDateResult);
    }

    /**
     * Test {@link PollTimeInfoService#fetchTimeToPoll(String)}.
     * <ul>
     *   <li>Then return toLocalTime toString is {@code 00:00}.</li>
     * </ul>
     * <p>
     * Method under test: {@link PollTimeInfoService#fetchTimeToPoll(String)}
     */
    @Test
    @DisplayName("Test fetchTimeToPoll(String); then return toLocalTime toString is '00:00'")
    void testFetchTimeToPoll_thenReturnToLocalTimeToStringIs0000() {
        // Arrange
        PollTimeInfo pollTimeInfo = mock(PollTimeInfo.class);
        when(pollTimeInfo.getStatus()).thenReturn(PollTimeInfo.Status.SUCCESS);
        doNothing().when(pollTimeInfo).setEmail(Mockito.<String>any());
        doNothing().when(pollTimeInfo).setEndTime(Mockito.<LocalDateTime>any());
        doNothing().when(pollTimeInfo).setId(Mockito.<Integer>any());
        doNothing().when(pollTimeInfo).setStartTime(Mockito.<LocalDateTime>any());
        doNothing().when(pollTimeInfo).setStatus(Mockito.<PollTimeInfo.Status>any());
        pollTimeInfo.setEmail("<EMAIL>");
        pollTimeInfo.setEndTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setId(1);
        pollTimeInfo.setStartTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setStatus(PollTimeInfo.Status.PROCESSING);
        Optional<PollTimeInfo> ofResult = Optional.of(pollTimeInfo);

        PollTimeInfo pollTimeInfo2 = new PollTimeInfo();
        pollTimeInfo2.setEmail("<EMAIL>");
        pollTimeInfo2.setEndTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo2.setId(1);
        LocalDate ofResult2 = LocalDate.of(1970, 1, 1);
        pollTimeInfo2.setStartTime(ofResult2.atStartOfDay());
        pollTimeInfo2.setStatus(PollTimeInfo.Status.PROCESSING);
        Optional<PollTimeInfo> ofResult3 = Optional.of(pollTimeInfo2);
        when(pollTimeInfoRepository.findFirstByEmailOrderByStartTimeDesc(Mockito.<String>any())).thenReturn(ofResult3);
        when(pollTimeInfoRepository.findFirstByEmailAndStatusOrderByEndTimeDesc(Mockito.<String>any(),
                Mockito.<PollTimeInfo.Status>any())).thenReturn(ofResult);

        // Act
        LocalDateTime actualFetchTimeToPollResult = pollTimeInfoService.fetchTimeToPoll("<EMAIL>");

        // Assert
        verify(pollTimeInfo).getStatus();
        verify(pollTimeInfo).setEmail(eq("<EMAIL>"));
        verify(pollTimeInfo).setEndTime(isA(LocalDateTime.class));
        verify(pollTimeInfo).setId(eq(1));
        verify(pollTimeInfo).setStartTime(isA(LocalDateTime.class));
        verify(pollTimeInfo).setStatus(eq(PollTimeInfo.Status.PROCESSING));
        verify(pollTimeInfoRepository).findFirstByEmailAndStatusOrderByEndTimeDesc(eq("<EMAIL>"),
                eq(PollTimeInfo.Status.SUCCESS));
        verify(pollTimeInfoRepository).findFirstByEmailOrderByStartTimeDesc(eq("<EMAIL>"));
        assertEquals("00:00", actualFetchTimeToPollResult.toLocalTime().toString());
        LocalDate toLocalDateResult = actualFetchTimeToPollResult.toLocalDate();
        assertEquals("1970-01-01", toLocalDateResult.toString());
        assertSame(ofResult2, toLocalDateResult);
    }
}
