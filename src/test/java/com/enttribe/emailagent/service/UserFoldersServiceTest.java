package com.enttribe.emailagent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.dto.UserFolderDto;
import com.enttribe.emailagent.dto.UserFoldersRecord;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.UserFolders;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.repository.impl.UserFolderDaoImpl;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;

import java.time.LocalDate;
import java.time.ZoneOffset;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {UserFoldersService.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class UserFoldersServiceTest {
    @MockBean
    private EmailUserDao emailUserDao;

    @MockBean
    private EwsService ewsService;

    @MockBean
    private GmailIntegration gmailIntegration;

    @MockBean
    private GraphIntegrationService graphIntegrationService;

    @MockBean
    private UserContextHolder userContextHolder;

    @MockBean
    private UserFolderDaoImpl userFolderDaoImpl;

    @MockBean
    private UserFoldersRepository userFoldersRepository;

    @Autowired
    private UserFoldersService userFoldersService;

    /**
     * Test {@link UserFoldersService#getUsersMailFolders()}.
     * <p>
     * Method under test: {@link UserFoldersService#getUsersMailFolders()}
     */
    @Test
    @DisplayName("Test getUsersMailFolders()")
    void testGetUsersMailFolders() throws Exception {
        // Arrange
        when(userFoldersRepository.findByEmail(Mockito.<String>any())).thenReturn(new ArrayList<>());

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any()))
                .thenThrow(new Exception("Inside @method getUsersMailFolders."));

        // Act
        List<UserFolders> actualUsersMailFolders = userFoldersService.getUsersMailFolders();

        // Assert
        verify(userFoldersRepository).findByEmail(eq("<EMAIL>"));
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        assertTrue(actualUsersMailFolders.isEmpty());
    }

    /**
     * Test {@link UserFoldersService#getUsersMailFolders()}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()} add {@link HashMap#HashMap()}.</li>
     *   <li>Then return Empty.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#getUsersMailFolders()}
     */
    @Test
    @DisplayName("Test getUsersMailFolders(); given ArrayList() add HashMap(); then return Empty")
    void testGetUsersMailFolders_givenArrayListAddHashMap_thenReturnEmpty() throws Exception {
        // Arrange
        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Inside @method getUsersMailFolders.");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        ArrayList<UserFolders> userFoldersList = new ArrayList<>();
        userFoldersList.add(userFolders);
        when(userFoldersRepository.findByEmail(Mockito.<String>any())).thenReturn(userFoldersList);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        ArrayList<Map<String, String>> mapList = new ArrayList<>();
        mapList.add(new HashMap<>());
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any())).thenReturn(mapList);

        // Act
        List<UserFolders> actualUsersMailFolders = userFoldersService.getUsersMailFolders();

        // Assert
        verify(userFoldersRepository).findByEmail(eq("<EMAIL>"));
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        assertTrue(actualUsersMailFolders.isEmpty());
    }

    /**
     * Test {@link UserFoldersService#getUsersMailFolders()}.
     * <ul>
     *   <li>Given {@link UserFolders} (default constructor) Active is
     * {@code false}.</li>
     *   <li>Then return size is two.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#getUsersMailFolders()}
     */
    @Test
    @DisplayName("Test getUsersMailFolders(); given UserFolders (default constructor) Active is 'false'; then return size is two")
    void testGetUsersMailFolders_givenUserFoldersActiveIsFalse_thenReturnSizeIsTwo() throws Exception {
        // Arrange
        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Inside @method getUsersMailFolders.");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        UserFolders userFolders2 = new UserFolders();
        userFolders2.setActive(false);
        userFolders2.setDisplayName("Getting folders for : {}");
        userFolders2.setEmail("<EMAIL>");
        userFolders2.setFolderId("Inside @method getUsersMailFolders.");
        userFolders2.setId(2);

        ArrayList<UserFolders> userFoldersList = new ArrayList<>();
        userFoldersList.add(userFolders2);
        userFoldersList.add(userFolders);
        doNothing().when(userFoldersRepository).delete(Mockito.<UserFolders>any());
        when(userFoldersRepository.findByEmail(Mockito.<String>any())).thenReturn(userFoldersList);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<UserFolders> actualUsersMailFolders = userFoldersService.getUsersMailFolders();

        // Assert
        verify(userFoldersRepository, atLeast(1)).findByEmail(eq("<EMAIL>"));
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        verify(userFoldersRepository, atLeast(1)).delete(Mockito.<UserFolders>any());
        assertEquals(2, actualUsersMailFolders.size());
        UserFolders getResult = actualUsersMailFolders.get(0);
        assertEquals("Getting folders for : {}", getResult.getDisplayName());
        assertEquals("Inside @method getUsersMailFolders.", getResult.getFolderId());
        assertEquals("<EMAIL>", getResult.getEmail());
        assertEquals(2, getResult.getId().intValue());
        assertFalse(getResult.isActive());
        assertSame(userFolders, actualUsersMailFolders.get(1));
    }

    /**
     * Test {@link UserFoldersService#getUsersMailFolders()}.
     * <ul>
     *   <li>Then return Empty.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#getUsersMailFolders()}
     */
    @Test
    @DisplayName("Test getUsersMailFolders(); then return Empty")
    void testGetUsersMailFolders_thenReturnEmpty() throws Exception {
        // Arrange
        when(userFoldersRepository.findByEmail(Mockito.<String>any())).thenReturn(new ArrayList<>());

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<UserFolders> actualUsersMailFolders = userFoldersService.getUsersMailFolders();

        // Assert
        verify(userFoldersRepository, atLeast(1)).findByEmail(eq("<EMAIL>"));
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        assertTrue(actualUsersMailFolders.isEmpty());
    }

    /**
     * Test {@link UserFoldersService#getUsersMailFolders()}.
     * <ul>
     *   <li>Then return size is one.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#getUsersMailFolders()}
     */
    @Test
    @DisplayName("Test getUsersMailFolders(); then return size is one")
    void testGetUsersMailFolders_thenReturnSizeIsOne() throws Exception {
        // Arrange
        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Inside @method getUsersMailFolders.");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        ArrayList<UserFolders> userFoldersList = new ArrayList<>();
        userFoldersList.add(userFolders);
        doNothing().when(userFoldersRepository).delete(Mockito.<UserFolders>any());
        when(userFoldersRepository.findByEmail(Mockito.<String>any())).thenReturn(userFoldersList);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<UserFolders> actualUsersMailFolders = userFoldersService.getUsersMailFolders();

        // Assert
        verify(userFoldersRepository, atLeast(1)).findByEmail(eq("<EMAIL>"));
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        verify(userFoldersRepository).delete(isA(UserFolders.class));
        assertEquals(1, actualUsersMailFolders.size());
        UserFolders getResult = actualUsersMailFolders.get(0);
        assertEquals("42", getResult.getFolderId());
        assertEquals("Inside @method getUsersMailFolders.", getResult.getDisplayName());
        assertEquals("<EMAIL>", getResult.getEmail());
        assertEquals(1, getResult.getId().intValue());
        assertTrue(getResult.isActive());
    }

    /**
     * Test {@link UserFoldersService#getUsersMailFolders()}.
     * <ul>
     *   <li>Then return size is three.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#getUsersMailFolders()}
     */
    @Test
    @DisplayName("Test getUsersMailFolders(); then return size is three")
    void testGetUsersMailFolders_thenReturnSizeIsThree() throws Exception {
        // Arrange
        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Inside @method getUsersMailFolders.");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        UserFolders userFolders2 = new UserFolders();
        userFolders2.setActive(false);
        userFolders2.setDisplayName("Getting folders for : {}");
        userFolders2.setEmail("<EMAIL>");
        userFolders2.setFolderId("Inside @method getUsersMailFolders.");
        userFolders2.setId(2);

        UserFolders userFolders3 = new UserFolders();
        userFolders3.setActive(true);
        userFolders3.setDisplayName("Inside @method updateUsersFolder.");
        userFolders3.setEmail("<EMAIL>");
        userFolders3.setFolderId("Getting folders for : {}");
        userFolders3.setId(3);

        ArrayList<UserFolders> userFoldersList = new ArrayList<>();
        userFoldersList.add(userFolders3);
        userFoldersList.add(userFolders2);
        userFoldersList.add(userFolders);
        doNothing().when(userFoldersRepository).delete(Mockito.<UserFolders>any());
        when(userFoldersRepository.findByEmail(Mockito.<String>any())).thenReturn(userFoldersList);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<UserFolders> actualUsersMailFolders = userFoldersService.getUsersMailFolders();

        // Assert
        verify(userFoldersRepository, atLeast(1)).findByEmail(eq("<EMAIL>"));
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        verify(userFoldersRepository, atLeast(1)).delete(Mockito.<UserFolders>any());
        assertEquals(3, actualUsersMailFolders.size());
        assertSame(userFolders3, actualUsersMailFolders.get(2));
    }

    /**
     * Test {@link UserFoldersService#refreshUsersFolder(String)}.
     * <p>
     * Method under test: {@link UserFoldersService#refreshUsersFolder(String)}
     */
    @Test
    @DisplayName("Test refreshUsersFolder(String)")
    void testRefreshUsersFolder() throws Exception {
        // Arrange
        when(userFoldersRepository.findByEmail(Mockito.<String>any())).thenReturn(new ArrayList<>());
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any()))
                .thenThrow(new Exception("Inside @method updateUsersFolder."));

        // Act
        boolean actualRefreshUsersFolderResult = userFoldersService.refreshUsersFolder("<EMAIL>");

        // Assert
        verify(userFoldersRepository).findByEmail(eq("<EMAIL>"));
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        assertFalse(actualRefreshUsersFolderResult);
    }

    /**
     * Test {@link UserFoldersService#refreshUsersFolder(String)}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()} add {@link HashMap#HashMap()}.</li>
     *   <li>Then return {@code false}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#refreshUsersFolder(String)}
     */
    @Test
    @DisplayName("Test refreshUsersFolder(String); given ArrayList() add HashMap(); then return 'false'")
    void testRefreshUsersFolder_givenArrayListAddHashMap_thenReturnFalse() throws Exception {
        // Arrange
        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Inside @method updateUsersFolder.");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        ArrayList<UserFolders> userFoldersList = new ArrayList<>();
        userFoldersList.add(userFolders);
        when(userFoldersRepository.findByEmail(Mockito.<String>any())).thenReturn(userFoldersList);

        ArrayList<Map<String, String>> mapList = new ArrayList<>();
        mapList.add(new HashMap<>());
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any())).thenReturn(mapList);

        // Act
        boolean actualRefreshUsersFolderResult = userFoldersService.refreshUsersFolder("<EMAIL>");

        // Assert
        verify(userFoldersRepository).findByEmail(eq("<EMAIL>"));
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        assertFalse(actualRefreshUsersFolderResult);
    }

    /**
     * Test {@link UserFoldersService#refreshUsersFolder(String)}.
     * <ul>
     *   <li>Then calls {@link CrudRepository#delete(Object)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#refreshUsersFolder(String)}
     */
    @Test
    @DisplayName("Test refreshUsersFolder(String); then calls delete(Object)")
    void testRefreshUsersFolder_thenCallsDelete() throws Exception {
        // Arrange
        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Inside @method updateUsersFolder.");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        ArrayList<UserFolders> userFoldersList = new ArrayList<>();
        userFoldersList.add(userFolders);
        doNothing().when(userFoldersRepository).delete(Mockito.<UserFolders>any());
        when(userFoldersRepository.findByEmail(Mockito.<String>any())).thenReturn(userFoldersList);
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        boolean actualRefreshUsersFolderResult = userFoldersService.refreshUsersFolder("<EMAIL>");

        // Assert
        verify(userFoldersRepository).findByEmail(eq("<EMAIL>"));
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        verify(userFoldersRepository).delete(isA(UserFolders.class));
        assertTrue(actualRefreshUsersFolderResult);
    }

    /**
     * Test {@link UserFoldersService#refreshUsersFolder(String)}.
     * <ul>
     *   <li>Then return {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#refreshUsersFolder(String)}
     */
    @Test
    @DisplayName("Test refreshUsersFolder(String); then return 'true'")
    void testRefreshUsersFolder_thenReturnTrue() throws Exception {
        // Arrange
        when(userFoldersRepository.findByEmail(Mockito.<String>any())).thenReturn(new ArrayList<>());
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        boolean actualRefreshUsersFolderResult = userFoldersService.refreshUsersFolder("<EMAIL>");

        // Assert
        verify(userFoldersRepository).findByEmail(eq("<EMAIL>"));
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        assertTrue(actualRefreshUsersFolderResult);
    }

    /**
     * Test {@link UserFoldersService#updateFolderStatus(List)}.
     * <ul>
     *   <li>Given {@link UserFoldersRepository}.</li>
     *   <li>When {@link ArrayList#ArrayList()}.</li>
     *   <li>Then return {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#updateFolderStatus(List)}
     */
    @Test
    @DisplayName("Test updateFolderStatus(List); given UserFoldersRepository; when ArrayList(); then return 'true'")
    void testUpdateFolderStatus_givenUserFoldersRepository_whenArrayList_thenReturnTrue() {
        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        boolean actualUpdateFolderStatusResult = userFoldersService.updateFolderStatus(new ArrayList<>());

        // Assert
        verify(userContextHolder).getCurrentUser();
        assertTrue(actualUpdateFolderStatusResult);
    }

    /**
     * Test {@link UserFoldersService#updateFolderStatus(List)}.
     * <ul>
     *   <li>Then calls {@link CrudRepository#save(Object)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#updateFolderStatus(List)}
     */
    @Test
    @DisplayName("Test updateFolderStatus(List); then calls save(Object)")
    void testUpdateFolderStatus_thenCallsSave() {
        // Arrange
        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        Optional<UserFolders> ofResult = Optional.of(userFolders);

        UserFolders userFolders2 = new UserFolders();
        userFolders2.setActive(true);
        userFolders2.setDisplayName("Display Name");
        userFolders2.setEmail("<EMAIL>");
        userFolders2.setFolderId("42");
        userFolders2.setId(1);
        when(userFoldersRepository.save(Mockito.<UserFolders>any())).thenReturn(userFolders2);
        when(userFoldersRepository.findByIdAndEmail(Mockito.<Integer>any(), Mockito.<String>any())).thenReturn(ofResult);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        ArrayList<UserFoldersRecord> foldersRecords = new ArrayList<>();
        foldersRecords.add(new UserFoldersRecord(1, true));

        // Act
        boolean actualUpdateFolderStatusResult = userFoldersService.updateFolderStatus(foldersRecords);

        // Assert
        verify(userFoldersRepository).findByIdAndEmail(eq(1), eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        verify(userFoldersRepository).save(isA(UserFolders.class));
        assertTrue(actualUpdateFolderStatusResult);
    }

    /**
     * Test {@link UserFoldersService#updateFolderStatus(List)}.
     * <ul>
     *   <li>Then calls {@link CrudRepository#save(Object)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#updateFolderStatus(List)}
     */
    @Test
    @DisplayName("Test updateFolderStatus(List); then calls save(Object)")
    void testUpdateFolderStatus_thenCallsSave2() {
        // Arrange
        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        Optional<UserFolders> ofResult = Optional.of(userFolders);

        UserFolders userFolders2 = new UserFolders();
        userFolders2.setActive(true);
        userFolders2.setDisplayName("Display Name");
        userFolders2.setEmail("<EMAIL>");
        userFolders2.setFolderId("42");
        userFolders2.setId(1);
        when(userFoldersRepository.save(Mockito.<UserFolders>any())).thenReturn(userFolders2);
        when(userFoldersRepository.findByIdAndEmail(Mockito.<Integer>any(), Mockito.<String>any())).thenReturn(ofResult);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        ArrayList<UserFoldersRecord> foldersRecords = new ArrayList<>();
        foldersRecords.add(new UserFoldersRecord(1, true));
        foldersRecords.add(new UserFoldersRecord(1, true));

        // Act
        boolean actualUpdateFolderStatusResult = userFoldersService.updateFolderStatus(foldersRecords);

        // Assert
        verify(userFoldersRepository, atLeast(1)).findByIdAndEmail(eq(1), eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        verify(userFoldersRepository, atLeast(1)).save(isA(UserFolders.class));
        assertTrue(actualUpdateFolderStatusResult);
    }

    /**
     * Test {@link UserFoldersService#updateFolderStatus(List)}.
     * <ul>
     *   <li>Then return {@code false}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#updateFolderStatus(List)}
     */
    @Test
    @DisplayName("Test updateFolderStatus(List); then return 'false'")
    void testUpdateFolderStatus_thenReturnFalse() {
        // Arrange
        Optional<UserFolders> emptyResult = Optional.empty();
        when(userFoldersRepository.findByIdAndEmail(Mockito.<Integer>any(), Mockito.<String>any())).thenReturn(emptyResult);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        ArrayList<UserFoldersRecord> foldersRecords = new ArrayList<>();
        foldersRecords.add(new UserFoldersRecord(1, true));

        // Act
        boolean actualUpdateFolderStatusResult = userFoldersService.updateFolderStatus(foldersRecords);

        // Assert
        verify(userFoldersRepository).findByIdAndEmail(eq(1), eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        assertFalse(actualUpdateFolderStatusResult);
    }

    /**
     * Test {@link UserFoldersService#saveUsersFolder()}.
     * <ul>
     *   <li>Given {@link GraphIntegrationService}.</li>
     *   <li>Then calls {@link ListCrudRepository#findAll()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#saveUsersFolder()}
     */
    @Test
    @DisplayName("Test saveUsersFolder(); given GraphIntegrationService; then calls findAll()")
    void testSaveUsersFolder_givenGraphIntegrationService_thenCallsFindAll() throws Exception {
        // Arrange
        when(emailUserDao.findAll()).thenReturn(new ArrayList<>());

        // Act
        userFoldersService.saveUsersFolder();

        // Assert that nothing has changed
        verify(emailUserDao).findAll();
    }

    /**
     * Test {@link UserFoldersService#saveUsersFolder()}.
     * <ul>
     *   <li>Then throw {@link Exception}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#saveUsersFolder()}
     */
    @Test
    @DisplayName("Test saveUsersFolder(); then throw Exception")
    void testSaveUsersFolder_thenThrowException() throws Exception {
        // Arrange
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any()))
                .thenThrow(new Exception("ConversationHistory"));

        EmailUser emailUser = new EmailUser();
        emailUser.setBatchId("42");
        emailUser.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setDeleted(true);
        emailUser.setEmail("<EMAIL>");
        emailUser.setId(1);
        emailUser.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setName("ConversationHistory");
        emailUser.setPhoneNumber("6625550144");
        emailUser.setQueueName("ConversationHistory");
        emailUser.setType("ConversationHistory");
        emailUser.setUserId("42");
        emailUser.setUserOID("ConversationHistory");

        ArrayList<EmailUser> emailUserList = new ArrayList<>();
        emailUserList.add(emailUser);
        when(emailUserDao.findAll()).thenReturn(emailUserList);

        // Act and Assert
        assertThrows(Exception.class, () -> userFoldersService.saveUsersFolder());
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        verify(emailUserDao).findAll();
    }

    /**
     * Test {@link UserFoldersService#saveUsersFolder()}.
     * <ul>
     *   <li>Then calls
     * {@link GraphIntegrationService#getUsersMailFolders(String)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserFoldersService#saveUsersFolder()}
     */
    @Test
    @DisplayName("Test saveUsersFolder(); then calls getUsersMailFolders(String)")
    void testSaveUsersFolder_thenCallsGetUsersMailFolders() throws Exception {
        // Arrange
        when(graphIntegrationService.getUsersMailFolders(Mockito.<String>any())).thenReturn(new ArrayList<>());

        EmailUser emailUser = new EmailUser();
        emailUser.setBatchId("42");
        emailUser.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setDeleted(true);
        emailUser.setEmail("<EMAIL>");
        emailUser.setId(1);
        emailUser.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailUser.setName("ConversationHistory");
        emailUser.setPhoneNumber("6625550144");
        emailUser.setQueueName("ConversationHistory");
        emailUser.setType("ConversationHistory");
        emailUser.setUserId("42");
        emailUser.setUserOID("ConversationHistory");

        ArrayList<EmailUser> emailUserList = new ArrayList<>();
        emailUserList.add(emailUser);
        when(emailUserDao.findAll()).thenReturn(emailUserList);

        // Act
        userFoldersService.saveUsersFolder();

        // Assert that nothing has changed
        verify(graphIntegrationService).getUsersMailFolders(eq("<EMAIL>"));
        verify(emailUserDao).findAll();
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()} add array of {@link Object} with
     * {@code 42} and {@code 42}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); given ArrayList() add array of Object with '42' and '42'")
    void testGetFoldersGroupByEmail_givenArrayListAddArrayOfObjectWith42And42() {
        // Arrange
        ArrayList<Object[]> objectArrayList = new ArrayList<>();
        objectArrayList.add(new Object[]{"42", "42"});
        when(userFolderDaoImpl.getFoldersGroupByEmail(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(objectArrayList);

        // Act
        List<UserFolderDto> actualFoldersGroupByEmail = userFoldersService.getFoldersGroupByEmail(1, 1, new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersGroupByEmail(isA(Map.class), eq(1), eq(1));
        assertNull(actualFoldersGroupByEmail);
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()} add array of {@link Object} with
     * {@code 42} and {@code 42}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); given ArrayList() add array of Object with '42' and '42'")
    void testGetFoldersGroupByEmail_givenArrayListAddArrayOfObjectWith42And422() {
        // Arrange
        ArrayList<Object[]> objectArrayList = new ArrayList<>();
        objectArrayList.add(new Object[]{"42", "42", 2});
        when(userFolderDaoImpl.getFoldersGroupByEmail(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(objectArrayList);

        // Act
        List<UserFolderDto> actualFoldersGroupByEmail = userFoldersService.getFoldersGroupByEmail(1, 1, new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersGroupByEmail(isA(Map.class), eq(1), eq(1));
        assertNull(actualFoldersGroupByEmail);
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()} add array of {@link Object} with
     * {@code 42} and {@code null}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); given ArrayList() add array of Object with '42' and 'null'")
    void testGetFoldersGroupByEmail_givenArrayListAddArrayOfObjectWith42AndNull() {
        // Arrange
        ArrayList<Object[]> objectArrayList = new ArrayList<>();
        objectArrayList.add(new Object[]{"42", null});
        when(userFolderDaoImpl.getFoldersGroupByEmail(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(objectArrayList);

        // Act
        List<UserFolderDto> actualFoldersGroupByEmail = userFoldersService.getFoldersGroupByEmail(1, 1, new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersGroupByEmail(isA(Map.class), eq(1), eq(1));
        assertNull(actualFoldersGroupByEmail);
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()} add array of {@link Object} with
     * {@code 42} and two.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); given ArrayList() add array of Object with '42' and two")
    void testGetFoldersGroupByEmail_givenArrayListAddArrayOfObjectWith42AndTwo() {
        // Arrange
        ArrayList<Object[]> objectArrayList = new ArrayList<>();
        objectArrayList.add(new Object[]{"42", 2});
        when(userFolderDaoImpl.getFoldersGroupByEmail(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(objectArrayList);

        // Act
        List<UserFolderDto> actualFoldersGroupByEmail = userFoldersService.getFoldersGroupByEmail(1, 1, new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersGroupByEmail(isA(Map.class), eq(1), eq(1));
        assertNull(actualFoldersGroupByEmail);
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()} add array of {@link Object} with
     * {@code 42}.</li>
     *   <li>Then return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); given ArrayList() add array of Object with '42'; then return 'null'")
    void testGetFoldersGroupByEmail_givenArrayListAddArrayOfObjectWith42_thenReturnNull() {
        // Arrange
        ArrayList<Object[]> objectArrayList = new ArrayList<>();
        objectArrayList.add(new Object[]{"42"});
        when(userFolderDaoImpl.getFoldersGroupByEmail(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(objectArrayList);

        // Act
        List<UserFolderDto> actualFoldersGroupByEmail = userFoldersService.getFoldersGroupByEmail(1, 1, new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersGroupByEmail(isA(Map.class), eq(1), eq(1));
        assertNull(actualFoldersGroupByEmail);
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()} add array of {@link Object} with two
     * and {@code 42}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); given ArrayList() add array of Object with two and '42'")
    void testGetFoldersGroupByEmail_givenArrayListAddArrayOfObjectWithTwoAnd42() {
        // Arrange
        ArrayList<Object[]> objectArrayList = new ArrayList<>();
        objectArrayList.add(new Object[]{2, "42"});
        when(userFolderDaoImpl.getFoldersGroupByEmail(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(objectArrayList);

        // Act
        List<UserFolderDto> actualFoldersGroupByEmail = userFoldersService.getFoldersGroupByEmail(1, 1, new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersGroupByEmail(isA(Map.class), eq(1), eq(1));
        assertNull(actualFoldersGroupByEmail);
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()} add empty array of
     * {@link Object}.</li>
     *   <li>Then return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); given ArrayList() add empty array of Object; then return 'null'")
    void testGetFoldersGroupByEmail_givenArrayListAddEmptyArrayOfObject_thenReturnNull() {
        // Arrange
        ArrayList<Object[]> objectArrayList = new ArrayList<>();
        objectArrayList.add(new Object[]{});
        when(userFolderDaoImpl.getFoldersGroupByEmail(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(objectArrayList);

        // Act
        List<UserFolderDto> actualFoldersGroupByEmail = userFoldersService.getFoldersGroupByEmail(1, 1, new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersGroupByEmail(isA(Map.class), eq(1), eq(1));
        assertNull(actualFoldersGroupByEmail);
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>Then return Empty.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); then return Empty")
    void testGetFoldersGroupByEmail_thenReturnEmpty() {
        // Arrange
        when(userFolderDaoImpl.getFoldersGroupByEmail(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(new ArrayList<>());

        // Act
        List<UserFolderDto> actualFoldersGroupByEmail = userFoldersService.getFoldersGroupByEmail(1, 1, new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersGroupByEmail(isA(Map.class), eq(1), eq(1));
        assertTrue(actualFoldersGroupByEmail.isEmpty());
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>Then return first InActive Empty.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); then return first InActive Empty")
    void testGetFoldersGroupByEmail_thenReturnFirstInActiveEmpty() {
        // Arrange
        ArrayList<Object[]> objectArrayList = new ArrayList<>();
        objectArrayList.add(new Object[]{"42", "42", null});
        when(userFolderDaoImpl.getFoldersGroupByEmail(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(objectArrayList);

        // Act
        List<UserFolderDto> actualFoldersGroupByEmail = userFoldersService.getFoldersGroupByEmail(1, 1, new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersGroupByEmail(isA(Map.class), eq(1), eq(1));
        assertEquals(1, actualFoldersGroupByEmail.size());
        UserFolderDto getResult = actualFoldersGroupByEmail.get(0);
        assertEquals("42", getResult.getEmail());
        List<String> active = getResult.getActive();
        assertEquals(1, active.size());
        assertEquals("42", active.get(0));
        assertTrue(getResult.getInActive().isEmpty());
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>Then return size is one.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); then return size is one")
    void testGetFoldersGroupByEmail_thenReturnSizeIsOne() {
        // Arrange
        ArrayList<Object[]> objectArrayList = new ArrayList<>();
        objectArrayList.add(new Object[]{"42", "42", "42"});
        when(userFolderDaoImpl.getFoldersGroupByEmail(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(objectArrayList);

        // Act
        List<UserFolderDto> actualFoldersGroupByEmail = userFoldersService.getFoldersGroupByEmail(1, 1, new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersGroupByEmail(isA(Map.class), eq(1), eq(1));
        assertEquals(1, actualFoldersGroupByEmail.size());
        UserFolderDto getResult = actualFoldersGroupByEmail.get(0);
        assertEquals("42", getResult.getEmail());
        List<String> active = getResult.getActive();
        assertEquals(1, active.size());
        assertEquals("42", active.get(0));
    }

    /**
     * Test
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}.
     * <ul>
     *   <li>When {@code null}.</li>
     *   <li>Then return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersGroupByEmail(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test getFoldersGroupByEmail(Integer, Integer, Map); when 'null'; then return 'null'")
    void testGetFoldersGroupByEmail_whenNull_thenReturnNull() {
        // Arrange, Act and Assert
        assertNull(userFoldersService.getFoldersGroupByEmail(null, 1, new HashMap<>()));
        assertNull(userFoldersService.getFoldersGroupByEmail(1, null, new HashMap<>()));
    }

    /**
     * Test {@link UserFoldersService#getFoldersCountGroupByEmail(Map)}.
     * <p>
     * Method under test:
     * {@link UserFoldersService#getFoldersCountGroupByEmail(Map)}
     */
    @Test
    @DisplayName("Test getFoldersCountGroupByEmail(Map)")
    void testGetFoldersCountGroupByEmail() {
        // Arrange
        when(userFolderDaoImpl.getFoldersCountGroupByEmail(Mockito.<Map<String, Object>>any()))
                .thenReturn(new ArrayList<>());

        // Act
        long actualFoldersCountGroupByEmail = userFoldersService.getFoldersCountGroupByEmail(new HashMap<>());

        // Assert
        verify(userFolderDaoImpl).getFoldersCountGroupByEmail(isA(Map.class));
        assertEquals(0L, actualFoldersCountGroupByEmail);
    }
}
