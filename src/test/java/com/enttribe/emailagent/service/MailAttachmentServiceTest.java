package com.enttribe.emailagent.service;

import com.enttribe.emailagent.dto.EmailAttachment;

import java.util.List;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {MailAttachmentService.class})
@ExtendWith(SpringExtension.class)
class MailAttachmentServiceTest {
    @Autowired
    private MailAttachmentService mailAttachmentService;

    /**
     * Test {@link MailAttachmentService#fetchAttachmentsGraph(String, String)}.
     * <p>
     * Method under test:
     * {@link MailAttachmentService#fetchAttachmentsGraph(String, String)}
     */
    @Test
    @DisplayName("Test fetchAttachmentsGraph(String, String)")
    @Disabled("TODO: Complete this test")
    void testFetchAttachmentsGraph() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Unsupported virtual threads call detected.
        //   Diffblue Cover does not support writing tests for methods that use
        //   virtual threads.

        // Arrange
        // TODO: Populate arranged inputs
        String emailId = "";
        String messageId = "";

        // Act
        List<EmailAttachment> actualFetchAttachmentsGraphResult = this.mailAttachmentService.fetchAttachmentsGraph(emailId,
                messageId);

        // Assert
        // TODO: Add assertions on result
    }
}
