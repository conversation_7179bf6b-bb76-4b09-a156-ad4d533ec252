package com.enttribe.emailagent.service;

import java.util.Map;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {GraphIntegrationService.class})
@ExtendWith(SpringExtension.class)
class GraphIntegrationServiceTest {
    @Autowired
    private GraphIntegrationService graphIntegrationService;

    /**
     * Test
     * {@link GraphIntegrationService#summarizeMailAttachment(String, String, String, String, String, String)}.
     * <p>
     * Method under test:
     * {@link GraphIntegrationService#summarizeMailAttachment(String, String, String, String, String, String)}
     */
    @Test
    @DisplayName("Test summarizeMailAttachment(String, String, String, String, String, String)")
    @Disabled("TODO: Complete this test")
    void testSummarizeMailAttachment() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Unsupported virtual threads call detected.
        //   Diffblue Cover does not support writing tests for methods that use
        //   virtual threads.

        // Arrange
        // TODO: Populate arranged inputs
        String conversationId = "";
        String internetMessageId = "";
        String emailId = "";
        String messageId = "";
        String userId = "";
        String subject = "";

        // Act
        this.graphIntegrationService.summarizeMailAttachment(conversationId, internetMessageId, emailId, messageId, userId,
                subject);

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Test {@link GraphIntegrationService#acceptMeeting(String)}.
     * <p>
     * Method under test: {@link GraphIntegrationService#acceptMeeting(String)}
     */
    @Test
    @DisplayName("Test acceptMeeting(String)")
    @Disabled("TODO: Complete this test")
    void testAcceptMeeting() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Unsupported virtual threads call detected.
        //   Diffblue Cover does not support writing tests for methods that use
        //   virtual threads.

        // Arrange
        // TODO: Populate arranged inputs
        String eventId = "";

        // Act
        Map<String, String> actualAcceptMeetingResult = this.graphIntegrationService.acceptMeeting(eventId);

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Test getters and setters.
     * <p>
     * Methods under test:
     * <ul>
     *   <li>{@link GraphIntegrationService#setAnswerUrlUrl(String)}
     *   <li>{@link GraphIntegrationService#setAttachmentFilePath(String)}
     *   <li>{@link GraphIntegrationService#setDocumentUploadUrl(String)}
     *   <li>{@link GraphIntegrationService#setSummaryAnswerUrl(String)}
     *   <li>{@link GraphIntegrationService#setSummaryUploadUrl(String)}
     * </ul>
     */
    @Test
    @DisplayName("Test getters and setters")
    void testGettersAndSetters() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     GraphIntegrationService.aiService
        //     GraphIntegrationService.allowOutsiders
        //     GraphIntegrationService.answerUrl
        //     GraphIntegrationService.attachmentFilePath
        //     GraphIntegrationService.bodyTokenLimit
        //     GraphIntegrationService.emailStatsDao
        //     GraphIntegrationService.emailUserDao
        //     GraphIntegrationService.foldersRepository
        //     GraphIntegrationService.iEmailStatsDao
        //     GraphIntegrationService.mailSummaryDao
        //     GraphIntegrationService.meetingDao
        //     GraphIntegrationService.meetingSummaryDao
        //     GraphIntegrationService.orgDomainName
        //     GraphIntegrationService.pollTimeInfoService
        //     GraphIntegrationService.pollingMode
        //     GraphIntegrationService.preferencesDao
        //     GraphIntegrationService.s3BucketName
        //     GraphIntegrationService.s3Service
        //     GraphIntegrationService.summaryAnswerUrl
        //     GraphIntegrationService.summaryUploadUrl
        //     GraphIntegrationService.supportedFormats
        //     GraphIntegrationService.threadDao
        //     GraphIntegrationService.tokenUtils
        //     GraphIntegrationService.uploadUrl
        //     GraphIntegrationService.userContextHolder
        //     GraphIntegrationService.userMailAttachmentRepository

        // Arrange
        GraphIntegrationService graphIntegrationService = new GraphIntegrationService();

        // Act
        graphIntegrationService.setAnswerUrlUrl("https://example.org/example");
        graphIntegrationService.setAttachmentFilePath("/directory/foo.txt");
        graphIntegrationService.setDocumentUploadUrl("https://example.org/example");
        graphIntegrationService.setSummaryAnswerUrl("https://example.org/example");
        graphIntegrationService.setSummaryUploadUrl("https://example.org/example");
    }
}
