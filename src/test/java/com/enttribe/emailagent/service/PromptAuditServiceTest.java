package com.enttribe.emailagent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.service.PromptAuditService.AuditRequest;

import java.io.UnsupportedEncodingException;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class PromptAuditServiceTest {
    @Autowired
    private PromptAuditService.AuditRequest auditRequest;

    @Autowired
    private PromptAuditService promptAuditService;

    @MockBean
    private RestTemplate restTemplate;

    /**
     * Test AuditRequest {@link AuditRequest#AuditRequest(String, String, String)}.
     * <p>
     * Method under test:
     * {@link PromptAuditService.AuditRequest#AuditRequest(String, String, String)}
     */
    @Test
    @DisplayName("Test AuditRequest new AuditRequest(String, String, String)")
    void testAuditRequestNewAuditRequest() {
        // Arrange and Act
        PromptAuditService.AuditRequest actualAuditRequest = new PromptAuditService.AuditRequest("Engine", "Request Text",
                "Response Text");

        // Assert
        assertEquals(" email-assistant", actualAuditRequest.getTags());
        assertEquals("Engine", actualAuditRequest.getEngine());
        assertEquals("Request Text", actualAuditRequest.getRequestText());
        assertEquals("Response Text", actualAuditRequest.getResponseText());
        assertEquals("email-assistant", actualAuditRequest.getAgentName());
        assertNull(actualAuditRequest.getResponseTime());
        assertNull(actualAuditRequest.getCompletionToken());
        assertNull(actualAuditRequest.getPromptToken());
        assertNull(actualAuditRequest.getTotalToken());
        assertNull(actualAuditRequest.getFailureCategory());
        assertNull(actualAuditRequest.getQuestion());
        assertNull(actualAuditRequest.getRemark());
        assertNull(actualAuditRequest.getStatus());
        assertNull(actualAuditRequest.getToolName());
        assertNull(actualAuditRequest.getUuid());
        assertNull(actualAuditRequest.getRequestEndTime());
        assertNull(actualAuditRequest.getRequestStartTime());
    }

    /**
     * Test {@link PromptAuditService#sendPromptAudit(AuditRequest)} with
     * {@code AuditRequest}.
     * <p>
     * Method under test:
     * {@link PromptAuditService#sendPromptAudit(PromptAuditService.AuditRequest)}
     */
    @Test
    @DisplayName("Test sendPromptAudit(AuditRequest) with 'AuditRequest'")
    void testSendPromptAuditWithAuditRequest() throws RestClientException {
        // Arrange
        when(restTemplate.postForEntity(Mockito.<String>any(), Mockito.<Object>any(), Mockito.<Class<Object>>any(),
                isA(Object[].class))).thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        // Act
        promptAuditService.sendPromptAudit(new PromptAuditService.AuditRequest());

        // Assert
        verify(restTemplate).postForEntity(eq("${promptAuditUrl}"), isA(Object.class), isA(Class.class),
                isA(Object[].class));
    }

    /**
     * Test {@link PromptAuditService#sendPromptAudit(AuditRequest)} with
     * {@code AuditRequest}.
     * <ul>
     *   <li>Then calls {@link ResponseEntity#getStatusCode()}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link PromptAuditService#sendPromptAudit(PromptAuditService.AuditRequest)}
     */
    @Test
    @DisplayName("Test sendPromptAudit(AuditRequest) with 'AuditRequest'; then calls getStatusCode()")
    void testSendPromptAuditWithAuditRequest_thenCallsGetStatusCode() throws RestClientException {
        // Arrange
        ResponseEntity<Object> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getStatusCode()).thenThrow(new RestClientException("Msg"));
        when(restTemplate.postForEntity(Mockito.<String>any(), Mockito.<Object>any(), Mockito.<Class<Object>>any(),
                isA(Object[].class))).thenReturn(responseEntity);

        // Act
        promptAuditService.sendPromptAudit(new PromptAuditService.AuditRequest());

        // Assert
        verify(responseEntity).getStatusCode();
        verify(restTemplate).postForEntity(eq("${promptAuditUrl}"), isA(Object.class), isA(Class.class),
                isA(Object[].class));
    }

    /**
     * Test {@link PromptAuditService#sendPromptAudit(Resource, String, String)}
     * with {@code Resource}, {@code String}, {@code String}.
     * <p>
     * Method under test:
     * {@link PromptAuditService#sendPromptAudit(Resource, String, String)}
     */
    @Test
    @DisplayName("Test sendPromptAudit(Resource, String, String) with 'Resource', 'String', 'String'")
    @Disabled("TODO: Complete this test")
    void testSendPromptAuditWithResourceStringString() throws UnsupportedEncodingException {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.util.concurrent.RejectedExecutionException
        //       at java.base/java.util.concurrent.ForkJoinPool.submissionQueue(ForkJoinPool.java:2351)
        //       at java.base/java.util.concurrent.ForkJoinPool.poolSubmit(ForkJoinPool.java:2368)
        //       at java.base/java.util.concurrent.ForkJoinPool.execute(ForkJoinPool.java:2847)
        //       at java.base/java.lang.VirtualThread.submitRunContinuation(VirtualThread.java:264)
        //       at java.base/java.lang.VirtualThread.start(VirtualThread.java:560)
        //       at java.base/java.lang.VirtualThread.start(VirtualThread.java:571)
        //       at com.enttribe.emailagent.service.PromptAuditService$$SpringCGLIB$$0.sendPromptAudit(<generated>)
        //   See https://diff.blue/R013 to resolve this issue.

        // Arrange and Act
        promptAuditService.sendPromptAudit(new ByteArrayResource("AXAXAXAX".getBytes("UTF-8")), "User Prompt", "Response");
    }

    /**
     * Test {@link PromptAuditService#sendPromptAudit(String, String, String)} with
     * {@code String}, {@code String}, {@code String}.
     * <p>
     * Method under test:
     * {@link PromptAuditService#sendPromptAudit(String, String, String)}
     */
    @Test
    @DisplayName("Test sendPromptAudit(String, String, String) with 'String', 'String', 'String'")
    void testSendPromptAuditWithStringStringString() throws RestClientException {
        // Arrange
        when(restTemplate.postForEntity(Mockito.<String>any(), Mockito.<Object>any(), Mockito.<Class<Object>>any(),
                isA(Object[].class))).thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        // Act
        promptAuditService.sendPromptAudit("System Prompt", "User Prompt", "Response");

        // Assert
        verify(restTemplate).postForEntity(eq("${promptAuditUrl}"), isA(Object.class), isA(Class.class),
                isA(Object[].class));
    }

    /**
     * Test {@link PromptAuditService#sendPromptAudit(String, String, String)} with
     * {@code String}, {@code String}, {@code String}.
     * <ul>
     *   <li>Then calls {@link ResponseEntity#getStatusCode()}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link PromptAuditService#sendPromptAudit(String, String, String)}
     */
    @Test
    @DisplayName("Test sendPromptAudit(String, String, String) with 'String', 'String', 'String'; then calls getStatusCode()")
    void testSendPromptAuditWithStringStringString_thenCallsGetStatusCode() throws RestClientException {
        // Arrange
        ResponseEntity<Object> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getStatusCode()).thenThrow(new RestClientException("Msg"));
        when(restTemplate.postForEntity(Mockito.<String>any(), Mockito.<Object>any(), Mockito.<Class<Object>>any(),
                isA(Object[].class))).thenReturn(responseEntity);

        // Act
        promptAuditService.sendPromptAudit("System Prompt", "User Prompt", "Response");

        // Assert
        verify(responseEntity).getStatusCode();
        verify(restTemplate).postForEntity(eq("${promptAuditUrl}"), isA(Object.class), isA(Class.class),
                isA(Object[].class));
    }
}
