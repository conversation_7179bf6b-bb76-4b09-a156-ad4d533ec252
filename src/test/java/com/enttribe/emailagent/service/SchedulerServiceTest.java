package com.enttribe.emailagent.service;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ExtendWith(SpringExtension.class)
class SchedulerServiceTest {
    @Autowired
    private SchedulerService schedulerService;

    /**
     * Test {@link SchedulerService#markDeletedEmail()}.
     * <p>
     * Method under test: {@link SchedulerService#markDeletedEmail()}
     */
    @Test
    @DisplayName("Test markDeletedEmail()")
    @Disabled("TODO: Complete this test")
    void testMarkDeletedEmail() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   - com.enttribe.emailagent.service.SchedulerService
        //   when running class:
        //   package com.enttribe.emailagent.service;
        //   @org.springframework.boot.test.context.SpringBootTest
        //   @org.junit.runner.RunWith(value = org.springframework.test.context.junit4.SpringRunner.class) // if JUnit 4
        //   @org.junit.jupiter.api.extension.ExtendWith(value = org.springframework.test.context.junit.jupiter.SpringExtension.class) // if JUnit 5
        //   public class DiffblueFakeClass2 {
        //     @org.springframework.beans.factory.annotation.Autowired com.enttribe.emailagent.service.SchedulerService schedulerService;
        //     @org.junit.Test // if JUnit 4
        //     @org.junit.jupiter.api.Test // if JUnit 5
        //     public void testSpringContextLoads() {}
        //   }
        //   See https://diff.blue/R027 to resolve this issue.

        // Arrange and Act
        schedulerService.markDeletedEmail();
    }
}
