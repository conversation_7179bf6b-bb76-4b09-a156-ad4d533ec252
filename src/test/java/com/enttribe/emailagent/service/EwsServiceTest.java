package com.enttribe.emailagent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.dao.UserMailAttachmentRepository;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.wrapper.MessageWrapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import java.util.Map;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ExtendWith(SpringExtension.class)
class EwsServiceTest {
    @Autowired
    private EwsService ewsService;

    /**
     * Test {@link EwsService#getAutoReplySettingsForUser(String)}.
     * <p>
     * Method under test: {@link EwsService#getAutoReplySettingsForUser(String)}
     */
    @Test
    @DisplayName("Test getAutoReplySettingsForUser(String)")
    @Disabled("TODO: Complete this test")
    void testGetAutoReplySettingsForUser() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@390bdc29 testClass = com.enttribe.emailagent.service.DiffblueFakeClass32, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getAutoReplySettingsForUser("<EMAIL>");
    }

    /**
     * Test {@link EwsService#cancelAutoReply(String)}.
     * <p>
     * Method under test: {@link EwsService#cancelAutoReply(String)}
     */
    @Test
    @DisplayName("Test cancelAutoReply(String)")
    @Disabled("TODO: Complete this test")
    void testCancelAutoReply() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@2da6bb39 testClass = com.enttribe.emailagent.service.DiffblueFakeClass6, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.cancelAutoReply("<EMAIL>");
    }

    /**
     * Test
     * {@link EwsService#setAutoReplyForUser(String, String, String, String, String, String)}.
     * <p>
     * Method under test:
     * {@link EwsService#setAutoReplyForUser(String, String, String, String, String, String)}
     */
    @Test
    @DisplayName("Test setAutoReplyForUser(String, String, String, String, String, String)")
    @Disabled("TODO: Complete this test")
    void testSetAutoReplyForUser() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@4cd8a1ed testClass = com.enttribe.emailagent.service.DiffblueFakeClass84, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.setAutoReplyForUser("<EMAIL>", "Internal Reply Message", "External Reply Message",
                "2020-03-01", "2020-03-01", "UTC");
    }

    /**
     * Test {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     * with {@code email}, {@code mailFolder}, {@code lowerLimit},
     * {@code upperLimit}.
     * <p>
     * Method under test:
     * {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     */
    @Test
    @DisplayName("Test getEmailsOfUser(String, String, Integer, Integer) with 'email', 'mailFolder', 'lowerLimit', 'upperLimit'")
    @Disabled("TODO: Complete this test")
    void testGetEmailsOfUserWithEmailMailFolderLowerLimitUpperLimit() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@49a82d81 testClass = com.enttribe.emailagent.service.DiffblueFakeClass56, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getEmailsOfUser("<EMAIL>", "Mail Folder", 1, 1);
    }

    /**
     * Test {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     * with {@code email}, {@code mailFolder}, {@code lowerLimit},
     * {@code upperLimit}.
     * <ul>
     *   <li>When empty string.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     */
    @Test
    @DisplayName("Test getEmailsOfUser(String, String, Integer, Integer) with 'email', 'mailFolder', 'lowerLimit', 'upperLimit'; when empty string")
    void testGetEmailsOfUserWithEmailMailFolderLowerLimitUpperLimit_whenEmptyString() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertTrue((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getEmailsOfUser("", "Drafts", 1, 1)
                .isEmpty());
    }

    /**
     * Test {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     * with {@code email}, {@code mailFolder}, {@code lowerLimit},
     * {@code upperLimit}.
     * <ul>
     *   <li>When four.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     */
    @Test
    @DisplayName("Test getEmailsOfUser(String, String, Integer, Integer) with 'email', 'mailFolder', 'lowerLimit', 'upperLimit'; when four")
    void testGetEmailsOfUserWithEmailMailFolderLowerLimitUpperLimit_whenFour() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertTrue((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getEmailsOfUser("<EMAIL>", "Drafts", 4, 1)
                .isEmpty());
    }

    /**
     * Test {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     * with {@code email}, {@code mailFolder}, {@code lowerLimit},
     * {@code upperLimit}.
     * <ul>
     *   <li>When {@code <EMAIL>}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     */
    @Test
    @DisplayName("Test getEmailsOfUser(String, String, Integer, Integer) with 'email', 'mailFolder', 'lowerLimit', 'upperLimit'; when '<EMAIL>'")
    void testGetEmailsOfUserWithEmailMailFolderLowerLimitUpperLimit_whenJaneDoeExampleOrg() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertTrue((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getEmailsOfUser("<EMAIL>", "Drafts", 1, 1)
                .isEmpty());
    }

    /**
     * Test {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     * with {@code email}, {@code mailFolder}, {@code lowerLimit},
     * {@code upperLimit}.
     * <ul>
     *   <li>When {@code Mail Folder}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     */
    @Test
    @DisplayName("Test getEmailsOfUser(String, String, Integer, Integer) with 'email', 'mailFolder', 'lowerLimit', 'upperLimit'; when 'Mail Folder'")
    void testGetEmailsOfUserWithEmailMailFolderLowerLimitUpperLimit_whenMailFolder() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertTrue((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getEmailsOfUser("<EMAIL>", "Mail Folder", 1, 1)
                .isEmpty());
    }

    /**
     * Test {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     * with {@code email}, {@code mailFolder}, {@code lowerLimit},
     * {@code upperLimit}.
     * <ul>
     *   <li>When minus one.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EwsService#getEmailsOfUser(String, String, Integer, Integer)}
     */
    @Test
    @DisplayName("Test getEmailsOfUser(String, String, Integer, Integer) with 'email', 'mailFolder', 'lowerLimit', 'upperLimit'; when minus one")
    void testGetEmailsOfUserWithEmailMailFolderLowerLimitUpperLimit_whenMinusOne() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertTrue((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getEmailsOfUser("<EMAIL>", "Drafts", -1, 1)
                .isEmpty());
    }

    /**
     * Test {@link EwsService#getEmailsOfUser(String, String, String, String)} with
     * {@code userId}, {@code email}, {@code timeFilter}, {@code folder}.
     * <p>
     * Method under test:
     * {@link EwsService#getEmailsOfUser(String, String, String, String)}
     */
    @Test
    @DisplayName("Test getEmailsOfUser(String, String, String, String) with 'userId', 'email', 'timeFilter', 'folder'")
    @Disabled("TODO: Complete this test")
    void testGetEmailsOfUserWithUserIdEmailTimeFilterFolder() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@79a4d12a testClass = com.enttribe.emailagent.service.DiffblueFakeClass58, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getEmailsOfUser("42", "<EMAIL>", "Time Filter", "Folder");
    }

    /**
     * Test {@link EwsService#getUnreadEmails(String, String)}.
     * <p>
     * Method under test: {@link EwsService#getUnreadEmails(String, String)}
     */
    @Test
    @DisplayName("Test getUnreadEmails(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetUnreadEmails() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@2f87fd2e testClass = com.enttribe.emailagent.service.DiffblueFakeClass72, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getUnreadEmails("<EMAIL>", "42");
    }

    /**
     * Test {@link EwsService#getDeletedEmailsOfUser(String, String)}.
     * <p>
     * Method under test: {@link EwsService#getDeletedEmailsOfUser(String, String)}
     */
    @Test
    @DisplayName("Test getDeletedEmailsOfUser(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetDeletedEmailsOfUser() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@7ff1593 testClass = com.enttribe.emailagent.service.DiffblueFakeClass46, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getDeletedEmailsOfUser("<EMAIL>", "Time Filter");
    }

    /**
     * Test
     * {@link EwsService#summarizeMailAttachment(String, String, String, String, String, String)}.
     * <p>
     * Method under test:
     * {@link EwsService#summarizeMailAttachment(String, String, String, String, String, String)}
     */
    @Test
    @DisplayName("Test summarizeMailAttachment(String, String, String, String, String, String)")
    @Disabled("TODO: Complete this test")
    void testSummarizeMailAttachment() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@1db3633 testClass = com.enttribe.emailagent.service.DiffblueFakeClass88, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.summarizeMailAttachment("42", "42", "<EMAIL>", "42", "42",
                "Hello from the Dreaming Spires");
    }

    /**
     * Test
     * {@link EwsService#summarizeMailAttachment(String, String, String, String, String, String)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EwsService#summarizeMailAttachment(String, String, String, String, String, String)}
     */
    @Test
    @DisplayName("Test summarizeMailAttachment(String, String, String, String, String, String); then throw ResourceNotFoundException")
    void testSummarizeMailAttachment_thenThrowResourceNotFoundException() throws Exception {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        when(emailUserDao.findByEmail(Mockito.<String>any())).thenThrow(new ResourceNotFoundException("An error occurred"));
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class,
                () -> (new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                        new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                        .summarizeMailAttachment("42", "42", "<EMAIL>", "42", "42",
                                "Hello from the Dreaming Spires"));
        verify(emailUserDao).findByEmail(eq("<EMAIL>"));
    }

    /**
     * Test {@link EwsService#getEmailByMessageId(String, String)}.
     * <p>
     * Method under test: {@link EwsService#getEmailByMessageId(String, String)}
     */
    @Test
    @DisplayName("Test getEmailByMessageId(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetEmailByMessageId() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@6567cf5a testClass = com.enttribe.emailagent.service.DiffblueFakeClass52, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getEmailByMessageId("<EMAIL>", "42");
    }

    /**
     * Test {@link EwsService#getMailDetails(String, String)}.
     * <p>
     * Method under test: {@link EwsService#getMailDetails(String, String)}
     */
    @Test
    @DisplayName("Test getMailDetails(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetMailDetails() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@450ac859 testClass = com.enttribe.emailagent.service.DiffblueFakeClass64, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getMailDetails("<EMAIL>", "42");
    }

    /**
     * Test {@link EwsService#getMailDetails(String, String)}.
     * <ul>
     *   <li>When {@code 42}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#getMailDetails(String, String)}
     */
    @Test
    @DisplayName("Test getMailDetails(String, String); when '42'")
    void testGetMailDetails_when42() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act
        MessageWrapper actualMailDetails = (new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getMailDetails("<EMAIL>", "42");

        // Assert
        assertNull(actualMailDetails.getHashAttachment());
        assertNull(actualMailDetails.getIsDraft());
        assertNull(actualMailDetails.getIsRead());
        assertNull(actualMailDetails.getContent());
        assertNull(actualMailDetails.getConvercationId());
        assertNull(actualMailDetails.getEmail());
        assertNull(actualMailDetails.getFrom());
        assertNull(actualMailDetails.getInternetMessageId());
        assertNull(actualMailDetails.getMessageId());
        assertNull(actualMailDetails.getReplyBody());
        assertNull(actualMailDetails.getSubject());
        assertNull(actualMailDetails.getUserId());
        assertNull(actualMailDetails.getUserName());
        assertNull(actualMailDetails.getAttachmentPathList());
        assertNull(actualMailDetails.getBccRecipients());
        assertNull(actualMailDetails.getCcRecipients());
        assertNull(actualMailDetails.getToRecipients());
    }

    /**
     * Test {@link EwsService#getMailDetails(String, String)}.
     * <ul>
     *   <li>When empty string.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#getMailDetails(String, String)}
     */
    @Test
    @DisplayName("Test getMailDetails(String, String); when empty string")
    void testGetMailDetails_whenEmptyString() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act
        MessageWrapper actualMailDetails = (new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getMailDetails("<EMAIL>", "");

        // Assert
        assertNull(actualMailDetails.getHashAttachment());
        assertNull(actualMailDetails.getIsDraft());
        assertNull(actualMailDetails.getIsRead());
        assertNull(actualMailDetails.getContent());
        assertNull(actualMailDetails.getConvercationId());
        assertNull(actualMailDetails.getEmail());
        assertNull(actualMailDetails.getFrom());
        assertNull(actualMailDetails.getInternetMessageId());
        assertNull(actualMailDetails.getMessageId());
        assertNull(actualMailDetails.getReplyBody());
        assertNull(actualMailDetails.getSubject());
        assertNull(actualMailDetails.getUserId());
        assertNull(actualMailDetails.getUserName());
        assertNull(actualMailDetails.getAttachmentPathList());
        assertNull(actualMailDetails.getBccRecipients());
        assertNull(actualMailDetails.getCcRecipients());
        assertNull(actualMailDetails.getToRecipients());
    }

    /**
     * Test {@link EwsService#getMailDetails(String, String)}.
     * <ul>
     *   <li>When empty string.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#getMailDetails(String, String)}
     */
    @Test
    @DisplayName("Test getMailDetails(String, String); when empty string")
    void testGetMailDetails_whenEmptyString2() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act
        MessageWrapper actualMailDetails = (new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getMailDetails("", "42");

        // Assert
        assertNull(actualMailDetails.getHashAttachment());
        assertNull(actualMailDetails.getIsDraft());
        assertNull(actualMailDetails.getIsRead());
        assertNull(actualMailDetails.getContent());
        assertNull(actualMailDetails.getConvercationId());
        assertNull(actualMailDetails.getEmail());
        assertNull(actualMailDetails.getFrom());
        assertNull(actualMailDetails.getInternetMessageId());
        assertNull(actualMailDetails.getMessageId());
        assertNull(actualMailDetails.getReplyBody());
        assertNull(actualMailDetails.getSubject());
        assertNull(actualMailDetails.getUserId());
        assertNull(actualMailDetails.getUserName());
        assertNull(actualMailDetails.getAttachmentPathList());
        assertNull(actualMailDetails.getBccRecipients());
        assertNull(actualMailDetails.getCcRecipients());
        assertNull(actualMailDetails.getToRecipients());
    }

    /**
     * Test {@link EwsService#getMailDetails(String, String)}.
     * <ul>
     *   <li>When {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#getMailDetails(String, String)}
     */
    @Test
    @DisplayName("Test getMailDetails(String, String); when 'null'")
    void testGetMailDetails_whenNull() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act
        MessageWrapper actualMailDetails = (new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getMailDetails("<EMAIL>", null);

        // Assert
        assertNull(actualMailDetails.getHashAttachment());
        assertNull(actualMailDetails.getIsDraft());
        assertNull(actualMailDetails.getIsRead());
        assertNull(actualMailDetails.getContent());
        assertNull(actualMailDetails.getConvercationId());
        assertNull(actualMailDetails.getEmail());
        assertNull(actualMailDetails.getFrom());
        assertNull(actualMailDetails.getInternetMessageId());
        assertNull(actualMailDetails.getMessageId());
        assertNull(actualMailDetails.getReplyBody());
        assertNull(actualMailDetails.getSubject());
        assertNull(actualMailDetails.getUserId());
        assertNull(actualMailDetails.getUserName());
        assertNull(actualMailDetails.getAttachmentPathList());
        assertNull(actualMailDetails.getBccRecipients());
        assertNull(actualMailDetails.getCcRecipients());
        assertNull(actualMailDetails.getToRecipients());
    }

    /**
     * Test {@link EwsService#deleteMailById(String, String, Boolean)}.
     * <p>
     * Method under test: {@link EwsService#deleteMailById(String, String, Boolean)}
     */
    @Test
    @DisplayName("Test deleteMailById(String, String, Boolean)")
    @Disabled("TODO: Complete this test")
    void testDeleteMailById() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@78516d00 testClass = com.enttribe.emailagent.service.DiffblueFakeClass24, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.deleteMailById("<EMAIL>", "42", true);
    }

    /**
     * Test {@link EwsService#deleteMailById(String, String, Boolean)}.
     * <ul>
     *   <li>When empty string.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#deleteMailById(String, String, Boolean)}
     */
    @Test
    @DisplayName("Test deleteMailById(String, String, Boolean); when empty string")
    void testDeleteMailById_whenEmptyString() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertFalse((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .deleteMailById("", "42", true));
    }

    /**
     * Test {@link EwsService#deleteMailById(String, String, Boolean)}.
     * <ul>
     *   <li>When {@code <EMAIL>}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#deleteMailById(String, String, Boolean)}
     */
    @Test
    @DisplayName("Test deleteMailById(String, String, Boolean); when '<EMAIL>'")
    void testDeleteMailById_whenJaneDoeExampleOrg() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertFalse((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .deleteMailById("<EMAIL>", "42", true));
    }

    /**
     * Test {@link EwsService#deleteMailById(String, String, Boolean)}.
     * <ul>
     *   <li>When {@code <EMAIL>}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#deleteMailById(String, String, Boolean)}
     */
    @Test
    @DisplayName("Test deleteMailById(String, String, Boolean); when '<EMAIL>'")
    void testDeleteMailById_whenJaneDoeExampleOrg2() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertFalse((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .deleteMailById("<EMAIL>", "", true));
    }

    /**
     * Test {@link EwsService#getEmailByInternetMessageId(String, String)} with
     * {@code email}, {@code internetMessageId}.
     * <p>
     * Method under test:
     * {@link EwsService#getEmailByInternetMessageId(String, String)}
     */
    @Test
    @DisplayName("Test getEmailByInternetMessageId(String, String) with 'email', 'internetMessageId'")
    @Disabled("TODO: Complete this test")
    void testGetEmailByInternetMessageIdWithEmailInternetMessageId() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@2837b0ca testClass = com.enttribe.emailagent.service.DiffblueFakeClass48, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getEmailByInternetMessageId("<EMAIL>", "42");
    }

    /**
     * Test {@link EwsService#getEmailByInternetMessageId(String, String, String)}
     * with {@code email}, {@code internetMessageId}, {@code folderName}.
     * <p>
     * Method under test:
     * {@link EwsService#getEmailByInternetMessageId(String, String, String)}
     */
    @Test
    @DisplayName("Test getEmailByInternetMessageId(String, String, String) with 'email', 'internetMessageId', 'folderName'")
    @Disabled("TODO: Complete this test")
    void testGetEmailByInternetMessageIdWithEmailInternetMessageIdFolderName() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@65fc727f testClass = com.enttribe.emailagent.service.DiffblueFakeClass50, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getEmailByInternetMessageId("<EMAIL>", "42", "Folder Name");
    }

    /**
     * Test
     * {@link EwsService#getMessageIdOfFirstEmailInConversation(String, String)}.
     * <p>
     * Method under test:
     * {@link EwsService#getMessageIdOfFirstEmailInConversation(String, String)}
     */
    @Test
    @DisplayName("Test getMessageIdOfFirstEmailInConversation(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetMessageIdOfFirstEmailInConversation() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@396c584f testClass = com.enttribe.emailagent.service.DiffblueFakeClass66, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getMessageIdOfFirstEmailInConversation("<EMAIL>", "42");
    }

    /**
     * Test {@link EwsService#getCalendarEvents(String, String, String, boolean)}.
     * <p>
     * Method under test:
     * {@link EwsService#getCalendarEvents(String, String, String, boolean)}
     */
    @Test
    @DisplayName("Test getCalendarEvents(String, String, String, boolean)")
    @Disabled("TODO: Complete this test")
    void testGetCalendarEvents() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@6f417ec testClass = com.enttribe.emailagent.service.DiffblueFakeClass44, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getCalendarEvents("<EMAIL>", "2020-03-01", "2020-03-01", true);
    }

    /**
     * Test {@link EwsService#updatePriority(String, String)}.
     * <p>
     * Method under test: {@link EwsService#updatePriority(String, String)}
     */
    @Test
    @DisplayName("Test updatePriority(String, String)")
    @Disabled("TODO: Complete this test")
    void testUpdatePriority() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@2ae0b189 testClass = com.enttribe.emailagent.service.DiffblueFakeClass92, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.updatePriority("42", "Priority");
    }

    /**
     * Test {@link EwsService#setCategory(String, String, List)}.
     * <p>
     * Method under test: {@link EwsService#setCategory(String, String, List)}
     */
    @Test
    @DisplayName("Test setCategory(String, String, List)")
    @Disabled("TODO: Complete this test")
    void testSetCategory() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@77dbdde0 testClass = com.enttribe.emailagent.service.DiffblueFakeClass86, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.setCategory("<EMAIL>", "42", new ArrayList<>());
    }

    /**
     * Test {@link EwsService#scheduleEvent(String, Map)}.
     * <p>
     * Method under test: {@link EwsService#scheduleEvent(String, Map)}
     */
    @Test
    @DisplayName("Test scheduleEvent(String, Map)")
    @Disabled("TODO: Complete this test")
    void testScheduleEvent() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@4f2368dc testClass = com.enttribe.emailagent.service.DiffblueFakeClass80, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.scheduleEvent("<EMAIL>", new HashMap<>());
    }

    /**
     * Test {@link EwsService#scheduleEventRecurring(String, Map)}.
     * <p>
     * Method under test: {@link EwsService#scheduleEventRecurring(String, Map)}
     */
    @Test
    @DisplayName("Test scheduleEventRecurring(String, Map)")
    @Disabled("TODO: Complete this test")
    void testScheduleEventRecurring() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@2beac893 testClass = com.enttribe.emailagent.service.DiffblueFakeClass82, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.scheduleEventRecurring("<EMAIL>", new HashMap<>());
    }

    /**
     * Test {@link EwsService#rescheduleEvent(Map)}.
     * <p>
     * Method under test: {@link EwsService#rescheduleEvent(Map)}
     */
    @Test
    @DisplayName("Test rescheduleEvent(Map)")
    @Disabled("TODO: Complete this test")
    void testRescheduleEvent() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@3d6c6e4a testClass = com.enttribe.emailagent.service.DiffblueFakeClass78, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.rescheduleEvent(new HashMap<>());
    }

    /**
     * Test {@link EwsService#acceptMeeting(String)}.
     * <p>
     * Method under test: {@link EwsService#acceptMeeting(String)}
     */
    @Test
    @DisplayName("Test acceptMeeting(String)")
    @Disabled("TODO: Complete this test")
    void testAcceptMeeting() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Unsupported virtual threads call detected.
        //   Diffblue Cover does not support writing tests for methods that use
        //   virtual threads.

        // Arrange
        // TODO: Populate arranged inputs
        String meetingRequestId = "";

        // Act
        Map<String, String> actualAcceptMeetingResult = this.ewsService.acceptMeeting(meetingRequestId);

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Test {@link EwsService#declineMeeting(String)}.
     * <p>
     * Method under test: {@link EwsService#declineMeeting(String)}
     */
    @Test
    @DisplayName("Test declineMeeting(String)")
    @Disabled("TODO: Complete this test")
    void testDeclineMeeting() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@6ab836d testClass = com.enttribe.emailagent.service.DiffblueFakeClass20, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.declineMeeting("42");
    }

    /**
     * Test {@link EwsService#tentativelyAcceptMeeting(String)}.
     * <p>
     * Method under test: {@link EwsService#tentativelyAcceptMeeting(String)}
     */
    @Test
    @DisplayName("Test tentativelyAcceptMeeting(String)")
    @Disabled("TODO: Complete this test")
    void testTentativelyAcceptMeeting() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@62978818 testClass = com.enttribe.emailagent.service.DiffblueFakeClass90, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.tentativelyAcceptMeeting("42");
    }

    /**
     * Test {@link EwsService#getUsersMailFolders(String)}.
     * <p>
     * Method under test: {@link EwsService#getUsersMailFolders(String)}
     */
    @Test
    @DisplayName("Test getUsersMailFolders(String)")
    @Disabled("TODO: Complete this test")
    void testGetUsersMailFolders() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@352d8959 testClass = com.enttribe.emailagent.service.DiffblueFakeClass74, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getUsersMailFolders("<EMAIL>");
    }

    /**
     * Test
     * {@link EwsService#createDraft(String, String, String, String, String, String, Boolean)}.
     * <p>
     * Method under test:
     * {@link EwsService#createDraft(String, String, String, String, String, String, Boolean)}
     */
    @Test
    @DisplayName("Test createDraft(String, String, String, String, String, String, Boolean)")
    @Disabled("TODO: Complete this test")
    void testCreateDraft() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@17952c35 testClass = com.enttribe.emailagent.service.DiffblueFakeClass8, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.createDraft("<EMAIL>", "Hello from the Dreaming Spires", "Not all who wander are lost",
                "<EMAIL>", "<EMAIL>", "<EMAIL>", true);
    }

    /**
     * Test
     * {@link EwsService#createDraftWithAttachment(String, String, String, String, String, String, Boolean, String)}.
     * <p>
     * Method under test:
     * {@link EwsService#createDraftWithAttachment(String, String, String, String, String, String, Boolean, String)}
     */
    @Test
    @DisplayName("Test createDraftWithAttachment(String, String, String, String, String, String, Boolean, String)")
    @Disabled("TODO: Complete this test")
    void testCreateDraftWithAttachment() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@7be004fb testClass = com.enttribe.emailagent.service.DiffblueFakeClass18, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.createDraftWithAttachment("<EMAIL>", "Hello from the Dreaming Spires",
                "Not all who wander are lost", "<EMAIL>", "<EMAIL>", "<EMAIL>", true,
                "Attachments");
    }

    /**
     * Test
     * {@link EwsService#createDraftReply(String, String, String, boolean, String, String)}.
     * <p>
     * Method under test:
     * {@link EwsService#createDraftReply(String, String, String, boolean, String, String)}
     */
    @Test
    @DisplayName("Test createDraftReply(String, String, String, boolean, String, String)")
    @Disabled("TODO: Complete this test")
    void testCreateDraftReply() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@3a098757 testClass = com.enttribe.emailagent.service.DiffblueFakeClass12, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.createDraftReply("<EMAIL>", "42", "Not all who wander are lost", true, "Type",
                "text/plain");
    }

    /**
     * Test
     * {@link EwsService#createDraftForward(String, String, String, String, String, boolean)}.
     * <p>
     * Method under test:
     * {@link EwsService#createDraftForward(String, String, String, String, String, boolean)}
     */
    @Test
    @DisplayName("Test createDraftForward(String, String, String, String, String, boolean)")
    @Disabled("TODO: Complete this test")
    void testCreateDraftForward() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@17dc8bb9 testClass = com.enttribe.emailagent.service.DiffblueFakeClass10, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.createDraftForward("<EMAIL>", "42", "Comment", "To Recipients", "Cc Recipients", true);
    }

    /**
     * Test
     * {@link EwsService#getAvailableSlotsAndConflict(List, String, String, int)}.
     * <p>
     * Method under test:
     * {@link EwsService#getAvailableSlotsAndConflict(List, String, String, int)}
     */
    @Test
    @DisplayName("Test getAvailableSlotsAndConflict(List, String, String, int)")
    @Disabled("TODO: Complete this test")
    void testGetAvailableSlotsAndConflict() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@49bf37d9 testClass = com.enttribe.emailagent.service.DiffblueFakeClass40, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getAvailableSlotsAndConflict(new ArrayList<>(), "2020-03-01", "2020-03-01", 1);
    }

    /**
     * Test
     * {@link EwsService#createDraftReplyWithAttachment(String, String, String, boolean, String)}
     * with {@code email}, {@code messageId}, {@code content}, {@code sendDraft},
     * {@code attachments}.
     * <p>
     * Method under test:
     * {@link EwsService#createDraftReplyWithAttachment(String, String, String, boolean, String)}
     */
    @Test
    @DisplayName("Test createDraftReplyWithAttachment(String, String, String, boolean, String) with 'email', 'messageId', 'content', 'sendDraft', 'attachments'")
    @Disabled("TODO: Complete this test")
    void testCreateDraftReplyWithAttachmentWithEmailMessageIdContentSendDraftAttachments() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@6b977f8a testClass = com.enttribe.emailagent.service.DiffblueFakeClass14, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.createDraftReplyWithAttachment("<EMAIL>", "42", "Not all who wander are lost", true,
                "Attachments");
    }

    /**
     * Test
     * {@link EwsService#createDraftReplyWithAttachment(String, String, String, boolean, String, boolean)}
     * with {@code email}, {@code messageId}, {@code content}, {@code sendDraft},
     * {@code attachments}, {@code isReplyAll}.
     * <p>
     * Method under test:
     * {@link EwsService#createDraftReplyWithAttachment(String, String, String, boolean, String, boolean)}
     */
    @Test
    @DisplayName("Test createDraftReplyWithAttachment(String, String, String, boolean, String, boolean) with 'email', 'messageId', 'content', 'sendDraft', 'attachments', 'isReplyAll'")
    @Disabled("TODO: Complete this test")
    void testCreateDraftReplyWithAttachmentWithEmailMessageIdContentSendDraftAttachmentsIsReplyAll() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@179b5131 testClass = com.enttribe.emailagent.service.DiffblueFakeClass16, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.createDraftReplyWithAttachment("<EMAIL>", "42", "Not all who wander are lost", true,
                "Attachments", true);
    }

    /**
     * Test {@link EwsService#getAvailableMeetingSlots(List, String, String, int)}.
     * <p>
     * Method under test:
     * {@link EwsService#getAvailableMeetingSlots(List, String, String, int)}
     */
    @Test
    @DisplayName("Test getAvailableMeetingSlots(List, String, String, int)")
    @Disabled("TODO: Complete this test")
    void testGetAvailableMeetingSlots() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@dcf559d testClass = com.enttribe.emailagent.service.DiffblueFakeClass34, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getAvailableMeetingSlots(new ArrayList<>(), "2020-03-01", "2020-03-01", 1);
    }

    /**
     * Test
     * {@link EwsService#getAvailableMeetingSlotsOOF(List, String, String, int)}.
     * <p>
     * Method under test:
     * {@link EwsService#getAvailableMeetingSlotsOOF(List, String, String, int)}
     */
    @Test
    @DisplayName("Test getAvailableMeetingSlotsOOF(List, String, String, int)")
    @Disabled("TODO: Complete this test")
    void testGetAvailableMeetingSlotsOOF() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@1205366c testClass = com.enttribe.emailagent.service.DiffblueFakeClass36, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getAvailableMeetingSlotsOOF(new ArrayList<>(), "2020-03-01", "2020-03-01", 1);
    }

    /**
     * Test
     * {@link EwsService#forwardEmail(String, String, String, String, String, String)}.
     * <p>
     * Method under test:
     * {@link EwsService#forwardEmail(String, String, String, String, String, String)}
     */
    @Test
    @DisplayName("Test forwardEmail(String, String, String, String, String, String)")
    @Disabled("TODO: Complete this test")
    void testForwardEmail() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@50193d4f testClass = com.enttribe.emailagent.service.DiffblueFakeClass30, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.forwardEmail("<EMAIL>", "42", "To Recipients", "Cc Recipients",
                "<EMAIL>", "Comment");
    }

    /**
     * Test
     * {@link EwsService#forwardEmail(String, String, String, String, String, String)}.
     * <ul>
     *   <li>When {@code 42}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EwsService#forwardEmail(String, String, String, String, String, String)}
     */
    @Test
    @DisplayName("Test forwardEmail(String, String, String, String, String, String); when '42'")
    void testForwardEmail_when42() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertEquals("failed",
                (new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao, new GraphIntegrationService(),
                        mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class))).forwardEmail(
                        "<EMAIL>", "42", "To Recipients", "Cc Recipients", "<EMAIL>",
                        "Comment"));
    }

    /**
     * Test
     * {@link EwsService#forwardEmail(String, String, String, String, String, String)}.
     * <ul>
     *   <li>When empty string.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EwsService#forwardEmail(String, String, String, String, String, String)}
     */
    @Test
    @DisplayName("Test forwardEmail(String, String, String, String, String, String); when empty string")
    void testForwardEmail_whenEmptyString() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertEquals("failed",
                (new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao, new GraphIntegrationService(),
                        mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                        .forwardEmail("<EMAIL>", "", null, null, null, "Comment"));
    }

    /**
     * Test
     * {@link EwsService#forwardEmail(String, String, String, String, String, String)}.
     * <ul>
     *   <li>When empty string.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EwsService#forwardEmail(String, String, String, String, String, String)}
     */
    @Test
    @DisplayName("Test forwardEmail(String, String, String, String, String, String); when empty string")
    void testForwardEmail_whenEmptyString2() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertEquals("failed",
                (new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao, new GraphIntegrationService(),
                        mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class))).forwardEmail("", "42",
                        "To Recipients", "Cc Recipients", "<EMAIL>", "Comment"));
    }

    /**
     * Test
     * {@link EwsService#forwardEmail(String, String, String, String, String, String)}.
     * <ul>
     *   <li>When {@code null}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EwsService#forwardEmail(String, String, String, String, String, String)}
     */
    @Test
    @DisplayName("Test forwardEmail(String, String, String, String, String, String); when 'null'")
    void testForwardEmail_whenNull() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertEquals("failed",
                (new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao, new GraphIntegrationService(),
                        mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                        .forwardEmail("<EMAIL>", null, null, null, null, "Comment"));
    }

    /**
     * Test {@link EwsService#getFlaggedEmails(String, String)}.
     * <p>
     * Method under test: {@link EwsService#getFlaggedEmails(String, String)}
     */
    @Test
    @DisplayName("Test getFlaggedEmails(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetFlaggedEmails() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@84842e2 testClass = com.enttribe.emailagent.service.DiffblueFakeClass62, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getFlaggedEmails("<EMAIL>", "42");
    }

    /**
     * Test {@link EwsService#getFlaggedEmails(String, String)}.
     * <ul>
     *   <li>When {@code 42}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#getFlaggedEmails(String, String)}
     */
    @Test
    @DisplayName("Test getFlaggedEmails(String, String); when '42'")
    void testGetFlaggedEmails_when42() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertTrue((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getFlaggedEmails("<EMAIL>", "42")
                .isEmpty());
    }

    /**
     * Test {@link EwsService#getFlaggedEmails(String, String)}.
     * <ul>
     *   <li>When empty string.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#getFlaggedEmails(String, String)}
     */
    @Test
    @DisplayName("Test getFlaggedEmails(String, String); when empty string")
    void testGetFlaggedEmails_whenEmptyString() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertTrue((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getFlaggedEmails("<EMAIL>", "")
                .isEmpty());
    }

    /**
     * Test {@link EwsService#getFlaggedEmails(String, String)}.
     * <ul>
     *   <li>When empty string.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#getFlaggedEmails(String, String)}
     */
    @Test
    @DisplayName("Test getFlaggedEmails(String, String); when empty string")
    void testGetFlaggedEmails_whenEmptyString2() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertTrue(
                (new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao, new GraphIntegrationService(),
                        mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class))).getFlaggedEmails("", "42")
                        .isEmpty());
    }

    /**
     * Test {@link EwsService#getFlaggedEmails(String, String)}.
     * <ul>
     *   <li>When {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EwsService#getFlaggedEmails(String, String)}
     */
    @Test
    @DisplayName("Test getFlaggedEmails(String, String); when 'null'")
    void testGetFlaggedEmails_whenNull() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertTrue((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getFlaggedEmails("<EMAIL>", null)
                .isEmpty());
    }

    /**
     * Test {@link EwsService#flagEmail(String, Integer, String)} with
     * {@code email}, {@code databaseId}, {@code flagStatus}.
     * <p>
     * Method under test: {@link EwsService#flagEmail(String, Integer, String)}
     */
    @Test
    @DisplayName("Test flagEmail(String, Integer, String) with 'email', 'databaseId', 'flagStatus'")
    @Disabled("TODO: Complete this test")
    void testFlagEmailWithEmailDatabaseIdFlagStatus() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@7531e485 testClass = com.enttribe.emailagent.service.DiffblueFakeClass26, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.flagEmail("<EMAIL>", 1, "Flag Status");
    }

    /**
     * Test {@link EwsService#flagEmail(String, String, String)} with {@code email},
     * {@code messageId}, {@code flagStatus}.
     * <p>
     * Method under test: {@link EwsService#flagEmail(String, String, String)}
     */
    @Test
    @DisplayName("Test flagEmail(String, String, String) with 'email', 'messageId', 'flagStatus'")
    @Disabled("TODO: Complete this test")
    void testFlagEmailWithEmailMessageIdFlagStatus() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@f81a9a0 testClass = com.enttribe.emailagent.service.DiffblueFakeClass28, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.flagEmail("<EMAIL>", "42", "Flag Status");
    }

    /**
     * Test {@link EwsService#getFlagStatus(String, String)}.
     * <p>
     * Method under test: {@link EwsService#getFlagStatus(String, String)}
     */
    @Test
    @DisplayName("Test getFlagStatus(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetFlagStatus() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@34a9fd1c testClass = com.enttribe.emailagent.service.DiffblueFakeClass60, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getFlagStatus("<EMAIL>", "42");
    }

    /**
     * Test {@link EwsService#getOutOfOfficeHours(String)}.
     * <p>
     * Method under test: {@link EwsService#getOutOfOfficeHours(String)}
     */
    @Test
    @DisplayName("Test getOutOfOfficeHours(String)")
    void testGetOutOfOfficeHours() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);

        // Act and Assert
        assertNull((new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class)))
                .getOutOfOfficeHours("<EMAIL>"));
    }

    /**
     * Test {@link EwsService#getOutOfOfficeHours(String)}.
     * <p>
     * Method under test: {@link EwsService#getOutOfOfficeHours(String)}
     */
    @Test
    @DisplayName("Test getOutOfOfficeHours(String)")
    @Disabled("TODO: Complete this test")
    void testGetOutOfOfficeHours2() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@523e90e testClass = com.enttribe.emailagent.service.DiffblueFakeClass70, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getOutOfOfficeHours("<EMAIL>");
    }

    /**
     * Test {@link EwsService#getEmailBySubject(Map)}.
     * <p>
     * Method under test: {@link EwsService#getEmailBySubject(Map)}
     */
    @Test
    @DisplayName("Test getEmailBySubject(Map)")
    @Disabled("TODO: Complete this test")
    void testGetEmailBySubject() throws Exception {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@2061b104 testClass = com.enttribe.emailagent.service.DiffblueFakeClass54, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getEmailBySubject(new HashMap<>());
    }

    /**
     * Test {@link EwsService#deleteEmailById(String, String)}.
     * <p>
     * Method under test: {@link EwsService#deleteEmailById(String, String)}
     */
    @Test
    @DisplayName("Test deleteEmailById(String, String)")
    @Disabled("TODO: Complete this test")
    void testDeleteEmailById() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@68259db6 testClass = com.enttribe.emailagent.service.DiffblueFakeClass22, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.deleteEmailById("<EMAIL>", "42");
    }

    /**
     * Test {@link EwsService#addCustomTags(String, String, String[])}.
     * <p>
     * Method under test: {@link EwsService#addCustomTags(String, String, String[])}
     */
    @Test
    @DisplayName("Test addCustomTags(String, String, String[])")
    @Disabled("TODO: Complete this test")
    void testAddCustomTags() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: Failed to load ApplicationContext for [WebMergedContextConfiguration@7c318e69 testClass = com.enttribe.emailagent.service.DiffblueFakeClass4, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@c2c43ca2, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:180)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   org.springframework.core.task.TaskRejectedException: ExecutorService in active state did not accept task: com.enttribe.emailagent.utils.TokenUtils.refreshToken
        //       at org.springframework.scheduling.concurrent.SimpleAsyncTaskScheduler.scheduleAtFixedRate(SimpleAsyncTaskScheduler.java:257)
        //       at org.springframework.scheduling.config.TaskSchedulerRouter.scheduleAtFixedRate(TaskSchedulerRouter.java:126)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleFixedRateTask(ScheduledTaskRegistrar.java:555)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleTasks(ScheduledTaskRegistrar.java:446)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.afterPropertiesSet(ScheduledTaskRegistrar.java:421)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.finishRegistration(ScheduledAnnotationBeanPostProcessor.java:264)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:659)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:110)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
        //       at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)
        //       at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)
        //       at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:984)
        //       at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
        //       at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
        //       at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
        //       at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137)
        //       at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58)
        //       at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46)
        //       at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1454)
        //       at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:553)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108)
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225)
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   java.util.concurrent.RejectedExecutionException
        //       at java.base/java.util.concurrent.ForkJoinPool.submissionQueue(ForkJoinPool.java:2351)
        //       at java.base/java.util.concurrent.ForkJoinPool.poolSubmit(ForkJoinPool.java:2368)
        //       at java.base/java.util.concurrent.ForkJoinPool.execute(ForkJoinPool.java:2847)
        //       at java.base/java.lang.VirtualThread.submitRunContinuation(VirtualThread.java:264)
        //       at java.base/java.lang.VirtualThread.start(VirtualThread.java:560)
        //       at java.base/java.lang.System$2.start(System.java:2577)
        //       at java.base/jdk.internal.vm.SharedThreadContainer.start(SharedThreadContainer.java:152)
        //       at java.base/java.util.concurrent.ThreadPoolExecutor.addWorker(ThreadPoolExecutor.java:953)
        //       at java.base/java.util.concurrent.ThreadPoolExecutor.ensurePrestart(ThreadPoolExecutor.java:1609)
        //       at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.delayedExecute(ScheduledThreadPoolExecutor.java:346)
        //       at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.scheduleAtFixedRate(ScheduledThreadPoolExecutor.java:632)
        //       at org.springframework.scheduling.concurrent.SimpleAsyncTaskScheduler.scheduleAtFixedRate(SimpleAsyncTaskScheduler.java:253)
        //       at org.springframework.scheduling.config.TaskSchedulerRouter.scheduleAtFixedRate(TaskSchedulerRouter.java:126)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleFixedRateTask(ScheduledTaskRegistrar.java:555)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.scheduleTasks(ScheduledTaskRegistrar.java:446)
        //       at org.springframework.scheduling.config.ScheduledTaskRegistrar.afterPropertiesSet(ScheduledTaskRegistrar.java:421)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.finishRegistration(ScheduledAnnotationBeanPostProcessor.java:264)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:659)
        //       at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.onApplicationEvent(ScheduledAnnotationBeanPostProcessor.java:110)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
        //       at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
        //       at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)
        //       at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)
        //       at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:984)
        //       at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
        //       at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
        //       at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
        //       at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137)
        //       at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58)
        //       at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46)
        //       at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1454)
        //       at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:553)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137)
        //       at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108)
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225)
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.addCustomTags("<EMAIL>", "42", "Tags");
    }

    /**
     * Test {@link EwsService#getMessageIdsByTag(String, String)}.
     * <p>
     * Method under test: {@link EwsService#getMessageIdsByTag(String, String)}
     */
    @Test
    @DisplayName("Test getMessageIdsByTag(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetMessageIdsByTag() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@442dc8d2 testClass = com.enttribe.emailagent.service.DiffblueFakeClass68, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getMessageIdsByTag("<EMAIL>", "Tag");
    }

    /**
     * Test {@link EwsService#getCalendarEventById(String, String)}.
     * <p>
     * Method under test: {@link EwsService#getCalendarEventById(String, String)}
     */
    @Test
    @DisplayName("Test getCalendarEventById(String, String)")
    @Disabled("TODO: Complete this test")
    void testGetCalendarEventById() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@6cdfb9cb testClass = com.enttribe.emailagent.service.DiffblueFakeClass42, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getCalendarEventById("<EMAIL>", "42");
    }

    /**
     * Test
     * {@link EwsService#getAvailableMeetingSlotsV2(List, String, String, int)}.
     * <p>
     * Method under test:
     * {@link EwsService#getAvailableMeetingSlotsV2(List, String, String, int)}
     */
    @Test
    @DisplayName("Test getAvailableMeetingSlotsV2(List, String, String, int)")
    @Disabled("TODO: Complete this test")
    void testGetAvailableMeetingSlotsV2() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@63431043 testClass = com.enttribe.emailagent.service.DiffblueFakeClass38, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.getAvailableMeetingSlotsV2(new ArrayList<>(), "2020-03-01", "2020-03-01", 1);
    }

    /**
     * Test {@link EwsService#markMessageReadUnread(String, String, boolean)}.
     * <p>
     * Method under test:
     * {@link EwsService#markMessageReadUnread(String, String, boolean)}
     */
    @Test
    @DisplayName("Test markMessageReadUnread(String, String, boolean)")
    @Disabled("TODO: Complete this test")
    void testMarkMessageReadUnread() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Failed to create Spring context.
        //   Attempt to initialize test context failed with
        //   java.lang.IllegalStateException: ApplicationContext failure threshold (1) exceeded: skipping repeated attempt to load context for [WebMergedContextConfiguration@dd2c934 testClass = com.enttribe.emailagent.service.DiffblueFakeClass76, locations = [], classes = [com.enttribe.emailagent.EmailagentApplication], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@256eebe7, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@3a8ed40, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@14e10559, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3095e1a3, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@27c0d156, org.springframework.boot.test.context.SpringBootTestAnnotation@6dd04882], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
        //       at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:145)
        //       at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
        //       at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
        //       at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
        //       at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        //       at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        //       at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        //       at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        //   See https://diff.blue/R026 to resolve this issue.

        // Arrange and Act
        ewsService.markMessageReadUnread("<EMAIL>", "42", true);
    }

    /**
     * Test getters and setters.
     * <p>
     * Methods under test:
     * <ul>
     *   <li>{@link EwsService#setAnswerUrl(String)}
     *   <li>{@link EwsService#setAttachmentFilePath(String)}
     *   <li>{@link EwsService#setUploadUrl(String)}
     * </ul>
     */
    @Test
    @DisplayName("Test getters and setters")
    void testGettersAndSetters() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     EwsService.allowOutsiders
        //     EwsService.answerUrl
        //     EwsService.attachmentFilePath
        //     EwsService.bodyTokenLimit
        //     EwsService.emailUserDao
        //     EwsService.foldersRepository
        //     EwsService.generatedBy
        //     EwsService.graphIntegrationService
        //     EwsService.mailSummaryDao
        //     EwsService.orgDomainName
        //     EwsService.preferencesDao
        //     EwsService.s3BucketName
        //     EwsService.s3Service
        //     EwsService.supportedFormats
        //     EwsService.uploadUrl
        //     EwsService.userContextHolder
        //     EwsService.userMailAttachmentRepository

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        UserContextHolder userContextHolder = new UserContextHolder();
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);
        EwsService ewsService = new EwsService(emailUserDao, preferencesDao, userContextHolder, mailSummaryDao,
                new GraphIntegrationService(), mock(UserMailAttachmentRepository.class), mock(UserFoldersRepository.class));

        // Act
        ewsService.setAnswerUrl("https://example.org/example");
        ewsService.setAttachmentFilePath("/directory/foo.txt");
        ewsService.setUploadUrl("https://example.org/example");
    }
}
