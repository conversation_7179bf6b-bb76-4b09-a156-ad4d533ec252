package com.enttribe.emailagent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;

import com.enttribe.emailagent.ai.dto.mail_summary.MailSummaryAIResponse;
import com.enttribe.emailagent.ai.dto.mail_summary.Summary;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.repository.EmailUserDao;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.PollTimeInfoRepository;
import com.enttribe.emailagent.utils.FCMTokenUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.json.JsonMapper;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;

import org.junit.jupiter.api.Disabled;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ExtendWith(SpringExtension.class)
class NotificationServiceTest {
    @Autowired
    private NotificationService notificationService;

    /**
     * Test {@link NotificationService#sendFCMNotification(String, MailSummary)}
     * with {@code deviceId}, {@code mailSummary}.
     * <p>
     * Method under test:
     * {@link NotificationService#sendFCMNotification(String, MailSummary)}
     */
    @Test
    @DisplayName("Test sendFCMNotification(String, MailSummary) with 'deviceId', 'mailSummary'")
    void testSendFCMNotificationWithDeviceIdMailSummary() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        PollTimeInfoRepository pollTimeInfoRepository = mock(PollTimeInfoRepository.class);
        PollTimeInfoService pollTimeInfoService = new PollTimeInfoService();
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        NotificationService notificationService = new NotificationService(mailSummaryDao, emailUserDao,
                pollTimeInfoRepository, pollTimeInfoService, preferencesDao, new FCMTokenUtils());

        MailSummary mailSummary = new MailSummary();
        mailSummary.setActionClearReason("Just cause");
        mailSummary.setActionOwner("Action Owner");
        mailSummary.setActionTaken(true);
        mailSummary.setAttachmentsList(new ArrayList<>());
        mailSummary.setCategory("Category");
        mailSummary.setCategoryReason("Just cause");
        mailSummary.setCcUser("Cc User");
        mailSummary.setConversationId("42");
        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setDecryptSummary("Decrypt Summary");
        mailSummary.setDeleted(true);
        mailSummary.setEmailTone("<EMAIL>");
        mailSummary.setEmailToneReason("Just cause");
        mailSummary.setFlagStatus("Flag Status");
        mailSummary.setFolderName("Folder Name");
        mailSummary.setFromUser("<EMAIL>");
        mailSummary.setHasAttachment(true);
        mailSummary.setId(1);
        mailSummary.setInternetMessageId("42");
        mailSummary.setIsActionable(true);
        mailSummary.setIsAttention(true);
        mailSummary.setIsHighPriority(true);
        mailSummary.setIsStarMarked(true);
        mailSummary.setIsUnread(true);
        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary.setMeetingPreview("Meeting Preview");
        mailSummary.setMessageId("42");
        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary.setObjective("Objective");
        mailSummary.setPriority("Priority");
        mailSummary.setPriorityReason("Just cause");
        mailSummary.setStarMarkReason("Just cause");
        mailSummary.setStarMarked(true);
        mailSummary.setSubject("Hello from the Dreaming Spires");
        mailSummary.setTag("Tag");
        mailSummary.setToUser("To User");
        mailSummary.setUserActions(new HashSet<>());
        mailSummary.setUserId("42");
        mailSummary.setMessageSummary("{}");
        mailSummary.setType("Email");

        // Act and Assert
        assertEquals("failed", notificationService.sendFCMNotification("42", mailSummary));
    }

    /**
     * Test {@link NotificationService#sendFCMNotification(String, MailSummary)}
     * with {@code deviceId}, {@code mailSummary}.
     * <p>
     * Method under test:
     * {@link NotificationService#sendFCMNotification(String, MailSummary)}
     */
    @Test
    @DisplayName("Test sendFCMNotification(String, MailSummary) with 'deviceId', 'mailSummary'")
    void testSendFCMNotificationWithDeviceIdMailSummary2() throws JsonProcessingException {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        PollTimeInfoRepository pollTimeInfoRepository = mock(PollTimeInfoRepository.class);
        PollTimeInfoService pollTimeInfoService = new PollTimeInfoService();
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        NotificationService notificationService = new NotificationService(mailSummaryDao, emailUserDao,
                pollTimeInfoRepository, pollTimeInfoService, preferencesDao, new FCMTokenUtils());

        Summary summaryObject = new Summary();
        summaryObject.setContent("Not all who wander are lost");
        summaryObject.setCreatedTime(1L);
        summaryObject.setInternetMessageId("42");
        summaryObject.setMessageId("42");
        summaryObject.setSender("Sender");

        MailSummaryAIResponse mailSummaryAIResponse = new MailSummaryAIResponse();
        mailSummaryAIResponse.setActionOwner(new ArrayList<>());
        mailSummaryAIResponse.setParticipants(new ArrayList<>());
        mailSummaryAIResponse.setSubject("Hello from the Dreaming Spires");
        mailSummaryAIResponse.setSummaryObject(summaryObject);
        String messageSummary = JsonMapper.builder().findAndAddModules().build().writeValueAsString(mailSummaryAIResponse);

        MailSummary mailSummary = new MailSummary();
        mailSummary.setActionClearReason("Just cause");
        mailSummary.setActionOwner("Action Owner");
        mailSummary.setActionTaken(true);
        mailSummary.setAttachmentsList(new ArrayList<>());
        mailSummary.setCategory("Category");
        mailSummary.setCategoryReason("Just cause");
        mailSummary.setCcUser("Cc User");
        mailSummary.setConversationId("42");
        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setDecryptSummary("Decrypt Summary");
        mailSummary.setDeleted(true);
        mailSummary.setEmailTone("<EMAIL>");
        mailSummary.setEmailToneReason("Just cause");
        mailSummary.setFlagStatus("Flag Status");
        mailSummary.setFolderName("Folder Name");
        mailSummary.setFromUser("<EMAIL>");
        mailSummary.setHasAttachment(true);
        mailSummary.setId(1);
        mailSummary.setInternetMessageId("42");
        mailSummary.setIsActionable(true);
        mailSummary.setIsAttention(true);
        mailSummary.setIsHighPriority(true);
        mailSummary.setIsStarMarked(true);
        mailSummary.setIsUnread(true);
        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary.setMeetingPreview("Meeting Preview");
        mailSummary.setMessageId("42");
        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary.setObjective("Objective");
        mailSummary.setPriority("Priority");
        mailSummary.setPriorityReason("Just cause");
        mailSummary.setStarMarkReason("Just cause");
        mailSummary.setStarMarked(true);
        mailSummary.setSubject("Hello from the Dreaming Spires");
        mailSummary.setTag("Tag");
        mailSummary.setToUser("To User");
        mailSummary.setUserActions(new HashSet<>());
        mailSummary.setUserId("42");
        mailSummary.setMessageSummary(messageSummary);
        mailSummary.setType("Email");

        // Act and Assert
        assertEquals("failed", notificationService.sendFCMNotification("42", mailSummary));
    }

    /**
     * Test {@link NotificationService#sendFCMNotification(String, MailSummary)}
     * with {@code deviceId}, {@code mailSummary}.
     * <p>
     * Method under test:
     * {@link NotificationService#sendFCMNotification(String, MailSummary)}
     */
    @Test
    @DisplayName("Test sendFCMNotification(String, MailSummary) with 'deviceId', 'mailSummary'")
    void testSendFCMNotificationWithDeviceIdMailSummary3() throws JsonProcessingException {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        PollTimeInfoRepository pollTimeInfoRepository = mock(PollTimeInfoRepository.class);
        PollTimeInfoService pollTimeInfoService = new PollTimeInfoService();
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        NotificationService notificationService = new NotificationService(null, emailUserDao, pollTimeInfoRepository,
                pollTimeInfoService, preferencesDao, new FCMTokenUtils());

        Summary summaryObject = new Summary();
        summaryObject.setContent("Not all who wander are lost");
        summaryObject.setCreatedTime(1L);
        summaryObject.setInternetMessageId("42");
        summaryObject.setMessageId("42");
        summaryObject.setSender("Sender");

        MailSummaryAIResponse mailSummaryAIResponse = new MailSummaryAIResponse();
        mailSummaryAIResponse.setActionOwner(new ArrayList<>());
        mailSummaryAIResponse.setParticipants(new ArrayList<>());
        mailSummaryAIResponse.setSubject("Hello from the Dreaming Spires");
        mailSummaryAIResponse.setSummaryObject(summaryObject);
        String messageSummary = JsonMapper.builder().findAndAddModules().build().writeValueAsString(mailSummaryAIResponse);

        MailSummary mailSummary = new MailSummary();
        mailSummary.setActionClearReason("Just cause");
        mailSummary.setActionOwner("Action Owner");
        mailSummary.setActionTaken(true);
        mailSummary.setAttachmentsList(new ArrayList<>());
        mailSummary.setCategory("Category");
        mailSummary.setCategoryReason("Just cause");
        mailSummary.setCcUser("Cc User");
        mailSummary.setConversationId("42");
        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setDecryptSummary("Decrypt Summary");
        mailSummary.setDeleted(true);
        mailSummary.setEmailTone("<EMAIL>");
        mailSummary.setEmailToneReason("Just cause");
        mailSummary.setFlagStatus("Flag Status");
        mailSummary.setFolderName("Folder Name");
        mailSummary.setFromUser("<EMAIL>");
        mailSummary.setHasAttachment(true);
        mailSummary.setId(1);
        mailSummary.setInternetMessageId("42");
        mailSummary.setIsActionable(true);
        mailSummary.setIsAttention(true);
        mailSummary.setIsHighPriority(true);
        mailSummary.setIsStarMarked(true);
        mailSummary.setIsUnread(true);
        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary.setMeetingPreview("Meeting Preview");
        mailSummary.setMessageId("42");
        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary.setObjective("Objective");
        mailSummary.setPriority("Priority");
        mailSummary.setPriorityReason("Just cause");
        mailSummary.setStarMarkReason("Just cause");
        mailSummary.setStarMarked(true);
        mailSummary.setSubject("Hello from the Dreaming Spires");
        mailSummary.setTag("Tag");
        mailSummary.setToUser("To User");
        mailSummary.setUserActions(new HashSet<>());
        mailSummary.setUserId("42");
        mailSummary.setMessageSummary(messageSummary);
        mailSummary.setType("Email");

        // Act and Assert
        assertEquals("failed", notificationService.sendFCMNotification("42", mailSummary));
    }

    /**
     * Test {@link NotificationService#sendFCMNotification(String, MailSummary)}
     * with {@code deviceId}, {@code mailSummary}.
     * <p>
     * Method under test:
     * {@link NotificationService#sendFCMNotification(String, MailSummary)}
     */
    @Test
    @DisplayName("Test sendFCMNotification(String, MailSummary) with 'deviceId', 'mailSummary'")
    @Disabled("TODO: Complete this test")
    void testSendFCMNotificationWithDeviceIdMailSummary4() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   - com.enttribe.emailagent.service.NotificationService
        //   when running class:
        //   package com.enttribe.emailagent.service;
        //   @org.springframework.boot.test.context.SpringBootTest
        //   @org.junit.runner.RunWith(value = org.springframework.test.context.junit4.SpringRunner.class) // if JUnit 4
        //   @org.junit.jupiter.api.extension.ExtendWith(value = org.springframework.test.context.junit.jupiter.SpringExtension.class) // if JUnit 5
        //   public class DiffblueFakeClass325 {
        //     @org.springframework.beans.factory.annotation.Autowired com.enttribe.emailagent.service.NotificationService notificationService;
        //     @org.junit.Test // if JUnit 4
        //     @org.junit.jupiter.api.Test // if JUnit 5
        //     public void testSpringContextLoads() {}
        //   }
        //   See https://diff.blue/R027 to resolve this issue.

        // Arrange
        MailSummary mailSummary = new MailSummary();
        mailSummary.setActionClearReason("Just cause");
        mailSummary.setActionOwner("Action Owner");
        mailSummary.setActionTaken(true);
        mailSummary.setAttachmentsList(new ArrayList<>());
        mailSummary.setCategory("Category");
        mailSummary.setCategoryReason("Just cause");
        mailSummary.setCcUser("Cc User");
        mailSummary.setConversationId("42");
        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setDecryptSummary("Decrypt Summary");
        mailSummary.setDeleted(true);
        mailSummary.setEmailTone("<EMAIL>");
        mailSummary.setEmailToneReason("Just cause");
        mailSummary.setFlagStatus("Flag Status");
        mailSummary.setFolderName("Folder Name");
        mailSummary.setFromUser("<EMAIL>");
        mailSummary.setHasAttachment(true);
        mailSummary.setId(1);
        mailSummary.setInternetMessageId("42");
        mailSummary.setIsActionable(true);
        mailSummary.setIsAttention(true);
        mailSummary.setIsHighPriority(true);
        mailSummary.setIsStarMarked(true);
        mailSummary.setIsUnread(true);
        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary.setMeetingPreview("Meeting Preview");
        mailSummary.setMessageId("42");
        mailSummary.setMessageSummary("Message Summary");
        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary.setObjective("Objective");
        mailSummary.setPriority("Priority");
        mailSummary.setPriorityReason("Just cause");
        mailSummary.setStarMarkReason("Just cause");
        mailSummary.setStarMarked(true);
        mailSummary.setSubject("Hello from the Dreaming Spires");
        mailSummary.setTag("Tag");
        mailSummary.setToUser("To User");
        mailSummary.setType("Type");
        mailSummary.setUserActions(new HashSet<>());
        mailSummary.setUserId("42");

        // Act
        notificationService.sendFCMNotification("42", mailSummary);
    }

    /**
     * Test {@link NotificationService#sendFCMNotification(String, MailSummary)}
     * with {@code deviceId}, {@code mailSummary}.
     * <ul>
     *   <li>Given {@code Mail Summary}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link NotificationService#sendFCMNotification(String, MailSummary)}
     */
    @Test
    @DisplayName("Test sendFCMNotification(String, MailSummary) with 'deviceId', 'mailSummary'; given 'Mail Summary'")
    void testSendFCMNotificationWithDeviceIdMailSummary_givenMailSummary() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        PollTimeInfoRepository pollTimeInfoRepository = mock(PollTimeInfoRepository.class);
        PollTimeInfoService pollTimeInfoService = new PollTimeInfoService();
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        NotificationService notificationService = new NotificationService(mailSummaryDao, emailUserDao,
                pollTimeInfoRepository, pollTimeInfoService, preferencesDao, new FCMTokenUtils());

        MailSummary mailSummary = new MailSummary();
        mailSummary.setActionClearReason("Just cause");
        mailSummary.setActionOwner("Action Owner");
        mailSummary.setActionTaken(true);
        mailSummary.setAttachmentsList(new ArrayList<>());
        mailSummary.setCategory("Category");
        mailSummary.setCategoryReason("Just cause");
        mailSummary.setCcUser("Cc User");
        mailSummary.setConversationId("42");
        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setDecryptSummary("Decrypt Summary");
        mailSummary.setDeleted(true);
        mailSummary.setEmailTone("<EMAIL>");
        mailSummary.setEmailToneReason("Just cause");
        mailSummary.setFlagStatus("Flag Status");
        mailSummary.setFolderName("Folder Name");
        mailSummary.setFromUser("<EMAIL>");
        mailSummary.setHasAttachment(true);
        mailSummary.setId(1);
        mailSummary.setInternetMessageId("42");
        mailSummary.setIsActionable(true);
        mailSummary.setIsAttention(true);
        mailSummary.setIsHighPriority(true);
        mailSummary.setIsStarMarked(true);
        mailSummary.setIsUnread(true);
        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary.setMeetingPreview("Meeting Preview");
        mailSummary.setMessageId("42");
        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary.setObjective("Objective");
        mailSummary.setPriority("Priority");
        mailSummary.setPriorityReason("Just cause");
        mailSummary.setStarMarkReason("Just cause");
        mailSummary.setStarMarked(true);
        mailSummary.setSubject("Hello from the Dreaming Spires");
        mailSummary.setTag("Tag");
        mailSummary.setToUser("To User");
        mailSummary.setUserActions(new HashSet<>());
        mailSummary.setUserId("42");
        mailSummary.setMessageSummary("Mail Summary");
        mailSummary.setType("Email");

        // Act and Assert
        assertEquals("failed", notificationService.sendFCMNotification("42", mailSummary));
    }

    /**
     * Test {@link NotificationService#sendFCMNotification(String, MailSummary)}
     * with {@code deviceId}, {@code mailSummary}.
     * <ul>
     *   <li>Given {@code null}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link NotificationService#sendFCMNotification(String, MailSummary)}
     */
    @Test
    @DisplayName("Test sendFCMNotification(String, MailSummary) with 'deviceId', 'mailSummary'; given 'null'")
    void testSendFCMNotificationWithDeviceIdMailSummary_givenNull() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        PollTimeInfoRepository pollTimeInfoRepository = mock(PollTimeInfoRepository.class);
        PollTimeInfoService pollTimeInfoService = new PollTimeInfoService();
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        NotificationService notificationService = new NotificationService(mailSummaryDao, emailUserDao,
                pollTimeInfoRepository, pollTimeInfoService, preferencesDao, new FCMTokenUtils());

        MailSummary mailSummary = new MailSummary();
        mailSummary.setActionClearReason("Just cause");
        mailSummary.setActionOwner("Action Owner");
        mailSummary.setActionTaken(true);
        mailSummary.setAttachmentsList(new ArrayList<>());
        mailSummary.setCategory("Category");
        mailSummary.setCategoryReason("Just cause");
        mailSummary.setCcUser("Cc User");
        mailSummary.setConversationId("42");
        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setDecryptSummary("Decrypt Summary");
        mailSummary.setDeleted(true);
        mailSummary.setEmailTone("<EMAIL>");
        mailSummary.setEmailToneReason("Just cause");
        mailSummary.setFlagStatus("Flag Status");
        mailSummary.setFolderName("Folder Name");
        mailSummary.setFromUser("<EMAIL>");
        mailSummary.setHasAttachment(true);
        mailSummary.setId(1);
        mailSummary.setInternetMessageId("42");
        mailSummary.setIsActionable(true);
        mailSummary.setIsAttention(true);
        mailSummary.setIsHighPriority(true);
        mailSummary.setIsStarMarked(true);
        mailSummary.setIsUnread(true);
        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary.setMeetingPreview("Meeting Preview");
        mailSummary.setMessageId("42");
        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary.setObjective("Objective");
        mailSummary.setPriority("Priority");
        mailSummary.setPriorityReason("Just cause");
        mailSummary.setStarMarkReason("Just cause");
        mailSummary.setStarMarked(true);
        mailSummary.setSubject("Hello from the Dreaming Spires");
        mailSummary.setTag("Tag");
        mailSummary.setToUser("To User");
        mailSummary.setUserActions(new HashSet<>());
        mailSummary.setUserId("42");
        mailSummary.setMessageSummary(null);
        mailSummary.setType("Email");

        // Act and Assert
        assertEquals("failed", notificationService.sendFCMNotification("42", mailSummary));
    }

    /**
     * Test {@link NotificationService#sendFCMNotification(String, MailSummary)}
     * with {@code deviceId}, {@code mailSummary}.
     * <ul>
     *   <li>Given {@code null}.</li>
     *   <li>When {@code null}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link NotificationService#sendFCMNotification(String, MailSummary)}
     */
    @Test
    @DisplayName("Test sendFCMNotification(String, MailSummary) with 'deviceId', 'mailSummary'; given 'null'; when 'null'")
    void testSendFCMNotificationWithDeviceIdMailSummary_givenNull_whenNull() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        PollTimeInfoRepository pollTimeInfoRepository = mock(PollTimeInfoRepository.class);
        PollTimeInfoService pollTimeInfoService = new PollTimeInfoService();
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        NotificationService notificationService = new NotificationService(mailSummaryDao, emailUserDao,
                pollTimeInfoRepository, pollTimeInfoService, preferencesDao, new FCMTokenUtils());

        MailSummary mailSummary = new MailSummary();
        mailSummary.setActionClearReason("Just cause");
        mailSummary.setActionOwner("Action Owner");
        mailSummary.setActionTaken(true);
        mailSummary.setAttachmentsList(new ArrayList<>());
        mailSummary.setCategory("Category");
        mailSummary.setCategoryReason("Just cause");
        mailSummary.setCcUser("Cc User");
        mailSummary.setConversationId("42");
        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setDecryptSummary("Decrypt Summary");
        mailSummary.setDeleted(true);
        mailSummary.setEmailTone("<EMAIL>");
        mailSummary.setEmailToneReason("Just cause");
        mailSummary.setFlagStatus("Flag Status");
        mailSummary.setFolderName("Folder Name");
        mailSummary.setFromUser("<EMAIL>");
        mailSummary.setHasAttachment(true);
        mailSummary.setId(1);
        mailSummary.setInternetMessageId("42");
        mailSummary.setIsActionable(true);
        mailSummary.setIsAttention(true);
        mailSummary.setIsHighPriority(true);
        mailSummary.setIsStarMarked(true);
        mailSummary.setIsUnread(true);
        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary.setMeetingPreview("Meeting Preview");
        mailSummary.setMessageId("42");
        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary.setObjective("Objective");
        mailSummary.setPriority("Priority");
        mailSummary.setPriorityReason("Just cause");
        mailSummary.setStarMarkReason("Just cause");
        mailSummary.setStarMarked(true);
        mailSummary.setSubject("Hello from the Dreaming Spires");
        mailSummary.setTag("Tag");
        mailSummary.setToUser("To User");
        mailSummary.setUserActions(new HashSet<>());
        mailSummary.setUserId("42");
        mailSummary.setMessageSummary(null);
        mailSummary.setType("Email");

        // Act and Assert
        assertEquals("failed", notificationService.sendFCMNotification(null, mailSummary));
    }

    /**
     * Test {@link NotificationService#sendFCMNotification(String, MailSummary)}
     * with {@code deviceId}, {@code mailSummary}.
     * <ul>
     *   <li>Given {@code %s %s}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link NotificationService#sendFCMNotification(String, MailSummary)}
     */
    @Test
    @DisplayName("Test sendFCMNotification(String, MailSummary) with 'deviceId', 'mailSummary'; given '%s %s'")
    void testSendFCMNotificationWithDeviceIdMailSummary_givenSS() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        IMailSummaryDao mailSummaryDao = mock(IMailSummaryDao.class);
        EmailUserDao emailUserDao = mock(EmailUserDao.class);
        PollTimeInfoRepository pollTimeInfoRepository = mock(PollTimeInfoRepository.class);
        PollTimeInfoService pollTimeInfoService = new PollTimeInfoService();
        IEmailPreferencesDao preferencesDao = mock(IEmailPreferencesDao.class);
        NotificationService notificationService = new NotificationService(mailSummaryDao, emailUserDao,
                pollTimeInfoRepository, pollTimeInfoService, preferencesDao, new FCMTokenUtils());

        MailSummary mailSummary = new MailSummary();
        mailSummary.setActionClearReason("Just cause");
        mailSummary.setActionOwner("Action Owner");
        mailSummary.setActionTaken(true);
        mailSummary.setAttachmentsList(new ArrayList<>());
        mailSummary.setCategory("%s\n%s");
        mailSummary.setCategoryReason("Just cause");
        mailSummary.setCcUser("Cc User");
        mailSummary.setConversationId("42");
        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setDecryptSummary("Decrypt Summary");
        mailSummary.setDeleted(true);
        mailSummary.setEmailTone("<EMAIL>");
        mailSummary.setEmailToneReason("Just cause");
        mailSummary.setFlagStatus("Flag Status");
        mailSummary.setFolderName("Folder Name");
        mailSummary.setFromUser("<EMAIL>");
        mailSummary.setHasAttachment(true);
        mailSummary.setId(1);
        mailSummary.setInternetMessageId("42");
        mailSummary.setIsActionable(true);
        mailSummary.setIsAttention(true);
        mailSummary.setIsHighPriority(true);
        mailSummary.setIsStarMarked(true);
        mailSummary.setIsUnread(true);
        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary.setMeetingPreview("Meeting Preview");
        mailSummary.setMessageId("42");
        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary.setObjective("Objective");
        mailSummary.setPriority("Priority");
        mailSummary.setPriorityReason("Just cause");
        mailSummary.setStarMarkReason("Just cause");
        mailSummary.setStarMarked(true);
        mailSummary.setSubject("Hello from the Dreaming Spires");
        mailSummary.setTag("Tag");
        mailSummary.setToUser("To User");
        mailSummary.setUserActions(new HashSet<>());
        mailSummary.setUserId("42");
        mailSummary.setMessageSummary("{}");
        mailSummary.setType("Email");

        // Act and Assert
        assertEquals("failed", notificationService.sendFCMNotification("42", mailSummary));
    }

    /**
     * Test {@link NotificationService#getBearerToken(String)}.
     * <p>
     * Method under test: {@link NotificationService#getBearerToken(String)}
     */
    @Test
    @DisplayName("Test getBearerToken(String)")
    void testGetBearerToken() {
        // Arrange, Act and Assert
        assertNull(NotificationService.getBearerToken("3"));
    }
}
