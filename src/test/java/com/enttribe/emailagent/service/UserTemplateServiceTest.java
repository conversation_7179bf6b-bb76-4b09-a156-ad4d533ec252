package com.enttribe.emailagent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.entity.UserTemplate;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.repository.UserTemplateRepository;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.repository.CrudRepository;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {UserTemplateService.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class UserTemplateServiceTest {
    @MockBean
    private UserContextHolder userContextHolder;

    @MockBean
    private UserTemplateRepository userTemplateRepository;

    @Autowired
    private UserTemplateService userTemplateService;

    /**
     * Test {@link UserTemplateService#add(Map)}.
     * <ul>
     *   <li>Given {@code Inside @method add. @param : requestBody -> {}}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#add(Map)}
     */
    @Test
    @DisplayName("Test add(Map); given 'Inside @method add. @param : requestBody -> {}'")
    void testAdd_givenInsideMethodAddParamRequestBody() {
        // Arrange
        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(new HashMap<>());
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);

        UserTemplate userTemplate2 = new UserTemplate();
        userTemplate2.setData("Data");
        userTemplate2.setDataMap(new HashMap<>());
        userTemplate2.setEmail("<EMAIL>");
        userTemplate2.setId(1);
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);
        when(userTemplateRepository.save(Mockito.<UserTemplate>any())).thenReturn(userTemplate2);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        HashMap<String, String> requestBody = new HashMap<>();
        requestBody.put("Inside @method add. @param : requestBody -> {}", "Inside @method add. @param : requestBody -> {}");

        // Act
        Map<String, String> actualAddResult = userTemplateService.add(requestBody);

        // Assert
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        verify(userTemplateRepository).save(isA(UserTemplate.class));
        assertEquals(1, actualAddResult.size());
        assertEquals("Template created successfully", actualAddResult.get("result"));
    }

    /**
     * Test {@link UserTemplateService#add(Map)}.
     * <ul>
     *   <li>Given {@link UserTemplate} (default constructor) Data is
     * {@code Data}.</li>
     *   <li>When {@link HashMap#HashMap()}.</li>
     *   <li>Then return size is one.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#add(Map)}
     */
    @Test
    @DisplayName("Test add(Map); given UserTemplate (default constructor) Data is 'Data'; when HashMap(); then return size is one")
    void testAdd_givenUserTemplateDataIsData_whenHashMap_thenReturnSizeIsOne() {
        // Arrange
        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(new HashMap<>());
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);

        UserTemplate userTemplate2 = new UserTemplate();
        userTemplate2.setData("Data");
        userTemplate2.setDataMap(new HashMap<>());
        userTemplate2.setEmail("<EMAIL>");
        userTemplate2.setId(1);
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);
        when(userTemplateRepository.save(Mockito.<UserTemplate>any())).thenReturn(userTemplate2);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        Map<String, String> actualAddResult = userTemplateService.add(new HashMap<>());

        // Assert
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        verify(userTemplateRepository).save(isA(UserTemplate.class));
        assertEquals(1, actualAddResult.size());
        assertEquals("Template created successfully", actualAddResult.get("result"));
    }

    /**
     * Test {@link UserTemplateService#add(Map)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#add(Map)}
     */
    @Test
    @DisplayName("Test add(Map); then throw ResourceNotFoundException")
    void testAdd_thenThrowResourceNotFoundException() {
        // Arrange
        when(userTemplateRepository.findByEmail(Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> userTemplateService.add(new HashMap<>()));
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
    }

    /**
     * Test {@link UserTemplateService#update(Map)}.
     * <ul>
     *   <li>Given {@code Inside @method add. @param : requestBody -> {}}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#update(Map)}
     */
    @Test
    @DisplayName("Test update(Map); given 'Inside @method add. @param : requestBody -> {}'")
    void testUpdate_givenInsideMethodAddParamRequestBody() {
        // Arrange
        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(new HashMap<>());
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);

        UserTemplate userTemplate2 = new UserTemplate();
        userTemplate2.setData("Data");
        userTemplate2.setDataMap(new HashMap<>());
        userTemplate2.setEmail("<EMAIL>");
        userTemplate2.setId(1);
        when(userTemplateRepository.save(Mockito.<UserTemplate>any())).thenReturn(userTemplate2);
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        HashMap<String, String> requestBody = new HashMap<>();
        requestBody.put("Inside @method add. @param : requestBody -> {}", "Inside @method add. @param : requestBody -> {}");

        // Act
        Map<String, String> actualUpdateResult = userTemplateService.update(requestBody);

        // Assert
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        verify(userTemplateRepository).save(isA(UserTemplate.class));
        assertEquals(1, actualUpdateResult.size());
        assertEquals("Template updated successfully", actualUpdateResult.get("result"));
    }

    /**
     * Test {@link UserTemplateService#update(Map)}.
     * <ul>
     *   <li>Given {@link UserTemplateRepository} {@link CrudRepository#save(Object)}
     * return {@link UserTemplate} (default constructor).</li>
     *   <li>Then return size is one.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#update(Map)}
     */
    @Test
    @DisplayName("Test update(Map); given UserTemplateRepository save(Object) return UserTemplate (default constructor); then return size is one")
    void testUpdate_givenUserTemplateRepositorySaveReturnUserTemplate_thenReturnSizeIsOne() {
        // Arrange
        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(new HashMap<>());
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);

        UserTemplate userTemplate2 = new UserTemplate();
        userTemplate2.setData("Data");
        userTemplate2.setDataMap(new HashMap<>());
        userTemplate2.setEmail("<EMAIL>");
        userTemplate2.setId(1);
        when(userTemplateRepository.save(Mockito.<UserTemplate>any())).thenReturn(userTemplate2);
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        Map<String, String> actualUpdateResult = userTemplateService.update(new HashMap<>());

        // Assert
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        verify(userTemplateRepository).save(isA(UserTemplate.class));
        assertEquals(1, actualUpdateResult.size());
        assertEquals("Template updated successfully", actualUpdateResult.get("result"));
    }

    /**
     * Test {@link UserTemplateService#update(Map)}.
     * <ul>
     *   <li>Then throw {@link BusinessException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#update(Map)}
     */
    @Test
    @DisplayName("Test update(Map); then throw BusinessException")
    void testUpdate_thenThrowBusinessException() {
        // Arrange
        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(new HashMap<>());
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);
        when(userTemplateRepository.save(Mockito.<UserTemplate>any()))
                .thenThrow(new BusinessException("An error occurred"));
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(BusinessException.class, () -> userTemplateService.update(new HashMap<>()));
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        verify(userTemplateRepository).save(isA(UserTemplate.class));
    }

    /**
     * Test {@link UserTemplateService#delete(String)}.
     * <ul>
     *   <li>Given {@link UserTemplate} (default constructor) Data is
     * {@code Data}.</li>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#delete(String)}
     */
    @Test
    @DisplayName("Test delete(String); given UserTemplate (default constructor) Data is 'Data'; then throw ResourceNotFoundException")
    void testDelete_givenUserTemplateDataIsData_thenThrowResourceNotFoundException() {
        // Arrange
        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(new HashMap<>());
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> userTemplateService.delete("Key"));
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
    }

    /**
     * Test {@link UserTemplateService#delete(String)}.
     * <ul>
     *   <li>Then throw {@link BusinessException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#delete(String)}
     */
    @Test
    @DisplayName("Test delete(String); then throw BusinessException")
    void testDelete_thenThrowBusinessException() {
        // Arrange
        when(userTemplateRepository.findByEmail(Mockito.<String>any()))
                .thenThrow(new BusinessException("An error occurred"));

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(BusinessException.class, () -> userTemplateService.delete("Key"));
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
    }

    /**
     * Test {@link UserTemplateService#get(String)}.
     * <ul>
     *   <li>Given {@link HashMap#HashMap()} {@code Fetched email: {}} is
     * {@code Fetched email: {}}.</li>
     *   <li>When {@code Key}.</li>
     *   <li>Then return {@code result} size is one.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#get(String)}
     */
    @Test
    @DisplayName("Test get(String); given HashMap() 'Fetched email: {}' is 'Fetched email: {}'; when 'Key'; then return 'result' size is one")
    void testGet_givenHashMapFetchedEmailIsFetchedEmail_whenKey_thenReturnResultSizeIsOne() {
        // Arrange
        HashMap<String, String> map = new HashMap<>();
        map.put("Fetched email: {}", "Fetched email: {}");
        map.put("Inside @method get. @param : key -> {}", "Inside @method get. @param : key -> {}");

        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(map);
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        Map<String, Object> actualGetResult = userTemplateService.get("Key");

        // Assert
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        assertEquals(1, actualGetResult.size());
        Object getResult = actualGetResult.get("result");
        assertTrue(getResult instanceof Map);
        assertEquals(1, ((Map<String, String>) getResult).size());
        assertEquals("Inside @method get. @param : key -> {}",
                ((Map<String, String>) getResult).get("Inside @method get. @param : key -> {}"));
    }

    /**
     * Test {@link UserTemplateService#get(String)}.
     * <ul>
     *   <li>Given {@link UserTemplate} (default constructor) Data is
     * {@code Data}.</li>
     *   <li>When empty string.</li>
     *   <li>Then return {@code result} Empty.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#get(String)}
     */
    @Test
    @DisplayName("Test get(String); given UserTemplate (default constructor) Data is 'Data'; when empty string; then return 'result' Empty")
    void testGet_givenUserTemplateDataIsData_whenEmptyString_thenReturnResultEmpty() {
        // Arrange
        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(new HashMap<>());
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        Map<String, Object> actualGetResult = userTemplateService.get("");

        // Assert
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        assertEquals(1, actualGetResult.size());
        Object getResult = actualGetResult.get("result");
        assertTrue(getResult instanceof Map);
        assertTrue(((Map<Object, Object>) getResult).isEmpty());
    }

    /**
     * Test {@link UserTemplateService#get(String)}.
     * <ul>
     *   <li>Given {@link UserTemplate} (default constructor) Data is
     * {@code Data}.</li>
     *   <li>When {@code Key}.</li>
     *   <li>Then return {@code result} Empty.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#get(String)}
     */
    @Test
    @DisplayName("Test get(String); given UserTemplate (default constructor) Data is 'Data'; when 'Key'; then return 'result' Empty")
    void testGet_givenUserTemplateDataIsData_whenKey_thenReturnResultEmpty() {
        // Arrange
        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(new HashMap<>());
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        Map<String, Object> actualGetResult = userTemplateService.get("Key");

        // Assert
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        assertEquals(1, actualGetResult.size());
        Object getResult = actualGetResult.get("result");
        assertTrue(getResult instanceof Map);
        assertTrue(((Map<Object, Object>) getResult).isEmpty());
    }

    /**
     * Test {@link UserTemplateService#get(String)}.
     * <ul>
     *   <li>Given {@link UserTemplate} (default constructor) Data is
     * {@code Data}.</li>
     *   <li>When {@code null}.</li>
     *   <li>Then return {@code result} Empty.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#get(String)}
     */
    @Test
    @DisplayName("Test get(String); given UserTemplate (default constructor) Data is 'Data'; when 'null'; then return 'result' Empty")
    void testGet_givenUserTemplateDataIsData_whenNull_thenReturnResultEmpty() {
        // Arrange
        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(new HashMap<>());
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        Map<String, Object> actualGetResult = userTemplateService.get(null);

        // Assert
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        assertEquals(1, actualGetResult.size());
        Object getResult = actualGetResult.get("result");
        assertTrue(getResult instanceof Map);
        assertTrue(((Map<Object, Object>) getResult).isEmpty());
    }

    /**
     * Test {@link UserTemplateService#get(String)}.
     * <ul>
     *   <li>Then return {@code result} size is one.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#get(String)}
     */
    @Test
    @DisplayName("Test get(String); then return 'result' size is one")
    void testGet_thenReturnResultSizeIsOne() {
        // Arrange
        HashMap<String, String> map = new HashMap<>();
        map.put("Inside @method get. @param : key -> {}", "Inside @method get. @param : key -> {}");

        UserTemplate userTemplate = new UserTemplate();
        userTemplate.setData("Data");
        userTemplate.setDataMap(map);
        userTemplate.setEmail("<EMAIL>");
        userTemplate.setId(1);
        when(userTemplateRepository.findByEmail(Mockito.<String>any())).thenReturn(userTemplate);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        Map<String, Object> actualGetResult = userTemplateService.get("Key");

        // Assert
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
        assertEquals(1, actualGetResult.size());
        Object getResult = actualGetResult.get("result");
        assertTrue(getResult instanceof Map);
        assertEquals(1, ((Map<String, String>) getResult).size());
        assertEquals("Inside @method get. @param : key -> {}",
                ((Map<String, String>) getResult).get("Inside @method get. @param : key -> {}"));
    }

    /**
     * Test {@link UserTemplateService#get(String)}.
     * <ul>
     *   <li>Then throw {@link BusinessException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link UserTemplateService#get(String)}
     */
    @Test
    @DisplayName("Test get(String); then throw BusinessException")
    void testGet_thenThrowBusinessException() {
        // Arrange
        when(userTemplateRepository.findByEmail(Mockito.<String>any()))
                .thenThrow(new BusinessException("An error occurred"));

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(BusinessException.class, () -> userTemplateService.get("Key"));
        verify(userTemplateRepository).findByEmail(eq("<EMAIL>"));
        verify(userContextHolder).getCurrentUser();
    }
}
