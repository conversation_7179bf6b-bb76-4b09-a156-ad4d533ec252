package com.enttribe.emailagent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.PollTimeInfo;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.repository.IEmailPreferencesDao;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.PollTimeInfoRepository;
import com.enttribe.emailagent.repository.impl.EmailPreferenceDaoImpl;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@ContextConfiguration(classes = {EmailPreferencesService.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class EmailPreferencesServiceTest {
    @MockBean
    private EmailPreferenceDaoImpl emailPreferenceDaoImpl;

    @Autowired
    private EmailPreferencesService emailPreferencesService;

    @MockBean
    private IEmailPreferencesDao iEmailPreferencesDao;

    @MockBean
    private IMailSummaryDao iMailSummaryDao;

    @MockBean
    private PollTimeInfoRepository pollTimeInfoRepository;

    @MockBean
    private UserContextHolder userContextHolder;

    /**
     * Test {@link EmailPreferencesService#updateTimeZone(String)}.
     * <ul>
     *   <li>Then return {@link EmailPreferences} (default constructor).</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#updateTimeZone(String)}
     */
    @Test
    @DisplayName("Test updateTimeZone(String); then return EmailPreferences (default constructor)")
    void testUpdateTimeZone_thenReturnEmailPreferences() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");

        EmailPreferences emailPreferences2 = new EmailPreferences();
        emailPreferences2.setAllowNotification(true);
        emailPreferences2.setBlackListedDomain("Black Listed Domain");
        emailPreferences2.setBlackListedSender("Black Listed Sender");
        emailPreferences2.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences2.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences2.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences2.setContactNumber("42");
        emailPreferences2.setConversationId("42");
        emailPreferences2
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setDateFormat("2020-03-01");
        emailPreferences2.setDebugMode(true);
        emailPreferences2.setDeviceId("42");
        emailPreferences2.setDisplayName("Display Name");
        emailPreferences2.setEmailSender("<EMAIL>");
        emailPreferences2.setEmailSubject("<EMAIL>");
        emailPreferences2.setFontColor("Font Color");
        emailPreferences2.setFontFamily("Font Family");
        emailPreferences2.setFontSize("Font Size");
        emailPreferences2.setGcmId("42");
        emailPreferences2.setId(1);
        emailPreferences2.setImportantTags("Important Tags");
        emailPreferences2.setIsCategoryEnabled(true);
        emailPreferences2.setIsPriorityEnabled(true);
        emailPreferences2.setIsToneEnabled(true);
        emailPreferences2.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences2.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setMaskContent(true);
        emailPreferences2.setMeetingType("Meeting Type");
        emailPreferences2
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setOnDemandEnabled(true);
        emailPreferences2.setPreferredMeetingDuration(1);
        emailPreferences2.setSenderCompany("Sender Company");
        emailPreferences2.setTimeFormat("Time Format");
        emailPreferences2.setTimeZone("UTC");
        emailPreferences2.setUserId("42");
        when(iEmailPreferencesDao.save(Mockito.<EmailPreferences>any())).thenReturn(emailPreferences2);
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        EmailPreferences actualUpdateTimeZoneResult = emailPreferencesService.updateTimeZone("UTC");

        // Assert
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(userContextHolder).getCurrentUser();
        verify(iEmailPreferencesDao).save(isA(EmailPreferences.class));
        assertSame(emailPreferences2, actualUpdateTimeZoneResult);
    }

    /**
     * Test {@link EmailPreferencesService#updateDeviceId(String)}.
     * <ul>
     *   <li>Then return {@code success}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#updateDeviceId(String)}
     */
    @Test
    @DisplayName("Test updateDeviceId(String); then return 'success'")
    void testUpdateDeviceId_thenReturnSuccess() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");

        EmailPreferences emailPreferences2 = new EmailPreferences();
        emailPreferences2.setAllowNotification(true);
        emailPreferences2.setBlackListedDomain("Black Listed Domain");
        emailPreferences2.setBlackListedSender("Black Listed Sender");
        emailPreferences2.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences2.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences2.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences2.setContactNumber("42");
        emailPreferences2.setConversationId("42");
        emailPreferences2
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setDateFormat("2020-03-01");
        emailPreferences2.setDebugMode(true);
        emailPreferences2.setDeviceId("42");
        emailPreferences2.setDisplayName("Display Name");
        emailPreferences2.setEmailSender("<EMAIL>");
        emailPreferences2.setEmailSubject("<EMAIL>");
        emailPreferences2.setFontColor("Font Color");
        emailPreferences2.setFontFamily("Font Family");
        emailPreferences2.setFontSize("Font Size");
        emailPreferences2.setGcmId("42");
        emailPreferences2.setId(1);
        emailPreferences2.setImportantTags("Important Tags");
        emailPreferences2.setIsCategoryEnabled(true);
        emailPreferences2.setIsPriorityEnabled(true);
        emailPreferences2.setIsToneEnabled(true);
        emailPreferences2.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences2.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setMaskContent(true);
        emailPreferences2.setMeetingType("Meeting Type");
        emailPreferences2
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setOnDemandEnabled(true);
        emailPreferences2.setPreferredMeetingDuration(1);
        emailPreferences2.setSenderCompany("Sender Company");
        emailPreferences2.setTimeFormat("Time Format");
        emailPreferences2.setTimeZone("UTC");
        emailPreferences2.setUserId("42");
        when(iEmailPreferencesDao.save(Mockito.<EmailPreferences>any())).thenReturn(emailPreferences2);
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        String actualUpdateDeviceIdResult = emailPreferencesService.updateDeviceId("42");

        // Assert
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(userContextHolder).getCurrentUser();
        verify(iEmailPreferencesDao).save(isA(EmailPreferences.class));
        assertEquals("success", actualUpdateDeviceIdResult);
    }

    /**
     * Test {@link EmailPreferencesService#create(EmailPreferences)}.
     * <p>
     * Method under test: {@link EmailPreferencesService#create(EmailPreferences)}
     */
    @Test
    @DisplayName("Test create(EmailPreferences)")
    void testCreate() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(iEmailPreferencesDao.save(Mockito.<EmailPreferences>any())).thenReturn(emailPreferences);

        EmailPreferences preferences = new EmailPreferences();
        preferences.setAllowNotification(true);
        preferences.setBlackListedDomain("Black Listed Domain");
        preferences.setBlackListedSender("Black Listed Sender");
        preferences.setBlackListedSubject("Hello from the Dreaming Spires");
        preferences.setCheckin(LocalTime.MIDNIGHT);
        preferences.setCheckout(LocalTime.MIDNIGHT);
        preferences.setContactNumber("42");
        preferences.setConversationId("42");
        preferences.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        preferences.setDateFormat("2020-03-01");
        preferences.setDebugMode(true);
        preferences.setDeviceId("42");
        preferences.setDisplayName("Display Name");
        preferences.setEmailSender("<EMAIL>");
        preferences.setEmailSubject("<EMAIL>");
        preferences.setFontColor("Font Color");
        preferences.setFontFamily("Font Family");
        preferences.setFontSize("Font Size");
        preferences.setGcmId("42");
        preferences.setId(1);
        preferences.setImportantTags("Important Tags");
        preferences.setIsCategoryEnabled(true);
        preferences.setIsPriorityEnabled(true);
        preferences.setIsToneEnabled(true);
        preferences.setKeyboardShortcuts("Keyboard Shortcuts");
        preferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        preferences.setMaskContent(true);
        preferences.setMeetingType("Meeting Type");
        preferences.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        preferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        preferences.setOnDemandEnabled(true);
        preferences.setPreferredMeetingDuration(1);
        preferences.setSenderCompany("Sender Company");
        preferences.setTimeFormat("Time Format");
        preferences.setTimeZone("UTC");
        preferences.setUserId("42");

        // Act
        EmailPreferences actualCreateResult = emailPreferencesService.create(preferences);

        // Assert
        verify(iEmailPreferencesDao).save(isA(EmailPreferences.class));
        assertSame(emailPreferences, actualCreateResult);
    }

    /**
     * Test {@link EmailPreferencesService#update(EmailPreferences)}.
     * <p>
     * Method under test: {@link EmailPreferencesService#update(EmailPreferences)}
     */
    @Test
    @DisplayName("Test update(EmailPreferences)")
    void testUpdate() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(iEmailPreferencesDao.save(Mockito.<EmailPreferences>any())).thenReturn(emailPreferences);

        EmailPreferences preferences = new EmailPreferences();
        preferences.setAllowNotification(true);
        preferences.setBlackListedDomain("Black Listed Domain");
        preferences.setBlackListedSender("Black Listed Sender");
        preferences.setBlackListedSubject("Hello from the Dreaming Spires");
        preferences.setCheckin(LocalTime.MIDNIGHT);
        preferences.setCheckout(LocalTime.MIDNIGHT);
        preferences.setContactNumber("42");
        preferences.setConversationId("42");
        preferences.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        preferences.setDateFormat("2020-03-01");
        preferences.setDebugMode(true);
        preferences.setDeviceId("42");
        preferences.setDisplayName("Display Name");
        preferences.setEmailSender("<EMAIL>");
        preferences.setEmailSubject("<EMAIL>");
        preferences.setFontColor("Font Color");
        preferences.setFontFamily("Font Family");
        preferences.setFontSize("Font Size");
        preferences.setGcmId("42");
        preferences.setId(1);
        preferences.setImportantTags("Important Tags");
        preferences.setIsCategoryEnabled(true);
        preferences.setIsPriorityEnabled(true);
        preferences.setIsToneEnabled(true);
        preferences.setKeyboardShortcuts("Keyboard Shortcuts");
        preferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        preferences.setMaskContent(true);
        preferences.setMeetingType("Meeting Type");
        preferences.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        preferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        preferences.setOnDemandEnabled(true);
        preferences.setPreferredMeetingDuration(1);
        preferences.setSenderCompany("Sender Company");
        preferences.setTimeFormat("Time Format");
        preferences.setTimeZone("UTC");
        preferences.setUserId("42");

        // Act
        EmailPreferences actualUpdateResult = emailPreferencesService.update(preferences);

        // Assert
        verify(iEmailPreferencesDao).save(isA(EmailPreferences.class));
        assertSame(emailPreferences, actualUpdateResult);
    }

    /**
     * Test
     * {@link EmailPreferencesService#saveOrUpdateEmailPreferences(EmailPreferences)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#saveOrUpdateEmailPreferences(EmailPreferences)}
     */
    @Test
    @DisplayName("Test saveOrUpdateEmailPreferences(EmailPreferences)")
    void testSaveOrUpdateEmailPreferences() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");

        EmailPreferences emailPreferences2 = new EmailPreferences();
        emailPreferences2.setAllowNotification(true);
        emailPreferences2.setBlackListedDomain("Black Listed Domain");
        emailPreferences2.setBlackListedSender("Black Listed Sender");
        emailPreferences2.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences2.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences2.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences2.setContactNumber("42");
        emailPreferences2.setConversationId("42");
        emailPreferences2
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setDateFormat("2020-03-01");
        emailPreferences2.setDebugMode(true);
        emailPreferences2.setDeviceId("42");
        emailPreferences2.setDisplayName("Display Name");
        emailPreferences2.setEmailSender("<EMAIL>");
        emailPreferences2.setEmailSubject("<EMAIL>");
        emailPreferences2.setFontColor("Font Color");
        emailPreferences2.setFontFamily("Font Family");
        emailPreferences2.setFontSize("Font Size");
        emailPreferences2.setGcmId("42");
        emailPreferences2.setId(1);
        emailPreferences2.setImportantTags("Important Tags");
        emailPreferences2.setIsCategoryEnabled(true);
        emailPreferences2.setIsPriorityEnabled(true);
        emailPreferences2.setIsToneEnabled(true);
        emailPreferences2.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences2.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setMaskContent(true);
        emailPreferences2.setMeetingType("Meeting Type");
        emailPreferences2
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setOnDemandEnabled(true);
        emailPreferences2.setPreferredMeetingDuration(1);
        emailPreferences2.setSenderCompany("Sender Company");
        emailPreferences2.setTimeFormat("Time Format");
        emailPreferences2.setTimeZone("UTC");
        emailPreferences2.setUserId("42");
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);
        when(iEmailPreferencesDao.save(Mockito.<EmailPreferences>any())).thenReturn(emailPreferences2);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        EmailPreferences preferences = new EmailPreferences();
        preferences.setAllowNotification(true);
        preferences.setBlackListedDomain("Black Listed Domain");
        preferences.setBlackListedSender("Black Listed Sender");
        preferences.setBlackListedSubject("Hello from the Dreaming Spires");
        preferences.setCheckin(LocalTime.MIDNIGHT);
        preferences.setCheckout(LocalTime.MIDNIGHT);
        preferences.setContactNumber("42");
        preferences.setConversationId("42");
        preferences.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        preferences.setDateFormat("2020-03-01");
        preferences.setDebugMode(true);
        preferences.setDeviceId("42");
        preferences.setDisplayName("Display Name");
        preferences.setEmailSender("<EMAIL>");
        preferences.setEmailSubject("<EMAIL>");
        preferences.setFontColor("Font Color");
        preferences.setFontFamily("Font Family");
        preferences.setFontSize("Font Size");
        preferences.setGcmId("42");
        preferences.setId(1);
        preferences.setImportantTags("Important Tags");
        preferences.setIsCategoryEnabled(true);
        preferences.setIsPriorityEnabled(true);
        preferences.setIsToneEnabled(true);
        preferences.setKeyboardShortcuts("Keyboard Shortcuts");
        preferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        preferences.setMaskContent(true);
        preferences.setMeetingType("Meeting Type");
        preferences.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        preferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        preferences.setOnDemandEnabled(true);
        preferences.setPreferredMeetingDuration(1);
        preferences.setSenderCompany("Sender Company");
        preferences.setTimeFormat("Time Format");
        preferences.setTimeZone("UTC");
        preferences.setUserId("42");

        // Act
        EmailPreferences actualSaveOrUpdateEmailPreferencesResult = emailPreferencesService
                .saveOrUpdateEmailPreferences(preferences);

        // Assert
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(userContextHolder).getCurrentUser();
        verify(iEmailPreferencesDao).save(isA(EmailPreferences.class));
        EmailPreferences emailPreferences3 = emailPreferencesService.getEmailPreferences();
        assertEquals("black listed domain", emailPreferences3.getBlackListedDomain());
        assertEquals("black listed sender", emailPreferences3.getBlackListedSender());
        assertEquals("hello from the dreaming spires", emailPreferences3.getBlackListedSubject());
        assertEquals("sender company", emailPreferences3.getSenderCompany());
        assertSame(emailPreferences2, actualSaveOrUpdateEmailPreferencesResult);
    }

    /**
     * Test {@link EmailPreferencesService#getEmailPreferences()}.
     * <ul>
     *   <li>Then return {@link EmailPreferences} (default constructor).</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getEmailPreferences()}
     */
    @Test
    @DisplayName("Test getEmailPreferences(); then return EmailPreferences (default constructor)")
    void testGetEmailPreferences_thenReturnEmailPreferences() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);

        PollTimeInfo pollTimeInfo = new PollTimeInfo();
        pollTimeInfo.setEmail("<EMAIL>");
        pollTimeInfo.setEndTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setId(1);
        pollTimeInfo.setStartTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        pollTimeInfo.setStatus(PollTimeInfo.Status.PROCESSING);
        Optional<PollTimeInfo> ofResult = Optional.of(pollTimeInfo);
        when(pollTimeInfoRepository.findFirstByEmailOrderByStartTimeDesc(Mockito.<String>any())).thenReturn(ofResult);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        EmailPreferences actualEmailPreferences = emailPreferencesService.getEmailPreferences();

        // Assert
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(pollTimeInfoRepository).findFirstByEmailOrderByStartTimeDesc(eq("42"));
        verify(userContextHolder).getCurrentUser();
        assertSame(emailPreferences, actualEmailPreferences);
    }

    /**
     * Test {@link EmailPreferencesService#getEmailPreferences()}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getEmailPreferences()}
     */
    @Test
    @DisplayName("Test getEmailPreferences(); then throw ResourceNotFoundException")
    void testGetEmailPreferences_thenThrowResourceNotFoundException() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);
        when(pollTimeInfoRepository.findFirstByEmailOrderByStartTimeDesc(Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailPreferencesService.getEmailPreferences());
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(pollTimeInfoRepository).findFirstByEmailOrderByStartTimeDesc(eq("42"));
        verify(userContextHolder).getCurrentUser();
    }

    /**
     * Test {@link EmailPreferencesService#checkPreference(String, String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#checkPreference(String, String)}
     */
    @Test
    @DisplayName("Test checkPreference(String, String)")
    void testCheckPreference() {
        // Arrange
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailPreferencesService.checkPreference("Preference", "Type"));
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(userContextHolder).getCurrentUser();
    }

    /**
     * Test {@link EmailPreferencesService#checkPreference(String, String)}.
     * <ul>
     *   <li>Given {@link EmailPreferences} (default constructor) AllowNotification is
     * {@code true}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#checkPreference(String, String)}
     */
    @Test
    @DisplayName("Test checkPreference(String, String); given EmailPreferences (default constructor) AllowNotification is 'true'")
    void testCheckPreference_givenEmailPreferencesAllowNotificationIsTrue() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailPreferencesService.checkPreference("Preference", "Type"));
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(userContextHolder).getCurrentUser();
    }

    /**
     * Test {@link EmailPreferencesService#addConversationId(String)}.
     * <ul>
     *   <li>Then return size is one.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#addConversationId(String)}
     */
    @Test
    @DisplayName("Test addConversationId(String); then return size is one")
    void testAddConversationId_thenReturnSizeIsOne() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");

        EmailPreferences emailPreferences2 = new EmailPreferences();
        emailPreferences2.setAllowNotification(true);
        emailPreferences2.setBlackListedDomain("Black Listed Domain");
        emailPreferences2.setBlackListedSender("Black Listed Sender");
        emailPreferences2.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences2.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences2.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences2.setContactNumber("42");
        emailPreferences2.setConversationId("42");
        emailPreferences2
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setDateFormat("2020-03-01");
        emailPreferences2.setDebugMode(true);
        emailPreferences2.setDeviceId("42");
        emailPreferences2.setDisplayName("Display Name");
        emailPreferences2.setEmailSender("<EMAIL>");
        emailPreferences2.setEmailSubject("<EMAIL>");
        emailPreferences2.setFontColor("Font Color");
        emailPreferences2.setFontFamily("Font Family");
        emailPreferences2.setFontSize("Font Size");
        emailPreferences2.setGcmId("42");
        emailPreferences2.setId(1);
        emailPreferences2.setImportantTags("Important Tags");
        emailPreferences2.setIsCategoryEnabled(true);
        emailPreferences2.setIsPriorityEnabled(true);
        emailPreferences2.setIsToneEnabled(true);
        emailPreferences2.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences2.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setMaskContent(true);
        emailPreferences2.setMeetingType("Meeting Type");
        emailPreferences2
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setOnDemandEnabled(true);
        emailPreferences2.setPreferredMeetingDuration(1);
        emailPreferences2.setSenderCompany("Sender Company");
        emailPreferences2.setTimeFormat("Time Format");
        emailPreferences2.setTimeZone("UTC");
        emailPreferences2.setUserId("42");
        when(iEmailPreferencesDao.save(Mockito.<EmailPreferences>any())).thenReturn(emailPreferences2);
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);
        when(iMailSummaryDao.setStarMarkedByConversationId(Mockito.<Boolean>any(), Mockito.<String>any())).thenReturn(1);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        Map<String, Object> actualAddConversationIdResult = emailPreferencesService.addConversationId("42");

        // Assert
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(iMailSummaryDao).setStarMarkedByConversationId(eq(true), eq("42"));
        verify(userContextHolder).getCurrentUser();
        verify(iEmailPreferencesDao).save(isA(EmailPreferences.class));
        assertEquals(1, actualAddConversationIdResult.size());
        assertEquals("failed", actualAddConversationIdResult.get("result"));
    }

    /**
     * Test {@link EmailPreferencesService#addConversationId(String)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#addConversationId(String)}
     */
    @Test
    @DisplayName("Test addConversationId(String); then throw ResourceNotFoundException")
    void testAddConversationId_thenThrowResourceNotFoundException() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");

        EmailPreferences emailPreferences2 = new EmailPreferences();
        emailPreferences2.setAllowNotification(true);
        emailPreferences2.setBlackListedDomain("Black Listed Domain");
        emailPreferences2.setBlackListedSender("Black Listed Sender");
        emailPreferences2.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences2.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences2.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences2.setContactNumber("42");
        emailPreferences2.setConversationId("42");
        emailPreferences2
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setDateFormat("2020-03-01");
        emailPreferences2.setDebugMode(true);
        emailPreferences2.setDeviceId("42");
        emailPreferences2.setDisplayName("Display Name");
        emailPreferences2.setEmailSender("<EMAIL>");
        emailPreferences2.setEmailSubject("<EMAIL>");
        emailPreferences2.setFontColor("Font Color");
        emailPreferences2.setFontFamily("Font Family");
        emailPreferences2.setFontSize("Font Size");
        emailPreferences2.setGcmId("42");
        emailPreferences2.setId(1);
        emailPreferences2.setImportantTags("Important Tags");
        emailPreferences2.setIsCategoryEnabled(true);
        emailPreferences2.setIsPriorityEnabled(true);
        emailPreferences2.setIsToneEnabled(true);
        emailPreferences2.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences2.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setMaskContent(true);
        emailPreferences2.setMeetingType("Meeting Type");
        emailPreferences2
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setOnDemandEnabled(true);
        emailPreferences2.setPreferredMeetingDuration(1);
        emailPreferences2.setSenderCompany("Sender Company");
        emailPreferences2.setTimeFormat("Time Format");
        emailPreferences2.setTimeZone("UTC");
        emailPreferences2.setUserId("42");
        when(iEmailPreferencesDao.save(Mockito.<EmailPreferences>any())).thenReturn(emailPreferences2);
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);
        when(iMailSummaryDao.setStarMarkedByConversationId(Mockito.<Boolean>any(), Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailPreferencesService.addConversationId("42"));
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(iMailSummaryDao).setStarMarkedByConversationId(eq(true), eq("42"));
        verify(userContextHolder).getCurrentUser();
        verify(iEmailPreferencesDao).save(isA(EmailPreferences.class));
    }

    /**
     * Test {@link EmailPreferencesService#deleteConversationId(String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#deleteConversationId(String)}
     */
    @Test
    @DisplayName("Test deleteConversationId(String)")
    void testDeleteConversationId() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");

        EmailPreferences emailPreferences2 = new EmailPreferences();
        emailPreferences2.setAllowNotification(true);
        emailPreferences2.setBlackListedDomain("Black Listed Domain");
        emailPreferences2.setBlackListedSender("Black Listed Sender");
        emailPreferences2.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences2.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences2.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences2.setContactNumber("42");
        emailPreferences2.setConversationId("42");
        emailPreferences2
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setDateFormat("2020-03-01");
        emailPreferences2.setDebugMode(true);
        emailPreferences2.setDeviceId("42");
        emailPreferences2.setDisplayName("Display Name");
        emailPreferences2.setEmailSender("<EMAIL>");
        emailPreferences2.setEmailSubject("<EMAIL>");
        emailPreferences2.setFontColor("Font Color");
        emailPreferences2.setFontFamily("Font Family");
        emailPreferences2.setFontSize("Font Size");
        emailPreferences2.setGcmId("42");
        emailPreferences2.setId(1);
        emailPreferences2.setImportantTags("Important Tags");
        emailPreferences2.setIsCategoryEnabled(true);
        emailPreferences2.setIsPriorityEnabled(true);
        emailPreferences2.setIsToneEnabled(true);
        emailPreferences2.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences2.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setMaskContent(true);
        emailPreferences2.setMeetingType("Meeting Type");
        emailPreferences2
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setOnDemandEnabled(true);
        emailPreferences2.setPreferredMeetingDuration(1);
        emailPreferences2.setSenderCompany("Sender Company");
        emailPreferences2.setTimeFormat("Time Format");
        emailPreferences2.setTimeZone("UTC");
        emailPreferences2.setUserId("42");
        when(iEmailPreferencesDao.save(Mockito.<EmailPreferences>any())).thenReturn(emailPreferences2);
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);
        when(iMailSummaryDao.setStarMarkedByConversationId(Mockito.<Boolean>any(), Mockito.<String>any())).thenReturn(1);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        Map<String, String> actualDeleteConversationIdResult = emailPreferencesService.deleteConversationId("42");

        // Assert
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(iMailSummaryDao).setStarMarkedByConversationId(eq(false), eq("42"));
        verify(userContextHolder).getCurrentUser();
        verify(iEmailPreferencesDao).save(isA(EmailPreferences.class));
        assertEquals("", emailPreferencesService.getEmailPreferences().getConversationId());
        assertEquals(1, actualDeleteConversationIdResult.size());
        assertEquals("success", actualDeleteConversationIdResult.get("result"));
    }

    /**
     * Test {@link EmailPreferencesService#deleteConversationId(String)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#deleteConversationId(String)}
     */
    @Test
    @DisplayName("Test deleteConversationId(String); then throw ResourceNotFoundException")
    void testDeleteConversationId_thenThrowResourceNotFoundException() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");

        EmailPreferences emailPreferences2 = new EmailPreferences();
        emailPreferences2.setAllowNotification(true);
        emailPreferences2.setBlackListedDomain("Black Listed Domain");
        emailPreferences2.setBlackListedSender("Black Listed Sender");
        emailPreferences2.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences2.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences2.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences2.setContactNumber("42");
        emailPreferences2.setConversationId("42");
        emailPreferences2
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setDateFormat("2020-03-01");
        emailPreferences2.setDebugMode(true);
        emailPreferences2.setDeviceId("42");
        emailPreferences2.setDisplayName("Display Name");
        emailPreferences2.setEmailSender("<EMAIL>");
        emailPreferences2.setEmailSubject("<EMAIL>");
        emailPreferences2.setFontColor("Font Color");
        emailPreferences2.setFontFamily("Font Family");
        emailPreferences2.setFontSize("Font Size");
        emailPreferences2.setGcmId("42");
        emailPreferences2.setId(1);
        emailPreferences2.setImportantTags("Important Tags");
        emailPreferences2.setIsCategoryEnabled(true);
        emailPreferences2.setIsPriorityEnabled(true);
        emailPreferences2.setIsToneEnabled(true);
        emailPreferences2.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences2.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setMaskContent(true);
        emailPreferences2.setMeetingType("Meeting Type");
        emailPreferences2
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setOnDemandEnabled(true);
        emailPreferences2.setPreferredMeetingDuration(1);
        emailPreferences2.setSenderCompany("Sender Company");
        emailPreferences2.setTimeFormat("Time Format");
        emailPreferences2.setTimeZone("UTC");
        emailPreferences2.setUserId("42");
        when(iEmailPreferencesDao.save(Mockito.<EmailPreferences>any())).thenReturn(emailPreferences2);
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);
        when(iMailSummaryDao.setStarMarkedByConversationId(Mockito.<Boolean>any(), Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailPreferencesService.deleteConversationId("42"));
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(iMailSummaryDao).setStarMarkedByConversationId(eq(false), eq("42"));
        verify(userContextHolder).getCurrentUser();
        verify(iEmailPreferencesDao).save(isA(EmailPreferences.class));
    }

    /**
     * Test {@link EmailPreferencesService#addPreferences(String, String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#addPreferences(String, String)}
     */
    @Test
    @DisplayName("Test addPreferences(String, String)")
    void testAddPreferences() {
        // Arrange
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailPreferencesService.addPreferences("Preference", "Type"));
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(userContextHolder).getCurrentUser();
    }

    /**
     * Test {@link EmailPreferencesService#addPreferences(String, String)}.
     * <ul>
     *   <li>Given {@link EmailPreferences} (default constructor) AllowNotification is
     * {@code true}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#addPreferences(String, String)}
     */
    @Test
    @DisplayName("Test addPreferences(String, String); given EmailPreferences (default constructor) AllowNotification is 'true'")
    void testAddPreferences_givenEmailPreferencesAllowNotificationIsTrue() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailPreferencesService.addPreferences("Preference", "Type"));
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(userContextHolder).getCurrentUser();
    }

    /**
     * Test {@link EmailPreferencesService#deletePreferences(String, String)}.
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#deletePreferences(String, String)}
     */
    @Test
    @DisplayName("Test deletePreferences(String, String)")
    void testDeletePreferences() {
        // Arrange
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class,
                () -> emailPreferencesService.deletePreferences("Preference", "Type"));
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(userContextHolder).getCurrentUser();
    }

    /**
     * Test {@link EmailPreferencesService#deletePreferences(String, String)}.
     * <ul>
     *   <li>Given {@link EmailPreferences} (default constructor) AllowNotification is
     * {@code true}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#deletePreferences(String, String)}
     */
    @Test
    @DisplayName("Test deletePreferences(String, String); given EmailPreferences (default constructor) AllowNotification is 'true'")
    void testDeletePreferences_givenEmailPreferencesAllowNotificationIsTrue() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class,
                () -> emailPreferencesService.deletePreferences("Preference", "Type"));
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(userContextHolder).getCurrentUser();
    }

    /**
     * Test
     * {@link EmailPreferencesService#search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}.
     * <ul>
     *   <li>Given {@link IEmailPreferencesDao}
     * {@link IEmailPreferencesDao#search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}
     * return {@link ArrayList#ArrayList()}.</li>
     *   <li>Then return Empty.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}
     */
    @Test
    @DisplayName("Test search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String); given IEmailPreferencesDao search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String) return ArrayList(); then return Empty")
    void testSearch_givenIEmailPreferencesDaoSearchReturnArrayList_thenReturnEmpty() {
        // Arrange
        when(iEmailPreferencesDao.search(Mockito.<Integer>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<Date>any(),
                Mockito.<Date>any(), Mockito.<String>any(), Mockito.<LocalTime>any(), Mockito.<LocalTime>any(),
                Mockito.<String>any(), Mockito.<String>any())).thenReturn(new ArrayList<>());
        Date createdTime = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());

        // Act
        List<EmailPreferences> actualSearchResult = emailPreferencesService.search(1, "42", "<EMAIL>",
                "Sender Company", "Important Tags", "Font Family", "Font Size", "<EMAIL>", "Black Listed Domain",
                "Hello from the Dreaming Spires", "UTC", createdTime,
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()), "42", LocalTime.MIDNIGHT,
                LocalTime.MIDNIGHT, "Font Color", "Display Name");

        // Assert
        verify(iEmailPreferencesDao).search(eq(1), eq("42"), eq("<EMAIL>"), eq("Sender Company"),
                eq("Important Tags"), eq("Font Family"), eq("Font Size"), eq("<EMAIL>"), eq("Black Listed Domain"),
                eq("Hello from the Dreaming Spires"), eq("UTC"), isA(Date.class), isA(Date.class), eq("42"),
                isA(LocalTime.class), isA(LocalTime.class), eq("Font Color"), eq("Display Name"));
        assertTrue(actualSearchResult.isEmpty());
    }

    /**
     * Test
     * {@link EmailPreferencesService#search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}
     */
    @Test
    @DisplayName("Test search(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String); then throw ResourceNotFoundException")
    void testSearch_thenThrowResourceNotFoundException() {
        // Arrange
        when(iEmailPreferencesDao.search(Mockito.<Integer>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<Date>any(),
                Mockito.<Date>any(), Mockito.<String>any(), Mockito.<LocalTime>any(), Mockito.<LocalTime>any(),
                Mockito.<String>any(), Mockito.<String>any())).thenThrow(new ResourceNotFoundException("An error occurred"));
        Date createdTime = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());

        // Act and Assert
        assertThrows(ResourceNotFoundException.class,
                () -> emailPreferencesService.search(1, "42", "<EMAIL>", "Sender Company", "Important Tags",
                        "Font Family", "Font Size", "<EMAIL>", "Black Listed Domain", "Hello from the Dreaming Spires",
                        "UTC", createdTime, Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()),
                        "42", LocalTime.MIDNIGHT, LocalTime.MIDNIGHT, "Font Color", "Display Name"));
        verify(iEmailPreferencesDao).search(eq(1), eq("42"), eq("<EMAIL>"), eq("Sender Company"),
                eq("Important Tags"), eq("Font Family"), eq("Font Size"), eq("<EMAIL>"), eq("Black Listed Domain"),
                eq("Hello from the Dreaming Spires"), eq("UTC"), isA(Date.class), isA(Date.class), eq("42"),
                isA(LocalTime.class), isA(LocalTime.class), eq("Font Color"), eq("Display Name"));
    }

    /**
     * Test
     * {@link EmailPreferencesService#count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}.
     * <ul>
     *   <li>Given {@link IEmailPreferencesDao}
     * {@link IEmailPreferencesDao#count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}
     * return three.</li>
     *   <li>Then return three.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}
     */
    @Test
    @DisplayName("Test count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String); given IEmailPreferencesDao count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String) return three; then return three")
    void testCount_givenIEmailPreferencesDaoCountReturnThree_thenReturnThree() {
        // Arrange
        when(iEmailPreferencesDao.count(Mockito.<Integer>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<Date>any(),
                Mockito.<Date>any(), Mockito.<String>any(), Mockito.<LocalTime>any(), Mockito.<LocalTime>any(),
                Mockito.<String>any(), Mockito.<String>any())).thenReturn(3L);
        Date createdTime = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());

        // Act
        long actualCountResult = emailPreferencesService.count(1, "42", "<EMAIL>", "Sender Company",
                "Important Tags", "Font Family", "Font Size", "<EMAIL>", "Black Listed Domain",
                "Hello from the Dreaming Spires", "UTC", createdTime,
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()), "42", LocalTime.MIDNIGHT,
                LocalTime.MIDNIGHT, "Font Color", "Display Name");

        // Assert
        verify(iEmailPreferencesDao).count(eq(1), eq("42"), eq("<EMAIL>"), eq("Sender Company"),
                eq("Important Tags"), eq("Font Family"), eq("Font Size"), eq("<EMAIL>"), eq("Black Listed Domain"),
                eq("Hello from the Dreaming Spires"), eq("UTC"), isA(Date.class), isA(Date.class), eq("42"),
                isA(LocalTime.class), isA(LocalTime.class), eq("Font Color"), eq("Display Name"));
        assertEquals(3L, actualCountResult);
    }

    /**
     * Test
     * {@link EmailPreferencesService#count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String)}
     */
    @Test
    @DisplayName("Test count(Integer, String, String, String, String, String, String, String, String, String, String, Date, Date, String, LocalTime, LocalTime, String, String); then throw ResourceNotFoundException")
    void testCount_thenThrowResourceNotFoundException() {
        // Arrange
        when(iEmailPreferencesDao.count(Mockito.<Integer>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<Date>any(),
                Mockito.<Date>any(), Mockito.<String>any(), Mockito.<LocalTime>any(), Mockito.<LocalTime>any(),
                Mockito.<String>any(), Mockito.<String>any())).thenThrow(new ResourceNotFoundException("An error occurred"));
        Date createdTime = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());

        // Act and Assert
        assertThrows(ResourceNotFoundException.class,
                () -> emailPreferencesService.count(1, "42", "<EMAIL>", "Sender Company", "Important Tags",
                        "Font Family", "Font Size", "<EMAIL>", "Black Listed Domain", "Hello from the Dreaming Spires",
                        "UTC", createdTime, Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()),
                        "42", LocalTime.MIDNIGHT, LocalTime.MIDNIGHT, "Font Color", "Display Name"));
        verify(iEmailPreferencesDao).count(eq(1), eq("42"), eq("<EMAIL>"), eq("Sender Company"),
                eq("Important Tags"), eq("Font Family"), eq("Font Size"), eq("<EMAIL>"), eq("Black Listed Domain"),
                eq("Hello from the Dreaming Spires"), eq("UTC"), isA(Date.class), isA(Date.class), eq("42"),
                isA(LocalTime.class), isA(LocalTime.class), eq("Font Color"), eq("Display Name"));
    }

    /**
     * Test
     * {@link EmailPreferencesService#preferenceByFilter(Integer, Integer, Map)}.
     * <ul>
     *   <li>Then return Empty.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#preferenceByFilter(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test preferenceByFilter(Integer, Integer, Map); then return Empty")
    void testPreferenceByFilter_thenReturnEmpty() {
        // Arrange
        when(emailPreferenceDaoImpl.Filteredpreference(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenReturn(new ArrayList<>());

        // Act
        List<EmailPreferences> actualPreferenceByFilterResult = emailPreferencesService.preferenceByFilter(1, 1,
                new HashMap<>());

        // Assert
        verify(emailPreferenceDaoImpl).Filteredpreference(isA(Map.class), eq(1), eq(1));
        assertTrue(actualPreferenceByFilterResult.isEmpty());
    }

    /**
     * Test
     * {@link EmailPreferencesService#preferenceByFilter(Integer, Integer, Map)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#preferenceByFilter(Integer, Integer, Map)}
     */
    @Test
    @DisplayName("Test preferenceByFilter(Integer, Integer, Map); then throw ResourceNotFoundException")
    void testPreferenceByFilter_thenThrowResourceNotFoundException() {
        // Arrange
        when(emailPreferenceDaoImpl.Filteredpreference(Mockito.<Map<String, Object>>any(), anyInt(), anyInt()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        // Act and Assert
        assertThrows(ResourceNotFoundException.class,
                () -> emailPreferencesService.preferenceByFilter(1, 1, new HashMap<>()));
        verify(emailPreferenceDaoImpl).Filteredpreference(isA(Map.class), eq(1), eq(1));
    }

    /**
     * Test {@link EmailPreferencesService#preferenceCountByFilter(Map)}.
     * <ul>
     *   <li>Then return zero.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#preferenceCountByFilter(Map)}
     */
    @Test
    @DisplayName("Test preferenceCountByFilter(Map); then return zero")
    void testPreferenceCountByFilter_thenReturnZero() {
        // Arrange
        when(emailPreferenceDaoImpl.FilteredpreferenceCount(Mockito.<Map<String, Object>>any()))
                .thenReturn(new ArrayList<>());

        // Act
        long actualPreferenceCountByFilterResult = emailPreferencesService.preferenceCountByFilter(new HashMap<>());

        // Assert
        verify(emailPreferenceDaoImpl).FilteredpreferenceCount(isA(Map.class));
        assertEquals(0L, actualPreferenceCountByFilterResult);
    }

    /**
     * Test {@link EmailPreferencesService#preferenceCountByFilter(Map)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#preferenceCountByFilter(Map)}
     */
    @Test
    @DisplayName("Test preferenceCountByFilter(Map); then throw ResourceNotFoundException")
    void testPreferenceCountByFilter_thenThrowResourceNotFoundException() {
        // Arrange
        when(emailPreferenceDaoImpl.FilteredpreferenceCount(Mockito.<Map<String, Object>>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        // Act and Assert
        assertThrows(ResourceNotFoundException.class,
                () -> emailPreferencesService.preferenceCountByFilter(new HashMap<>()));
        verify(emailPreferenceDaoImpl).FilteredpreferenceCount(isA(Map.class));
    }

    /**
     * Test {@link EmailPreferencesService#getTimeZonesLike(String)}.
     * <ul>
     *   <li>When {@code Africa/Abidjan}.</li>
     *   <li>Then return first is {@code Africa/Abidjan}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getTimeZonesLike(String)}
     */
    @Test
    @DisplayName("Test getTimeZonesLike(String); when 'Africa/Abidjan'; then return first is 'Africa/Abidjan'")
    void testGetTimeZonesLike_whenAfricaAbidjan_thenReturnFirstIsAfricaAbidjan() {
        // Arrange and Act
        List<String> actualTimeZonesLike = emailPreferencesService.getTimeZonesLike("Africa/Abidjan");

        // Assert
        assertEquals(1, actualTimeZonesLike.size());
        assertEquals("Africa/Abidjan", actualTimeZonesLike.get(0));
    }

    /**
     * Test {@link EmailPreferencesService#getTimeZonesLike(String)}.
     * <ul>
     *   <li>When {@code Africa/Accra}.</li>
     *   <li>Then return first is {@code Africa/Accra}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getTimeZonesLike(String)}
     */
    @Test
    @DisplayName("Test getTimeZonesLike(String); when 'Africa/Accra'; then return first is 'Africa/Accra'")
    void testGetTimeZonesLike_whenAfricaAccra_thenReturnFirstIsAfricaAccra() {
        // Arrange and Act
        List<String> actualTimeZonesLike = emailPreferencesService.getTimeZonesLike("Africa/Accra");

        // Assert
        assertEquals(1, actualTimeZonesLike.size());
        assertEquals("Africa/Accra", actualTimeZonesLike.get(0));
    }

    /**
     * Test {@link EmailPreferencesService#getTimeZonesLike(String)}.
     * <ul>
     *   <li>When {@code Africa/Addis_Ababa}.</li>
     *   <li>Then return first is {@code Africa/Addis_Ababa}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getTimeZonesLike(String)}
     */
    @Test
    @DisplayName("Test getTimeZonesLike(String); when 'Africa/Addis_Ababa'; then return first is 'Africa/Addis_Ababa'")
    void testGetTimeZonesLike_whenAfricaAddisAbaba_thenReturnFirstIsAfricaAddisAbaba() {
        // Arrange and Act
        List<String> actualTimeZonesLike = emailPreferencesService.getTimeZonesLike("Africa/Addis_Ababa");

        // Assert
        assertEquals(1, actualTimeZonesLike.size());
        assertEquals("Africa/Addis_Ababa", actualTimeZonesLike.get(0));
    }

    /**
     * Test {@link EmailPreferencesService#getTimeZonesLike(String)}.
     * <ul>
     *   <li>When {@code Africa/Algiers}.</li>
     *   <li>Then return first is {@code Africa/Algiers}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getTimeZonesLike(String)}
     */
    @Test
    @DisplayName("Test getTimeZonesLike(String); when 'Africa/Algiers'; then return first is 'Africa/Algiers'")
    void testGetTimeZonesLike_whenAfricaAlgiers_thenReturnFirstIsAfricaAlgiers() {
        // Arrange and Act
        List<String> actualTimeZonesLike = emailPreferencesService.getTimeZonesLike("Africa/Algiers");

        // Assert
        assertEquals(1, actualTimeZonesLike.size());
        assertEquals("Africa/Algiers", actualTimeZonesLike.get(0));
    }

    /**
     * Test {@link EmailPreferencesService#getTimeZonesLike(String)}.
     * <ul>
     *   <li>When {@code Africa/Asmera}.</li>
     *   <li>Then return first is {@code Africa/Asmera}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getTimeZonesLike(String)}
     */
    @Test
    @DisplayName("Test getTimeZonesLike(String); when 'Africa/Asmera'; then return first is 'Africa/Asmera'")
    void testGetTimeZonesLike_whenAfricaAsmera_thenReturnFirstIsAfricaAsmera() {
        // Arrange and Act
        List<String> actualTimeZonesLike = emailPreferencesService.getTimeZonesLike("Africa/Asmera");

        // Assert
        assertEquals(1, actualTimeZonesLike.size());
        assertEquals("Africa/Asmera", actualTimeZonesLike.get(0));
    }

    /**
     * Test {@link EmailPreferencesService#getTimeZonesLike(String)}.
     * <ul>
     *   <li>When {@code Africa/Bamako}.</li>
     *   <li>Then return first is {@code Africa/Bamako}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getTimeZonesLike(String)}
     */
    @Test
    @DisplayName("Test getTimeZonesLike(String); when 'Africa/Bamako'; then return first is 'Africa/Bamako'")
    void testGetTimeZonesLike_whenAfricaBamako_thenReturnFirstIsAfricaBamako() {
        // Arrange and Act
        List<String> actualTimeZonesLike = emailPreferencesService.getTimeZonesLike("Africa/Bamako");

        // Assert
        assertEquals(1, actualTimeZonesLike.size());
        assertEquals("Africa/Bamako", actualTimeZonesLike.get(0));
    }

    /**
     * Test {@link EmailPreferencesService#getTimeZonesLike(String)}.
     * <ul>
     *   <li>When {@code Africa/Bangui}.</li>
     *   <li>Then return first is {@code Africa/Bangui}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getTimeZonesLike(String)}
     */
    @Test
    @DisplayName("Test getTimeZonesLike(String); when 'Africa/Bangui'; then return first is 'Africa/Bangui'")
    void testGetTimeZonesLike_whenAfricaBangui_thenReturnFirstIsAfricaBangui() {
        // Arrange and Act
        List<String> actualTimeZonesLike = emailPreferencesService.getTimeZonesLike("Africa/Bangui");

        // Assert
        assertEquals(1, actualTimeZonesLike.size());
        assertEquals("Africa/Bangui", actualTimeZonesLike.get(0));
    }

    /**
     * Test {@link EmailPreferencesService#getTimeZonesLike(String)}.
     * <ul>
     *   <li>When empty string.</li>
     *   <li>Then return size is four hundred eighteen.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getTimeZonesLike(String)}
     */
    @Test
    @DisplayName("Test getTimeZonesLike(String); when empty string; then return size is four hundred eighteen")
    void testGetTimeZonesLike_whenEmptyString_thenReturnSizeIsFourHundredEighteen() {
        // Arrange and Act
        List<String> actualTimeZonesLike = emailPreferencesService.getTimeZonesLike("");

        // Assert
        assertEquals(418, actualTimeZonesLike.size());
        assertEquals("Africa/Accra", actualTimeZonesLike.get(1));
        assertEquals("Africa/Addis_Ababa", actualTimeZonesLike.get(2));
        assertEquals("Africa/Algiers", actualTimeZonesLike.get(3));
        assertEquals("Africa/Asmera", actualTimeZonesLike.get(4));
        assertEquals("Africa/Bamako", actualTimeZonesLike.get(5));
        assertEquals("Pacific/Tahiti", actualTimeZonesLike.get(412));
        assertEquals("Pacific/Tarawa", actualTimeZonesLike.get(413));
        assertEquals("Pacific/Tongatapu", actualTimeZonesLike.get(414));
        assertEquals("Pacific/Truk", actualTimeZonesLike.get(415));
        assertEquals("Pacific/Wake", actualTimeZonesLike.get(416));
        assertEquals("Pacific/Wallis", actualTimeZonesLike.get(417));
    }

    /**
     * Test {@link EmailPreferencesService#getTimeZonesLike(String)}.
     * <ul>
     *   <li>When {@code Pattern}.</li>
     *   <li>Then return Empty.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailPreferencesService#getTimeZonesLike(String)}
     */
    @Test
    @DisplayName("Test getTimeZonesLike(String); when 'Pattern'; then return Empty")
    void testGetTimeZonesLike_whenPattern_thenReturnEmpty() {
        // Arrange, Act and Assert
        assertTrue(emailPreferencesService.getTimeZonesLike("Pattern").isEmpty());
    }

    /**
     * Test {@link EmailPreferencesService#updateNotificationInfo(Boolean, String)}.
     * <ul>
     *   <li>Then return {@code success}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailPreferencesService#updateNotificationInfo(Boolean, String)}
     */
    @Test
    @DisplayName("Test updateNotificationInfo(Boolean, String); then return 'success'")
    void testUpdateNotificationInfo_thenReturnSuccess() {
        // Arrange
        EmailPreferences emailPreferences = new EmailPreferences();
        emailPreferences.setAllowNotification(true);
        emailPreferences.setBlackListedDomain("Black Listed Domain");
        emailPreferences.setBlackListedSender("Black Listed Sender");
        emailPreferences.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences.setContactNumber("42");
        emailPreferences.setConversationId("42");
        emailPreferences
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setDateFormat("2020-03-01");
        emailPreferences.setDebugMode(true);
        emailPreferences.setDeviceId("42");
        emailPreferences.setDisplayName("Display Name");
        emailPreferences.setEmailSender("<EMAIL>");
        emailPreferences.setEmailSubject("<EMAIL>");
        emailPreferences.setFontColor("Font Color");
        emailPreferences.setFontFamily("Font Family");
        emailPreferences.setFontSize("Font Size");
        emailPreferences.setGcmId("42");
        emailPreferences.setId(1);
        emailPreferences.setImportantTags("Important Tags");
        emailPreferences.setIsCategoryEnabled(true);
        emailPreferences.setIsPriorityEnabled(true);
        emailPreferences.setIsToneEnabled(true);
        emailPreferences.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setMaskContent(true);
        emailPreferences.setMeetingType("Meeting Type");
        emailPreferences
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences.setOnDemandEnabled(true);
        emailPreferences.setPreferredMeetingDuration(1);
        emailPreferences.setSenderCompany("Sender Company");
        emailPreferences.setTimeFormat("Time Format");
        emailPreferences.setTimeZone("UTC");
        emailPreferences.setUserId("42");

        EmailPreferences emailPreferences2 = new EmailPreferences();
        emailPreferences2.setAllowNotification(true);
        emailPreferences2.setBlackListedDomain("Black Listed Domain");
        emailPreferences2.setBlackListedSender("Black Listed Sender");
        emailPreferences2.setBlackListedSubject("Hello from the Dreaming Spires");
        emailPreferences2.setCheckin(LocalTime.MIDNIGHT);
        emailPreferences2.setCheckout(LocalTime.MIDNIGHT);
        emailPreferences2.setContactNumber("42");
        emailPreferences2.setConversationId("42");
        emailPreferences2
                .setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setDateFormat("2020-03-01");
        emailPreferences2.setDebugMode(true);
        emailPreferences2.setDeviceId("42");
        emailPreferences2.setDisplayName("Display Name");
        emailPreferences2.setEmailSender("<EMAIL>");
        emailPreferences2.setEmailSubject("<EMAIL>");
        emailPreferences2.setFontColor("Font Color");
        emailPreferences2.setFontFamily("Font Family");
        emailPreferences2.setFontSize("Font Size");
        emailPreferences2.setGcmId("42");
        emailPreferences2.setId(1);
        emailPreferences2.setImportantTags("Important Tags");
        emailPreferences2.setIsCategoryEnabled(true);
        emailPreferences2.setIsPriorityEnabled(true);
        emailPreferences2.setIsToneEnabled(true);
        emailPreferences2.setKeyboardShortcuts("Keyboard Shortcuts");
        emailPreferences2.setLastPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setMaskContent(true);
        emailPreferences2.setMeetingType("Meeting Type");
        emailPreferences2
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        emailPreferences2.setNextPollTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        emailPreferences2.setOnDemandEnabled(true);
        emailPreferences2.setPreferredMeetingDuration(1);
        emailPreferences2.setSenderCompany("Sender Company");
        emailPreferences2.setTimeFormat("Time Format");
        emailPreferences2.setTimeZone("UTC");
        emailPreferences2.setUserId("42");
        when(iEmailPreferencesDao.save(Mockito.<EmailPreferences>any())).thenReturn(emailPreferences2);
        when(iEmailPreferencesDao.getEmailPreferencesByUserId(Mockito.<String>any())).thenReturn(emailPreferences);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        String actualUpdateNotificationInfoResult = emailPreferencesService.updateNotificationInfo(true, "42");

        // Assert
        verify(iEmailPreferencesDao).getEmailPreferencesByUserId(eq("42"));
        verify(userContextHolder).getCurrentUser();
        verify(iEmailPreferencesDao).save(isA(EmailPreferences.class));
        assertEquals("success", actualUpdateNotificationInfoResult);
    }
}
