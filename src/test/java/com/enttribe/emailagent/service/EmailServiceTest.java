package com.enttribe.emailagent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.enttribe.emailagent.dao.MeetingSummaryDao;
import com.enttribe.emailagent.dto.UnReadEmail;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.MeetingSummary;
import com.enttribe.emailagent.entity.UserFolders;
import com.enttribe.emailagent.exception.ResourceNotFoundException;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.UserFoldersRepository;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;

import java.time.LocalDate;
import java.time.ZoneOffset;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.repository.CrudRepository;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {EmailService.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class EmailServiceTest {
    @Autowired
    private EmailService emailService;

    @MockBean
    private EwsService ewsService;

    @MockBean
    private GmailIntegration gmailIntegration;

    @MockBean
    private GraphIntegrationService graphIntegrationService;

    @MockBean
    private IMailSummaryDao iMailSummaryDao;

    @MockBean
    private MeetingSummaryDao meetingSummaryDao;

    @MockBean
    private PollTimeInfoService pollTimeInfoService;

    @MockBean
    private UserContextHolder userContextHolder;

    @MockBean
    private UserFoldersRepository userFoldersRepository;

    /**
     * Test {@link EmailService#getReceivedDateTimeFilter(String)}.
     * <p>
     * Method under test: {@link EmailService#getReceivedDateTimeFilter(String)}
     */
    @Test
    @DisplayName("Test getReceivedDateTimeFilter(String)")
    void testGetReceivedDateTimeFilter() {
        // Arrange
        when(pollTimeInfoService.fetchTimeToPoll(Mockito.<String>any()))
                .thenReturn(LocalDate.of(1970, 1, 1).atStartOfDay());

        // Act
        emailService.getReceivedDateTimeFilter("<EMAIL>");

        // Assert
        verify(pollTimeInfoService).fetchTimeToPoll(eq("<EMAIL>"));
    }

    /**
     * Test {@link EmailService#getReceivedDateTimeFilter(String)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getReceivedDateTimeFilter(String)}
     */
    @Test
    @DisplayName("Test getReceivedDateTimeFilter(String); then throw ResourceNotFoundException")
    void testGetReceivedDateTimeFilter_thenThrowResourceNotFoundException() {
        // Arrange
        when(pollTimeInfoService.fetchTimeToPoll(Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailService.getReceivedDateTimeFilter("<EMAIL>"));
        verify(pollTimeInfoService).fetchTimeToPoll(eq("<EMAIL>"));
    }

    /**
     * Test {@link EmailService#updatePriority(String, String)}.
     * <ul>
     *   <li>Given {@link IMailSummaryDao} {@link CrudRepository#save(Object)} return
     * {@link MailSummary} (default constructor).</li>
     *   <li>Then return {@code success}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#updatePriority(String, String)}
     */
    @Test
    @DisplayName("Test updatePriority(String, String); given IMailSummaryDao save(Object) return MailSummary (default constructor); then return 'success'")
    void testUpdatePriority_givenIMailSummaryDaoSaveReturnMailSummary_thenReturnSuccess() {
        // Arrange
        MailSummary mailSummary = new MailSummary();
        mailSummary.setActionClearReason("Just cause");
        mailSummary.setActionOwner("Action Owner");
        mailSummary.setActionTaken(true);
        mailSummary.setAttachmentsList(new ArrayList<>());
        mailSummary.setCategory("Category");
        mailSummary.setCategoryReason("Just cause");
        mailSummary.setCcUser("Cc User");
        mailSummary.setConversationId("42");
        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setDecryptSummary("Decrypt Summary");
        mailSummary.setDeleted(true);
        mailSummary.setEmailTone("<EMAIL>");
        mailSummary.setEmailToneReason("Just cause");
        mailSummary.setFlagStatus("Flag Status");
        mailSummary.setFolderName("Folder Name");
        mailSummary.setFromUser("<EMAIL>");
        mailSummary.setHasAttachment(true);
        mailSummary.setId(1);
        mailSummary.setInternetMessageId("42");
        mailSummary.setIsActionable(true);
        mailSummary.setIsAttention(true);
        mailSummary.setIsHighPriority(true);
        mailSummary.setIsStarMarked(true);
        mailSummary.setIsUnread(true);
        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary.setMeetingPreview("Meeting Preview");
        mailSummary.setMessageId("42");
        mailSummary.setMessageSummary("Message Summary");
        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary.setObjective("Objective");
        mailSummary.setPriority("Priority");
        mailSummary.setPriorityReason("Just cause");
        mailSummary.setStarMarkReason("Just cause");
        mailSummary.setStarMarked(true);
        mailSummary.setSubject("Hello from the Dreaming Spires");
        mailSummary.setTag("Tag");
        mailSummary.setToUser("To User");
        mailSummary.setType("Type");
        mailSummary.setUserActions(new HashSet<>());
        mailSummary.setUserId("42");

        MailSummary mailSummary2 = new MailSummary();
        mailSummary2.setActionClearReason("Just cause");
        mailSummary2.setActionOwner("Action Owner");
        mailSummary2.setActionTaken(true);
        mailSummary2.setAttachmentsList(new ArrayList<>());
        mailSummary2.setCategory("Category");
        mailSummary2.setCategoryReason("Just cause");
        mailSummary2.setCcUser("Cc User");
        mailSummary2.setConversationId("42");
        mailSummary2.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary2.setDecryptSummary("Decrypt Summary");
        mailSummary2.setDeleted(true);
        mailSummary2.setEmailTone("<EMAIL>");
        mailSummary2.setEmailToneReason("Just cause");
        mailSummary2.setFlagStatus("Flag Status");
        mailSummary2.setFolderName("Folder Name");
        mailSummary2.setFromUser("<EMAIL>");
        mailSummary2.setHasAttachment(true);
        mailSummary2.setId(1);
        mailSummary2.setInternetMessageId("42");
        mailSummary2.setIsActionable(true);
        mailSummary2.setIsAttention(true);
        mailSummary2.setIsHighPriority(true);
        mailSummary2.setIsStarMarked(true);
        mailSummary2.setIsUnread(true);
        mailSummary2.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary2.setMeetingPreview("Meeting Preview");
        mailSummary2.setMessageId("42");
        mailSummary2.setMessageSummary("Message Summary");
        mailSummary2.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary2.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary2.setObjective("Objective");
        mailSummary2.setPriority("Priority");
        mailSummary2.setPriorityReason("Just cause");
        mailSummary2.setStarMarkReason("Just cause");
        mailSummary2.setStarMarked(true);
        mailSummary2.setSubject("Hello from the Dreaming Spires");
        mailSummary2.setTag("Tag");
        mailSummary2.setToUser("To User");
        mailSummary2.setType("Type");
        mailSummary2.setUserActions(new HashSet<>());
        mailSummary2.setUserId("42");
        when(iMailSummaryDao.save(Mockito.<MailSummary>any())).thenReturn(mailSummary2);
        when(iMailSummaryDao.findByInternetMessageId(Mockito.<String>any(), Mockito.<String>any())).thenReturn(mailSummary);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act
        String actualUpdatePriorityResult = emailService.updatePriority("42", "Priority");

        // Assert
        verify(iMailSummaryDao).findByInternetMessageId(eq("42"), eq("42"));
        verify(userContextHolder).getCurrentUser();
        verify(iMailSummaryDao).save(isA(MailSummary.class));
        assertEquals("success", actualUpdatePriorityResult);
    }

    /**
     * Test {@link EmailService#updatePriority(String, String)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#updatePriority(String, String)}
     */
    @Test
    @DisplayName("Test updatePriority(String, String); then throw ResourceNotFoundException")
    void testUpdatePriority_thenThrowResourceNotFoundException() {
        // Arrange
        MailSummary mailSummary = new MailSummary();
        mailSummary.setActionClearReason("Just cause");
        mailSummary.setActionOwner("Action Owner");
        mailSummary.setActionTaken(true);
        mailSummary.setAttachmentsList(new ArrayList<>());
        mailSummary.setCategory("Category");
        mailSummary.setCategoryReason("Just cause");
        mailSummary.setCcUser("Cc User");
        mailSummary.setConversationId("42");
        mailSummary.setCreatedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setDecryptSummary("Decrypt Summary");
        mailSummary.setDeleted(true);
        mailSummary.setEmailTone("<EMAIL>");
        mailSummary.setEmailToneReason("Just cause");
        mailSummary.setFlagStatus("Flag Status");
        mailSummary.setFolderName("Folder Name");
        mailSummary.setFromUser("<EMAIL>");
        mailSummary.setHasAttachment(true);
        mailSummary.setId(1);
        mailSummary.setInternetMessageId("42");
        mailSummary.setIsActionable(true);
        mailSummary.setIsAttention(true);
        mailSummary.setIsHighPriority(true);
        mailSummary.setIsStarMarked(true);
        mailSummary.setIsUnread(true);
        mailSummary.setMailReceivedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        mailSummary.setMeetingPreview("Meeting Preview");
        mailSummary.setMessageId("42");
        mailSummary.setMessageSummary("Message Summary");
        mailSummary.setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        mailSummary.setNotificationStatus(MailSummary.NotificationStatus.SENT);
        mailSummary.setObjective("Objective");
        mailSummary.setPriority("Priority");
        mailSummary.setPriorityReason("Just cause");
        mailSummary.setStarMarkReason("Just cause");
        mailSummary.setStarMarked(true);
        mailSummary.setSubject("Hello from the Dreaming Spires");
        mailSummary.setTag("Tag");
        mailSummary.setToUser("To User");
        mailSummary.setType("Type");
        mailSummary.setUserActions(new HashSet<>());
        mailSummary.setUserId("42");
        when(iMailSummaryDao.save(Mockito.<MailSummary>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));
        when(iMailSummaryDao.findByInternetMessageId(Mockito.<String>any(), Mockito.<String>any())).thenReturn(mailSummary);

        UserInfo userInfo = new UserInfo();
        userInfo.setEmail("<EMAIL>");
        userInfo.setExpiration(LocalDate.of(1970, 1, 1).atStartOfDay());
        userInfo.setId("42");
        when(userContextHolder.getCurrentUser()).thenReturn(userInfo);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailService.updatePriority("42", "Priority"));
        verify(iMailSummaryDao).findByInternetMessageId(eq("42"), eq("42"));
        verify(userContextHolder).getCurrentUser();
        verify(iMailSummaryDao).save(isA(MailSummary.class));
    }

    /**
     * Test {@link EmailService#getMeetingSummaryByInternetMessageId(String)}.
     * <ul>
     *   <li>Then return {@link MeetingSummary} (default constructor).</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailService#getMeetingSummaryByInternetMessageId(String)}
     */
    @Test
    @DisplayName("Test getMeetingSummaryByInternetMessageId(String); then return MeetingSummary (default constructor)")
    void testGetMeetingSummaryByInternetMessageId_thenReturnMeetingSummary() {
        // Arrange
        MeetingSummary meetingSummary = new MeetingSummary();
        meetingSummary.setActionItems("Action Items");
        meetingSummary
                .setCreationTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        meetingSummary.setId(1);
        meetingSummary.setInternetMessageId("42");
        meetingSummary.setLongSummary("Long Summary");
        meetingSummary.setMeetingId("42");
        meetingSummary
                .setModifiedTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        meetingSummary.setProcessingError("An error occurred");
        meetingSummary.setShortSummary("Short Summary");
        meetingSummary.setStatus("Status");
        meetingSummary.setSubject("Hello from the Dreaming Spires");
        meetingSummary.setUserId("42");
        when(meetingSummaryDao.findMeetingSummaryByInternetMessageId(Mockito.<String>any())).thenReturn(meetingSummary);

        // Act
        MeetingSummary actualMeetingSummaryByInternetMessageId = emailService.getMeetingSummaryByInternetMessageId("42");

        // Assert
        verify(meetingSummaryDao).findMeetingSummaryByInternetMessageId(eq("42"));
        assertSame(meetingSummary, actualMeetingSummaryByInternetMessageId);
    }

    /**
     * Test {@link EmailService#getMeetingSummaryByInternetMessageId(String)}.
     * <ul>
     *   <li>Then throw {@link ResourceNotFoundException}.</li>
     * </ul>
     * <p>
     * Method under test:
     * {@link EmailService#getMeetingSummaryByInternetMessageId(String)}
     */
    @Test
    @DisplayName("Test getMeetingSummaryByInternetMessageId(String); then throw ResourceNotFoundException")
    void testGetMeetingSummaryByInternetMessageId_thenThrowResourceNotFoundException() {
        // Arrange
        when(meetingSummaryDao.findMeetingSummaryByInternetMessageId(Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailService.getMeetingSummaryByInternetMessageId("42"));
        verify(meetingSummaryDao).findMeetingSummaryByInternetMessageId(eq("42"));
    }

    /**
     * Test {@link EmailService#getUnreadMail(String, String)}.
     * <p>
     * Method under test: {@link EmailService#getUnreadMail(String, String)}
     */
    @Test
    @DisplayName("Test getUnreadMail(String, String)")
    void testGetUnreadMail() {
        // Arrange
        when(graphIntegrationService.getUnreadEmails(Mockito.<String>any(), Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailService.getUnreadMail("User Mail", "User Folder Name"));
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
        verify(graphIntegrationService).getUnreadEmails(eq("User Mail"), eq("42"));
    }

    /**
     * Test {@link EmailService#getUnreadMail(String, String)}.
     * <p>
     * Method under test: {@link EmailService#getUnreadMail(String, String)}
     */
    @Test
    @DisplayName("Test getUnreadMail(String, String)")
    void testGetUnreadMail2() {
        // Arrange
        when(userFoldersRepository.getActiveMailFoldersByEmail(Mockito.<String>any())).thenReturn(new ArrayList<>());
        UserFolders userFolders = mock(UserFolders.class);
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        // Act
        List<UnReadEmail> actualUnreadMail = emailService.getUnreadMail("User Mail", null);

        // Assert
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).getActiveMailFoldersByEmail(eq("User Mail"));
        assertTrue(actualUnreadMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getUnreadMail(String, String)}.
     * <p>
     * Method under test: {@link EmailService#getUnreadMail(String, String)}
     */
    @Test
    @DisplayName("Test getUnreadMail(String, String)")
    void testGetUnreadMail3() {
        // Arrange
        when(userFoldersRepository.getActiveMailFoldersByEmail(Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));
        UserFolders userFolders = mock(UserFolders.class);
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailService.getUnreadMail("User Mail", null));
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).getActiveMailFoldersByEmail(eq("User Mail"));
    }

    /**
     * Test {@link EmailService#getUnreadMail(String, String)}.
     * <ul>
     *   <li>Given {@link UserFolders} {@link UserFolders#getDisplayName()} return
     * {@code Deleted Items}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getUnreadMail(String, String)}
     */
    @Test
    @DisplayName("Test getUnreadMail(String, String); given UserFolders getDisplayName() return 'Deleted Items'")
    void testGetUnreadMail_givenUserFoldersGetDisplayNameReturnDeletedItems() {
        // Arrange
        UserFolders userFolders = mock(UserFolders.class);
        when(userFolders.getDisplayName()).thenReturn("Deleted Items");
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act
        List<UnReadEmail> actualUnreadMail = emailService.getUnreadMail("User Mail", "User Folder Name");

        // Assert
        verify(userFolders, atLeast(1)).getDisplayName();
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
        assertTrue(actualUnreadMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getUnreadMail(String, String)}.
     * <ul>
     *   <li>Given {@link UserFolders} {@link UserFolders#getDisplayName()} return
     * {@code SENT}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getUnreadMail(String, String)}
     */
    @Test
    @DisplayName("Test getUnreadMail(String, String); given UserFolders getDisplayName() return 'SENT'")
    void testGetUnreadMail_givenUserFoldersGetDisplayNameReturnSent() {
        // Arrange
        UserFolders userFolders = mock(UserFolders.class);
        when(userFolders.getDisplayName()).thenReturn("SENT");
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act
        List<UnReadEmail> actualUnreadMail = emailService.getUnreadMail("User Mail", "User Folder Name");

        // Assert
        verify(userFolders, atLeast(1)).getDisplayName();
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
        assertTrue(actualUnreadMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getUnreadMail(String, String)}.
     * <ul>
     *   <li>Given {@link UserFolders} {@link UserFolders#getDisplayName()} return
     * {@code Sent Items}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getUnreadMail(String, String)}
     */
    @Test
    @DisplayName("Test getUnreadMail(String, String); given UserFolders getDisplayName() return 'Sent Items'")
    void testGetUnreadMail_givenUserFoldersGetDisplayNameReturnSentItems() {
        // Arrange
        UserFolders userFolders = mock(UserFolders.class);
        when(userFolders.getDisplayName()).thenReturn("Sent Items");
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act
        List<UnReadEmail> actualUnreadMail = emailService.getUnreadMail("User Mail", "User Folder Name");

        // Assert
        verify(userFolders).getDisplayName();
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
        assertTrue(actualUnreadMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getUnreadMail(String, String)}.
     * <ul>
     *   <li>Then calls {@link UserFolders#getFolderId()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getUnreadMail(String, String)}
     */
    @Test
    @DisplayName("Test getUnreadMail(String, String); then calls getFolderId()")
    void testGetUnreadMail_thenCallsGetFolderId() {
        // Arrange
        UserFolders userFolders = mock(UserFolders.class);
        when(userFolders.getFolderId()).thenThrow(new ResourceNotFoundException("An error occurred"));
        when(userFolders.getDisplayName()).thenReturn("Display Name");
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailService.getUnreadMail("User Mail", "User Folder Name"));
        verify(userFolders, atLeast(1)).getDisplayName();
        verify(userFolders).getFolderId();
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
    }

    /**
     * Test {@link EmailService#getUnreadMail(String, String)}.
     * <ul>
     *   <li>Then calls
     * {@link GraphIntegrationService#getUnreadEmails(String, String)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getUnreadMail(String, String)}
     */
    @Test
    @DisplayName("Test getUnreadMail(String, String); then calls getUnreadEmails(String, String)")
    void testGetUnreadMail_thenCallsGetUnreadEmails() {
        // Arrange
        when(graphIntegrationService.getUnreadEmails(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act
        List<UnReadEmail> actualUnreadMail = emailService.getUnreadMail("User Mail", "User Folder Name");

        // Assert
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
        verify(graphIntegrationService).getUnreadEmails(eq("User Mail"), eq("42"));
        assertTrue(actualUnreadMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getUnreadMail(String, String)}.
     * <ul>
     *   <li>When empty string.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getUnreadMail(String, String)}
     */
    @Test
    @DisplayName("Test getUnreadMail(String, String); when empty string")
    void testGetUnreadMail_whenEmptyString() {
        // Arrange
        when(userFoldersRepository.getActiveMailFoldersByEmail(Mockito.<String>any())).thenReturn(new ArrayList<>());
        UserFolders userFolders = mock(UserFolders.class);
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        // Act
        List<UnReadEmail> actualUnreadMail = emailService.getUnreadMail("User Mail", "");

        // Assert
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).getActiveMailFoldersByEmail(eq("User Mail"));
        assertTrue(actualUnreadMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getFlagMail(String, String)}.
     * <p>
     * Method under test: {@link EmailService#getFlagMail(String, String)}
     */
    @Test
    @DisplayName("Test getFlagMail(String, String)")
    void testGetFlagMail() {
        // Arrange
        when(graphIntegrationService.getFlaggedEmails(Mockito.<String>any(), Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));

        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailService.getFlagMail("User Mail", "User Folder Name"));
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
        verify(graphIntegrationService).getFlaggedEmails(eq("User Mail"), eq("42"));
    }

    /**
     * Test {@link EmailService#getFlagMail(String, String)}.
     * <p>
     * Method under test: {@link EmailService#getFlagMail(String, String)}
     */
    @Test
    @DisplayName("Test getFlagMail(String, String)")
    void testGetFlagMail2() {
        // Arrange
        when(userFoldersRepository.getActiveMailFoldersByEmail(Mockito.<String>any())).thenReturn(new ArrayList<>());
        UserFolders userFolders = mock(UserFolders.class);
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        // Act
        List<String> actualFlagMail = emailService.getFlagMail("User Mail", null);

        // Assert
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).getActiveMailFoldersByEmail(eq("User Mail"));
        assertTrue(actualFlagMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getFlagMail(String, String)}.
     * <p>
     * Method under test: {@link EmailService#getFlagMail(String, String)}
     */
    @Test
    @DisplayName("Test getFlagMail(String, String)")
    void testGetFlagMail3() {
        // Arrange
        when(userFoldersRepository.getActiveMailFoldersByEmail(Mockito.<String>any()))
                .thenThrow(new ResourceNotFoundException("An error occurred"));
        UserFolders userFolders = mock(UserFolders.class);
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailService.getFlagMail("User Mail", null));
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).getActiveMailFoldersByEmail(eq("User Mail"));
    }

    /**
     * Test {@link EmailService#getFlagMail(String, String)}.
     * <ul>
     *   <li>Given {@link UserFolders} {@link UserFolders#getDisplayName()} return
     * {@code Deleted Items}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getFlagMail(String, String)}
     */
    @Test
    @DisplayName("Test getFlagMail(String, String); given UserFolders getDisplayName() return 'Deleted Items'")
    void testGetFlagMail_givenUserFoldersGetDisplayNameReturnDeletedItems() {
        // Arrange
        UserFolders userFolders = mock(UserFolders.class);
        when(userFolders.getDisplayName()).thenReturn("Deleted Items");
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act
        List<String> actualFlagMail = emailService.getFlagMail("User Mail", "User Folder Name");

        // Assert
        verify(userFolders, atLeast(1)).getDisplayName();
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
        assertTrue(actualFlagMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getFlagMail(String, String)}.
     * <ul>
     *   <li>Given {@link UserFolders} {@link UserFolders#getDisplayName()} return
     * {@code Sent Items}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getFlagMail(String, String)}
     */
    @Test
    @DisplayName("Test getFlagMail(String, String); given UserFolders getDisplayName() return 'Sent Items'")
    void testGetFlagMail_givenUserFoldersGetDisplayNameReturnSentItems() {
        // Arrange
        UserFolders userFolders = mock(UserFolders.class);
        when(userFolders.getDisplayName()).thenReturn("Sent Items");
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act
        List<String> actualFlagMail = emailService.getFlagMail("User Mail", "User Folder Name");

        // Assert
        verify(userFolders).getDisplayName();
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
        assertTrue(actualFlagMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getFlagMail(String, String)}.
     * <ul>
     *   <li>Given {@link UserFolders} {@link UserFolders#getDisplayName()} return
     * {@code SENT}.</li>
     *   <li>Then calls {@link UserFolders#getDisplayName()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getFlagMail(String, String)}
     */
    @Test
    @DisplayName("Test getFlagMail(String, String); given UserFolders getDisplayName() return 'SENT'; then calls getDisplayName()")
    void testGetFlagMail_givenUserFoldersGetDisplayNameReturnSent_thenCallsGetDisplayName() {
        // Arrange
        UserFolders userFolders = mock(UserFolders.class);
        when(userFolders.getDisplayName()).thenReturn("SENT");
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act
        List<String> actualFlagMail = emailService.getFlagMail("User Mail", "User Folder Name");

        // Assert
        verify(userFolders, atLeast(1)).getDisplayName();
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
        assertTrue(actualFlagMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getFlagMail(String, String)}.
     * <ul>
     *   <li>Then calls
     * {@link GraphIntegrationService#getFlaggedEmails(String, String)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getFlagMail(String, String)}
     */
    @Test
    @DisplayName("Test getFlagMail(String, String); then calls getFlaggedEmails(String, String)")
    void testGetFlagMail_thenCallsGetFlaggedEmails() {
        // Arrange
        when(graphIntegrationService.getFlaggedEmails(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        UserFolders userFolders = new UserFolders();
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act
        List<String> actualFlagMail = emailService.getFlagMail("User Mail", "User Folder Name");

        // Assert
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
        verify(graphIntegrationService).getFlaggedEmails(eq("User Mail"), eq("42"));
        assertTrue(actualFlagMail.isEmpty());
    }

    /**
     * Test {@link EmailService#getFlagMail(String, String)}.
     * <ul>
     *   <li>Then calls {@link UserFolders#getFolderId()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getFlagMail(String, String)}
     */
    @Test
    @DisplayName("Test getFlagMail(String, String); then calls getFolderId()")
    void testGetFlagMail_thenCallsGetFolderId() {
        // Arrange
        UserFolders userFolders = mock(UserFolders.class);
        when(userFolders.getFolderId()).thenThrow(new ResourceNotFoundException("An error occurred"));
        when(userFolders.getDisplayName()).thenReturn("Display Name");
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);
        when(userFoldersRepository.findByDisplayNameAndEmail(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(userFolders);

        // Act and Assert
        assertThrows(ResourceNotFoundException.class, () -> emailService.getFlagMail("User Mail", "User Folder Name"));
        verify(userFolders, atLeast(1)).getDisplayName();
        verify(userFolders).getFolderId();
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).findByDisplayNameAndEmail(eq("User Folder Name"), eq("User Mail"));
    }

    /**
     * Test {@link EmailService#getFlagMail(String, String)}.
     * <ul>
     *   <li>When empty string.</li>
     * </ul>
     * <p>
     * Method under test: {@link EmailService#getFlagMail(String, String)}
     */
    @Test
    @DisplayName("Test getFlagMail(String, String); when empty string")
    void testGetFlagMail_whenEmptyString() {
        // Arrange
        when(userFoldersRepository.getActiveMailFoldersByEmail(Mockito.<String>any())).thenReturn(new ArrayList<>());
        UserFolders userFolders = mock(UserFolders.class);
        doNothing().when(userFolders).setActive(anyBoolean());
        doNothing().when(userFolders).setDisplayName(Mockito.<String>any());
        doNothing().when(userFolders).setEmail(Mockito.<String>any());
        doNothing().when(userFolders).setFolderId(Mockito.<String>any());
        doNothing().when(userFolders).setId(Mockito.<Integer>any());
        userFolders.setActive(true);
        userFolders.setDisplayName("Display Name");
        userFolders.setEmail("<EMAIL>");
        userFolders.setFolderId("42");
        userFolders.setId(1);

        // Act
        List<String> actualFlagMail = emailService.getFlagMail("User Mail", "");

        // Assert
        verify(userFolders).setActive(eq(true));
        verify(userFolders).setDisplayName(eq("Display Name"));
        verify(userFolders).setEmail(eq("<EMAIL>"));
        verify(userFolders).setFolderId(eq("42"));
        verify(userFolders).setId(eq(1));
        verify(userFoldersRepository).getActiveMailFoldersByEmail(eq("User Mail"));
        assertTrue(actualFlagMail.isEmpty());
    }
}
