#-target 21 ##Specify the java version number
-verbose
-dontshrink ##Default is enabled, here the shrink is turned off, that is, the unused classes/members are not deleted.
-dontoptimize ##Default is enabled, here to turn off bytecode level optimization
-useuniqueclassmembernames ## Take a unique strategy for confusing the naming of class members
-adaptclassstrings ## After confusing the class name, replace it with a place like Class.forName('className')
-dontnote
-ignorewarnings ## warnings are ignored
-dontwarn
#-keep public class * extends org.springframework.boot.web.support.SpringBootServletInitializer
#-keepdirectories ## Keep the package structure
-keepclasseswithmembers public class * { public static void main(java.lang.String[]);} ##Maintain the class of the main method and its method name
-keepclassmembers enum * { *; }  ##Reserving enumeration members and methods
-keep @jakarta.persistence.* class * { 
	*; 
}
-keepparameternames
-keepclassmembers class * {
     @org.springframework.beans.factory.annotation.Autowired *;
     @org.springframework.beans.factory.annotation.Qualifier *;
     @org.springframework.beans.factory.annotation.Value *;
     @org.springframework.beans.factory.annotation.Required *;
     @org.springframework.context.annotation.Bean *;
     @org.springframework.context.annotation.Primary *;
     @org.springframework.boot.context.properties.ConfigurationProperties *;
     @org.springframework.boot.context.properties.EnableConfigurationProperties *;
     @jakarta.inject.Inject *;
     @jakarta.annotation.PostConstruct *;
     @jakarta.annotation.PreDestroy *;
     @org.aspectj.lang.annotation.* *;
     @org.springframework.cache.annotation.Cacheable *;
     @org.springframework.cache.annotation.CacheEvict *;
}
-keep @org.springframework.cache.annotation.EnableCaching class *
-keep @org.springframework.context.annotation.Configuration class *
-keep @org.springframework.boot.context.properties.ConfigurationProperties class ** {
<fields>;
void set*(***);
  *** get*();
}

#-keep @org.springframework.boot.context.properties.ConfigurationProperties class * {*;}
-keep @org.springframework.boot.autoconfigure.SpringBootApplication class *
-keep @org.springframework.web.bind.annotation.ControllerAdvice class *
-keep @org.springframework.cloud.openfeign.FeignClient interface *
-allowaccessmodification
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,*Annotation*,EnclosingMethod,AnnotationDefault,LineNumberTable,SourceFile
#-adaptresourcefilenames    **.properties,**.xml,**.xhtml,**.jsp,**.gif,**.jpg,**.png
#-adaptresourcefilecontents **.properties,**.xml,**.xhtml,**.jsp
#-keepdirectories com
#-keepdirectories org.springframework.boot.autoconfigure
## Do not change names of the getters and setter, if you remove this ##thymeleaf unable to find the getter and setter i.e: ##${greetingDTO.message}
#-keepclassmembers class * {
#   *** get*();
#   void set*(***);
#}
-keepclassmembernames class * {
     java.lang.Class class$(java.lang.String);
     java.lang.Class class$(java.lang.String, boolean);
}
-keepclassmembers enum * {
     public static **[] values();
     public static ** valueOf(java.lang.String);
     public static ** fromValue(java.lang.String);
}
-keepnames class * implements java.io.Serializable
#-keepnames class ** 
-keep interface * extends org.springframework.data.jpa.repository.JpaRepository { *; }
-keep @org.springframework.web.bind.annotation.RestController class *
-keep @org.springframework.stereotype.** class *
-keepclassmembers class * implements java.io.Serializable {
     static final long serialVersionUID;
     private static final java.io.ObjectStreamField[] serialPersistentFields;
     !static !transient <fields>;
     !private <fields>;
     !private <methods>;
     private void writeObject(java.io.ObjectOutputStream);
     private void readObject(java.io.ObjectInputStream);
     java.lang.Object writeReplace();
     java.lang.Object readResolve();
}
-keepclassmembers class * {
     @org.springframework.beans.factory.annotation.Autowired <fields>;
     @org.springframework.beans.factory.annotation.Autowired <methods>;
     @org.springframework.cache.annotation.Cacheable public *** *(...);
#     @org.springframework.security.access.prepost.PreAuthorize <methods>;
}

-keepclassmembers class com.enttribe.**.model.* {
    private <fields>;
}

-keepclassmembers class com.enttribe.**.wrapper.* {
    <fields>;
    <methods>;
}
-keepclassmembers class com.enttribe.**.*Wrapper*{*;}
-keepclassmembers @org.springframework.boot.context.properties.ConfigurationProperties class * {<fields>;}

-keep class com.enttribe.**.model.*{*;}
-keep class com.enttribe.**.wrapper.*{*;}
-keep class com.enttribe.**.*Wrapper*{*;}
-keep interface com.enttribe.core.generic.utils.Monitorable{*;}
-keep class com.enttribe.**.rest.*{*;}


-keep class org.**{*;}
-keep class cz.**{*;}


-libraryjars /Users/<USER>/Documents/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.25/(!tomcat-embed-core-10.1.25.jar;)
